APP_ENV=local
APP_KEY=

APP_DEBUG=true
API_DEBUG=true
REQUESTS_DEBUG=true
QUERIES_DEBUG=false
DEBUGBAR_ENABLED=false
TEST_RESPONSE_LOGGER_ENABLED=true

APP_LOG_LEVEL=debug
LOG_CHANNEL=stack

APP_NAME="apiato"
APP_URL=http://apiato.test
API_URL=http://api.apiato.test
API_PREFIX=/

HASH_ID=true
HASH_ID_KEY=apiato
HASH_ID_LENGTH=16

API_RATE_LIMIT_ENABLED=true
API_RATE_LIMIT_ATTEMPTS=30
API_RATE_LIMIT_EXPIRES=1

API_REQUEST_APPLY_REQUEST_CRITERIA=true

PAGINATION_LIMIT_DEFAULT=10
PAGINATION_SKIP=false

API_TOKEN_EXPIRES=1440
API_REFRESH_TOKEN_EXPIRES=43200
API_ENABLE_IMPLICIT_GRANT=true

ELOQUENT_QUERY_CACHE=false
ELOQUENT_QUERY_CACHE_TIME=60

SRC_PATH=app

ROOT_NAMESPACE=App\
USER_NAMESPACE=App\Containers\User\Models\

DB_CONNECTION=mysql
QUEUE_CONNECTION=sync
CACHE_DRIVER=file
SESSION_DRIVER=file
BROADCAST_DRIVER=log
FILESYSTEM_DRIVER=local

DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=apiato_db
DB_USERNAME=root
DB_PASSWORD=secret

REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DATABASE=0

QUEUE_HOST=localhost
QUEUE_DEFAULT=default
QUEUE_RETRY_AFTER=90

SESSION_COOKIE=apiato

MAIL_MAILER=log
MAIL_HOST=smtp.mailtrap.io
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=null
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

STRIPE_KEY=
STRIPE_SECRET=

PAYPAL_ID=
PAYPAL_SECRET=

AUTH_FACEBOOK_CLIENT_ID=
AUTH_FACEBOOK_CLIENT_SECRET=
AUTH_FACEBOOK_CLIENT_REDIRECT=

AUTH_TWITTER_CLIENT_ID=
AUTH_TWITTER_CLIENT_SECRET=
AUTH_TWITTER_CLIENT_REDIRECT=

AUTH_GOOGLE_CLIENT_ID=
AUTH_GOOGLE_CLIENT_SECRET=
AUTH_GOOGLE_CLIENT_REDIRECT=

CLIENT_WEB_ADMIN_ID=
CLIENT_WEB_ADMIN_SECRET=

CLIENT_MOBILE_ADMIN_ID=
CLIENT_MOBILE_ADMIN_SECRET=
