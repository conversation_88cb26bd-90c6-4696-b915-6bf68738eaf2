{"private": true, "scripts": {"dev": "npm run development", "development": "cross-env NODE_ENV=development node_modules/webpack/bin/webpack.js --progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js", "watch": "webpack --watch --watch-poll --config=node_modules/laravel-mix/setup/webpack.config.js", "watch-poll": "npm run watch -- --watch-poll", "hot": "cross-env NODE_ENV=development node_modules/webpack-dev-server/bin/webpack-dev-server.js --inline --hot --config=node_modules/laravel-mix/setup/webpack.config.js", "prod": "npm run production", "production": "cross-env NODE_ENV=production node_modules/webpack/bin/webpack.js --no-progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js", "debug": "cross-env NODE_ENV=development node_modules/webpack/bin/webpack.js --no-progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js"}, "devDependencies": {"apidoc": "^0.19.1", "cross-env": "^5.1", "eslint": "^7.32.0", "eslint-config-airbnb-base": "^14.2.1", "eslint-config-google": "^0.14.0", "eslint-plugin-import": "^2.24.2", "eslint-plugin-vue": "^7.15.1", "laravel-mix": "^4.0.7", "resolve-url-loader": "^3.1.2", "sass": "^1.32.6", "sass-loader": "^7.3.1", "font-awesome": "^4.7.0", "vue-template-compiler": "^2.6.12"}, "dependencies": {"@caohenghu/vue-colorpicker": "github:caohenghu/vue-colorpicker", "@tinymce/tinymce-vue": "^3.2.8", "async-validator": "^3.5.2", "axios": "^0.21.1", "chart.js": "^2.9.4", "core-js": "^2.6.12", "element-ui": "^2.15.2", "laravel-vue-pagination": "^2.3.1", "moment": "^2.29.1", "passport": "^0.4.1", "regenerator-runtime": "^0.13.7", "tinymce": "^5.8.2", "tinymce-vue": "^1.0.0", "vue": "^2.6.12", "vue-chartjs": "^3.5.1", "vue-router": "^3.5.2", "vue-sweetalert2": "^5.0.2", "vuetify": "^2.4.6", "@fortawesome/fontawesome-free": "^6.2.0", "@popperjs/core": "^2.11.6", "bootstrap": "^5.2.1", "bootstrap-scss": "^5.2.1", "vuex": "^3.6.2"}, "name": "hada-mall", "description": "", "version": "1.0.0", "main": ".eslintrc.js", "repository": {"type": "git", "url": "**************:websolutions/hada-mall.git"}, "author": "", "license": "ISC"}