<template>
  <div id="promotionCampaignApp">
       <router-view></router-view>
  </div>
</template>
<script>
// import TagEdit from './TagEdit.vue'
export default {
  name: 'promotionCampaignApp',
  components: { },
  data() {
    return {
    };
  },
  computed: {
    // result () {
    //   return this.$store.state.result
    // }
  },
  mounted() {

  },

  methods: {
  },
}; // End class

</script>
