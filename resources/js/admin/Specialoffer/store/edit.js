export default {
    namespaced: true,
    state: {
      content: {
        id: '',
        image: '',
        status: 1,
        offer_time_start: null,
        offer_time_end: null,
        offer_discount_type: 'price',
        offer_discount_value: '',
      },
      type: {},
      collection: {},
      arrIDs: [],
      editableTabs: {},
    },
    mutations: {
        content(state, payload) {
            state.content = payload;
        },
        setStatus(state, value) {
          state.content.status = value;
        },
        setContentTabPrd(state, value) {
          state.editableTabs[value.tabDetect].content = value.prd;
          state.editableTabs = Object.assign({}, state.editableTabs, state.editableTabs);
        },
        setArrIDs(state) {
          const arrIDs = [];
          Object.values(state.collection).forEach((child) => {
            arrIDs.push(child.id);
          });
          state.arrIDs = arrIDs;
        },
        collectionData(state, payload) {
          const lastKey = parseInt(Object.keys(state.collection).length) + 1;
          if (!state.arrIDs.includes(payload.product_id)) {
              state.collection[lastKey] = {
                id: payload.product_id,
                name: payload.name,
                description: payload.description,
                short_description: payload.short_description,
                sort: 0,
              };
              if (payload.product != null ) {
                state.collection[lastKey].image = payload.product.image ?? '';
                state.collection[lastKey].imageUrl = payload.product.image_url ?? '';
                state.collection[lastKey].categories = {};
                if (typeof payload.product.categories !== 'undefined') {
                        payload.product.categories.forEach((element, index) => {
                        state.collection[lastKey].categories[index] = element.desc;
                    });
                }
              }
            state.collection = Object.assign({}, state.collection, state.collection);
          } else {
              console.log('Sản phẩm bị trùng');
          }
        },
        async addPrdsCollectionData(state, payload) {
          const pickPrd={};
          let lastKey = parseInt(Object.keys(state.collection).length) + 1;
          Array.from(payload).forEach((child) => {
              if (!state.arrIDs.includes(child.id)) {
                  lastKey++;
                  pickPrd[lastKey] = {
                      id: child.id,
                      name: child.desc.name,
                      description: child.description,
                      short_description: child.desc.short_description,
                      sort: 0,
                  };
                  if (child != null ) {
                      pickPrd[lastKey].image = child.image ?? '';
                      pickPrd[lastKey].imageUrl = child.image_url ?? '';
                      pickPrd[lastKey].categories = {};
                      if (typeof child.categories !== 'undefined') {
                              child.categories.forEach((element, index) => {
                              pickPrd[lastKey].categories[index] = element.desc;
                          });
                      }
                  }
              } else {
                console.log('Sản phẩm bị trùng');
              }
          });
          const merged = { ...state.collection, ...pickPrd };
          state.collection = merged;
          state.collection = Object.assign({}, state.collection, state.collection);
        },
        addEditTableTabs(state, value) {
          state.editableTabs[value.name] = value;
          // state.editableTabs.push(value);
          // state.editableTabs.push(value);
        },
        setEditTableTabs(state, value) {
          state.editableTabs = value;
          state.editableTabs = Object.assign({}, state.editableTabs, state.editableTabs);
        },

        filterEditTableTabs(state, value) {
          // state.editableTabs[value.index] = state.editableTabsOriginFilter[value.index];
          Object.filter = (obj, predicate) =>
            Object.keys(obj)
                  .filter( (key) => predicate(obj[key]) )
                  .reduce( (res, key) => (res[key] = obj[key], res), {} );
          const filtered = Object.filter(state.editableTabs[value.index].content, (score) => {
            if ( score.product !=null ) {
              return score.product.desc.name.includes(value.filter);
            }
          } );
          state.editableTabs[value.index].content = filtered;
          state.editableTabs = Object.assign({}, state.editableTabs, state.editableTabs);
        },
        updateLangData(state, value) {
          state.langData = value;
        },
        setImg(state, value) {
          state.content.image = value;
        },
        setTypeDiscount(state, value) {
          state.content.offer_discount_type = value;
        },
        setPriceDiscount(state, value) {
          state.content.offer_discount_value = value;
        },
        setTimeStart(state, value) {
          state.content.offer_time_start = value;
        },
        setTimeEnd(state, value) {
          state.content.offer_time_end = value;
        },
        setType(state, value) {
          state.type = value;
        },
        removeElement(state, payload) {
          delete state.collection[payload];
          state.collection = Object.assign({}, state.collection, state.collection);
        },
    },
    actions: {
      content({ commit }, content) {
        commit('content', content);
      },
      setStatus({ commit }, content) {
        commit('setStatus', content);
      },
      collectionData({ commit }, content) {
        commit('collectionData', content);
      },
      addEditTableTabs({ commit }, content) {
        commit('addEditTableTabs', content);
      },
      setImg({ commit }, content) {
        commit('setImg', content);
      },
      setTypeDiscount({ commit }, content) {
        commit('setTypeDiscount', content);
      },
      setPriceDiscount({ commit }, content) {
        commit('setPriceDiscount', content);
      },
      setTimeStart({ commit }, content) {
        commit('setTimeStart', content);
      },
      setTimeEnd({ commit }, content) {
        commit('setTimeEnd', content);
      },
      updateLangData({ commit }, content) {
        commit('updateLangData', content);
      },
      addPrdsCollectionData({ commit }, content) {
        commit('addPrdsCollectionData', content);
      },
      setType({ commit }, content) {
        commit('setType', content);
      },
      removeElement({ commit }, content) {
        commit('removeElement', content);
      },
      setEditTableTabs({ commit }, content) {
        commit('setEditTableTabs', content);
      },
      filterEditTableTabs({ commit }, content) {
        commit('filterEditTableTabs', content);
      },
      setContentTabPrd({ commit }, content) {
        commit('setContentTabPrd', content);
      },
      setArrIDs({ commit }, content) {
        commit('setArrIDs', content);
      },
    },
    getters: {

    },
  };
