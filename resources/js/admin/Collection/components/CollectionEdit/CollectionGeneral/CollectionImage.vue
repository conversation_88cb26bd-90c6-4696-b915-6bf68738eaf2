<template>
  <div>
    <el-card class="box-card">
      <h2>Ảnh slider</h2>
      <el-upload
        action="" ref="upload"
        list-type="picture-card"
        :auto-upload="false"
        :on-change='uploadFiles' :file-list="fileList" >
          <i slot="default" class="el-icon-plus"></i>
          <div slot="file" slot-scope="{file}">
            <img
              class="el-upload-list__item-thumbnail"
              :src="file.url" alt=""
            >
            <span class="el-upload-list__item-actions">
              <span
                class="el-upload-list__item-preview"
                @click="handlePictureCardPreview(file)"
              >
                <i class="el-icon-zoom-in"></i>
              </span>
              <span
              v-if="!disabled"
              class="el-upload-list__item-delete"
                @click="handleEdit(file)"
              >
                <i class="el-icon-edit"></i>
              </span>
              <span
                v-if="!disabled"
                class="el-upload-list__item-delete"
                @click="handleDownload(file)"
              >
                <i class="el-icon-download"></i>
              </span>
              <span
                v-if="!disabled"
                class="el-upload-list__item-delete"
                @click="handleRemove(file)"
              >
                <i class="el-icon-delete"></i>
              </span>
            </span>
          </div>
      </el-upload>
      <br>
      <h2>Ảnh đại diện</h2>
      <el-upload
        action="#" :auto-upload="false"
        class="avatar-uploader"
        :show-file-list="false" :on-change='uploadFilesImageCollection'
        :on-success="handleAvatarSuccess"
        :before-upload="beforeAvatarUpload">
        <img v-if="imageUrl" :src="herf+'/upload/'+type+'/thumb_1300x0/'+imageUrl" class="avatar">
        <i v-else class="el-icon-plus avatar-uploader-icon"></i>

      </el-upload>

        <el-button type="danger" icon="el-icon-delete" circle @click='clearImage'></el-button>
    </el-card>
     <el-dialog :visible.sync="dialogVisible">
      <img width="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
    <el-dialog
      title="Edit"
      :visible.sync="dialogEditVisible"
      width="40%"
      :before-close="handleClose"
    >
      <div class="margin-bottom-30">
        <label class="margin-top-10">Thứ tự: </label>
      <el-input placeholder="Nhập thứ tự" v-model="editImage.sort" class="input-edit"></el-input>
      </div>
      <div class="margin-bottom-30">
        <label class="margin-top-10">Link: </label>
      <el-input placeholder="Nhập link" v-model="editImage.link" class="input-edit"></el-input>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogEditVisible = false">Cancel</el-button>
        <el-button type="primary" @click="handleUpdateImage">Lưu</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<style>
  @import "../../../css/imagebox.css";
  @import "../../../css/imageUpload.css";
</style>
<script>
import api from '../../../helpers/api';
import axios from 'axios';

  export default {
    data() {
      return {
        dialogImageUrl: '',
        dialogVisible: false,
        editImage: {},
        dialogEditVisible: false,
        disabled: false,
        attachments: [],
        fileList: this.$store.state.c.fileList,
        collectionId: this.$route.query.id ?? '',
        herf: window.location.origin,
        type: 'collection',
      };
    },
    mounted() {
        if (typeof this.$route.query.id != 'undefined') {
          this.collectionImgByID();
        }
    },
    computed: {
      imageUrl() {
          return this.$store.state.b.general.image;
      },
    },
    methods: {
      change(file, fileList) {
        this.fileList = fileList;
      },
      handleClose(done) {
      this.$confirm('Bạn có chắc muốn đóng?', 'Cảnh báo!', {
          confirmButtonText: 'Đồng ý',
          cancelButtonText: 'Bỏ qua',
        })
          .then((_) => {
            done();
          })
          .catch((_) => {});
      },
      handleEdit(file) {
          this.editImage = file;
          this.dialogEditVisible = true;
      },
      async handleUpdateImage() {
        const editImage = this.editImage;
        const dataImg = this.$store.state.c.fileList;
        const url = 'admin/ajax/collection/controller/itemImgUpdate';
        const campaign = await api.request('POST', url, { editImage });
        if (!campaign.data.error) {
          dataImg.forEach((element, index) => {
            if (element.id == campaign.data.data.id) {
              this.$store.state.c.fileList[index].sort = campaign.data.data.sort;
            }
          });
          this.openSuccess(campaign.data.msg);
          this.dialogEditVisible = false;
        } else {
          this.openError(campaign.data.msg);
        }
      },
      openSuccess(msg) {
        this.$message({
          message: msg,
          type: 'success',
        });
      },
      openError(msg) {
        this.$message.error(msg);
      },
      clearImage() {
          this.$store.dispatch('b/setImg', '');
      },
      getAndSetData(content) {
        this.$store.dispatch('c/imagetData', content);
      },
      handleRemove(file) {
        this.removeImg(file.uid);
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url;
        this.dialogVisible = true;
      },
      handleDownload(file) {
        window.location.assign(file.url);
      },

      async removeImg(img_id) {
        if (confirm('Bạn có chắc chắn muốn xóa ?')) {
            // let url = `admin/ajax/collection/${this.$route.query.id }-${img_id}/controller/itemImgDel`;
            // let collection = await api.request('GET', url);
            if (typeof this.fileList != 'undefined') {
              const file=[];
              const unique = (value, index, self) => {
                      return self.indexOf(value) === index;
              };
              Array.from(this.fileList).forEach((child, index) => {
                if (img_id == child.uid) {
                    delete this.fileList[index];
                    this.fileList = this.fileList.filter(unique);
                }
                this.$store.dispatch('c/fileList', this.fileList);
              });
              // this.fileList = file;
              // this.getAndSetData(collection.data.data);
            }
        }
      },
      async collectionImgByID() {
        const url = `admin/ajax/collection/${this.$route.query.id}/controller/imgData`;
        const collection = await api.request('GET', url);
        if (collection) {
          const file=[];
          if (typeof collection.data.data != 'undefined') {
            Array.from(collection.data.data).forEach((child) => {
              file.push({
              name: child.img,
              id: child.id,
              link: child.link,
              url: child.imglarge,
              sort: child.sort,
            });
          });
          }
          this.fileList = file;
          this.$store.dispatch('c/fileList', this.fileList);

          // this.getAndSetData(products.data.data);
        }
      },
      async uploadFilesImageCollection(req) {
          const formdata = new FormData();
          formdata.append('Filedata', req.raw);
          // formdata.append('object_id', this.productId);
          const config = {
            headers: { 'Content-Type': 'multipart/form-data', 'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content },
        };
        axios.post('/admin/ajax/collection/controller/imgUpload', formdata, config)
          .then((res) => {
              if (typeof res.data.data.images != 'undefined') {
                  Array.from(res.data.data.images).forEach((child) => {
                    this.$store.dispatch('b/setImg', child.img);
                });
              }
              console.log('image upload succeed.');
          })
          .catch((err) => {
              console.log(err.message);
          });
      },
      handleAvatarSuccess(res, file) {
        this.imageUrl = URL.createObjectURL(file.raw);
      },
      beforeAvatarUpload(file) {
          const isJPG = file.type === 'image/jpeg';
          const isLt2M = file.size / 1024 / 1024 < 2;

          if (!isJPG) {
          this.$message.error('Avatar picture must be JPG format!');
          }
          if (!isLt2M) {
          this.$message.error('Avatar picture size can not exceed 2MB!');
          }
          return isJPG && isLt2M;
      },
      async uploadFiles(req) {
        const formdata = new FormData();
        formdata.append('Filedata', req.raw);
        // formdata.append('object_id', this.productId);

        const config = {
            headers: { 'Content-Type': 'multipart/form-data', 'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content },
        };
        axios.post('/admin/ajax/collection/controller/imgUpload', formdata, config)
        .then((res) => {
            const file=[];
            if (typeof res.data.data.images != 'undefined') {
                Array.from(res.data.data.images).forEach((child) => {
                  file.push({
                  name: child.img,
                  url: child.imglarge,
                  object_id: child.object_id,
                  id: child.id,
                  link: child.link,
                  sort: child.sort,
                });
              });
            }

            this.fileList.push(file[0]);

            this.$store.dispatch('c/fileList', this.fileList);
            console.log('image upload succeed.');
        })
        .catch((err) => {
            console.log(err.message);
        });
    },

    },
  };
</script>
