<template>
    <el-form  label-width="120px">
         <div>
             <el-form-item label="<PERSON>i<PERSON> tốt theo ng<PERSON>">
            <el-radio-group v-model="goodPriceStatus">
                <el-radio-button label="1">On</el-radio-button>
                <el-radio-button label="0">Off</el-radio-button>
            </el-radio-group>
            </el-form-item>
            <el-form-item label="Trạng thái">
                <el-radio-group v-model="statusPick">
                    <el-radio-button label="1">Hiện</el-radio-button>
                    <el-radio-button label="0">Ẩn</el-radio-button>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="Màu sắc">
                <el-input v-model="colorPk"></el-input><div class="backgroundColor" :style="{background: colorPk}">  </div>
                <a class="btn btn-info" data-toggle="collapse" href="#collapseExample" role="button" aria-expanded="false" aria-controls="collapseExample">
                Ch<PERSON><PERSON> màu
                </a>
                <div class="collapse" id="collapseExample">
                <div class="card card-body">
                    <color-picker style='box-sizing: unset;'
                theme="light"
                :color="color"
                :sucker-hide="false"
                :sucker-canvas="suckerCanvas"
                :sucker-area="suckerArea"
                @changeColor="changeColor"
                @openSucker="openSucker"
                />
                </div>
                </div>
            </el-form-item>
        </div>
    </el-form>
</template>
<style lang="scss" scoped>
    .backgroundColor{
    width:40px;
    height:40px;
    float: left;
    border: black solid 1px;
     }
</style>
<script>
import colorPicker from '@caohenghu/vue-colorpicker';

    export default {
        components: { colorPicker },
        data() {
            return {
                radio1: '',
                 suckerCanvas: null,
                suckerArea: [],
                isSucking: false,
                color: '',
            };
        },
        computed: {
            goodPriceStatus: {
                set(value) {
                   this.$store.dispatch('b/setGoodPrice', value);
                },
                get() {
                  return this.$store.state.b.general.is_good_price;
                },
            },
            statusPick: {
                set(value) {
                   this.$store.dispatch('b/setStatus', value);
                },
                get() {
                  return this.$store.state.b.general.status;
                },
            },
            colorPk: {
                get() {
                    return this.$store.state.b.general.color;
                },
                set(value) {
                    this.color = value;
                    this.$store.dispatch('b/setColor', value);
                },
            },
        },
        methods: {
            changeColor(color) {
              this.$store.dispatch('b/setColor', color.hex);
              this.color = color.hex;
            },
            openSucker(isOpen) {
                if (isOpen) {
                    // ... canvas be created
                    // this.suckerCanvas = canvas
                    // this.suckerArea = [x1, y1, x2, y2]
                } else {
                    // this.suckerCanvas && this.suckerCanvas.remove
                }
            },
        },
    };
</script>
