export default {
    namespaced: true,
    state: {
      content: {
      },
      dialogVisible: false,
      dialogEditVisible: false,
      dialogDeliveryConnectVisible: false,
      dialogVisiblePrdVariantSet: false,
      dataDetail: {},
      loading: true,
      choiceVariant: '',
      ruleForm: {

      },
      // warehouseDetail: {},
    },
    mutations: {
        content(state, payload) {
            state.content = payload;
        },
        dialogVisible(state, payload) {
          state.dialogVisible = payload;
        },
        
        dialogEditVisible(state, payload) {
          state.dialogEditVisible = payload;
        },

        dialogDeliveryConnectVisible(state, payload) {
          state.dialogDeliveryConnectVisible = payload;
        },

        warehouseDetail(state, payload) {
          state.ruleForm = payload;
        },
        
        setDialogVisiblePrdVariantSet(state, payload) {
          state.dialogVisiblePrdVariantSet = payload;
        },
        dataDetail(state, payload) {
          state.dataDetail = payload;
        },
        setLoading(state, payload) {
          state.loading = payload;
        },
        choiceVariant(state, payload) {
          state.choiceVariant = payload;
        },
    },
    actions: {
      content({ commit }, content) {
        commit('content', content);
      },
      dialogVisible({ commit }, content) {
        commit('dialogVisible', content);
      },
      setDialogVisiblePrdVariantSet({ commit }, content) {
        commit('setDialogVisiblePrdVariantSet', content);
      },
      dataDetail({ commit }, content) {
        commit('dataDetail', content);
      },
      setLoading({ commit }, content) {
        commit('setLoading', content);
      },
      choiceVariant({ commit }, content) {
        commit('choiceVariant', content);
      },
      dialogEditVisible({ commit }, content) {
        commit('dialogEditVisible', content);
      },

      dialogDeliveryConnectVisible({ commit }, content) {
        commit('dialogDeliveryConnectVisible', content)
      },

      warehouseDetail({ commit }, content) {
        commit('warehouseDetail', content);
      },
    },
    getters: {

    },
  };
