<template>
  <div v-if="warehouseData">
    <el-table :data="warehouseData.data" class="w-100 table-responsive" border>
      <el-table-column
        label="STT"
        width="50"
       >
       <template slot-scope="scope">{{ scope.row.id }}</template>
      </el-table-column>

      <el-table-column
        prop="name"
        label="Tên địa chỉ"
        width="180">
      </el-table-column>

      <el-table-column
        prop="address"
        label="Địa chỉ">
      </el-table-column>

      <el-table-column
        label="Đơn vị vận chuyển">
        <template slot-scope="scope">
        </template>
      </el-table-column>

      <el-table-column label="Hành động">
        <template slot-scope="scope">
           <el-link icon="el-icon-edit" type="primary" @click="showFormEditAddress(scope.row)">Chỉnh sửa</el-link>
           <el-link icon="el-icon-delete" type="danger" class="mx-3" @click="deleteAddress(scope.row)">Xóa</el-link>
           <el-button size="small" type="primary" @click="showAddressDetail(scope.row)">Xem chi tiết <i class="el-icon-arrow-right"></i></el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      class="pt-3 px-0"
      @size-change="onSizeChange"
      @current-change="listWarehouse"
      :current-page.sync="warehouseData.current_page"
      :page-sizes="[10, 20, 50]"
      :page-size="warehouseData.per_page"
      layout="sizes, prev, pager, next, jumper"
      :total="warehouseData.total">
    </el-pagination>
  </div>
</template>

<script>
import messageMixin from '../mixins/messageMixin.js';
import { eventBus } from '../app.js';
import api from '../helpers/api';

export default {
  mixins: [messageMixin],
  name: 'WarehouseTable',
  data() {
    return {
      warehouseData: [],
      filter: {
        page: 1,
        limit: 10
      }
    };
  },

  computed: {},

  mounted() {
    this.listWarehouse();
  },

  created() {
    eventBus.$on('pushNewAddress', this.onPushNewAddress)
            .$on('onUpdateAddress', this.onUpdateAddress);
  },

  methods: {
    onPushNewAddress(address) {
     this.warehouseData.data.unshift(address.data);
    },

    onUpdateAddress(address) {
      let index = this.warehouseData.data.findIndex(item => {
        return item.id == address.id;
      });

      this.$set(this.warehouseData.data, index, address);
    },

    async listWarehouse(page=1, limit=10) {
      this.filter.page = page;
      this.filter.limit = limit;

      const url = `ajax/warehouse/controller/warehouseData?page=${page}&limit=${limit}`;
      const warehouseData = await api.request('GET', url);
      if (warehouseData) {
        this.warehouseData = warehouseData.data.data;
        this.$store.dispatch('a/warehouseData', warehouseData.data.data);
      }
    },

    onSizeChange(limit) {
     this.listWarehouse(this.page, limit);
    },

    async deleteAddress(addressItem) {
      this.$confirm('Xóa địa chỉ nhận hàng này. Tiếp tục?', 'Cảnh báo', {
          confirmButtonText: 'Đồng ý',
          cancelButtonText: 'Đóng',
          type: 'warning'
      }).then( async () => {
          const url = `ajax/warehouse/controller/${addressItem.id}/warehouseDelete`;
          const warehouseData = await api.request('GET', url);
          if (!warehouseData.data.error) {
            let index = this.warehouseData.data.findIndex(item => {
              return item.id == addressItem.id
            });

            this.warehouseData.data.splice(index, 1);
            this.showMessage( warehouseData.data.msg, 'success');
          } else {
            this.showMessage( warehouseData.data.msg, 'error');
          }
      });
    },

    showFormEditAddress(addressItem) {
      this.$store.commit('b/warehouseDetail', addressItem);
      this.$store.dispatch('b/dialogEditVisible', true);
    },

    showAddressDetail(addressItem) {
      this.$store.commit('detail/SET_ADDRESS_DETAIL_ITEM', addressItem);
      this.$store.commit('detail/SHOW_DIALOG_ADDRESS_DETAIL', true);
    }
  },

  computed: {
   
  }
}; // End class
</script>
