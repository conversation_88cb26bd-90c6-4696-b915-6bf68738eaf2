<template>
  <div class="search-bar d-flex mb-3">
    <el-input type="text" class="mr-3 w-50">
       <el-button type="primary" slot="append" icon="el-icon-search"></el-button>
    </el-input>

    <el-button type="primary" @click="openDialogAdd">
        <i class="fa fa-plus"></i> 
        Thêm địa chỉ mới
    </el-button>
   

    <detailAdd></detailAdd>
    <detailEdit></detailEdit>
    <detail-show></detail-show>
  </div>
</template>

<script>
import detailAdd from '../Dialog/detailAdd.vue';
import detailEdit from '../Dialog/detailEdit.vue';
import detailShow from '../Dialog/detailShow.vue';
import api from '../../helpers/api';
import messageMixin from '../../helpers/messageMixin';
import { eventBus } from '../../app.js';

export default {
  name: 'SearchBar',
  mixins: [messageMixin],
  components: { detailAdd, detailEdit, detailShow },
  // components: {detail},
  data() {
    return {
      value: '',
      warehouseData: '',
    };
  },
  computed: {
    warehouseID: {
      get() {
        return this.$store.state.a.searchFilter.idWarehouse;
      },
      set(value) {
        this.$store.dispatch('a/searchWarehouseID', value);
      },
    },
    inputTitle: {
      // eslint-disable-next-line vue/return-in-computed-property 
      get() {
        if (typeof this.$store.state.a.searchFilter !== 'undefined') {
          return this.$store.state.a.searchFilter.title;
        }
      },
      set(value) {
        this.$store.dispatch('a/filterTitle', value);
      },
    },
  },
  mounted() {
    
  },

  methods: {
    async editWarehouse() {
      const url = `ajax/warehouse/controller/${this.warehouseID}/warehouseEdit`;
      const warehouseData = await api.request('GET', url);
    },
    
    openDialogAdd() {
      this.$store.dispatch('b/dialogVisible', true);
    },

    openDialogEdit() {
      this.$store.dispatch('b/dialogEditVisible', true);
      this.getDataWarehouse();
    },

    openDialogDeliveryConnect() {
      this.$store.dispatch('b/dialogDeliveryConnectVisible', true);
      this.listWarehouse();
    },

    async getDataWarehouse() {
      const url = `ajax/warehouse/controller/${this.warehouseID}/warehouseDataByID`;
      const warehouseData = await api.request('GET', url);
      if (!warehouseData.data.error) {
          this.$store.dispatch('b/warehouseDetail', warehouseData.data.data).then(() => {
            eventBus.$emit('fireMethodGetDistrict');
          });
      }
    },
    someComponent2Method: function() {
      // your code here
      eventBus.$emit('fireMethod');
    },
    
  },
}; // End class
</script>
