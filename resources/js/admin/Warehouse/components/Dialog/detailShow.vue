<template>
  <div>
    <el-dialog
      title="Thông tin địa chỉ nhận hàng"
      :visible.sync="dialogVisible"
      width="50%"
      @opened="getAddressDetail"
    >
      <p><b>Tê<PERSON> địa chỉ:</b> {{ address.name }}</p>
      <p><b><PERSON><PERSON><PERSON> chỉ chi tiết:</b> {{ address.address }}</p>
      <p><b>SĐT:</b> {{ address.phone }}</p>
      <p>
        Danh sách đối tác giao hàng:
      </p>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeDialogTableV"><PERSON><PERSON>g lại</el-button>
        <el-button type="primary" @click="submitForm('ruleForm')">Cập nhật</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import api from '../../helpers/api';
import messageMixin from '../../mixins/messageMixin.js';
export default {
  mixins: [ messageMixin ],
  data() {
    return {
      address: {}
    };
  },

  computed: {
    dialogVisible: {
      get() {
        return this.$store.state.detail.dialogDetailVisible;
      },
      set(value) {
        this.$store.commit('detail/SHOW_DIALOG_ADDRESS_DETAIL', value);
      },
    },
  },

  mounted() {
    
  },

  methods: {
    async getAddressDetail() {
      let address = this.$store.state.detail.address;
      this.address = address;
    },


    closeDialogTableV() {
      this.$store.commit('detail/SHOW_DIALOG_ADDRESS_DETAIL', false);
    },
  },
};
</script>
