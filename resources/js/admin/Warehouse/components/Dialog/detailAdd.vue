<template>
  <div>
    <el-dialog
      title="Tạo địa chỉ nhận hàng"
      :visible.sync="dialogVisible"
      width="50%"
    >
      <el-form
        v-bind:model="ruleForm"
        v-bind:rules="rules"
        label-width="200px"
        ref="ruleForm"
        id="formTaoDiaChi"
      >
        <el-form-item label="Tên địa chỉ" prop="warehouseStoreName">
          <el-input v-model="ruleForm.warehouseStoreName" class="w-100"></el-input>
        </el-form-item>

        <el-form-item label="Địa chỉ chi tiết" prop="warehouseAddress">
          <el-input
            type="textarea"
            v-model="ruleForm.warehouseAddress"
          ></el-input>
        </el-form-item>

        <el-form-item label="Tỉnh/Thành phố" prop="city">
          <div class="d-flex">
            <el-col :span="11" id="tinh-thanh">
            <el-select
              v-model="ruleForm.city"
              placeholder="Tỉnh/Thành phố"
              @change="getDistrict"
              no-data-text="Không có dữ liệu"
              filterable
            >
              <el-option
                v-for="(item, index) in cityList"
                :label="item.name_with_type"
                :value="item.code"
                :key="index"
                >{{ item.name_with_type }}</el-option
              >
            </el-select>
          </el-col>

          <el-col :span="11" id="quan-huyen" class="ml-auto">
            <el-select
              v-model="ruleForm.district"
              placeholder="Quận/huyện"
              no-data-text="Không có dữ liệu"
            >
              <el-option
                v-for="(item, index) in districtList"
                :label="item.name_with_type"
                :value="item.code"
                :key="index"
                >{{ item.name_with_type }}</el-option
              >
            </el-select>
          </el-col>
          </div>
        </el-form-item>


        <el-form-item label="Số điện thoại" prop="phone">
          <el-input v-model="phoneCom" type="phone" maxlength="11" class="w-100"></el-input>
        </el-form-item>

        <el-form-item label="Đơn vị vận chuyển" prop="phone">
          <el-checkbox-group v-model="shippingUnit">
            <el-checkbox label="Option A" class="d-block"></el-checkbox>
            <el-checkbox label="Option B" class="d-block"></el-checkbox>
            <el-checkbox label="Option C" class="d-block"></el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="closeDialogTableV">Đóng lại</el-button>
        <el-button type="primary" @click="submitForm('ruleForm')">Lưu thay đổi</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<style lang="scss" scoped></style>
<script>
import api from "../../helpers/api";
import messageMixin from "../../mixins/messageMixin.js";
import { eventBus } from '../../app.js';

export default {
  mixins: [messageMixin],
  name: "detailAdd",
  data() {
    return {
      cityList: [],
      districtList: [],

      ruleForm: {
        warehouseStoreName: "",
        warehouseAddress: "",
        city: "",
        district: "",
        phone: "",
      },

      rules: {
        warehouseStoreName: [
          {
            required: true,
            message: "Hãy nhập tên địa chỉ nhận hàng",
            trigger: "blur",
          },
        ],

        warehouseAddress: [
          {
            required: true,
            message: "Hãy nhập địa chỉ chi tiết",
            trigger: "blur",
          },
        ],

        city: [
          {
            required: true,
            message: "Hãy chọn tỉnh thành",
            trigger: "blur",
          },
        ],

        district: [
          {
            required: true,
            message: "Hãy chọn quận huyện",
            trigger: "blur",
          },
        ],
      },

      shippingUnit: []
    };
  },

  computed: {
    phoneCom: {
      get() {
        return this.ruleForm.phone;
      },
      set(value) {
        this.ruleForm.phone = value.replace(/[^0-9]/g, "");
      },
    },
    dialogVisible: {
      get() {
        return this.$store.state.b.dialogVisible;
      },
      set(value) {
        this.$store.dispatch("b/dialogVisible", value);
      },
    },
  },

  mounted() {
    this.getCity();
  },

  methods: {
    async getCity() {
      const url = "https://crm.bizfly.vn/public-api/location/get_all_city";
      const result = await jQuery.get(url);
      if (result.status == 200) {
        this.cityList = result.data;
      }
    },

    async getDistrict() {
      const url = `https://crm.bizfly.vn/public-api/location/get_sub_location?parent_code=${this.ruleForm.city}`;
      const result = await jQuery.get(url);
      if (result.status == 200) {
        this.districtList = result.data;
      }
    },

    closeDialogTableV() {
      this.$store.dispatch("b/dialogVisible", false);
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const warehouseStoreName = this.ruleForm.warehouseStoreName;
          const warehouseAddress = this.ruleForm.warehouseAddress;
          const city = this.ruleForm.city;
          const district = this.ruleForm.district;
          const phone = this.ruleForm.phone;
          const url = "ajax/warehouse/controller/saveWarehouse";
          api.request("POST", url, {
              warehouseStoreName,
              warehouseAddress,
              city,
              district,
              phone,
            })
            .then((response) => {
              eventBus.$emit('pushNewAddress', response.data)
              this.closeDialogTableV();
              return this.showMessage("Thành công", "success");
            })
            .catch((_) => {
              return this.showMessage("Có lỗi.", "error");
            });
        } else {
          this.showMessage("Có lỗi.", "error");
          return false;
        }
      });
    },
    openError(msg) {
      this.$message.error(msg);
    },
    openSuccess(msg) {
      this.$message({
        message: msg,
        type: "success",
      });
    },
  },
};
</script>

<style>
#tinh-thanh .el-select, #tinh-thanh .el-input {
  width: 100%;
}

#quan-huyen .el-select, #quan-huyen .el-input {
  width: 100%;
}

#formTaoDiaChi .el-form-item__content {
  line-height: unset;
}
</style>
