<template>
  <div>
    <el-dialog
      title="Sửa thông tin địa chỉ nhận hàng"
      :visible.sync="dialogVisible"
      width="50%"
      @opened="getSelectedDistrict"
    >
      <el-form
        v-bind:model="ruleForm"
        v-bind:rules="rules"
        label-width="200px"
        ref="ruleForm"
      >
          <el-form-item label="Tên địa chỉ" prop="name">
            <el-input v-model="ruleForm.name"></el-input>
          </el-form-item>

          <el-form-item label="Địa chỉ chi tiết" prop="address">
            <el-input  type="textarea" v-model="ruleForm.address"></el-input>
          </el-form-item>

          <el-form-item label="Tỉnh/Thành phố" prop="city">
          <div class="d-flex">
            <el-col :span="11" id="tinh-thanh">
            <el-select
              v-model="ruleForm.p_code"
              placeholder="Tỉnh/Thành phố"
              @change="getDistrict"
              no-data-text="Không có dữ liệu"
              filterable
            >
              <el-option
                v-for="(item, index) in cityList"
                :label="item.name_with_type"
                :value="item.code"
                :key="index"
                >{{ item.name_with_type }}</el-option
              >
            </el-select>
          </el-col>

          <el-col :span="11" id="quan-huyen" class="ml-auto">
            <el-select
              v-model="ruleForm.d_code"
              placeholder="Quận/huyện"
              no-data-text="Không có dữ liệu"
            >
              <el-option
                v-for="(item, index) in districtList"
                :label="item.name_with_type"
                :value="item.code"
                :key="index"
                >{{ item.name_with_type }}</el-option
              >
            </el-select>
          </el-col>
          </div>
        </el-form-item>

          <el-form-item label="Phone" prop="phone">
              <el-input v-model="phoneCom" type="phone" maxlength="11"></el-input>
          </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="closeDialogTableV">Đóng lại</el-button>
        <el-button type="primary" @click="submitForm('ruleForm')">Cập nhật</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import api from '../../helpers/api';
import messageMixin from '../../mixins/messageMixin.js';
import { eventBus } from '../../app.js';
export default {
  mixins: [ messageMixin ],
  name: 'detailEdit',
  data() {
    return {
      cityList: [],
      districtList: [],
      
      rules: {
        name: [
          {
            required: true,
            message: 'Hãy nhập tên Kho',
            trigger: 'blur',
          },
          
        ],
        address: [
          {
            required: true,
            message: 'Hãy nhập địa chỉ chi tiết Kho',
            trigger: 'blur',
          },
        ],
        p_code: [{
            required: true,
            message: 'Hãy chọn tỉnh thành',
            trigger: 'blur',
        }],
        d_code: [{
            required: true,
            message: 'Hãy chọn quận huyện',
            trigger: 'blur',
        }],
        // phone: [{ type: 'number', message: 'Sdt phải là số' }],
      },
    };
  },

  computed: {
    ruleForm: {
      get() {
        return this.$store.state.b.ruleForm;
      },
      set(value) {
        this.$store.state.b.ruleForm = value;
      },
    },
    phoneCom: {
      get() {
        return this.$store.state.b.ruleForm.phone;
      },
      set(value) {
        this.$store.state.b.ruleForm.phone = value.replace(/[^0-9]/g, '');
      },
    },
    dialogVisible: {
      get() {
        return this.$store.state.b.dialogEditVisible;
      },
      set(value) {
        this.$store.dispatch('b/dialogEditVisible', value);
      },
    },
  },

  mounted() {
    
  },

  methods: {
    async getCity() {
      const url = "https://crm.bizfly.vn/public-api/location/get_all_city";
      const result = await jQuery.get(url);
      if (result.status == 200) {
        this.cityList = result.data;
      }
    },

    getSelectedDistrict() {
      this.getCity();
      this.getDistrict(this.ruleForm.p_code, false);
    },

    async getDistrict(cityCode='', isChange=true) {
      cityCode = cityCode.length > 0 ? cityCode : this.ruleForm.p_code;
      const url = `https://crm.bizfly.vn/public-api/location/get_sub_location?parent_code=${cityCode}`;
      const result = await jQuery.get(url);
      if (result.status == 200) {
        this.districtList = result.data;
        if (isChange) {
          this.ruleForm.d_code = '';
        }
      }
    },

    closeDialogTableV() {
      this.$store.dispatch('b/dialogEditVisible', false);
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const warehouseStoreName = this.ruleForm.name;
          const warehouseAddress = this.ruleForm.address;
          const city = this.ruleForm.p_code;
          const district = this.ruleForm.d_code;
          const phone = this.ruleForm.phone;
          const warehouseID = this.ruleForm.id;
          const url = 'ajax/warehouse/controller/saveWarehouse';
          api.request('POST', url, { warehouseStoreName, warehouseAddress, city, district, phone, warehouseID }).then((response) => {
              eventBus.$emit('onUpdateAddress', response);
              this.closeDialogTableV();
              return this.showMessage(response.data.msg, 'success');
            })
            .catch((_) => {
              return this.showMessage('Có lỗi.', 'error');
            });
        } else {
          this.showMessage('Có lỗi.', 'error');
          return false;
        }
      });
    },
    openError(msg) {
      this.$message.error(msg);
    },
    openSuccess(msg) {
      this.$message({
        message: msg,
        type: 'success',
      });
    },
   
  },
};
</script>
