<template>
  <div>
    <el-dialog
      title="Kết nối kho hàng với đơn vị vận chuyển"
      :visible.sync="dialogDeliveryConnectVisible"
      width="50%"
      :before-close="closeDialog"
    >
      <el-checkbox-group v-model="warehouse_using">
        <el-checkbox v-for="wh in warehouseData" :key="wh.id"
                     :label="wh.id"
        >
          {{ wh.name }} <span v-show="wh.is_connect_bizfly_delivery > 0">(đã kết nối)</span>
        </el-checkbox>
      </el-checkbox-group>

      <iframe title="Kết nối đơn vị vận chuyển" 
                    name="delivery_config" 
                    :src="iframeUrl" 
                    frameborder="0" 
                    width="100%" 
                    height="600px"
                    v-show="iframeUrl">
      </iframe>

      <span slot="footer" class="dialog-footer">
        <el-button @click="closeDialog">Đóng</el-button>
        <el-button type="primary" @click="useWarehouseForConnectDelivery" :loading="loading">
          <span v-show="!iframeUrl">K<PERSON><PERSON> nối</span>
          <span v-show="iframeUrl">Kết nối lại</span>
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>
<style lang="scss" scoped></style>
<script>
import api from '../../helpers/api';
import messageMixin from "../../mixins/messageMixin.js";

export default {
  mixins: [messageMixin],
  name: "detailDeliveryConnect",

  data() {
    return {
      warehouse_using: [],
      iframeUrl: '',
      loading: false,
    };
  },

  computed: {
    warehouse() {
      return this.$store.state.b.ruleForm;
    },

    dialogDeliveryConnectVisible: {
      get() {
        return this.$store.state.b.dialogDeliveryConnectVisible;
      },
      set(value) {
        this.$store.dispatch("b/dialogEditVisible", value);
      },
    },

    warehouseData() {
      return this.$store.state.a.warehouseData;
    }
  },

  mounted() {
    
  },

  created() {
    window.addEventListener('message', receiveMessage, false);

    function receiveMessage(event) {
        let msg = event.data;
        if (msg.type == 'create-hub') {
            shop.ajax_popup('/settings/deliverytype/bizfly-delivery', 'POST', {
                warehouse_info: msg.data
            }, function(json) {
                console.log(json);
            });
            
        }
    }
  },

  methods: {
    async useWarehouseForConnectDelivery() {
      this.loading = true;
      try {
        let result = await api.request('POST', 'warehouse/delivery-connect', {
          ids: this.warehouse_using
        });

        if (result.data.success) {
          this.iframeUrl = result.data.data.response.url_redirect;
        }
      }catch(err) {
        let errors = err.response.data.errors;
        this.$message.error(errors.ids.join(','));
      }finally {
        this.loading = false;  
      }
    },

    closeDialog() {
      this.$store.commit('b/dialogDeliveryConnectVisible', false);
    },
  },
};
</script>
