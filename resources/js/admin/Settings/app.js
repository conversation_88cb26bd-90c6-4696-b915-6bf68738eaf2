import Vue from 'vue';
import ElementUI from 'element-ui';
import 'element-ui/lib/theme-chalk/reset.css';
import 'element-ui/lib/theme-chalk/index.css';
import { store } from './store.js';
import Vuex from 'vuex';
import VueRouter from 'vue-router';

import WarehouseApp from './components/WarehouseApp.vue';

import locale from 'element-ui/lib/locale/lang/vi';

Vue.use(VueRouter);
Vue.use(ElementUI, { locale });

const routes = [];

const router = new VueRouter({
  mode: 'history',
  routes: routes
});

export const eventBus = new Vue(); // added line

new Vue({
  el: '#ShippingApp',
  store,
  computed: {
    ...Vuex.mapState({
      a: (state) => state.a
    }),
  },
  router: router,
  components: { WarehouseApp },
  render: (h) => h(WarehouseApp),
}).$mount('#ShippingApp');