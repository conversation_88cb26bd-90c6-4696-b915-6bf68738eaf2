export default {
  namespaced: true,
  state: {
    content: {
      draft_name: '',
      global_price: '',
      image: '',
      image_url: '',
      price: '',
      stock: 0,
      sku: '',
      sku_group: '',
      status: 2,
      published: 0,
      stockStatus: '',
      stock_status_id: '',
      weight: '',
      primary_category_id: '',
      manufacturer_id: '',
      shipping_required: 1,
      product_variants: [
        {
          stock: 0,
        },
      ],
    },
    arrID_variants_add: {},
  },
  mutations: {
    contentData(state, payloadData) {
      state.content = payloadData;
    },
    deleteArrIDVariantAdd(state, value) {
      delete state.arrID_variants_add[value];
      state.arrID_variants_add = Object.assign({}, state.arrID_variants_add, state.arrID_variants_add);
    },
    addVariantID(state, value) {
      // state.arrID_variants_add.push(value.product_variant_id);
      const keyArr = value.product_variant_id;
      const productOptionInsert = value.productOptionInsert;
      state.arrID_variants_add[keyArr] = productOptionInsert;

      // console.log(productOptions);
    },
    updateSku(state, value) {
      state.content.sku = value;
    },
    updateSkuGroup(state, value) {
      state.content.sku_group = value;
    },
    setFreeship(state, value) {
      state.content.shipping_required = value;
    },
    updateManufacturer(state, value) {
      state.content.manufacturer_id = value;
    },
    updateGlobal_price(state, value) {
      state.content.global_price = value;
    },
    updatePrice(state, value) {
      state.content.price = value;
    },
    updateStock(state, value) {
      state.content.product_variants[0].stock = value;
      state.content.stock = value;
    },
    updateStock_status_id(state, value) {
      state.content.stock_status_id = value;
    },
    updateStatus(state, value) {
      state.content.status = value;
    },
    updatePublished(state, value) {
      state.content.published = value;
    },
    updateWeight(state, value) {
      state.content.weight = value;
    },
    setImg(state, value) {
      state.content.image = value;
    },
    setImgUrl(state, value) {
      state.content.image_url = value;
    },
    setDataPrdOrigin(state, value) {
      state.content.stockStatus = value;
      state.content = Object.assign({}, state.content, state.content);
    },
    setPrimaryCategoryId(state, value) {
      state.content.primary_category_id = value;
    },
  },
  actions: {
    contentData({ commit }, content) {
      commit('contentData', content);
    },
    updateSku({ commit }, content) {
      commit('updateSku', content);
    },
    updateSkuGroup({ commit }, content) {
      commit('updateSkuGroup', content);
    },
    updateManufacturer({ commit }, content) {
      commit('updateManufacturer', content);
    },
    updateGlobal_price({ commit }, content) {
      commit('updateGlobal_price', content);
    },
    updatePrice({ commit }, content) {
      commit('updatePrice', content);
    },
    updateStock({ commit }, content) {
      commit('updateStock', content);
    },
    updateStock_status_id({ commit }, content) {
      commit('updateStock_status_id', content);
    },
    updateStatus({ commit }, content) {
      commit('updateStatus', content);
    },
    updatePublished({ commit }, content) {
      commit('updatePublished', content);
    },
    updateWeight({ commit }, content) {
      commit('updateWeight', content);
    },
    setImg({ commit }, content) {
      commit('setImg', content);
    },
    setImgUrl({ commit }, content) {
      commit('setImgUrl', content);
    },
    setDataPrdOrigin({ commit }, content) {
      commit('setDataPrdOrigin', content);
    },
    setPrimaryCategoryId({ commit }, content) {
      commit('setPrimaryCategoryId', content);
    },
    setFreeship({ commit }, content) {
      commit('setFreeship', content);
    },
    addVariantID({ commit }, content) {
      commit('addVariantID', content);
    },
    deleteArrIDVariantAdd({ commit }, content) {
      commit('deleteArrIDVariantAdd', content);
    },
  },
  getters: {},
};
