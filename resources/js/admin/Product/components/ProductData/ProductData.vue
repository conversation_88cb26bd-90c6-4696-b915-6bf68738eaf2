<template>
  <el-form label-width="220px">
    <el-form-item label="SKU ( mã sản phẩm )">
      <!-- <el-input v-model="sku" v-if="isNaN(this.productId)" ></el-input> -->
      <el-input v-model="sku"></el-input>
    </el-form-item>
    <el-form-item label="SKU nhóm ( mã nhóm mã mẫu )">
      <!-- <el-input v-model="sku" v-if="isNaN(this.productId)" ></el-input> -->
      <el-input v-model="skuGroup"></el-input>
    </el-form-item>
    <el-form-item label="<PERSON><PERSON><PERSON> thị trường">
      <!-- <el-input v-if="!eshopID" v-model="global_price"></el-input> -->
      <el-input v-model="global_price"></el-input>
    </el-form-item>
    <el-form-item label="<PERSON><PERSON><PERSON> bán">
      <!-- <el-input  v-if="!eshopID" v-model="price"></el-input> -->
      <el-input v-model="price"></el-input>
    </el-form-item>
    <el-form-item label="Số lượng">
      <!-- <el-input v-if="!eshopID" v-model="quantity"></el-input> -->
      <el-input v-model="stock"></el-input>
    </el-form-item>
    <!-- <el-form-item label="Thương hiệu">
        <el-select v-model="manufacturer_id" placeholder="Select">
           <el-option
            v-for="item in manufacturer"
            :key="item.manufacturer_id"
            :label="item.name"
            :value="item.manufacturer_id">
          </el-option>
        </el-select>
      </el-form-item> -->
    <!-- <el-form-item label="Trạng thái kho">
          <el-select v-model="stock_status_id" placeholder="Select" >
            <el-option
                v-for="(item) in stock"
                :key="item.stock_status_id"
                :label="item.name"
                :value="item.stock_status_id">
            </el-option>
          </el-select>
      </el-form-item>
       <el-form-item label="Trọng lượng">
        <el-input type='number' v-model="weight"></el-input>
      </el-form-item> -->
    <el-form-item label="Trọng lượng khoảng">
      <el-input v-model="weight"></el-input>
    </el-form-item> 
    <el-form-item label="Trạng thái">
      <el-select v-model="status" placeholder="Select">
        <el-option v-for="item in optionsStatus" :key="item.value" :label="item.label" :value="item.value"> </el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="Xuất bản">
      <el-select v-model="published" placeholder="Select">
        <el-option v-for="item in publishList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
      </el-select>
    </el-form-item>
  </el-form>
</template>
<script>
import numberFormat from '../../mixins/numberFormat.js';
import api from '../../helpers/api';
import { mapState } from 'vuex';
export default {
  name: 'ProductData',
  // props:['lang','label','image'],
  mixins: [numberFormat],
  data() {
    return {
      // langData: this.lang,
      productId: Number(window.product_id),
      optionsStatus: [
        {
          value: 1,
          label: 'Ẩn',
        },
        {
          value: 2,
          label: 'Hiển thị',
        },
        {
          value: -1,
          label: 'Đã xóa',
        },
      ],
      publishList: [
        {
          value: 0,
          label: 'Không xuất bản',
        },
        {
          value: 1,
          label: 'Xuất bản',
        },
      ],
      manufacturer: '',
    };
  },
  mounted() {
    this.productDataByID();
    // this.stockData();
    this.manufacturerData();
  },
  computed: {
    freeship: {
      get() {
        return this.$store.state.b.content.shipping_required;
      },
      set(value) {
        this.$store.dispatch('b/setFreeship', value);
      },
    },
    eshopID() {
      return this.$store.state.b.content.eshop_product_id;
    },
    sku: {
      // eslint-disable-next-line vue/return-in-computed-property
      get() {
        if (typeof this.$store.state.b.content.sku !== 'undefined') {
          return this.$store.state.b.content.sku;
        }
      },
      set(value) {
        this.$store.dispatch('b/updateSku', value);
      },
    },
    skuGroup: {
      // eslint-disable-next-line vue/return-in-computed-property
      get() {
        if (typeof this.$store.state.b.content.sku_group !== 'undefined') {
          return this.$store.state.b.content.sku_group;
        }
      },
      set(value) {
        this.$store.dispatch('b/updateSkuGroup', value);
      },
    },
    global_price: {
      // eslint-disable-next-line vue/return-in-computed-property
      get() {
        if (typeof this.$store.state.b.content.global_price !== 'undefined') {
          const priceOrigin = this.$store.state.b.content.global_price;
          return this.numberFormat(priceOrigin);
        }
      },
      set(value) {
        this.$store.dispatch('b/updateGlobal_price', value.replace(/[^0-9]/g, ''));
      },
    },
    price: {
      // eslint-disable-next-line vue/return-in-computed-property
      get() {
        if (typeof this.$store.state.b.content.price !== 'undefined') {
          const priceOrigin = this.$store.state.b.content.price;
          return this.numberFormat(priceOrigin);
        }
      },
      set(value) {
        this.$store.dispatch('b/updatePrice', value.replace(/[^0-9]/g, ''));
      },
    },
    quantity: {
      // eslint-disable-next-line vue/return-in-computed-property
      get() {
        if (typeof this.$store.state.b.content.product_variants != 'undefined' && typeof this.$store.state.b.content.product_variants[0].stock !== 'undefined') {
          const quantityOrigin = this.$store.state.b.content.product_variants[0].stock;
          return this.numberFormat(quantityOrigin);
        }
      },
      set(value) {
        this.$store.dispatch('b/updateStock', value.replace(/[^0-9]/g, ''));
      },
    },
    stock_status_id: {
      // eslint-disable-next-line vue/return-in-computed-property
      get() {
        if (typeof this.$store.state.b.content.stock_status_id !== 'undefined') {
          return this.$store.state.b.content.stock_status_id;
        }
      },
      set(value) {
        this.$store.dispatch('b/updateStock_status_id', value);
      },
    },
    manufacturer_id: {
      // eslint-disable-next-line vue/return-in-computed-property
      get() {
        if (typeof this.$store.state.b.content.manufacturer_id !== 'undefined') {
          return this.$store.state.b.content.manufacturer_id;
        }
      },
      set(value) {
        this.$store.dispatch('b/updateManufacturer', value);
      },
    },
    status: {
      // eslint-disable-next-line vue/return-in-computed-property
      get() {
        if (typeof this.$store.state.b.content.status !== 'undefined') {
          return this.$store.state.b.content.status;
        }
      },
      set(value) {
        this.$store.dispatch('b/updateStatus', value);
      },
    },
    published: {
      // eslint-disable-next-line vue/return-in-computed-property
      get() {
        if (typeof this.$store.state.b.content.published !== 'undefined') {
          return this.$store.state.b.content.published;
        }
      },
      set(value) {
        this.$store.dispatch('b/updatePublished', value);
      },
    },
    stock: {
      // eslint-disable-next-line vue/return-in-computed-property
      get() {
        if (typeof this.$store.state.b.content.stock !== 'undefined') {
          return this.$store.state.b.content.stock;
        }
      },
      set(value) {
        this.$store.dispatch('b/updateStock', value);
      },
    },
    weight: {
      // eslint-disable-next-line vue/return-in-computed-property
      get() {
        if (typeof this.$store.state.b.content.weight !== 'undefined') {
          return this.$store.state.b.content.weight;
        }
      },
      set(value) {
          this.$store.dispatch('b/updateWeight', value);
      },
    },
  },
  methods: {
    getAndSetData(content) {
      this.$store.dispatch('b/contentData', content);
    },
    async productDataByID() {
      if (!Number.isNaN(this.productId)) {
        const url = `admin/ajax/products/${this.productId}/controller/prdData`;
        const products = await api.request('GET', url);
        if (products) {
          this.getAndSetData(products.data.data);
        }
      }
    },
    async stockData() {
      const url = 'admin/ajax/products/controller/getStock';
      const stock = await api.request('GET', url);
      this.$store.dispatch('b/setDataPrdOrigin', stock.data.data);
      this.stock = stock.data.data;
    },
    async manufacturerData() {
      const fillters = [];
      const noPagi = 1;
      const perPage = 50;
      const url = 'admin/ajax/manufacturer/controller/manufacturerList';
      const result = await api.request('POST', url, { fillters, noPagi, perPage });
      if (!result.data.error) {
        this.manufacturer = result.data.data;
      }
    },
  },
};
</script>
