<template>
  <el-form>
    <div class="form-group">
      <label>
        Tên <span class="small text-danger">({{ this.label }})</span>
      </label>
      <el-input placeholder="" v-model="title">
        <template slot="prepend"><img :src="'/admin/img/lang/' + this.image" :title="this.label" /></template>
      </el-input>
    </div>
    <div class="form-group">
      <label>
        Slug <span class="small text-danger">({{ this.label }})</span>
      </label>
      <el-input placeholder="" v-model="slug">
        <template slot="prepend"><img :src="'/admin/img/lang/' + this.image" :title="this.label" /></template>
      </el-input>
    </div>
    <div class="form-group">
      <label>
        Mô tả ngắn <span class="small text-danger">({{ this.label }})</span>
      </label>
      <el-input placeholder="" v-model="short_description">
        <template slot="prepend"><img :src="'/admin/img/lang/' + this.image" :title="this.label" /></template>
      </el-input>
    </div>
    <div class="form-group">
      <label>
        Thông tin ưu đãi <span class="small text-danger">({{ this.label }}) <img :src="'/admin/img/lang/' + this.image" :title="this.label" /></span>
      </label>
      <!-- <editor api-key='7uxgu2n6ebpmly1xgl7lb89xehagtrwxr38hi9jbqg7ims0p' v-model="description"  :init="{ height: 500, }" /> -->
      <tinyMCEpromotion></tinyMCEpromotion>
    </div>
    <div class="form-group">
      <label>
        Nội dung <span class="small text-danger">({{ this.label }}) <img :src="'/admin/img/lang/' + this.image" :title="this.label" /></span>
      </label>
      <!-- <editor api-key='7uxgu2n6ebpmly1xgl7lb89xehagtrwxr38hi9jbqg7ims0p' v-model="description"  :init="{ height: 500, }" /> -->
      <tinyMCE></tinyMCE>
    </div>
    <div class="form-group">
      <label>
        Hướng dẫn sử dụng <span class="small text-danger">({{ this.label }}) <img :src="'/admin/img/lang/' + this.image" :title="this.label" /></span>
      </label>
      <!-- <editor api-key='7uxgu2n6ebpmly1xgl7lb89xehagtrwxr38hi9jbqg7ims0p' v-model="description"  :init="{ height: 500, }" /> -->
      <tinyMCEpolicy></tinyMCEpolicy>
    </div>
    <div class="form-group">
      <label>
        Thông tin phụ kiện <span class="small text-danger">({{ this.label }}) <img :src="'/admin/img/lang/' + this.image" :title="this.label" /></span>
      </label>
      <!-- <editor api-key='7uxgu2n6ebpmly1xgl7lb89xehagtrwxr38hi9jbqg7ims0p' v-model="description"  :init="{ height: 500, }" /> -->
      <tinyMCErelated></tinyMCErelated>
    </div>
    <hr />
    <div class="form-group">
      <el-form-item label="Meta Tag Title" label-width="160px">
        <el-input placeholder="" v-model="meta_title">
          <span class="small text-danger">({{ this.label }})</span><template slot="prepend"><img :src="'/admin/img/lang/' + this.image" :title="this.label" /></template>
        </el-input>
      </el-form-item>
    </div>
    <div class="form-group">
      <el-form-item label="Meta Tag Description" label-width="160px">
        <el-input placeholder="" v-model="meta_description">
          <span class="small text-danger">({{ this.label }})</span><template slot="prepend"><img :src="'/admin/img/lang/' + this.image" :title="this.label" /></template>
        </el-input>
      </el-form-item>
    </div>
    <div class="form-group">
      <el-form-item label="Meta Tag Keywords" label-width="160px">
        <el-input placeholder="" v-model="meta_keyword">
          <span class="small text-danger">({{ this.label }})</span><template slot="prepend"><img :src="'/admin/img/lang/' + this.image" :title="this.label" /></template>
        </el-input>
      </el-form-item>
    </div>
  </el-form>
</template>
<script>
import api from '../../helpers/api';
// import Editor from '@tinymce/tinymce-vue';
import TinyMCE from './tinyMCE.vue';
import tinyMCEpolicy from './tinyMCEpolicy.vue';
import tinyMCErelated from './tinyMCErelated.vue';
import tinyMCEpromotion from './tinyMCEpromotion.vue';
export default {
  name: 'ProductGeneral',
  props: ['lang', 'label', 'image'],
  data: function () {
    return {
      langData: this.lang,
      productId: Number(window.product_id),
    };
  },
  components: {
    // Editor,
    TinyMCE,
    tinyMCEpolicy,
    tinyMCErelated,
    tinyMCEpromotion,
  },
  mounted() {
    this.productByID();
  },
  computed: {
    title: {
      // eslint-disable-next-line vue/return-in-computed-property
      get() {
        if (typeof this.$store.state.a.content[this.langData] !== 'undefined') {
          return this.$store.state.a.content[this.langData].name;
        }
      },
      set(value) {
        this.$store.dispatch('a/updateTitle', value);
      },
    },
    slug: {
      // eslint-disable-next-line vue/return-in-computed-property
      get() {
        if (typeof this.$store.state.a.content[this.langData] !== 'undefined') {
          return this.$store.state.a.content[this.langData].slug;
        }
      },
      set(value) {
        this.$store.dispatch('a/updateSlug', value);
      },
    },
    short_description: {
      // eslint-disable-next-line vue/return-in-computed-property
      get() {
        if (typeof this.$store.state.a.content[this.langData] !== 'undefined') {
          return this.$store.state.a.content[this.langData].short_description;
        }
      },
      set(value) {
        this.$store.dispatch('a/updateShort_description', value);
      },
    },
    description: {
      // eslint-disable-next-line vue/return-in-computed-property
      get() {
        if (typeof this.$store.state.a.content[this.langData] !== 'undefined') {
          return this.$store.state.a.content[this.langData].description;
        }
      },
      set(value) {
        this.$store.dispatch('a/updateDescription', value);
      },
    },
    policy: {
      // eslint-disable-next-line vue/return-in-computed-property
      get() {
        if (typeof this.$store.state.a.content[this.langData] !== 'undefined') {
          return this.$store.state.a.content[this.langData].policy;
        }
      },
      set(value) {
        this.$store.dispatch('a/updatePolicy', value);
      },
    },
    related: {
      // eslint-disable-next-line vue/return-in-computed-property
      get() {
        if (typeof this.$store.state.a.content[this.langData] !== 'undefined') {
          return this.$store.state.a.content[this.langData].related;
        }
      },
      set(value) {
        this.$store.dispatch('a/updateRelated', value);
      },
    },
    promotion: {
      // eslint-disable-next-line vue/return-in-computed-property
      get() {
        if (typeof this.$store.state.a.content[this.langData] !== 'undefined') {
          return this.$store.state.a.content[this.langData].promotion;
        }
      },
      set(value) {
        this.$store.dispatch('a/updatePromotion', value);
      },
    },
    meta_title: {
      // eslint-disable-next-line vue/return-in-computed-property
      get() {
        if (typeof this.$store.state.a.content[this.langData] !== 'undefined') {
          return this.$store.state.a.content[this.langData].meta_title;
        }
      },
      set(value) {
        this.$store.dispatch('a/updateMeta_title', value);
      },
    },
    meta_description: {
      // eslint-disable-next-line vue/return-in-computed-property
      get() {
        if (typeof this.$store.state.a.content[this.langData] !== 'undefined') {
          return this.$store.state.a.content[this.langData].meta_description;
        }
      },
      set(value) {
        this.$store.dispatch('a/updateMeta_description', value);
      },
    },
    meta_keyword: {
      // eslint-disable-next-line vue/return-in-computed-property
      get() {
        if (typeof this.$store.state.a.content[this.langData] !== 'undefined') {
          return this.$store.state.a.content[this.langData].meta_keyword;
        }
      },
      set(value) {
        this.$store.dispatch('a/updateMeta_keyword', value);
      },
    },
  },
  methods: {
    // onSubmit() {
    //   console.log('submit!');
    // }
    getAndSetData(content) {
      this.$store.dispatch('a/contentGeneral', content);
    },
    async productByID() {
      if (!isNaN(this.productId)) {
        const url = `admin/ajax/products/${this.productId}-${this.langData}/controller`;
        const products = await api.request('GET', url);
        if (!products.data.error) {
          this.getAndSetData(products.data.data);
        }
      }
    },
  },
};
</script>
