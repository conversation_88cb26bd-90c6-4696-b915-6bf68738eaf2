<template>
  <div id="productVariantAdd" class="ml-2 ml-auto">
    <center>
      <el-button
        v-if="!isNaN(this.productId)"
        type="primary"
        class="mt-2 w-50"
        @click="centerDialogVisible = true"
        plain
        style="border: 1px dashed;"
      >
        <i class="el-icon-plus"></i> Tạo biến thể sản phẩm
      </el-button>
      <el-button
        type="primary"
        v-else
        class="mt-2 w-50"
        @click="addVariantPrd"
        plain
        style="border: 1px dashed;"
      >
        <i class="el-icon-plus"></i> Tạo biến thể sản phẩm
      </el-button>
    </center>

    <el-dialog
      title="Tạo biến thể của sản phẩm"
      :visible.sync="centerDialogVisible"
      width="80%"
      center
      @opened="openedHook"
    >
      <el-form ref="form" :model="form" label-width="130px">
        <el-form-item label="Thuộc t<PERSON>h">
          <span slot="label">
            Thu<PERSON><PERSON> t<PERSON>h <br />
            <el-button type="text" class="mt-2" @click="addAttrGroup"
              >+<PERSON>h<PERSON><PERSON> thu<PERSON><PERSON> t<PERSON>h</el-button
            >
          </span>
          <div class="row">
            <div
              class="col-4"
              v-for="(item, index) in numberOfAttrGroup"
              :key="index"
            >
              <el-card
                class="box-card"
                v-if="numberOfAttrGroup[index]['t']"
                :body-style="{ padding: '0px' }"
              >
                <div class="text item">
                  <el-form-item label="Thuộc tính" class="mt-2 mr-2">
                    <el-select
                      placeholder="Màu sắc, Kích thước, v.v..."
                      class="w-100"
                      v-model="choice.optionIds[index]"
                      @change="filterOptionValueByOptionId($event, index)"
                    >
                      <el-option
                        v-for="opt in options.data"
                        :key="opt.id"
                        :label="opt.desc.name !=null ? opt.desc.name : 'Trống tên: Mã '+opt.id"
                        :value="opt.id"
                      >
                        <p v-if="opt.desc.name !=null && opt.desc.short_description !=null">{{opt.desc.name + ' (' + opt.desc.short_description + ')'}}</p>
                        <p v-if="opt.desc.name ==null && opt.desc.short_description == null">{{'Trống tên: Mã '+opt.id }}</p>
                      </el-option>
                    </el-select>
                  </el-form-item>

                  <el-form-item label="Giá trị" class="mr-2">
                    <el-select
                      placeholder="Vàng, XXL, v.v..."
                      class="w-100"
                      v-model="choice.optionValueIds[index]"
                      filterable
                      allow-create
                      remote
                    >
                      <el-option
                        v-for="optVal in optionValueByOptionId[index]"
                        :key="optVal.desc.name"
                        :label="optVal.desc.name !=null ? optVal.desc.name : 'Trống tên: Mã '+optVal.id"
                        :value="optVal.id"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                  <div
                    :id="'box-' + index"
                    @click="detectIndexImage(index)"
                    v-if="
                      typeof choice.optionIds[index] !== 'undefined' &&
                        choice.optionIds[index] in optionCanAddImg
                    "
                  >
                    <el-upload
                      action=""
                      :name="'upload' + index"
                      ref="upload"
                      list-type="picture-card"
                      :show-file-list="true"
                      :auto-upload="false"
                      :on-change="uploadFiles"
                      :file-list="imageVarials[index]"
                    >
                      <i slot="default" class="el-icon-plus"></i>
                      <div slot="file" slot-scope="{ file }">
                        <!-- <pre>{{ file }}</pre> -->
                        <!-- <div v-for="(img, index) in imageVarials[index]" :key="index"> -->
                        <img
                          class="el-upload-list__item-thumbnail"
                          :src="
                            herf +
                              '/upload/' +
                              type +
                              '/thumb_350x0/' +
                              file.name
                          "
                          alt=""
                        />
                        <span class="el-upload-list__item-actions">
                          <!-- <span
                          class="el-upload-list__item-preview"
                          @click="handlePictureCardPreview(file)"
                        >
                          <i class="el-icon-zoom-in"></i>
                        </span>
                        <span
                          v-if="!disabled"
                          class="el-upload-list__item-delete"
                          @click="handleEdit(file)"
                        >
                          <i class="el-icon-edit"></i>
                        </span> -->
                          <span
                            v-if="!disabled"
                            class="el-upload-list__item-delete"
                            @click="handleRemove(file)"
                          >
                            <i class="el-icon-delete"></i>
                          </span>
                        </span>
                      </div>
                    </el-upload>
                  </div>
                  <el-button
                    class="p-2 card-close-btn"
                    type="primary"
                    @click="removeAttrGroup(index)"
                  >
                    <i class="el-icon-close"></i>
                  </el-button>
                </div>
                <!-- text item -->
              </el-card>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="Giá tiền">
          <el-input v-model="price"></el-input>
        </el-form-item>
        <el-form-item label="Giá gạch">
          <el-input v-model="priceGlobal"></el-input>
        </el-form-item>
        <el-form-item label="Mã SKU">
          <el-input v-model="form.sku"></el-input>
        </el-form-item>

        <el-form-item label="Số lượng">
          <el-input-number
            v-model="form.stock"
            :min="0"
            :max="9999"
          ></el-input-number>
        </el-form-item> 

        <el-form-item label="Trạng thái bán">
          <el-radio-group v-model="form.status">
            <el-radio label="1" border>Đang bán</el-radio>
            <el-radio label="2" border>Không bán</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item class="dialog-footer">
          <el-button @click="centerDialogVisible = false">Đóng</el-button>
          <el-button
            type="primary"
            @click="storeProductVariant"
            v-show="canCreateVariant"
            >Tạo biến thể</el-button
          >
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script src="../ProductAdd.js"></script>
<style lang="css" scope>
.card-close-btn {
  float: right;
  position: absolute;
  top: -10px;
  right: 0;
  border: 1px solid;
  border-radius: 22px;
}
</style>
