import axios from 'axios';
import api from '../../helpers/api.js';
import arrayMixin from '../../mixins/arrayMixin.js';
import messageMixin from '../../mixins/messageMixin.js';
import { eventBus } from '../../app.js';
import numberFormat from '../../mixins/numberFormat.js';

export default {
  mixins: [arrayMixin, messageMixin, numberFormat],
  name: 'ProductVariantEdit',
  props: {
    productVariant: Object,
  },

  data() {
    return {
      centerDialogVisible: false,
      form: {
        price: 0,
        global_price: 0,
        sku: '',
        weight: 0,
        stock: 0,
        optionIds: this.optionIds,
        optionValueIds: this.optionValueIds,
        status: '1',
      },

      productId: isNaN(Number(window.product_id)) ? 0 : Number(window.product_id),

      // Mảng check thứ tự thuộc tính
      numberOfAttrGroup: [],
      disabled: false,
      options: [],
      options_value: [],
      choice: {
        optionIds: [],
        optionValueIds: [],
      },
      optionValueByOptionId: [],

      canCreateVariant: true,
      indexImageOption: '',
      herf: window.location.origin,
      type: 'product',
      checkRun: 0,
      loading: true,
    };
  },

  mounted() {
    // this.getAllOptionsValue();
  },
  computed: {
    imageVarials() {
      return this.$store.state.c.imageVarials;
    },
    optionCanAddImg() {
      return this.$store.state.c.optionCanAddImg;
    },
    price: {
      get() {
        return this.numberFormat(this.form.price);
      },
      set(value) {
        return this.form.price = value.replace(/[^0-9]/g, '');
      },
    },
    weight:{
      get() {
        return  this.form.weight;
      },
      set(value) {
         this.form.weight = value;
      }
    },
    priceGlobal: {
      get() {
        return this.numberFormat(this.form.global_price);
      },
      set(value) {
        return this.form.global_price = value.replace(/[^0-9]/g, '');
      },
    },
    eshopIsRun() {
      if (typeof this.$store.state.b.content.eshop_product_id !== 'undefined' && typeof this.$store.state.b.content.eshop_product_id != null && this.$store.state.b.content.eshop_is_run) {
        return true;
      }
      return false;
    },
  },
  methods: {
    isValidHttpUrl(string) {
      let url;
      try {
        url = new URL(string);
      } catch (_) {
        return `${this.herf
        }/upload/${
          this.type
        }/thumb_350x0/${string}`;
      }

      return string;
    },
    setDefaultImgVariable() {
      this.$store.dispatch('c/setImageVarialsAdd', []);
    },
    handleRemove(file) {
      console.log(file);
      // const data = this.$store.state.c.imageVarials[file.sort];
      this.$store.dispatch('c/deleteImageVarials', file);
    },
    async loadImageVarials() {
      const data = [];
      // console.log(this.productVariant.product_option_values);
      this.productVariant.product_option_values.forEach((element, index) => {
        if (!this.eshopIsRun) {
        data[index] = typeof element.images !== 'undefined' && element.images !== '' ? JSON.parse(element.images) : [];
        } else {
          if (typeof element.images !== 'undefined' && element.images !== '') {
            if (element.images.charAt(0)==='[') {
              data[index] = JSON.parse(element.images);
            } else if (element.images.charAt(0)==='{') {
              data[index] = JSON.parse(`[${element.images}]`);
            } else {
              data[index] = [];
            }
          } else {
            data[index] = [];
          }
        }
      });
      // console.log(data);
      this.$store.dispatch('c/setImageVarials', data);
    },
    detectIndexImage(index) {
      this.indexImageOption = index;
    },
    openedHook() {
      this.setDefaultImgVariable();
      this.getAllOptions().then((res) => {
        this.getAllOptionsValue().then((res) => {
          this.fillForm();
        });
      });
    },
    async uploadFiles(req) {
      const formdata = new FormData();
      formdata.append('Filedata', req.raw);
      // formdata.append('object_id', this.productId);
      const config = {
        headers: {
          'Content-Type': 'multipart/form-data',
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')
            .content,
        },
      };
      axios
        .post('/admin/ajax/products/controller/imgUpload', formdata, config)
        .then((res) => {
          this.$store.dispatch('c/imageVarials', [res.data.data.fileName, this.indexImageOption]);
          this.imageVarials = this.$store.state.c.imageVarials;
          console.log('image upload succeed.');
          this.canCreateVariant = true;
        })
        .catch((err) => {
          console.log(err.message);
        });
    },
    async fillForm() {
      // this.numberOfAttrGroup = [];
      this.form = {
        price: this.productVariant.price,
        global_price: this.productVariant.global_price,
        sku: this.productVariant.sku,
        stock: this.productVariant.stock,
        status: this.productVariant.status,
      };

      await this.loadImageVarials();
      this.choice.optionIds = [];
      this.choice.optionValueIds = [];
      await this.productVariant.product_option_values.forEach((productVariantItem, index) => {
        this.numberOfAttrGroup[index] = { t: 123 };
        this.choice.optionIds.push(productVariantItem.option_id);
        this.choice.optionValueIds.push(productVariantItem.option_value_id);
        const optionValuesFollowByOptionId = this.options_value.data.filter((optionValue) => optionValue.option_id == productVariantItem.option_id);
        this.optionValueByOptionId[index] = optionValuesFollowByOptionId;
        this.loading = false;
      });
      this.loading = false;
    },

    async updateProductVariant() {
      const form = {
        price: this.form.price,
        global_price: this.form.global_price,
        sku: this.form.sku,
        stock: this.form.stock,
        optionIds: this.form.optionIds,
        optionValueIds: this.form.optionValueIds,
        status: this.form.status,
        imageVarials: this.$store.state.c.imageVarials,
        choice: this.choice,
        product_variant_id: this.productVariant.product_variant_id,
        old_product_option_values: this.productVariant.product_option_values,
      };
      const url = `admin/ajax/products/${this.productId}/variants/storeUpdate`;
      const res = await api.request('POST', url, form);
      if (res.data.success) {
        if (res.data.data != 'Error') {
          this.showMessage(res.data.message, 'success');
        } else {
          this.showMessage(res.data.message, 'error');
        }
        // this.$bus.emit('storeNewProductVariant', res.data);
        this.centerDialogVisible = false;
        this.someComponent2Method();
      }
    },
    someComponent2Method() {
      // your code here
      eventBus.$emit('fireMethod');
    },
    addAttrGroup() {
      // if (
      //     this.choice.optionIds.includes(null) ||
      //     this.choice.optionValueIds.includes(null) ||
      //     this.choice.optionIds.length == 0 ||
      //     this.choice.optionValueIds.length == 0 ||
      //     this.choice.optionIds.length != this.numberOfAttrGroup.length ||
      //     this.choice.optionValueIds.length != this.numberOfAttrGroup.length
      // ) {
      //     this.canCreateVariant = false;
      //     return this.showMessage('Bạn cần chọn đầy đủ thông tin thuộc tính & giá trị thuộc tính trước đã.', 'error');
      // } else {
      this.numberOfAttrGroup.push({ t: Date.now() });
      // }
    },

    removeAttrGroup(index) {
      // Lấy current optionId đang được chọn nếu có
      if (this.numberOfAttrGroup.length > 1) {
        this.$delete(this.choice.optionIds, index);
        this.$delete(this.choice.optionValueIds, index);
        this.$delete(this.numberOfAttrGroup, index);
      } else {
        return this.showMessage('Sản phẩm bắt buộc phải có thuộc tính', 'error');
      }
    },

    async getAllOptions() {
      const options = await api.request('GET', 'admin/ajax/option/all');
      this.options = options.data;
    },

    async getAllOptionsValue() {
      const optionsValue = await api.request('GET', 'admin/ajax/option-value/all');
      this.options_value = optionsValue.data;
      return this.options_value;
    },

    filterOptionValueByOptionId(optionId, position) {
      const displayTime = this.choice.optionIds.filter((optionIdItem) => optionIdItem == optionId).length;
      if (displayTime <= 1) {
        const optionValueByPosition = this.options_value.data.filter((optionValue) => optionValue.option_id == optionId);

        return this.optionValueByOptionId[position] = optionValueByPosition;
      }
      return this.$message({
        showClose: true,
        message: 'Thuộc tính này đã được chọn, hoặc đã xảy ra lỗi khi vận hành',
        type: 'error',
        duration: 10000,
      });
    },
  },
}; // End class