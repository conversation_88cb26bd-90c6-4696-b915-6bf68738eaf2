<template>
  <el-table
    :data="listProductVariants.data"
    border
    class="w-100" empty-text="Không có dữ liệu">
    <el-table-column
      label="ID">
      <template slot-scope="scope">
        <p>{{scope.row.product_variant_id}}</p>
        <i v-if="!isNaN(productId)">EID:{{scope.row.eshop_variant_id}}</i>
      </template>
    </el-table-column>

    <el-table-column
      label="Biến thể"
      width="250">
      <template slot-scope="scope">
        <p v-for="prodOptValue in scope.row.product_option_values" :key="prodOptValue.product_option_value_id">
          <span v-if="prodOptValue.option && prodOptValue.option_value">
            <b>{{ prodOptValue.option.desc.name  }}</b>:
            {{ prodOptValue.option_value.desc.name  }}
          </span>
        </p>
      </template>
    </el-table-column>

    <el-table-column
      label="Giá tiền">
      <template slot-scope="scope">
        <p>{{numberFormat(scope.row.price)}}</p>
        <s>{{numberFormat(scope.row.global_price)}}</s>
      </template>
    </el-table-column>

    <el-table-column
      label="Mã SKU">
      <template slot-scope="scope">
        {{ scope.row.sku }}
      </template>
    </el-table-column>

    <el-table-column
      label="Tồn kho">
      <template slot-scope="scope">
        {{ scope.row.stock }}
      </template>
    </el-table-column>

    <el-table-column label="Trạng thái">
      <template slot-scope="scope">
        <el-tag :type="scope.row.status_type">{{ scope.row.status_text }}</el-tag>
      </template>
    </el-table-column>

    <el-table-column label="Ngày tạo">
      <template slot-scope="scope">
        <span  style="display: block;
                      word-break: break-word;">{{ scope.row.created_at }}</span>
      </template>
    </el-table-column>

    <el-table-column label="Ngày cập nhật">
      <template slot-scope="scope">
         <span style="display: block;
                      word-break: break-word;"
         >{{ scope.row.updated_at }}</span>
      </template>
    </el-table-column>


    <el-table-column label="Thao tác">
      <template slot-scope="scope">
        <el-dropdown trigger="click">
          <span class="el-dropdown-link">
            Chọn<i class="el-icon-arrow-down el-icon--right"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <product-variant-edit :product-variant="scope.row"></product-variant-edit>
            <product-variant-delete :product-variant="scope.row"></product-variant-delete>
            <!-- <el-dropdown-item icon="el-icon-circle-plus-outline">Bật/Tắt trạng thái bán</el-dropdown-item> -->
          </el-dropdown-menu>
        </el-dropdown>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
import api from '../../helpers/api';
import ProductVariantDelete from '../ProductVariant/ProductVariantDelete.vue';
import ProductVariantEdit from '../ProductVariant/ProductVariantEdit.vue';
import { eventBus } from '../../app.js';

export default {
  components: { ProductVariantDelete, ProductVariantEdit },
  data() {
    return {
      listProductVariants: [],
      productId: Number(window.product_id),
    };
  },

  created() {
    this.$bus.on('storeNewProductVariant', (productVariant) => {
      this.listProductVariants.data.unshift(productVariant.data);
    });

    this.$bus.on('deleteProductVariant', (productVariant) => {
      const position = this.listProductVariants.data.indexOf(productVariant);
      this.listProductVariants.data.splice(position, 1);
    });
    eventBus.$on('fireMethod', () => {
              this.getAllProductVariants();
    });
  },

  mounted() {
    this.getAllOption();
  },
  methods: {
    numberFormat(number) {
      return new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND',
      }).format(number);
    },
     async getAllOption() {
        this.getAllProductVariants();
        const url = 'admin/ajax/option/all';
        const option = await api.request('GET', url);
        const dataCheck={};
        if (option.data.success && this.checkRun==0) {
            option.data.data.forEach((element) => {
              if (element.show_image > 0) {
                dataCheck[element.id]=element.id;
              }
            });
            this.$store.dispatch('c/optionCanAddImg', dataCheck);
            this.optionCanAddImg = dataCheck;
            this.checkRun++;
        }
    },
    async getAllProductVariants() {
      if (!isNaN(this.productId)) {
        const url = `admin/ajax/products/${this.productId}/variants`;
        const productsVariant = await api.request('GET', url);
        this.listProductVariants = productsVariant.data;
      } else {
        const prdID = 0;
        const url = `admin/ajax/products/${prdID}/variantsByIDs`;
        const ids = this.$store.state.b.arrID_variants_add;
        const productsVariant = await api.request('POST', url, { ids });
        this.listProductVariants = productsVariant.data;
      }
    },
  },
};
</script>
