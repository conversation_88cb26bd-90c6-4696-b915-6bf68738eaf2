<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="./vendor/phpunit/phpunit/phpunit.xsd"
         backupGlobals="false"
         backupStaticAttributes="false"
         bootstrap="vendor/autoload.php"
         colors="true"
         convertErrorsToExceptions="true"
         convertNoticesToExceptions="true"
         convertWarningsToExceptions="true"
         processIsolation="false"
         stopOnFailure="false">
    <testsuites>
        <testsuite name="Containers Tests">
            <directory suffix="Test.php">./app/Containers</directory>
        </testsuite>
    </testsuites>
    <filter>
        <whitelist processUncoveredFilesFromWhitelist="true">
            <directory suffix=".php">./app</directory>
            <exclude>
                <!--
                NOTE: Exclude the following directories because they fail when directly included with code-coverage
                -->
                <directory suffix=".php">./app/Containers/*/UI/*/Routes</directory>
                <directory suffix=".php">./app/Containers/*/Data/Factories</directory>
            </exclude>
        </whitelist>
    </filter>
    <php>
      <env name="API_FULL_URL"          value="http://api.apiato.test"/>
      <server name="APP_ENV" value="testing"/>
      <server name="BCRYPT_ROUNDS" value="4"/>
      <server name="CACHE_DRIVER" value="array"/>
      <server name="DB_CONNECTION" value="sqlite"/>
      <server name="DB_DATABASE" value=":memory:"/>
      <server name="MAIL_DRIVER" value="array"/>
      <server name="QUEUE_CONNECTION" value="sync"/>
      <server name="SESSION_DRIVER" value="array"/>
    </php>
</phpunit>
