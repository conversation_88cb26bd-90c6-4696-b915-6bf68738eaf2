<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title><PERSON><PERSON><PERSON> thieu</title>
    <!-- mmenu css -->
    <link rel="stylesheet" href="libs/mmenu/jquery.mmenu.all.css">
    <!-- animate css-->
    <link rel="stylesheet" href="libs/animate/animate.min.css">
    <link rel="stylesheet" href="libs/aos/aos.css">
    <!-- swiper css-->
    <link rel="stylesheet" href="libs/swiper/swiper-bundle.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Barlow:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/style.css">
</head>

<body>
    <div id="app">
        <myheader></myheader>
        <main class="">
            <div class="bannerslide-main">
                <div class="swiper bannerslide--slide">
                    <div class="swiper-wrapper">
                        <div class="swiper-slide" v-for="n in 2">
                            <div class="bannerslide--items style--2" style="background-image: url('images/bg-kgb2.jpg');">
                                <div class="container">
                                    <div class="row align-items-center">
                                        <div class="col-md-6">
                                            <div class="bannerslide--other">
                                                <div class="box-title">
                                                    <h2 class="title">
                                                        Kim gia bảo
                                                    </h2>
                                                    <div class="sub_title">
                                                        Vàng tích trữ
                                                    </div>
                                                    <div class="desc">
                                                        Với hàm lượng chuẩn, trọng lượng đa dạng và mẫu mã tinh gọn, mỗi sản phẩm là sự lựa chọn đáng tin cậy cho những ai muốn bảo toàn giá trị và truyền lại gia sản cho thế hệ mai sau.
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <h1 class="goldmain-title">
                                                Ẩn Tùng
                                            </h1>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="producbox--panigation">
                        <div class="swiper-pagination swiper-pagination-bannerslide"></div>
                    </div>
                </div>
            </div>
            <div class="block-virtue">
                <div class="container">
                    <div class="row a">
                        <div class="col-md-6">
                            <div class="virtue--content">
                                <img src="images/logo-title2.png" alt="">
                                <div class="line"></div>
                                <div class="desc">
                                    Tứ Quý là biểu tượng của phẩm hạnh, được nghệ thuật chế tác khắc ghi dấu ấn thời gian.
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="virtue--video">
                                <div class="virtue--other video--other">
                                    <div onclick="playVideo(this, 'https://www.youtube.com/embed/RdYgHs0fGKM?autoplay=1&rel=0&showinfo=0&controls=0')" class="buttonclick-autoplay" style="background-image: url('images/img-poster.jpg');">
                                        <span class="btn-links">
                                            Xem Video
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="block-pricegold">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-6">
                            <div class="pricegold--list">
                                <div class="swiper pricegold--slide">
                                    <div class="swiper-wrapper">
                                        <div class="swiper-slide" v-for="n in 3">
                                            <div class="box-title">
                                                <h2 class="title">
                                                    Kim gia Bảo Tứ quý
                                                </h2>
                                                <div class="sub_title">
                                                    Đồng vàng
                                                </div>
                                            </div>
                                            <div class="img">
                                                <img src="images/img-vdd.jpg" alt="">
                                            </div>
                                            <div class="desc">
                                                Đồng vàng óng ánh bền lâu,<br /> Ngàn năm vẫn sáng một màu phú sang.
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="producbox--panigation">
                                    <div class="swiper-pagination swiper-pagination-pricegold"></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="pricegold--right">
                                <div class="pricegold--other">
                                    <h3 class="title">
                                        Mua nhanh
                                    </h3>
                                    <form action="">
                                        <div class="pricegold--form">
                                            <div class="pricegold--row" v-for="n in 3">
                                                <div class="pricegold--col1">
                                                    <img src="images/img-ex1.png" alt="">
                                                </div>
                                                <div class="pricegold--col2">
                                                    <div class="pricegold--box">
                                                        <div class="name">
                                                            Nhẫn trơn Kim Gia Bảo
                                                        </div>
                                                        <div class="parameter">
                                                            Vàng 24K (999.9)
                                                        </div>
                                                        <div class="price">
                                                            13.000.000
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="pricegold--col3">
                                                    <div class="pricegold--quantity">
                                                        <div class="quantity--title">
                                                            Định lượng (Chỉ)
                                                        </div>
                                                        <div class="pricegold--input">
                                                            <select name="" id="">
                                                                <option value="">
                                                                    1 chỉ
                                                                </option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="pricegold--col3">
                                                    <div class="pricegold--quantity">
                                                        <div class="quantity--title">
                                                            Số lượng
                                                        </div>
                                                        <div class="pricegold--input">
                                                            <div class="inp-sl">
                                                                <span class="minus">
                                                                    <svg xmlns="http://www.w3.org/2000/svg" width="6" height="2" viewBox="0 0 6 2" fill="none">
  <path d="M0.679688 0.792969H5.90429V1.98599H0.679688V0.792969Z" fill="#4F4F4F"/>
</svg>
                                                                </span>
                                                                <input type="text" class="num" value="01">
                                                                <span class="plus">
                                                                    <svg xmlns="http://www.w3.org/2000/svg" width="9" height="9" viewBox="0 0 9 9" fill="none">
<path d="M0 3.82589H3.82589V0H5.01891V3.82589H8.8448V5.01891H5.01891V8.8448H3.82589V5.01891H0V3.82589Z" fill="#4F4F4F"/>
</svg>
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="pricegold--total">
                                            <div class="number">
                                                <div class="txt">
                                                    Tổng
                                                </div>
                                                <div class="total--price">
                                                    13.000.000
                                                </div>
                                            </div>
                                            <button class="storesystem--btn" type="submit">
Mua
                                            </button>
                                        </div>
                                    </form>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="block-product">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="box-title">
                                <h2 class="title">
                                    Kim gia bảo
                                </h2>
                            </div>
                            <div class="product--list">
                                <div class="row gx-lg-4 row-cols-lg-4 row-cols-md-3 row-cols-2">
                                    <div class="col" v-for="n in 12">
                                        <div class="product--items">
                                            <a href="" class="img">
                                                <img src="images/img-prod3.png" alt="">
                                            </a>
                                            <div class="product--body">
                                                <h3 class="title">
                                                    <a href="">
                                                                        Đồng Kim Gia Bảo - Tùng
                                                                    </a>
                                                </h3>
                                                <div class="gold--infor">
                                                    1 chỉ | Vàng 24K (999.9)
                                                </div>
                                                <div class="price">
                                                    13.000.000
                                                </div>
                                                <button class="add--cart" type="submit">
                                                                    <svg
                                                                        xmlns="http://www.w3.org/2000/svg"
                                                                        width="43"
                                                                        height="42"
                                                                        viewBox="0 0 43 42"
                                                                        fill="none"
                                                                    >
                                                                        <path
                                                                            d="M32.2422 40.833H0.584914V14.2905H32.2422"
                                                                            stroke="#AE8751"
                                                                            stroke-width="1.17444"
                                                                            stroke-miterlimit="10"
                                                                        />
                                                                        <path
                                                                            d="M8.87572 16.6765V7.37212C8.87572 3.69604 12.243 0.68457 16.4301 0.68457C20.6171 0.68457 23.9844 3.67527 23.9844 7.37212V16.6765"
                                                                            stroke="#AE8751"
                                                                            stroke-width="1.17444"
                                                                            stroke-miterlimit="10"
                                                                        />
                                                                        <path d="M31.9793 37.1113C26.3145 37.1113 21.7222 32.8061 21.7222 27.4953C21.7222 22.1846 26.3145 17.8794 31.9793 17.8794C37.6441 17.8794 42.2363 22.1846 42.2363 27.4953C42.2363 32.8061 37.6441 37.1113 31.9793 37.1113Z" fill="#AE8751"/>
                                                                        <path
                                                                            d="M31.9961 20.7104V34.2309"
                                                                            stroke="white"
                                                                            stroke-width="1.17444"
                                                                            stroke-miterlimit="10"
                                                                        />
                                                                        <path
                                                                            d="M24.7793 27.478H39.2012"
                                                                            stroke="white"
                                                                            stroke-width="1.17444"
                                                                            stroke-miterlimit="10"
                                                                        />
                                                                    </svg>
                                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="product--panigation">
                                <div class="txt">
                                    11/92 sản phẩm
                                </div>
                                <div class="box--btn">
                                    <a href="" class="btn-links">
                                                Xem toàn bộ sản phẩm
                                            </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <myfooter></myfooter>
        </main>
    </div>
    <script src="js/vue.js"></script>
    <script src="js/httpvueloader.js"></script>
    <script src="js/jquery-3.4.1.js"></script>
    <script src="libs/bootstrap/bootstrap.bundle.min.js"></script>
    <script src="libs/lazyload/jquery.lazy.min.js"></script>
    <!-- swiper js-->
    <script src="libs/swiper/swiper-bundle.min.js"></script>
    <script src="libs/mansonry/masonry.pkgd.min.js"></script>
    <!-- mmenu -->
    <script src="libs/mmenu/jquery.mmenu.all.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script type="text/javascript" src="libs/aos/aos.js"></script>
    <script src="js/main.js"></script>
    <script></script>
    <script>
        var app = new Vue({
            el: "#app",
            components: {
                myheader: httpVueLoader("vue_components/header.vue"),
                myfooter: httpVueLoader("vue_components/footer.vue"),
                partner: httpVueLoader("vue_components/partner.vue"),
                locationair: httpVueLoader("vue_components/location-air.vue"),
            },
            data: {

            },

            mounted() {

            },
        });
    </script>
</body>

</html>