<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>home</title>
    <!-- mmenu css -->
    <link rel="stylesheet" href="libs/mmenu/jquery.mmenu.all.css">
    <!-- animate css-->
    <link rel="stylesheet" href="libs/animate/animate.min.css">
    <link rel="stylesheet" href="libs/aos/aos.css">
    <!-- swiper css-->
    <link rel="stylesheet" href="libs/swiper/swiper-bundle.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Barlow:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/style.css">
</head>

<body>
    <div id="app">
        <myheader></myheader>
        <main class="page-home">
            <div class="banner-main">
                <div class="img">
                    <img src="images/img-banner.jpg" alt="">
                </div>
            </div>
            <div class="home-timelessstory">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="timelessstory--list">
                                <div class="_left">
                                    <div class="box-title">
                                        <h2 class="title">
                                            Bảo Tín Mạnh Hải
                                        </h2>
                                        <div class="sub_title">
                                            Câu chuyện vượt thời gian
                                        </div>
                                    </div>
                                    <div class="timelessstory--body">
                                        <div class="desc">
                                            <p>
                                                Trải qua bề dày 32 năm xây dựng và phát triển, cùng với triết lý kinh doanh "Giữ tín nhiệm hơn giữ vàng", thương hiệu Bảo Tín Mạnh Hải đã và đang có 11 cửa hàng tại khu vực phía Bắc, và là một trong số ít những thương hiệu ngành vàng nổi tiếng của Việt
                                                Nam nhận được sự tin tưởng, yêu quý của khách hàng.
                                            </p>
                                            <p>
                                                Không chỉ nhận được sự tin tưởng tuyệt đối trong kinh doanh vàng tích trữ, Bảo Tín Mạnh Hải còn là một thương hiệu vàng trang sức nổi bật về số lượng mẫu mã phong phú, phù hợp với nhu cầu đa dạng của các khách hàng.
                                            </p>
                                        </div>
                                    </div>
                                    <div class="box--btn">
                                        <a href="" class="btn-links">
                                                Tìm hiểu thêm
                                            </a>
                                    </div>
                                </div>
                                <div class="_right">
                                    <div class="img">
                                        <img src="images/img-1.jpg" alt="">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="home-product">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="box-title">
                                <h2 class="title">
                                    Các Sản phẩm của Bảo Tín Mạnh Hải
                                </h2>
                                <div class="desc">
                                    Trang sức vàng bạc đá quý Bảo Tín Mạnh Hải – tinh hoa chế tác, tôn vinh vẻ đẹp sang trọng và giá trị vững bền.
                                </div>
                            </div>
                            <div class="producthome--list">
                                <div class="swiper producthome-slide">
                                    <div class="swiper-wrapper">
                                        <div class="swiper-slide">
                                            <div class="productcate--items">
                                                <a href="" class="img">
                                                    <span>
                                                            <img src="images/img-prod1.png" alt="">
                                                        </span>
                                                </a>
                                                <h3 class="cate--title">
                                                    <a href="">
                                                            Nhẫn
                                                        </a>
                                                </h3>
                                            </div>
                                        </div>
                                        <div class="swiper-slide" v-for="n in 6">
                                            <div class="productcate--items">
                                                <a href="" class="img">
                                                    <span>
                                                            <img src="images/img-prod2.png" alt="">
                                                        </span>
                                                </a>
                                                <h3 class="cate--title">
                                                    <a href="">
                                                            Nhẫn
                                                        </a>
                                                </h3>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="producbox--btn">
                                    <div class="swiper-button-next swiper-button-next-producthome">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="10" height="16" viewBox="0 0 10 16" fill="none">
                                                <path
                                                    fill-rule="evenodd"
                                                    clip-rule="evenodd"
                                                    d="M1.92383 15.209L0.675545 13.9607L6.91696 7.71929L0.675546 1.47787L1.92383 0.229586L8.78933 7.09509C8.78937 7.09513 8.78939 7.09514 8.16524 7.71929L8.78933 7.09509L9.41353 7.71929L1.92383 15.209Z"
                                                    fill="#4F4F4F"
                                                />
                                            </svg>
                                    </div>
                                    <div class="swiper-button-prev swiper-button-prev-producthome">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="10" height="16" viewBox="0 0 10 16" fill="none">
                                                <path
                                                    fill-rule="evenodd"
                                                    clip-rule="evenodd"
                                                    d="M8.07617 15.209L9.32445 13.9607L3.08304 7.71929L9.32445 1.47787L8.07617 0.229586L1.21067 7.09509C1.21063 7.09513 1.21061 7.09514 1.83476 7.71929L1.21067 7.09509L0.586473 7.71929L8.07617 15.209Z"
                                                    fill="#4F4F4F"
                                                />
                                            </svg>
                                    </div>
                                </div>
                                <div class="producbox--panigation">
                                    <div class="swiper-pagination swiper-pagination-producthome"></div>
                                </div>
                            </div>
                            <div class="box--btn">
                                <a href="" class="btn-links">
                                        Xem toàn bộ sản phẩm
                                    </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="home-store">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="store--list">
                                <div class="_left">
                                    <div class="box-title">
                                        <h2 class="title">
                                            Kim gia bảo
                                        </h2>
                                        <div class="sub_title">
                                            Vàng tích trữ
                                        </div>
                                        <div class="desc">
                                            Vàng tích trữ Kim Gia Bảo không chỉ là tài sản an toàn, mà còn là nền tảng vững chắc cho kế hoạch tài chính.
                                        </div>
                                    </div>
                                    <div class="box--btn">
                                        <a href="" class="btn-links">
                                                Xem toàn bộ sản phẩm
                                            </a>
                                    </div>
                                </div>
                                <div class="_right">
                                    <div class="store--box">
                                        <div class="swiper store--slide">
                                            <div class="swiper-wrapper">
                                                <div class="swiper-slide" v-for="n in 4">
                                                    <div class="product--items">
                                                        <a href="" class="img">
                                                            <img src="images/img-prod3.png" alt="">
                                                        </a>
                                                        <div class="product--body">
                                                            <h3 class="title">
                                                                <a href="">
                                                                        Đồng Kim Gia Bảo - Tùng
                                                                    </a>
                                                            </h3>
                                                            <div class="gold--infor">
                                                                1 chỉ | Vàng 24K (999.9)
                                                            </div>
                                                            <div class="price">
                                                                13.000.000
                                                            </div>
                                                            <button class="add--cart" type="submit">
                                                                    <svg
                                                                        xmlns="http://www.w3.org/2000/svg"
                                                                        width="43"
                                                                        height="42"
                                                                        viewBox="0 0 43 42"
                                                                        fill="none"
                                                                    >
                                                                        <path
                                                                            d="M32.2422 40.833H0.584914V14.2905H32.2422"
                                                                            stroke="#AE8751"
                                                                            stroke-width="1.17444"
                                                                            stroke-miterlimit="10"
                                                                        />
                                                                        <path
                                                                            d="M8.87572 16.6765V7.37212C8.87572 3.69604 12.243 0.68457 16.4301 0.68457C20.6171 0.68457 23.9844 3.67527 23.9844 7.37212V16.6765"
                                                                            stroke="#AE8751"
                                                                            stroke-width="1.17444"
                                                                            stroke-miterlimit="10"
                                                                        />
                                                                        <path d="M31.9793 37.1113C26.3145 37.1113 21.7222 32.8061 21.7222 27.4953C21.7222 22.1846 26.3145 17.8794 31.9793 17.8794C37.6441 17.8794 42.2363 22.1846 42.2363 27.4953C42.2363 32.8061 37.6441 37.1113 31.9793 37.1113Z" fill="#AE8751"/>
                                                                        <path
                                                                            d="M31.9961 20.7104V34.2309"
                                                                            stroke="white"
                                                                            stroke-width="1.17444"
                                                                            stroke-miterlimit="10"
                                                                        />
                                                                        <path
                                                                            d="M24.7793 27.478H39.2012"
                                                                            stroke="white"
                                                                            stroke-width="1.17444"
                                                                            stroke-miterlimit="10"
                                                                        />
                                                                    </svg>
                                                                </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="producbox--panigation">
                                            <div class="swiper-pagination swiper-pagination-store"></div>
                                        </div>
                                        <div class="producbox--btn">
                                            <div class="swiper-button-next swiper-button-next-store">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="10" height="16" viewBox="0 0 10 16" fill="none">
                                                        <path
                                                            fill-rule="evenodd"
                                                            clip-rule="evenodd"
                                                            d="M1.92383 15.209L0.675545 13.9607L6.91696 7.71929L0.675546 1.47787L1.92383 0.229586L8.78933 7.09509C8.78937 7.09513 8.78939 7.09514 8.16524 7.71929L8.78933 7.09509L9.41353 7.71929L1.92383 15.209Z"
                                                            fill="#4F4F4F"
                                                        />
                                                    </svg>
                                            </div>
                                            <div class="swiper-button-prev swiper-button-prev-store">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="10" height="16" viewBox="0 0 10 16" fill="none">
                                                        <path
                                                            fill-rule="evenodd"
                                                            clip-rule="evenodd"
                                                            d="M8.07617 15.209L9.32445 13.9607L3.08304 7.71929L9.32445 1.47787L8.07617 0.229586L1.21067 7.09509C1.21063 7.09513 1.21061 7.09514 1.83476 7.71929L1.21067 7.09509L0.586473 7.71929L8.07617 15.209Z"
                                                            fill="#4F4F4F"
                                                        />
                                                    </svg>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="home-dowryjewelry">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="dowryjewelry--list">
                                <div class="_left">
                                    <div class="dowryjewelry--other">
                                        <div class="swiper dowryjewelry--slide">
                                            <div class="swiper-wrapper">
                                                <div class="swiper-slide" v-for="n in 3">
                                                    <div class="dowryjewelry--box">
                                                        <div class="dowryjewelry-col1">
                                                            <div class="product--items">
                                                                <a href="" class="img">
                                                                    <img src="images/img-prod3.png" alt="">
                                                                </a>
                                                                <div class="product--body">
                                                                    <h3 class="title">
                                                                        <a href="">
                                                                                Đồng Kim Gia Bảo - Tùng
                                                                            </a>
                                                                    </h3>
                                                                    <div class="gold--infor">
                                                                        1 chỉ | Vàng 24K (999.9)
                                                                    </div>
                                                                    <div class="price">
                                                                        13.000.000
                                                                    </div>
                                                                    <button class="add--cart" type="submit">
                                                                            <svg
                                                                                xmlns="http://www.w3.org/2000/svg"
                                                                                width="43"
                                                                                height="42"
                                                                                viewBox="0 0 43 42"
                                                                                fill="none"
                                                                            >
                                                                                <path
                                                                                    d="M32.2422 40.833H0.584914V14.2905H32.2422"
                                                                                    stroke="#AE8751"
                                                                                    stroke-width="1.17444"
                                                                                    stroke-miterlimit="10"
                                                                                />
                                                                                <path
                                                                                    d="M8.87572 16.6765V7.37212C8.87572 3.69604 12.243 0.68457 16.4301 0.68457C20.6171 0.68457 23.9844 3.67527 23.9844 7.37212V16.6765"
                                                                                    stroke="#AE8751"
                                                                                    stroke-width="1.17444"
                                                                                    stroke-miterlimit="10"
                                                                                />
                                                                                <path d="M31.9793 37.1113C26.3145 37.1113 21.7222 32.8061 21.7222 27.4953C21.7222 22.1846 26.3145 17.8794 31.9793 17.8794C37.6441 17.8794 42.2363 22.1846 42.2363 27.4953C42.2363 32.8061 37.6441 37.1113 31.9793 37.1113Z" fill="#AE8751"/>
                                                                                <path
                                                                                    d="M31.9961 20.7104V34.2309"
                                                                                    stroke="white"
                                                                                    stroke-width="1.17444"
                                                                                    stroke-miterlimit="10"
                                                                                />
                                                                                <path
                                                                                    d="M24.7793 27.478H39.2012"
                                                                                    stroke="white"
                                                                                    stroke-width="1.17444"
                                                                                    stroke-miterlimit="10"
                                                                                />
                                                                            </svg>
                                                                        </button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="dowryjewelry-img">
                                                            <a href="">
                                                                <img src="images/img-spm.jpg" alt="">
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="producbox--btn">
                                            <div class="swiper-button-next swiper-button-next-dowryjewelry">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="10" height="16" viewBox="0 0 10 16" fill="none">
                                                        <path
                                                            fill-rule="evenodd"
                                                            clip-rule="evenodd"
                                                            d="M1.92383 15.209L0.675545 13.9607L6.91696 7.71929L0.675546 1.47787L1.92383 0.229586L8.78933 7.09509C8.78937 7.09513 8.78939 7.09514 8.16524 7.71929L8.78933 7.09509L9.41353 7.71929L1.92383 15.209Z"
                                                            fill="#4F4F4F"
                                                        />
                                                    </svg>
                                            </div>
                                            <div class="swiper-button-prev swiper-button-prev-dowryjewelry">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="10" height="16" viewBox="0 0 10 16" fill="none">
                                                        <path
                                                            fill-rule="evenodd"
                                                            clip-rule="evenodd"
                                                            d="M8.07617 15.209L9.32445 13.9607L3.08304 7.71929L9.32445 1.47787L8.07617 0.229586L1.21067 7.09509C1.21063 7.09513 1.21061 7.09514 1.83476 7.71929L1.21067 7.09509L0.586473 7.71929L8.07617 15.209Z"
                                                            fill="#4F4F4F"
                                                        />
                                                    </svg>
                                            </div>
                                        </div>
                                        <div class="producbox--panigation">
                                            <div class="swiper-pagination swiper-pagination-dowryjewelry"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="_right">
                                    <div class="box-title">
                                        <h2 class="title">
                                            Kim gia bảo
                                        </h2>
                                        <div class="sub_title">
                                            Trang Sức Hồi Môn & Cưới
                                        </div>
                                        <div class="desc">
                                            Trang sức vàng Kim Gia Bảo – chế tác tinh xảo từ vàng chuẩn, kiểu dáng sang trọng, hiện đại. Không chỉ tôn vinh vẻ đẹp người đeo, mỗi sản phẩm còn giữ vững giá trị, là món quà ý nghĩa trao truyền qua nhiều thế hệ.
                                        </div>
                                    </div>
                                    <div class="box--btn">
                                        <a href="" class="btn-links">
                                                Xem toàn bộ sản phẩm
                                            </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="home-collection" style="background-image: url(images/bg-banner1.jpg);">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="box-title">
                                <h2 class="title">
                                    Lá Ngọc Cành Vàng
                                </h2>
                                <div class="sub_title">
                                    Bộ Sưu Tập
                                </div>
                                <div class="desc">
                                    Trong văn hóa Á Đông, hình ảnh Lá Ngọc Cành Vàng không chỉ gợi lên sự cao quý và thanh tao mà còn mang ý nghĩa chúc phúc cho một cuộc sống sung túc, trọn vẹn.
                                </div>
                            </div>
                            <div class="box--btn">
                                <a href="" class="btn-links">
                                        Xem bộ sưu tập
                                    </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="home-brand">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="brand-content" style="background-image: url(images/bg-banrd.jpg);">
                                <div class="brand-other">
                                    <div class="brand--name">
                                        Thương hiệu MH
                                    </div>
                                    <div class="box-title">
                                        <h2 class="title">
                                            Vẻ đẹp thanh lịch vượt thời gian dành cho tình yêu của bạn
                                        </h2>
                                        <div class="desc">
                                            Với hàm lượng chuẩn, trọng lượng đa dạng và mẫu mã tinh gọn, mỗi sản phẩm là sự lựa chọn đáng tin cậy cho những ai muốn bảo toàn giá trị và truyền lại gia sản cho thế hệ mai sau.
                                        </div>
                                    </div>
                                    <div class="box--btn">
                                        <a href="" class="btn-links">
                                                Tìm hiểu về MH
                                            </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="home-chart">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="chart--fieldset">
                                <div class="fieldset--title">
                                    Biểu đồ vàng
                                </div>
                                <div class="chart--fieldset---list">
                                    <div class="_left">
                                        <div class="--title">
                                            Nhẫn ép vỉ vàng Rồng Thăng Long
                                        </div>
                                        <div class="price--discount">
                                            <div class="--number">
                                                92.700.000
                                            </div>
                                            <div class="--number--discount">
                                                +20.000 (+0.03%)
                                                <svg xmlns="http://www.w3.org/2000/svg" width="15" height="16" viewBox="0 0 15 16" fill="none">
                                                        <path
                                                            d="M1.65625 8.01159L7.98633 2L14.3164 8.01159"
                                                            stroke="#EDD0A7"
                                                            stroke-width="2"
                                                            stroke-miterlimit="10"
                                                        />
                                                        <path
                                                            d="M7.98828 2L7.98828 15.2122"
                                                            stroke="#EDD0A7"
                                                            stroke-width="2"
                                                            stroke-miterlimit="10"
                                                        />
                                                    </svg>
                                            </div>
                                        </div>
                                        <canvas id="goldChart"></canvas>
                                    </div>
                                    <div class="_right">
                                        <div class="--title">
                                            Loại vàng
                                        </div>
                                        <div class="pricegold--input">
                                            <select name="" id="">
                                                    <option value="">
                                                        Thẻ Vàng Kim Gia Bảo
                                                    </option>
                                                </select>
                                        </div>
                                        <div class="chart--fieldset---box">
                                            <div class="--title">
                                                Xem theo:
                                            </div>
                                            <div class="chart--fieldset---check">
                                                <div class="--items">
                                                    <input id="checkbox1" type="radio" hidden name="1" checked>
                                                    <label for="checkbox1">
                                                        1 ngày
                                                    </label>
                                                </div>
                                                <div class="--items">
                                                    <input id="checkbox2" type="radio" hidden name="1">
                                                    <label for="checkbox2">
                                                        1 tuần
                                                    </label>
                                                </div>
                                                <div class="--items">
                                                    <input id="checkbox3" type="radio" hidden name="1">
                                                    <label for="checkbox3">
                                                        1 tháng
                                                    </label>
                                                </div>
                                                <div class="--items">
                                                    <input id="checkbox4" type="radio" hidden name="1">
                                                    <label for="checkbox4">
                                                        3 tháng
                                                    </label>
                                                </div>
                                                <div class="--items">
                                                    <input id="checkbox5" type="radio" hidden name="1">
                                                    <label for="checkbox5">
                                                        6 tháng
                                                    </label>
                                                </div>
                                                <div class="--items">
                                                    <input id="checkbox6" type="radio" hidden name="1">
                                                    <label for="checkbox6">
                                                        12 tháng
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="chart--fieldset---unit">
                                            <div class="--measure">
                                                Đơn vị tính: <br /> Đồng / Chỉ
                                            </div>
                                            <div class="--explain">
                                                <div class="--sell-out">
                                                    Bán ra
                                                </div>
                                                <div class="--buy-in">
                                                    Mua vào
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="chart--table---title">
                                Thị trường vàng
                            </div>
                            <div class="chart--table">
                                <div class="_left">
                                    <table class="chart--table-box">
                                        <thead>
                                            <tr>
                                                <th>Loại vàng</th>
                                                <th width="154">Mua</th>
                                                <th width="154">Bán</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr v-for="n in 3">
                                                <td>Thẻ Vàng Kim Gia Bảo</td>
                                                <td>
                                                    <div class="number">
                                                        9.220.000
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="15" height="20" viewBox="0 0 15 20" fill="none">
                                                                <path
                                                                    d="M0.597656 9.10916L7.33508 2.00024L14.0725 9.10916"
                                                                    stroke="#2ED151"
                                                                    stroke-width="1.5"
                                                                    stroke-miterlimit="10"
                                                                />
                                                                <path
                                                                    d="M7.33594 2.00024V19.0734"
                                                                    stroke="#2ED151"
                                                                    stroke-width="1.5"
                                                                    stroke-miterlimit="10"
                                                                />
                                                            </svg>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="number">
                                                        9.220.000
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="15" height="20" viewBox="0 0 15 20" fill="none">
                                                                <path
                                                                    d="M14.0703 10.8894L7.33289 17.9983L0.595465 10.8894"
                                                                    stroke="#922E38"
                                                                    stroke-width="1.5"
                                                                    stroke-miterlimit="10"
                                                                />
                                                                <path
                                                                    d="M7.33203 17.9983L7.33203 0.925088"
                                                                    stroke="#922E38"
                                                                    stroke-width="1.5"
                                                                    stroke-miterlimit="10"
                                                                />
                                                            </svg>
                                                    </div>
                                                </td>
                                            </tr>
                                        </tbody>
                                        <tfoot>
                                            <tr>
                                                <td colspan="3">
                                                    <div class="table--footer">
                                                        <p>
                                                            Cập nhật lúc 10:03 04/03/2025
                                                        </p>
                                                        <p>
                                                            Đơn vị tính: Đồng / Chỉ
                                                        </p>
                                                    </div>
                                                </td>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                                <div class="_right">
                                    <div class="account-content">
                                        <ul class="nav nav-pills" id="pills-tab" role="tablist">
                                            <li class="nav-item" role="presentation">
                                                <button class="nav-link active" id="pills-home-tab" data-bs-toggle="pill" data-bs-target="#pills-home" type="button" role="tab" aria-controls="pills-home" aria-selected="true">
                                                        Giao dịch Mua
                                                    </button>
                                            </li>
                                            <li class="nav-item" role="presentation">
                                                <button class="nav-link" id="pills-profile-tab" data-bs-toggle="pill" data-bs-target="#pills-profile" type="button" role="tab" aria-controls="pills-profile" aria-selected="false">
                                                        Giao dịch Bán
                                                    </button>
                                            </li>
                                        </ul>
                                        <div class="tab-content" id="pills-tabContent">
                                            <div class="tab-pane fade show active" id="pills-home" role="tabpanel" aria-labelledby="pills-home-tab">
                                                <form action="">
                                                    <div class="form--content">
                                                        <div class="form--col12">
                                                            <div class="form--row">
                                                                <div class="pricegold--quantity">
                                                                    <div class="quantity--title">
                                                                        Sản phẩm bán
                                                                    </div>
                                                                    <div class="pricegold--input">
                                                                        <select name="" id="">
                                                                                <option value="">
                                                                                    Nhẫn tròn ép vỉ Kim Gia Bảo
                                                                                </option>
                                                                            </select>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="form--col">
                                                            <div class="form--row">
                                                                <div class="pricegold--quantity">
                                                                    <div class="quantity--title">
                                                                        Số lượng
                                                                    </div>
                                                                    <div class="pricegold--input">
                                                                        <select name="" id="">
                                                                                <option value="">
                                                                                    1 chỉ
                                                                                </option>
                                                                            </select>
                                                                    </div>
                                                                    <div class="--note">
                                                                        Số lượng tối đa 10 (chỉ)
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="form--col">
                                                            <div class="form--row">
                                                                <div class="pricegold--quantity">
                                                                    <div class="quantity--title">
                                                                        Thành tiền
                                                                    </div>
                                                                    <div class="quantity--total">
                                                                        <div class="number">
                                                                            9.120.000
                                                                        </div>
                                                                        <span>
                                                                                vnđ
                                                                            </span>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="chart--table--btn">
                                                        <div class="txt">
                                                            Thời gian giữ giá mua: 16s
                                                        </div>
                                                        <div class="form--btn">
                                                            <button type="submit" class="storesystem--btn">
                                                                    Mua
                                                                </button>
                                                        </div>
                                                    </div>
                                                </form>
                                            </div>
                                            <div class="tab-pane fade" id="pills-profile" role="tabpanel" aria-labelledby="pills-profile-tab">
                                                <form action="">
                                                    <div class="form--content">
                                                        <div class="form--col12">
                                                            <div class="form--row">
                                                                <div class="pricegold--quantity">
                                                                    <div class="quantity--title">
                                                                        Sản phẩm bán
                                                                    </div>
                                                                    <div class="pricegold--input">
                                                                        <select name="" id="">
                                                                                <option value="">
                                                                                    Nhẫn tròn ép vỉ Kim Gia Bảo
                                                                                </option>
                                                                            </select>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="form--col">
                                                            <div class="form--row">
                                                                <div class="pricegold--quantity">
                                                                    <div class="quantity--title">
                                                                        Số lượng
                                                                    </div>
                                                                    <div class="pricegold--input">
                                                                        <select name="" id="">
                                                                                <option value="">
                                                                                    1 chỉ
                                                                                </option>
                                                                            </select>
                                                                    </div>
                                                                    <div class="--note">
                                                                        Số lượng tối đa 10 (chỉ)
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="form--col">
                                                            <div class="form--row">
                                                                <div class="pricegold--quantity">
                                                                    <div class="quantity--title">
                                                                        Thành tiền
                                                                    </div>
                                                                    <div class="quantity--total">
                                                                        <div class="number">
                                                                            9.120.000
                                                                        </div>
                                                                        <span>
                                                                                vnđ
                                                                            </span>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="chart--table--btn">
                                                        <div class="txt">
                                                            Thời gian giữ giá mua: 16s
                                                        </div>
                                                        <div class="form--btn">
                                                            <button type="submit" class="storesystem--btn">
                                                                    Mua
                                                                </button>
                                                        </div>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="home-news">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="box-title">
                                <h2 class="title">
                                    Tin tức
                                </h2>
                            </div>
                            <div class="newshome--list">
                                <div class="swiper newshome--slide">
                                    <div class="swiper-wrapper">
                                        <div class="swiper-slide" v-for="n in 3">
                                            <div class="news--items">
                                                <a href="" class="img">
                                                    <img src="images/img-news1.jpg" alt="">
                                                </a>
                                                <div class="news--body">
                                                    <a href="" class="sub_cate">
                                                            Tin tức vàng
                                                        </a>
                                                    <h3 class="title">
                                                        <a href="">
                                                                Đồng vàng Phát Lộc Tài: Tài lộc vượng phát - Đại cát đại lợi
                                                            </a>
                                                    </h3>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="producbox--btn">
                                    <div class="swiper-button-next swiper-button-next-newshome">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="10" height="16" viewBox="0 0 10 16" fill="none">
                                                <path
                                                    fill-rule="evenodd"
                                                    clip-rule="evenodd"
                                                    d="M1.92383 15.209L0.675545 13.9607L6.91696 7.71929L0.675546 1.47787L1.92383 0.229586L8.78933 7.09509C8.78937 7.09513 8.78939 7.09514 8.16524 7.71929L8.78933 7.09509L9.41353 7.71929L1.92383 15.209Z"
                                                    fill="#4F4F4F"
                                                />
                                            </svg>
                                    </div>
                                    <div class="swiper-button-prev swiper-button-prev-newshome">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="10" height="16" viewBox="0 0 10 16" fill="none">
                                                <path
                                                    fill-rule="evenodd"
                                                    clip-rule="evenodd"
                                                    d="M8.07617 15.209L9.32445 13.9607L3.08304 7.71929L9.32445 1.47787L8.07617 0.229586L1.21067 7.09509C1.21063 7.09513 1.21061 7.09514 1.83476 7.71929L1.21067 7.09509L0.586473 7.71929L8.07617 15.209Z"
                                                    fill="#4F4F4F"
                                                />
                                            </svg>
                                    </div>
                                </div>
                                <div class="producbox--panigation">
                                    <div class="swiper-pagination swiper-pagination-newshome"></div>
                                </div>
                            </div>
                            <div class="box--btn">
                                <a href="" class="btn-links">
                                        Xem thêm tin tức
                                    </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="home-storesystem">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="box-title">
                                <h2 class="title">
                                    Hệ thống cửa hàng
                                </h2>
                            </div>
                            <div class="storesystem--list">
                                <div class="swiper storesystem--slide">
                                    <div class="swiper-wrapper">
                                        <div class="swiper-slide" v-for="n in 5">
                                            <div class="storesystem--items">
                                                <div class="img">
                                                    <img src="images/img-systemstore.jpg" alt="">
                                                </div>
                                                <div class="storesystem--body">
                                                    <div class="storesystem--base">
                                                        <a href="" class="name">
Cơ sở quang trung
                                                        </a>
                                                        <div class="--adress">
                                                            15 quang trung- hà đông- hà nội
                                                        </div>
                                                    </div>
                                                    <a href="" class="storesystem--btn">
                                                    Đặt lịch hẹn
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- <div class="producbox--btn">
                                    <div class="swiper-button-next swiper-button-next-storesystem">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="10" height="16" viewBox="0 0 10 16" fill="none">
                                                <path
                                                    fill-rule="evenodd"
                                                    clip-rule="evenodd"
                                                    d="M1.92383 15.209L0.675545 13.9607L6.91696 7.71929L0.675546 1.47787L1.92383 0.229586L8.78933 7.09509C8.78937 7.09513 8.78939 7.09514 8.16524 7.71929L8.78933 7.09509L9.41353 7.71929L1.92383 15.209Z"
                                                    fill="#4F4F4F"
                                                />
                                            </svg>
                                    </div>
                                    <div class="swiper-button-prev swiper-button-prev-storesystem">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="10" height="16" viewBox="0 0 10 16" fill="none">
                                                <path
                                                    fill-rule="evenodd"
                                                    clip-rule="evenodd"
                                                    d="M8.07617 15.209L9.32445 13.9607L3.08304 7.71929L9.32445 1.47787L8.07617 0.229586L1.21067 7.09509C1.21063 7.09513 1.21061 7.09514 1.83476 7.71929L1.21067 7.09509L0.586473 7.71929L8.07617 15.209Z"
                                                    fill="#4F4F4F"
                                                />
                                            </svg>
                                    </div>
                                </div>
                                <div class="producbox--panigation">
                                    <div class="swiper-pagination swiper-pagination-storesystem"></div>
                                </div> -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="home-policy">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-3 col-sm-6">
                            <div class="policy--items">
                                <div class="img">
                                    <img src="images/img-policy1.png" alt="">
                                </div>
                                <div class="policy--body">
                                    <h3 class="title">
                                        <a href="">
Chính Sách Bảo Hành và Thu đổi
                                        </a>
                                    </h3>
                                    <div class="desc">
                                        Chúng tôi cung cấp dịch vụ giao hàng và trả hàng<br /> miễn phí cho tất cả các đơn hàng từ BTMH.
                                    </div>
                                    <div class="box--btn">
                                        <a href="" class="btn-links">
                                        Tìm hiểu thêm
                                    </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-sm-6">
                            <div class="policy--items">
                                <div class="img">
                                    <img src="images/img-policy2.png" alt="">
                                </div>
                                <div class="policy--body">
                                    <h3 class="title">
                                        <a href="">
                                        Hướng Dẫn Đo Size Trang Sức
                                        </a>
                                    </h3>
                                    <div class="desc">
                                        Các chuyên gia của chúng tôi <br /> luôn sẵn sàng hỗ trợ bạn.
                                    </div>
                                    <div class="box--btn">
                                        <a href="" class="btn-links">
                                        Tìm hiểu thêm
                                    </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-sm-6">
                            <div class="policy--items">
                                <div class="img">
                                    <img src="images/img-policy3.png" alt="">
                                </div>
                                <div class="policy--body">
                                    <h3 class="title">
                                        <a href="">
                                        đặt lịch hẹn
                                        </a>
                                    </h3>
                                    <div class="desc">
                                        Chúng tôi sẵn sàng hỗ trợ bạn với các
                                        <r /> cuộc hẹn trực tiếp hoặc trực tuyến.
                                    </div>
                                    <div class="box--btn">
                                        <a href="" class="btn-links">
                                       Đặt lịch ngay
                                    </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-sm-6">
                            <div class="policy--items">
                                <div class="img">
                                    <img src="images/img-policy4.png" alt="">
                                </div>
                                <div class="policy--body">
                                    <h3 class="title">
                                        <a href="">
                                        Chính Sách Bảo Mật Thông Tin 
                                        </a>
                                    </h3>
                                    <div class="desc">
                                        Uy tín của Công ty trước khách hàng <br /> luôn được đặt lên hàng đầu
                                    </div>
                                    <div class="box--btn">
                                        <a href="" class="btn-links">
                                        Tìm hiểu thêm
                                    </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
        <myfooter></myfooter>
    </div>

    <script src="js/vue.js"></script>
    <script src="js/httpvueloader.js"></script>
    <script src="js/jquery-3.4.1.js"></script>
    <script src="libs/bootstrap/bootstrap.bundle.min.js"></script>
    <script src="libs/lazyload/jquery.lazy.min.js"></script>
    <!-- swiper js-->
    <script src="libs/swiper/swiper-bundle.min.js"></script>
    <script src="libs/mansonry/masonry.pkgd.min.js"></script>
    <!-- mmenu -->
    <script src="libs/mmenu/jquery.mmenu.all.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script type="text/javascript" src="libs/aos/aos.js"></script>
    <script src="js/main.js"></script>
    <script></script>
    <script>
        var app = new Vue({
            el: "#app",
            components: {
                myheader: httpVueLoader("vue_components/header.vue"),
                myfooter: httpVueLoader("vue_components/footer.vue"),
                partner: httpVueLoader("vue_components/partner.vue"),
                locationair: httpVueLoader("vue_components/location-air.vue"),
            },
            data: {

            },

            mounted() {

            },
        });
    </script>
</body>

</html>