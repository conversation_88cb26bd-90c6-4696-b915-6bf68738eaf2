{"_from": "lazyload", "_id": "lazyload@2.0.0-rc.2", "_inBundle": false, "_integrity": "sha512-v3OKwYrKHX09eAyeAmkpvVCF5qWZo8rxERT9AVOUFaRwlTIuCyMYVBfHYSra1uyBdKDEHnUkvpZTLgNWI9Y7lA==", "_location": "/lazyload", "_phantomChildren": {}, "_requested": {"type": "tag", "registry": true, "raw": "lazyload", "name": "lazyload", "escapedName": "lazyload", "rawSpec": "", "saveSpec": null, "fetchSpec": "latest"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmjs.org/lazyload/-/lazyload-2.0.0-rc.2.tgz", "_shasum": "c774eb7966f334c9bed2f27580fc333a770eb256", "_spec": "lazyload", "_where": "D:\\VCCorpProject\\test", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/tuupola/lazyload/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Vanilla JavaScript plugin for lazyloading images", "devDependencies": {"jshint": "^2.9.5", "uglify-es": "^3.0.28"}, "files": ["lazyload.js", "lazyload.min.js"], "homepage": "https://github.com/tuupola/lazyload#readme", "keywords": ["lazyload"], "license": "MIT", "main": "lazyload.js", "name": "lazyload", "repository": {"type": "git", "url": "git+https://github.com/tuupola/lazyload.git"}, "version": "2.0.0-rc.2"}