.<PERSON>Zoom,.<PERSON>Zoom:hover{outline:0!important;margin: auto;display: block !important;}
.MagicZoom img{border:0!important;outline:0!important;margin:0!important;padding:0!important}
.MagicZoomHint{color:#444;font-size:8pt;font-family:sans-serif;line-height:24px;min-height:24px;text-align:left;text-decoration:none;margin:0;padding:2px 2px 2px 20px !important}
.MagicZoomExternalTitle{display:none}
.MagicZoomBigImageCont{background:#fff;border:1px solid #999}
.MagicZoomBigImageCont *{display:inline}
.MagicZoomBigImageCont img{max-width:none!important;max-height:none!important;height:auto!important;width:auto!important}
.MagicZoomHeader{background:#666;color:#fff;font-size:10pt!important;line-height:normal!important;text-align:center!important}
.MagicZoomPup{background:#fff;border:1px solid #aaa;cursor:move}
.MagicZoomLoading{border:1px solid #ccc;color:#444;font-family:sans-serif;font-size:8pt;line-height:1.5em;text-align:left;text-decoration:none;margin:0;padding:4px 4px 4px 24px !important}
/* Style of shadow effect behind zoomed image */
.MagicBoxShadow {
-moz-box-shadow: 3px 3px 4px #888888;
-webkit-box-shadow: 3px 3px 4px #888888;
box-shadow: 3px 3px 4px #888888;
border-collapse: separate;
/* For IE 5.5 - 7 */
filter: progid:DXImageTransform.Microsoft.Shadow(Strength=4, Direction=135, Color='#888888') !important;
/* For IE 8 */
-ms-filter: "progid:DXImageTransform.Microsoft.Shadow(Strength=4, Direction=135, Color='#888888')" !important;
}
/* Style of glow effect behind zoomed image */
.MagicBoxGlow {
-moz-box-shadow: 0px 0px 4px 4px #888888;
-webkit-box-shadow: 0px 0px 4px 4px #888888;
box-shadow: 0px 0px 4px 4px #888888;
border-collapse: separate;
/* For IE 5.5 - 7 */
filter: progid:DXImageTransform.Microsoft.Glow(Strength=4, Color='#888888') !important;
/* For IE 8 */
-ms-filter: "progid:DXImageTransform.Microsoft.Glow(Strength=4, Color='#888888')" !important;
}