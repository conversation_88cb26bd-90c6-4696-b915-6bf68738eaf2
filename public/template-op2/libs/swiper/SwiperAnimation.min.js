/*!
 * swiper-animation v1.4.0
 * Homepage: https://github.com/cycdpo/swiper-animation#readme
 * Released under the MIT License.
 */
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.SwiperAnimation=t():e.SwiperAnimation=t()}(window,function(){return(n={},a.m=i={tjUo:function(e,t,i){"use strict";i.r(t);function n(e){return Array.isArray(e)?e:function(e){return"[object NodeList]"===Object.prototype.toString.call(e)}(e)?Array.from?Array.from(e):Array.prototype.slice.call(e):new Array(e)}i.d(t,"default",function(){return r});var a="visibility: hidden;",r=function(){function e(){this.swiper=null,this.allBoxes=[],this.activeBoxes=[],this.appendedPromise=!1,this.isPromiseReady=!1}var t=e.prototype;return t.init=function(e){var t=this;return this.swiper||(this.swiper=e),this.isPromiseReady||window.Promise?this.isPromiseReady=!0:this._initPromisePolyfill(function(){t.isPromiseReady=!0}),this},t.animate=function(){var t=this;return this.isPromiseReady?Promise.resolve().then(function(){return t._cache()}).then(function(){return t._outAnimate()}).then(function(){return t._clear()}).then(function(){t.activeBoxes=[].concat(n(t.swiper.slides[t.swiper.activeIndex].querySelectorAll("[data-swiper-animation]")),n(t.swiper.slides[t.swiper.activeIndex].querySelectorAll("[data-swiper-animation-once]")));var e=t.activeBoxes.map(function(t){return new Promise(function(e){t.style.visibility="visible",t.style.cssText+=" animation-duration:"+t.__animationData.duration+"; -webkit-animation-duration:"+t.__animationData.duration+"; animation-delay:"+t.__animationData.delay+"; -webkit-animation-delay:"+t.__animationData.delay+";",t.classList.add(t.__animationData.effect,"animated"),t.__animationData.isRecovery=!1,setTimeout(e,0)})});return Promise.all(e)}):setTimeout(function(){return t.animate()},500)},t._outAnimate=function(){var e=this.activeBoxes.map(function(t){return t.__animationData.isRecovery?Promise.resolve():t.__animationData.outEffect?new Promise(function(e){t.style.cssText=t.styleCache,t.style.visibility="visible",t.style.cssText+=" animation-duration:"+t.__animationData.outDuration+"; -webkit-animation-duration:"+t.__animationData.outDuration+";",t.classList.add(t.__animationData.outEffect,"animated"),setTimeout(e,500)}):Promise.resolve()});return Promise.all(e)},t._clear=function(){var e=this,t=this.activeBoxes.map(function(i){return i.__animationData.isRecovery?Promise.resolve():i.__animationData.runOnce?Promise.resolve():new Promise(function(e){var t;i.style.cssText=i.__animationData.styleCache,(t=i.classList).remove.apply(t,[i.__animationData.effect,i.__animationData.outEffect,"animated"].filter(function(e){return!!e})),i.__animationData.isRecovery=!0,setTimeout(e,0)})});return Promise.all(t).then(function(){return e.activeBoxes=[]})},t._cache=function(){var t=this;return this.allBoxes.length?Promise.resolve():Promise.resolve().then(function(){return t._initAllBoxes()}).then(function(){var e=t.allBoxes.map(function(t){return new Promise(function(e){t.__animationData={styleCache:t.attributes.style?a+t.style.cssText:a,effect:t.dataset.swiperAnimation||t.dataset.swiperAnimationOnce||"",duration:t.dataset.duration||".5s",delay:t.dataset.delay||".5s",outEffect:t.dataset.swiperOutAnimation||"",outDuration:t.dataset.outDuration||".5s",isRecovery:!0,runOnce:!!t.dataset.swiperAnimationOnce},t.style.cssText=t.__animationData.styleCache,setTimeout(e,0)})});return Promise.all(e)})},t._initAllBoxes=function(){var i=this;return this.allBoxes.length?Promise.resolve():new Promise(function(e){var t=null;i.swiper.wrapperEl?t=i.swiper.wrapperEl:i.swiper.wrapper&&(t=i.swiper.wrapper[0]),i.allBoxes=[].concat(n(t.querySelectorAll("[data-swiper-animation]")),n(t.querySelectorAll("[data-swiper-animation-once]"))),setTimeout(e,0)})},t._initPromisePolyfill=function(e){if(void 0===e&&(e=function(){}),!this.appendedPromise){var t=document.createElement("script");t.type="text/javascript",t.onload=function(){return e()},t.src="https://cdn.jsdelivr.net/npm/promise-polyfill@7/dist/polyfill.min.js",document.querySelector("head").appendChild(t),this.appendedPromise=!0}},e}()}},a.c=n,a.d=function(e,t,i){a.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:i})},a.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},a.t=function(t,e){if(1&e&&(t=a(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var i=Object.create(null);if(a.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var n in t)a.d(i,n,function(e){return t[e]}.bind(null,n));return i},a.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return a.d(t,"a",t),t},a.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},a.p="",a(a.s="tjUo")).default;function a(e){if(n[e])return n[e].exports;var t=n[e]={i:e,l:!1,exports:{}};return i[e].call(t.exports,t,t.exports,a),t.l=!0,t.exports}var i,n});