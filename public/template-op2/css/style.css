@charset "UTF-8";
/* FONT PATH
 * -------------------------- */
@import url("https://fonts.googleapis.com/css2?family=Sarabun:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Nunito+Sans:ital,opsz,wght@0,6..12,200..1000;1,6..12,200..1000&display=swap");
@font-face {
  font-family: "FontAwesome";
  src: url("../fonts/awesome/fonts/fontawesome-webfont.eot?v=4.7.0");
  src: url("../fonts/awesome/fonts/fontawesome-webfont.eot?#iefix&v=4.7.0") format("embedded-opentype"), url("../fonts/awesome/fonts/fontawesome-webfont.woff2?v=4.7.0") format("woff2"), url("../fonts/awesome/fonts/fontawesome-webfont.woff?v=4.7.0") format("woff"), url("../fonts/awesome/fonts/fontawesome-webfont.ttf?v=4.7.0") format("truetype"), url("../fonts/awesome/fonts/fontawesome-webfont.svg?v=4.7.0#fontawesomeregular") format("svg");
  font-weight: normal;
  font-style: normal;
}
.fa {
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* makes the font 33% larger relative to the icon container */
.fa-lg {
  font-size: 1.3333333333em;
  line-height: 0.75em;
  vertical-align: -15%;
}

.fa-2x {
  font-size: 2em;
}

.fa-3x {
  font-size: 3em;
}

.fa-4x {
  font-size: 4em;
}

.fa-5x {
  font-size: 5em;
}

.fa-fw {
  width: 1.2857142857em;
  text-align: center;
}

.fa-ul {
  padding-left: 0;
  margin-left: 2.1428571429em;
  list-style-type: none;
}
.fa-ul > li {
  position: relative;
}

.fa-li {
  position: absolute;
  left: -2.1428571429em;
  width: 2.1428571429em;
  top: 0.1428571429em;
  text-align: center;
}
.fa-li.fa-lg {
  left: -1.8571428571em;
}

.fa-border {
  padding: 0.2em 0.25em 0.15em;
  border: solid 0.08em #eee;
  border-radius: 0.1em;
}

.fa-pull-left {
  float: left;
}

.fa-pull-right {
  float: right;
}

.fa.fa-pull-left {
  margin-right: 0.3em;
}
.fa.fa-pull-right {
  margin-left: 0.3em;
}

/* Deprecated as of 4.4.0 */
.pull-right {
  float: right;
}

.pull-left {
  float: left;
}

.fa.pull-left {
  margin-right: 0.3em;
}
.fa.pull-right {
  margin-left: 0.3em;
}

.fa-spin {
  animation: fa-spin 2s infinite linear;
}

.fa-pulse {
  animation: fa-spin 1s infinite steps(8);
}
@keyframes fa-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(359deg);
  }
}
.fa-rotate-90 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=1)";
  transform: rotate(90deg);
}

.fa-rotate-180 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2)";
  transform: rotate(180deg);
}

.fa-rotate-270 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=3)";
  transform: rotate(270deg);
}

.fa-flip-horizontal {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=0, mirror=1)";
  transform: scale(-1, 1);
}

.fa-flip-vertical {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1)";
  transform: scale(1, -1);
}

:root .fa-rotate-90,
:root .fa-rotate-180,
:root .fa-rotate-270,
:root .fa-flip-horizontal,
:root .fa-flip-vertical {
  filter: none;
}

.fa-stack {
  position: relative;
  display: inline-block;
  width: 2em;
  height: 2em;
  line-height: 2em;
  vertical-align: middle;
}

.fa-stack-1x, .fa-stack-2x {
  position: absolute;
  left: 0;
  width: 100%;
  text-align: center;
}

.fa-stack-1x {
  line-height: inherit;
}

.fa-stack-2x {
  font-size: 2em;
}

.fa-inverse {
  color: #fff;
}

/* Font Awesome uses the Unicode Private Use Area (PUA) to ensure screen
   readers do not read off random characters that represent icons */
.fa-glass:before {
  content: "\f000";
}

.fa-music:before {
  content: "\f001";
}

.fa-search:before {
  content: "\f002";
}

.fa-envelope-o:before {
  content: "\f003";
}

.fa-heart:before {
  content: "\f004";
}

.fa-star:before {
  content: "\f005";
}

.fa-star-o:before {
  content: "\f006";
}

.fa-user:before {
  content: "\f007";
}

.fa-film:before {
  content: "\f008";
}

.fa-th-large:before {
  content: "\f009";
}

.fa-th:before {
  content: "\f00a";
}

.fa-th-list:before {
  content: "\f00b";
}

.fa-check:before {
  content: "\f00c";
}

.fa-remove:before,
.fa-close:before,
.fa-times:before {
  content: "\f00d";
}

.fa-search-plus:before {
  content: "\f00e";
}

.fa-search-minus:before {
  content: "\f010";
}

.fa-power-off:before {
  content: "\f011";
}

.fa-signal:before {
  content: "\f012";
}

.fa-gear:before,
.fa-cog:before {
  content: "\f013";
}

.fa-trash-o:before {
  content: "\f014";
}

.fa-home:before {
  content: "\f015";
}

.fa-file-o:before {
  content: "\f016";
}

.fa-clock-o:before {
  content: "\f017";
}

.fa-road:before {
  content: "\f018";
}

.fa-download:before {
  content: "\f019";
}

.fa-arrow-circle-o-down:before {
  content: "\f01a";
}

.fa-arrow-circle-o-up:before {
  content: "\f01b";
}

.fa-inbox:before {
  content: "\f01c";
}

.fa-play-circle-o:before {
  content: "\f01d";
}

.fa-rotate-right:before,
.fa-repeat:before {
  content: "\f01e";
}

.fa-refresh:before {
  content: "\f021";
}

.fa-list-alt:before {
  content: "\f022";
}

.fa-lock:before {
  content: "\f023";
}

.fa-flag:before {
  content: "\f024";
}

.fa-headphones:before {
  content: "\f025";
}

.fa-volume-off:before {
  content: "\f026";
}

.fa-volume-down:before {
  content: "\f027";
}

.fa-volume-up:before {
  content: "\f028";
}

.fa-qrcode:before {
  content: "\f029";
}

.fa-barcode:before {
  content: "\f02a";
}

.fa-tag:before {
  content: "\f02b";
}

.fa-tags:before {
  content: "\f02c";
}

.fa-book:before {
  content: "\f02d";
}

.fa-bookmark:before {
  content: "\f02e";
}

.fa-print:before {
  content: "\f02f";
}

.fa-camera:before {
  content: "\f030";
}

.fa-font:before {
  content: "\f031";
}

.fa-bold:before {
  content: "\f032";
}

.fa-italic:before {
  content: "\f033";
}

.fa-text-height:before {
  content: "\f034";
}

.fa-text-width:before {
  content: "\f035";
}

.fa-align-left:before {
  content: "\f036";
}

.fa-align-center:before {
  content: "\f037";
}

.fa-align-right:before {
  content: "\f038";
}

.fa-align-justify:before {
  content: "\f039";
}

.fa-list:before {
  content: "\f03a";
}

.fa-dedent:before,
.fa-outdent:before {
  content: "\f03b";
}

.fa-indent:before {
  content: "\f03c";
}

.fa-video-camera:before {
  content: "\f03d";
}

.fa-photo:before,
.fa-image:before,
.fa-picture-o:before {
  content: "\f03e";
}

.fa-pencil:before {
  content: "\f040";
}

.fa-map-marker:before {
  content: "\f041";
}

.fa-adjust:before {
  content: "\f042";
}

.fa-tint:before {
  content: "\f043";
}

.fa-edit:before,
.fa-pencil-square-o:before {
  content: "\f044";
}

.fa-share-square-o:before {
  content: "\f045";
}

.fa-check-square-o:before {
  content: "\f046";
}

.fa-arrows:before {
  content: "\f047";
}

.fa-step-backward:before {
  content: "\f048";
}

.fa-fast-backward:before {
  content: "\f049";
}

.fa-backward:before {
  content: "\f04a";
}

.fa-play:before {
  content: "\f04b";
}

.fa-pause:before {
  content: "\f04c";
}

.fa-stop:before {
  content: "\f04d";
}

.fa-forward:before {
  content: "\f04e";
}

.fa-fast-forward:before {
  content: "\f050";
}

.fa-step-forward:before {
  content: "\f051";
}

.fa-eject:before {
  content: "\f052";
}

.fa-chevron-left:before {
  content: "\f053";
}

.fa-chevron-right:before {
  content: "\f054";
}

.fa-plus-circle:before {
  content: "\f055";
}

.fa-minus-circle:before {
  content: "\f056";
}

.fa-times-circle:before {
  content: "\f057";
}

.fa-check-circle:before {
  content: "\f058";
}

.fa-question-circle:before {
  content: "\f059";
}

.fa-info-circle:before {
  content: "\f05a";
}

.fa-crosshairs:before {
  content: "\f05b";
}

.fa-times-circle-o:before {
  content: "\f05c";
}

.fa-check-circle-o:before {
  content: "\f05d";
}

.fa-ban:before {
  content: "\f05e";
}

.fa-arrow-left:before {
  content: "\f060";
}

.fa-arrow-right:before {
  content: "\f061";
}

.fa-arrow-up:before {
  content: "\f062";
}

.fa-arrow-down:before {
  content: "\f063";
}

.fa-mail-forward:before,
.fa-share:before {
  content: "\f064";
}

.fa-expand:before {
  content: "\f065";
}

.fa-compress:before {
  content: "\f066";
}

.fa-plus:before {
  content: "\f067";
}

.fa-minus:before {
  content: "\f068";
}

.fa-asterisk:before {
  content: "\f069";
}

.fa-exclamation-circle:before {
  content: "\f06a";
}

.fa-gift:before {
  content: "\f06b";
}

.fa-leaf:before {
  content: "\f06c";
}

.fa-fire:before {
  content: "\f06d";
}

.fa-eye:before {
  content: "\f06e";
}

.fa-eye-slash:before {
  content: "\f070";
}

.fa-warning:before,
.fa-exclamation-triangle:before {
  content: "\f071";
}

.fa-plane:before {
  content: "\f072";
}

.fa-calendar:before {
  content: "\f073";
}

.fa-random:before {
  content: "\f074";
}

.fa-comment:before {
  content: "\f075";
}

.fa-magnet:before {
  content: "\f076";
}

.fa-chevron-up:before {
  content: "\f077";
}

.fa-chevron-down:before {
  content: "\f078";
}

.fa-retweet:before {
  content: "\f079";
}

.fa-shopping-cart:before {
  content: "\f07a";
}

.fa-folder:before {
  content: "\f07b";
}

.fa-folder-open:before {
  content: "\f07c";
}

.fa-arrows-v:before {
  content: "\f07d";
}

.fa-arrows-h:before {
  content: "\f07e";
}

.fa-bar-chart-o:before,
.fa-bar-chart:before {
  content: "\f080";
}

.fa-twitter-square:before {
  content: "\f081";
}

.fa-facebook-square:before {
  content: "\f082";
}

.fa-camera-retro:before {
  content: "\f083";
}

.fa-key:before {
  content: "\f084";
}

.fa-gears:before,
.fa-cogs:before {
  content: "\f085";
}

.fa-comments:before {
  content: "\f086";
}

.fa-thumbs-o-up:before {
  content: "\f087";
}

.fa-thumbs-o-down:before {
  content: "\f088";
}

.fa-star-half:before {
  content: "\f089";
}

.fa-heart-o:before {
  content: "\f08a";
}

.fa-sign-out:before {
  content: "\f08b";
}

.fa-linkedin-square:before {
  content: "\f08c";
}

.fa-thumb-tack:before {
  content: "\f08d";
}

.fa-external-link:before {
  content: "\f08e";
}

.fa-sign-in:before {
  content: "\f090";
}

.fa-trophy:before {
  content: "\f091";
}

.fa-github-square:before {
  content: "\f092";
}

.fa-upload:before {
  content: "\f093";
}

.fa-lemon-o:before {
  content: "\f094";
}

.fa-phone:before {
  content: "\f095";
}

.fa-square-o:before {
  content: "\f096";
}

.fa-bookmark-o:before {
  content: "\f097";
}

.fa-phone-square:before {
  content: "\f098";
}

.fa-twitter:before {
  content: "\f099";
}

.fa-facebook-f:before,
.fa-facebook:before {
  content: "\f09a";
}

.fa-github:before {
  content: "\f09b";
}

.fa-unlock:before {
  content: "\f09c";
}

.fa-credit-card:before {
  content: "\f09d";
}

.fa-feed:before,
.fa-rss:before {
  content: "\f09e";
}

.fa-hdd-o:before {
  content: "\f0a0";
}

.fa-bullhorn:before {
  content: "\f0a1";
}

.fa-bell:before {
  content: "\f0f3";
}

.fa-certificate:before {
  content: "\f0a3";
}

.fa-hand-o-right:before {
  content: "\f0a4";
}

.fa-hand-o-left:before {
  content: "\f0a5";
}

.fa-hand-o-up:before {
  content: "\f0a6";
}

.fa-hand-o-down:before {
  content: "\f0a7";
}

.fa-arrow-circle-left:before {
  content: "\f0a8";
}

.fa-arrow-circle-right:before {
  content: "\f0a9";
}

.fa-arrow-circle-up:before {
  content: "\f0aa";
}

.fa-arrow-circle-down:before {
  content: "\f0ab";
}

.fa-globe:before {
  content: "\f0ac";
}

.fa-wrench:before {
  content: "\f0ad";
}

.fa-tasks:before {
  content: "\f0ae";
}

.fa-filter:before {
  content: "\f0b0";
}

.fa-briefcase:before {
  content: "\f0b1";
}

.fa-arrows-alt:before {
  content: "\f0b2";
}

.fa-group:before,
.fa-users:before {
  content: "\f0c0";
}

.fa-chain:before,
.fa-link:before {
  content: "\f0c1";
}

.fa-cloud:before {
  content: "\f0c2";
}

.fa-flask:before {
  content: "\f0c3";
}

.fa-cut:before,
.fa-scissors:before {
  content: "\f0c4";
}

.fa-copy:before,
.fa-files-o:before {
  content: "\f0c5";
}

.fa-paperclip:before {
  content: "\f0c6";
}

.fa-save:before,
.fa-floppy-o:before {
  content: "\f0c7";
}

.fa-square:before {
  content: "\f0c8";
}

.fa-navicon:before,
.fa-reorder:before,
.fa-bars:before {
  content: "\f0c9";
}

.fa-list-ul:before {
  content: "\f0ca";
}

.fa-list-ol:before {
  content: "\f0cb";
}

.fa-strikethrough:before {
  content: "\f0cc";
}

.fa-underline:before {
  content: "\f0cd";
}

.fa-table:before {
  content: "\f0ce";
}

.fa-magic:before {
  content: "\f0d0";
}

.fa-truck:before {
  content: "\f0d1";
}

.fa-pinterest:before {
  content: "\f0d2";
}

.fa-pinterest-square:before {
  content: "\f0d3";
}

.fa-google-plus-square:before {
  content: "\f0d4";
}

.fa-google-plus:before {
  content: "\f0d5";
}

.fa-money:before {
  content: "\f0d6";
}

.fa-caret-down:before {
  content: "\f0d7";
}

.fa-caret-up:before {
  content: "\f0d8";
}

.fa-caret-left:before {
  content: "\f0d9";
}

.fa-caret-right:before {
  content: "\f0da";
}

.fa-columns:before {
  content: "\f0db";
}

.fa-unsorted:before,
.fa-sort:before {
  content: "\f0dc";
}

.fa-sort-down:before,
.fa-sort-desc:before {
  content: "\f0dd";
}

.fa-sort-up:before,
.fa-sort-asc:before {
  content: "\f0de";
}

.fa-envelope:before {
  content: "\f0e0";
}

.fa-linkedin:before {
  content: "\f0e1";
}

.fa-rotate-left:before,
.fa-undo:before {
  content: "\f0e2";
}

.fa-legal:before,
.fa-gavel:before {
  content: "\f0e3";
}

.fa-dashboard:before,
.fa-tachometer:before {
  content: "\f0e4";
}

.fa-comment-o:before {
  content: "\f0e5";
}

.fa-comments-o:before {
  content: "\f0e6";
}

.fa-flash:before,
.fa-bolt:before {
  content: "\f0e7";
}

.fa-sitemap:before {
  content: "\f0e8";
}

.fa-umbrella:before {
  content: "\f0e9";
}

.fa-paste:before,
.fa-clipboard:before {
  content: "\f0ea";
}

.fa-lightbulb-o:before {
  content: "\f0eb";
}

.fa-exchange:before {
  content: "\f0ec";
}

.fa-cloud-download:before {
  content: "\f0ed";
}

.fa-cloud-upload:before {
  content: "\f0ee";
}

.fa-user-md:before {
  content: "\f0f0";
}

.fa-stethoscope:before {
  content: "\f0f1";
}

.fa-suitcase:before {
  content: "\f0f2";
}

.fa-bell-o:before {
  content: "\f0a2";
}

.fa-coffee:before {
  content: "\f0f4";
}

.fa-cutlery:before {
  content: "\f0f5";
}

.fa-file-text-o:before {
  content: "\f0f6";
}

.fa-building-o:before {
  content: "\f0f7";
}

.fa-hospital-o:before {
  content: "\f0f8";
}

.fa-ambulance:before {
  content: "\f0f9";
}

.fa-medkit:before {
  content: "\f0fa";
}

.fa-fighter-jet:before {
  content: "\f0fb";
}

.fa-beer:before {
  content: "\f0fc";
}

.fa-h-square:before {
  content: "\f0fd";
}

.fa-plus-square:before {
  content: "\f0fe";
}

.fa-angle-double-left:before {
  content: "\f100";
}

.fa-angle-double-right:before {
  content: "\f101";
}

.fa-angle-double-up:before {
  content: "\f102";
}

.fa-angle-double-down:before {
  content: "\f103";
}

.fa-angle-left:before {
  content: "\f104";
}

.fa-angle-right:before {
  content: "\f105";
}

.fa-angle-up:before {
  content: "\f106";
}

.fa-angle-down:before {
  content: "\f107";
}

.fa-desktop:before {
  content: "\f108";
}

.fa-laptop:before {
  content: "\f109";
}

.fa-tablet:before {
  content: "\f10a";
}

.fa-mobile-phone:before,
.fa-mobile:before {
  content: "\f10b";
}

.fa-circle-o:before {
  content: "\f10c";
}

.fa-quote-left:before {
  content: "\f10d";
}

.fa-quote-right:before {
  content: "\f10e";
}

.fa-spinner:before {
  content: "\f110";
}

.fa-circle:before {
  content: "\f111";
}

.fa-mail-reply:before,
.fa-reply:before {
  content: "\f112";
}

.fa-github-alt:before {
  content: "\f113";
}

.fa-folder-o:before {
  content: "\f114";
}

.fa-folder-open-o:before {
  content: "\f115";
}

.fa-smile-o:before {
  content: "\f118";
}

.fa-frown-o:before {
  content: "\f119";
}

.fa-meh-o:before {
  content: "\f11a";
}

.fa-gamepad:before {
  content: "\f11b";
}

.fa-keyboard-o:before {
  content: "\f11c";
}

.fa-flag-o:before {
  content: "\f11d";
}

.fa-flag-checkered:before {
  content: "\f11e";
}

.fa-terminal:before {
  content: "\f120";
}

.fa-code:before {
  content: "\f121";
}

.fa-mail-reply-all:before,
.fa-reply-all:before {
  content: "\f122";
}

.fa-star-half-empty:before,
.fa-star-half-full:before,
.fa-star-half-o:before {
  content: "\f123";
}

.fa-location-arrow:before {
  content: "\f124";
}

.fa-crop:before {
  content: "\f125";
}

.fa-code-fork:before {
  content: "\f126";
}

.fa-unlink:before,
.fa-chain-broken:before {
  content: "\f127";
}

.fa-question:before {
  content: "\f128";
}

.fa-info:before {
  content: "\f129";
}

.fa-exclamation:before {
  content: "\f12a";
}

.fa-superscript:before {
  content: "\f12b";
}

.fa-subscript:before {
  content: "\f12c";
}

.fa-eraser:before {
  content: "\f12d";
}

.fa-puzzle-piece:before {
  content: "\f12e";
}

.fa-microphone:before {
  content: "\f130";
}

.fa-microphone-slash:before {
  content: "\f131";
}

.fa-shield:before {
  content: "\f132";
}

.fa-calendar-o:before {
  content: "\f133";
}

.fa-fire-extinguisher:before {
  content: "\f134";
}

.fa-rocket:before {
  content: "\f135";
}

.fa-maxcdn:before {
  content: "\f136";
}

.fa-chevron-circle-left:before {
  content: "\f137";
}

.fa-chevron-circle-right:before {
  content: "\f138";
}

.fa-chevron-circle-up:before {
  content: "\f139";
}

.fa-chevron-circle-down:before {
  content: "\f13a";
}

.fa-html5:before {
  content: "\f13b";
}

.fa-css3:before {
  content: "\f13c";
}

.fa-anchor:before {
  content: "\f13d";
}

.fa-unlock-alt:before {
  content: "\f13e";
}

.fa-bullseye:before {
  content: "\f140";
}

.fa-ellipsis-h:before {
  content: "\f141";
}

.fa-ellipsis-v:before {
  content: "\f142";
}

.fa-rss-square:before {
  content: "\f143";
}

.fa-play-circle:before {
  content: "\f144";
}

.fa-ticket:before {
  content: "\f145";
}

.fa-minus-square:before {
  content: "\f146";
}

.fa-minus-square-o:before {
  content: "\f147";
}

.fa-level-up:before {
  content: "\f148";
}

.fa-level-down:before {
  content: "\f149";
}

.fa-check-square:before {
  content: "\f14a";
}

.fa-pencil-square:before {
  content: "\f14b";
}

.fa-external-link-square:before {
  content: "\f14c";
}

.fa-share-square:before {
  content: "\f14d";
}

.fa-compass:before {
  content: "\f14e";
}

.fa-toggle-down:before,
.fa-caret-square-o-down:before {
  content: "\f150";
}

.fa-toggle-up:before,
.fa-caret-square-o-up:before {
  content: "\f151";
}

.fa-toggle-right:before,
.fa-caret-square-o-right:before {
  content: "\f152";
}

.fa-euro:before,
.fa-eur:before {
  content: "\f153";
}

.fa-gbp:before {
  content: "\f154";
}

.fa-dollar:before,
.fa-usd:before {
  content: "\f155";
}

.fa-rupee:before,
.fa-inr:before {
  content: "\f156";
}

.fa-cny:before,
.fa-rmb:before,
.fa-yen:before,
.fa-jpy:before {
  content: "\f157";
}

.fa-ruble:before,
.fa-rouble:before,
.fa-rub:before {
  content: "\f158";
}

.fa-won:before,
.fa-krw:before {
  content: "\f159";
}

.fa-bitcoin:before,
.fa-btc:before {
  content: "\f15a";
}

.fa-file:before {
  content: "\f15b";
}

.fa-file-text:before {
  content: "\f15c";
}

.fa-sort-alpha-asc:before {
  content: "\f15d";
}

.fa-sort-alpha-desc:before {
  content: "\f15e";
}

.fa-sort-amount-asc:before {
  content: "\f160";
}

.fa-sort-amount-desc:before {
  content: "\f161";
}

.fa-sort-numeric-asc:before {
  content: "\f162";
}

.fa-sort-numeric-desc:before {
  content: "\f163";
}

.fa-thumbs-up:before {
  content: "\f164";
}

.fa-thumbs-down:before {
  content: "\f165";
}

.fa-youtube-square:before {
  content: "\f166";
}

.fa-youtube:before {
  content: "\f167";
}

.fa-xing:before {
  content: "\f168";
}

.fa-xing-square:before {
  content: "\f169";
}

.fa-youtube-play:before {
  content: "\f16a";
}

.fa-dropbox:before {
  content: "\f16b";
}

.fa-stack-overflow:before {
  content: "\f16c";
}

.fa-instagram:before {
  content: "\f16d";
}

.fa-flickr:before {
  content: "\f16e";
}

.fa-adn:before {
  content: "\f170";
}

.fa-bitbucket:before {
  content: "\f171";
}

.fa-bitbucket-square:before {
  content: "\f172";
}

.fa-tumblr:before {
  content: "\f173";
}

.fa-tumblr-square:before {
  content: "\f174";
}

.fa-long-arrow-down:before {
  content: "\f175";
}

.fa-long-arrow-up:before {
  content: "\f176";
}

.fa-long-arrow-left:before {
  content: "\f177";
}

.fa-long-arrow-right:before {
  content: "\f178";
}

.fa-apple:before {
  content: "\f179";
}

.fa-windows:before {
  content: "\f17a";
}

.fa-android:before {
  content: "\f17b";
}

.fa-linux:before {
  content: "\f17c";
}

.fa-dribbble:before {
  content: "\f17d";
}

.fa-skype:before {
  content: "\f17e";
}

.fa-foursquare:before {
  content: "\f180";
}

.fa-trello:before {
  content: "\f181";
}

.fa-female:before {
  content: "\f182";
}

.fa-male:before {
  content: "\f183";
}

.fa-gittip:before,
.fa-gratipay:before {
  content: "\f184";
}

.fa-sun-o:before {
  content: "\f185";
}

.fa-moon-o:before {
  content: "\f186";
}

.fa-archive:before {
  content: "\f187";
}

.fa-bug:before {
  content: "\f188";
}

.fa-vk:before {
  content: "\f189";
}

.fa-weibo:before {
  content: "\f18a";
}

.fa-renren:before {
  content: "\f18b";
}

.fa-pagelines:before {
  content: "\f18c";
}

.fa-stack-exchange:before {
  content: "\f18d";
}

.fa-arrow-circle-o-right:before {
  content: "\f18e";
}

.fa-arrow-circle-o-left:before {
  content: "\f190";
}

.fa-toggle-left:before,
.fa-caret-square-o-left:before {
  content: "\f191";
}

.fa-dot-circle-o:before {
  content: "\f192";
}

.fa-wheelchair:before {
  content: "\f193";
}

.fa-vimeo-square:before {
  content: "\f194";
}

.fa-turkish-lira:before,
.fa-try:before {
  content: "\f195";
}

.fa-plus-square-o:before {
  content: "\f196";
}

.fa-space-shuttle:before {
  content: "\f197";
}

.fa-slack:before {
  content: "\f198";
}

.fa-envelope-square:before {
  content: "\f199";
}

.fa-wordpress:before {
  content: "\f19a";
}

.fa-openid:before {
  content: "\f19b";
}

.fa-institution:before,
.fa-bank:before,
.fa-university:before {
  content: "\f19c";
}

.fa-mortar-board:before,
.fa-graduation-cap:before {
  content: "\f19d";
}

.fa-yahoo:before {
  content: "\f19e";
}

.fa-google:before {
  content: "\f1a0";
}

.fa-reddit:before {
  content: "\f1a1";
}

.fa-reddit-square:before {
  content: "\f1a2";
}

.fa-stumbleupon-circle:before {
  content: "\f1a3";
}

.fa-stumbleupon:before {
  content: "\f1a4";
}

.fa-delicious:before {
  content: "\f1a5";
}

.fa-digg:before {
  content: "\f1a6";
}

.fa-pied-piper-pp:before {
  content: "\f1a7";
}

.fa-pied-piper-alt:before {
  content: "\f1a8";
}

.fa-drupal:before {
  content: "\f1a9";
}

.fa-joomla:before {
  content: "\f1aa";
}

.fa-language:before {
  content: "\f1ab";
}

.fa-fax:before {
  content: "\f1ac";
}

.fa-building:before {
  content: "\f1ad";
}

.fa-child:before {
  content: "\f1ae";
}

.fa-paw:before {
  content: "\f1b0";
}

.fa-spoon:before {
  content: "\f1b1";
}

.fa-cube:before {
  content: "\f1b2";
}

.fa-cubes:before {
  content: "\f1b3";
}

.fa-behance:before {
  content: "\f1b4";
}

.fa-behance-square:before {
  content: "\f1b5";
}

.fa-steam:before {
  content: "\f1b6";
}

.fa-steam-square:before {
  content: "\f1b7";
}

.fa-recycle:before {
  content: "\f1b8";
}

.fa-automobile:before,
.fa-car:before {
  content: "\f1b9";
}

.fa-cab:before,
.fa-taxi:before {
  content: "\f1ba";
}

.fa-tree:before {
  content: "\f1bb";
}

.fa-spotify:before {
  content: "\f1bc";
}

.fa-deviantart:before {
  content: "\f1bd";
}

.fa-soundcloud:before {
  content: "\f1be";
}

.fa-database:before {
  content: "\f1c0";
}

.fa-file-pdf-o:before {
  content: "\f1c1";
}

.fa-file-word-o:before {
  content: "\f1c2";
}

.fa-file-excel-o:before {
  content: "\f1c3";
}

.fa-file-powerpoint-o:before {
  content: "\f1c4";
}

.fa-file-photo-o:before,
.fa-file-picture-o:before,
.fa-file-image-o:before {
  content: "\f1c5";
}

.fa-file-zip-o:before,
.fa-file-archive-o:before {
  content: "\f1c6";
}

.fa-file-sound-o:before,
.fa-file-audio-o:before {
  content: "\f1c7";
}

.fa-file-movie-o:before,
.fa-file-video-o:before {
  content: "\f1c8";
}

.fa-file-code-o:before {
  content: "\f1c9";
}

.fa-vine:before {
  content: "\f1ca";
}

.fa-codepen:before {
  content: "\f1cb";
}

.fa-jsfiddle:before {
  content: "\f1cc";
}

.fa-life-bouy:before,
.fa-life-buoy:before,
.fa-life-saver:before,
.fa-support:before,
.fa-life-ring:before {
  content: "\f1cd";
}

.fa-circle-o-notch:before {
  content: "\f1ce";
}

.fa-ra:before,
.fa-resistance:before,
.fa-rebel:before {
  content: "\f1d0";
}

.fa-ge:before,
.fa-empire:before {
  content: "\f1d1";
}

.fa-git-square:before {
  content: "\f1d2";
}

.fa-git:before {
  content: "\f1d3";
}

.fa-y-combinator-square:before,
.fa-yc-square:before,
.fa-hacker-news:before {
  content: "\f1d4";
}

.fa-tencent-weibo:before {
  content: "\f1d5";
}

.fa-qq:before {
  content: "\f1d6";
}

.fa-wechat:before,
.fa-weixin:before {
  content: "\f1d7";
}

.fa-send:before,
.fa-paper-plane:before {
  content: "\f1d8";
}

.fa-send-o:before,
.fa-paper-plane-o:before {
  content: "\f1d9";
}

.fa-history:before {
  content: "\f1da";
}

.fa-circle-thin:before {
  content: "\f1db";
}

.fa-header:before {
  content: "\f1dc";
}

.fa-paragraph:before {
  content: "\f1dd";
}

.fa-sliders:before {
  content: "\f1de";
}

.fa-share-alt:before {
  content: "\f1e0";
}

.fa-share-alt-square:before {
  content: "\f1e1";
}

.fa-bomb:before {
  content: "\f1e2";
}

.fa-soccer-ball-o:before,
.fa-futbol-o:before {
  content: "\f1e3";
}

.fa-tty:before {
  content: "\f1e4";
}

.fa-binoculars:before {
  content: "\f1e5";
}

.fa-plug:before {
  content: "\f1e6";
}

.fa-slideshare:before {
  content: "\f1e7";
}

.fa-twitch:before {
  content: "\f1e8";
}

.fa-yelp:before {
  content: "\f1e9";
}

.fa-newspaper-o:before {
  content: "\f1ea";
}

.fa-wifi:before {
  content: "\f1eb";
}

.fa-calculator:before {
  content: "\f1ec";
}

.fa-paypal:before {
  content: "\f1ed";
}

.fa-google-wallet:before {
  content: "\f1ee";
}

.fa-cc-visa:before {
  content: "\f1f0";
}

.fa-cc-mastercard:before {
  content: "\f1f1";
}

.fa-cc-discover:before {
  content: "\f1f2";
}

.fa-cc-amex:before {
  content: "\f1f3";
}

.fa-cc-paypal:before {
  content: "\f1f4";
}

.fa-cc-stripe:before {
  content: "\f1f5";
}

.fa-bell-slash:before {
  content: "\f1f6";
}

.fa-bell-slash-o:before {
  content: "\f1f7";
}

.fa-trash:before {
  content: "\f1f8";
}

.fa-copyright:before {
  content: "\f1f9";
}

.fa-at:before {
  content: "\f1fa";
}

.fa-eyedropper:before {
  content: "\f1fb";
}

.fa-paint-brush:before {
  content: "\f1fc";
}

.fa-birthday-cake:before {
  content: "\f1fd";
}

.fa-area-chart:before {
  content: "\f1fe";
}

.fa-pie-chart:before {
  content: "\f200";
}

.fa-line-chart:before {
  content: "\f201";
}

.fa-lastfm:before {
  content: "\f202";
}

.fa-lastfm-square:before {
  content: "\f203";
}

.fa-toggle-off:before {
  content: "\f204";
}

.fa-toggle-on:before {
  content: "\f205";
}

.fa-bicycle:before {
  content: "\f206";
}

.fa-bus:before {
  content: "\f207";
}

.fa-ioxhost:before {
  content: "\f208";
}

.fa-angellist:before {
  content: "\f209";
}

.fa-cc:before {
  content: "\f20a";
}

.fa-shekel:before,
.fa-sheqel:before,
.fa-ils:before {
  content: "\f20b";
}

.fa-meanpath:before {
  content: "\f20c";
}

.fa-buysellads:before {
  content: "\f20d";
}

.fa-connectdevelop:before {
  content: "\f20e";
}

.fa-dashcube:before {
  content: "\f210";
}

.fa-forumbee:before {
  content: "\f211";
}

.fa-leanpub:before {
  content: "\f212";
}

.fa-sellsy:before {
  content: "\f213";
}

.fa-shirtsinbulk:before {
  content: "\f214";
}

.fa-simplybuilt:before {
  content: "\f215";
}

.fa-skyatlas:before {
  content: "\f216";
}

.fa-cart-plus:before {
  content: "\f217";
}

.fa-cart-arrow-down:before {
  content: "\f218";
}

.fa-diamond:before {
  content: "\f219";
}

.fa-ship:before {
  content: "\f21a";
}

.fa-user-secret:before {
  content: "\f21b";
}

.fa-motorcycle:before {
  content: "\f21c";
}

.fa-street-view:before {
  content: "\f21d";
}

.fa-heartbeat:before {
  content: "\f21e";
}

.fa-venus:before {
  content: "\f221";
}

.fa-mars:before {
  content: "\f222";
}

.fa-mercury:before {
  content: "\f223";
}

.fa-intersex:before,
.fa-transgender:before {
  content: "\f224";
}

.fa-transgender-alt:before {
  content: "\f225";
}

.fa-venus-double:before {
  content: "\f226";
}

.fa-mars-double:before {
  content: "\f227";
}

.fa-venus-mars:before {
  content: "\f228";
}

.fa-mars-stroke:before {
  content: "\f229";
}

.fa-mars-stroke-v:before {
  content: "\f22a";
}

.fa-mars-stroke-h:before {
  content: "\f22b";
}

.fa-neuter:before {
  content: "\f22c";
}

.fa-genderless:before {
  content: "\f22d";
}

.fa-facebook-official:before {
  content: "\f230";
}

.fa-pinterest-p:before {
  content: "\f231";
}

.fa-whatsapp:before {
  content: "\f232";
}

.fa-server:before {
  content: "\f233";
}

.fa-user-plus:before {
  content: "\f234";
}

.fa-user-times:before {
  content: "\f235";
}

.fa-hotel:before,
.fa-bed:before {
  content: "\f236";
}

.fa-viacoin:before {
  content: "\f237";
}

.fa-train:before {
  content: "\f238";
}

.fa-subway:before {
  content: "\f239";
}

.fa-medium:before {
  content: "\f23a";
}

.fa-yc:before,
.fa-y-combinator:before {
  content: "\f23b";
}

.fa-optin-monster:before {
  content: "\f23c";
}

.fa-opencart:before {
  content: "\f23d";
}

.fa-expeditedssl:before {
  content: "\f23e";
}

.fa-battery-4:before,
.fa-battery:before,
.fa-battery-full:before {
  content: "\f240";
}

.fa-battery-3:before,
.fa-battery-three-quarters:before {
  content: "\f241";
}

.fa-battery-2:before,
.fa-battery-half:before {
  content: "\f242";
}

.fa-battery-1:before,
.fa-battery-quarter:before {
  content: "\f243";
}

.fa-battery-0:before,
.fa-battery-empty:before {
  content: "\f244";
}

.fa-mouse-pointer:before {
  content: "\f245";
}

.fa-i-cursor:before {
  content: "\f246";
}

.fa-object-group:before {
  content: "\f247";
}

.fa-object-ungroup:before {
  content: "\f248";
}

.fa-sticky-note:before {
  content: "\f249";
}

.fa-sticky-note-o:before {
  content: "\f24a";
}

.fa-cc-jcb:before {
  content: "\f24b";
}

.fa-cc-diners-club:before {
  content: "\f24c";
}

.fa-clone:before {
  content: "\f24d";
}

.fa-balance-scale:before {
  content: "\f24e";
}

.fa-hourglass-o:before {
  content: "\f250";
}

.fa-hourglass-1:before,
.fa-hourglass-start:before {
  content: "\f251";
}

.fa-hourglass-2:before,
.fa-hourglass-half:before {
  content: "\f252";
}

.fa-hourglass-3:before,
.fa-hourglass-end:before {
  content: "\f253";
}

.fa-hourglass:before {
  content: "\f254";
}

.fa-hand-grab-o:before,
.fa-hand-rock-o:before {
  content: "\f255";
}

.fa-hand-stop-o:before,
.fa-hand-paper-o:before {
  content: "\f256";
}

.fa-hand-scissors-o:before {
  content: "\f257";
}

.fa-hand-lizard-o:before {
  content: "\f258";
}

.fa-hand-spock-o:before {
  content: "\f259";
}

.fa-hand-pointer-o:before {
  content: "\f25a";
}

.fa-hand-peace-o:before {
  content: "\f25b";
}

.fa-trademark:before {
  content: "\f25c";
}

.fa-registered:before {
  content: "\f25d";
}

.fa-creative-commons:before {
  content: "\f25e";
}

.fa-gg:before {
  content: "\f260";
}

.fa-gg-circle:before {
  content: "\f261";
}

.fa-tripadvisor:before {
  content: "\f262";
}

.fa-odnoklassniki:before {
  content: "\f263";
}

.fa-odnoklassniki-square:before {
  content: "\f264";
}

.fa-get-pocket:before {
  content: "\f265";
}

.fa-wikipedia-w:before {
  content: "\f266";
}

.fa-safari:before {
  content: "\f267";
}

.fa-chrome:before {
  content: "\f268";
}

.fa-firefox:before {
  content: "\f269";
}

.fa-opera:before {
  content: "\f26a";
}

.fa-internet-explorer:before {
  content: "\f26b";
}

.fa-tv:before,
.fa-television:before {
  content: "\f26c";
}

.fa-contao:before {
  content: "\f26d";
}

.fa-500px:before {
  content: "\f26e";
}

.fa-amazon:before {
  content: "\f270";
}

.fa-calendar-plus-o:before {
  content: "\f271";
}

.fa-calendar-minus-o:before {
  content: "\f272";
}

.fa-calendar-times-o:before {
  content: "\f273";
}

.fa-calendar-check-o:before {
  content: "\f274";
}

.fa-industry:before {
  content: "\f275";
}

.fa-map-pin:before {
  content: "\f276";
}

.fa-map-signs:before {
  content: "\f277";
}

.fa-map-o:before {
  content: "\f278";
}

.fa-map:before {
  content: "\f279";
}

.fa-commenting:before {
  content: "\f27a";
}

.fa-commenting-o:before {
  content: "\f27b";
}

.fa-houzz:before {
  content: "\f27c";
}

.fa-vimeo:before {
  content: "\f27d";
}

.fa-black-tie:before {
  content: "\f27e";
}

.fa-fonticons:before {
  content: "\f280";
}

.fa-reddit-alien:before {
  content: "\f281";
}

.fa-edge:before {
  content: "\f282";
}

.fa-credit-card-alt:before {
  content: "\f283";
}

.fa-codiepie:before {
  content: "\f284";
}

.fa-modx:before {
  content: "\f285";
}

.fa-fort-awesome:before {
  content: "\f286";
}

.fa-usb:before {
  content: "\f287";
}

.fa-product-hunt:before {
  content: "\f288";
}

.fa-mixcloud:before {
  content: "\f289";
}

.fa-scribd:before {
  content: "\f28a";
}

.fa-pause-circle:before {
  content: "\f28b";
}

.fa-pause-circle-o:before {
  content: "\f28c";
}

.fa-stop-circle:before {
  content: "\f28d";
}

.fa-stop-circle-o:before {
  content: "\f28e";
}

.fa-shopping-bag:before {
  content: "\f290";
}

.fa-shopping-basket:before {
  content: "\f291";
}

.fa-hashtag:before {
  content: "\f292";
}

.fa-bluetooth:before {
  content: "\f293";
}

.fa-bluetooth-b:before {
  content: "\f294";
}

.fa-percent:before {
  content: "\f295";
}

.fa-gitlab:before {
  content: "\f296";
}

.fa-wpbeginner:before {
  content: "\f297";
}

.fa-wpforms:before {
  content: "\f298";
}

.fa-envira:before {
  content: "\f299";
}

.fa-universal-access:before {
  content: "\f29a";
}

.fa-wheelchair-alt:before {
  content: "\f29b";
}

.fa-question-circle-o:before {
  content: "\f29c";
}

.fa-blind:before {
  content: "\f29d";
}

.fa-audio-description:before {
  content: "\f29e";
}

.fa-volume-control-phone:before {
  content: "\f2a0";
}

.fa-braille:before {
  content: "\f2a1";
}

.fa-assistive-listening-systems:before {
  content: "\f2a2";
}

.fa-asl-interpreting:before,
.fa-american-sign-language-interpreting:before {
  content: "\f2a3";
}

.fa-deafness:before,
.fa-hard-of-hearing:before,
.fa-deaf:before {
  content: "\f2a4";
}

.fa-glide:before {
  content: "\f2a5";
}

.fa-glide-g:before {
  content: "\f2a6";
}

.fa-signing:before,
.fa-sign-language:before {
  content: "\f2a7";
}

.fa-low-vision:before {
  content: "\f2a8";
}

.fa-viadeo:before {
  content: "\f2a9";
}

.fa-viadeo-square:before {
  content: "\f2aa";
}

.fa-snapchat:before {
  content: "\f2ab";
}

.fa-snapchat-ghost:before {
  content: "\f2ac";
}

.fa-snapchat-square:before {
  content: "\f2ad";
}

.fa-pied-piper:before {
  content: "\f2ae";
}

.fa-first-order:before {
  content: "\f2b0";
}

.fa-yoast:before {
  content: "\f2b1";
}

.fa-themeisle:before {
  content: "\f2b2";
}

.fa-google-plus-circle:before,
.fa-google-plus-official:before {
  content: "\f2b3";
}

.fa-fa:before,
.fa-font-awesome:before {
  content: "\f2b4";
}

.fa-handshake-o:before {
  content: "\f2b5";
}

.fa-envelope-open:before {
  content: "\f2b6";
}

.fa-envelope-open-o:before {
  content: "\f2b7";
}

.fa-linode:before {
  content: "\f2b8";
}

.fa-address-book:before {
  content: "\f2b9";
}

.fa-address-book-o:before {
  content: "\f2ba";
}

.fa-vcard:before,
.fa-address-card:before {
  content: "\f2bb";
}

.fa-vcard-o:before,
.fa-address-card-o:before {
  content: "\f2bc";
}

.fa-user-circle:before {
  content: "\f2bd";
}

.fa-user-circle-o:before {
  content: "\f2be";
}

.fa-user-o:before {
  content: "\f2c0";
}

.fa-id-badge:before {
  content: "\f2c1";
}

.fa-drivers-license:before,
.fa-id-card:before {
  content: "\f2c2";
}

.fa-drivers-license-o:before,
.fa-id-card-o:before {
  content: "\f2c3";
}

.fa-quora:before {
  content: "\f2c4";
}

.fa-free-code-camp:before {
  content: "\f2c5";
}

.fa-telegram:before {
  content: "\f2c6";
}

.fa-thermometer-4:before,
.fa-thermometer:before,
.fa-thermometer-full:before {
  content: "\f2c7";
}

.fa-thermometer-3:before,
.fa-thermometer-three-quarters:before {
  content: "\f2c8";
}

.fa-thermometer-2:before,
.fa-thermometer-half:before {
  content: "\f2c9";
}

.fa-thermometer-1:before,
.fa-thermometer-quarter:before {
  content: "\f2ca";
}

.fa-thermometer-0:before,
.fa-thermometer-empty:before {
  content: "\f2cb";
}

.fa-shower:before {
  content: "\f2cc";
}

.fa-bathtub:before,
.fa-s15:before,
.fa-bath:before {
  content: "\f2cd";
}

.fa-podcast:before {
  content: "\f2ce";
}

.fa-window-maximize:before {
  content: "\f2d0";
}

.fa-window-minimize:before {
  content: "\f2d1";
}

.fa-window-restore:before {
  content: "\f2d2";
}

.fa-times-rectangle:before,
.fa-window-close:before {
  content: "\f2d3";
}

.fa-times-rectangle-o:before,
.fa-window-close-o:before {
  content: "\f2d4";
}

.fa-bandcamp:before {
  content: "\f2d5";
}

.fa-grav:before {
  content: "\f2d6";
}

.fa-etsy:before {
  content: "\f2d7";
}

.fa-imdb:before {
  content: "\f2d8";
}

.fa-ravelry:before {
  content: "\f2d9";
}

.fa-eercast:before {
  content: "\f2da";
}

.fa-microchip:before {
  content: "\f2db";
}

.fa-snowflake-o:before {
  content: "\f2dc";
}

.fa-superpowers:before {
  content: "\f2dd";
}

.fa-wpexplorer:before {
  content: "\f2de";
}

.fa-meetup:before {
  content: "\f2e0";
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}

.sr-only-focusable:active, .sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  margin: 0;
  overflow: visible;
  clip: auto;
}

@font-face {
  font-family: "SVN-Artifex CF";
  font-weight: 400;
  font-style: normal;
  src: url("../fonts/Artifex/SVN-ArtifexCF-Regular.otf") format("opentype");
}
@font-face {
  font-family: "SVN-Artifex CF";
  font-weight: 400;
  font-style: italic;
  src: url("../fonts/Artifex/SVN-ArtifexCF-RegularItalic.otf") format("opentype");
}
@font-face {
  font-family: "SVN-Artifex CF";
  font-weight: 200;
  font-style: normal;
  src: url("../fonts/Artifex/SVN-ArtifexCF-Light.otf") format("opentype");
}
@font-face {
  font-family: "SVN-Artifex CF";
  font-weight: 200;
  font-style: italic;
  src: url("../fonts/Artifex/SVN-ArtifexCF-ExtraLightItalic.otf") format("opentype");
}
@font-face {
  font-family: "SVN-Artifex CF";
  font-weight: 300;
  font-style: normal;
  src: url("../fonts/Artifex/SVN-ArtifexCF-Light.otf") format("opentype");
}
@font-face {
  font-family: "SVN-Artifex CF";
  font-weight: 300;
  font-style: italic;
  src: url("../fonts/Artifex/SVN-ArtifexCF-LightItalic.otf") format("opentype");
}
@font-face {
  font-family: "SVN-Artifex CF";
  font-weight: 500;
  font-style: normal;
  src: url("../fonts/Artifex/SVN-ArtifexCF-Book.otf") format("opentype");
}
@font-face {
  font-family: "SVN-Artifex CF";
  font-weight: 500;
  font-style: italic;
  src: url("../fonts/Artifex/SVN-ArtifexCF-BookItalic.otf") format("opentype");
}
@font-face {
  font-family: "SVN-Artifex CF";
  font-weight: 600;
  font-style: normal;
  src: url("../fonts/Artifex/SVN-ArtifexCF-DemiBold.otf") format("opentype");
}
@font-face {
  font-family: "SVN-Artifex CF";
  font-weight: 600;
  font-style: italic;
  src: url("../fonts/Artifex/SVN-ArtifexCF-DemiBoldItalic.otf") format("opentype");
}
@font-face {
  font-family: "SVN-Artifex CF";
  font-weight: 700;
  font-style: normal;
  src: url("../fonts/Artifex/SVN-ArtifexCF-Bold.otf") format("opentype");
}
@font-face {
  font-family: "SVN-Artifex CF";
  font-weight: 700;
  font-style: italic;
  src: url("../fonts/Artifex/SVN-ArtifexCF-BoldItalic.otf") format("opentype");
}
@font-face {
  font-family: "SVN-Artifex CF";
  font-weight: 800;
  font-style: normal;
  src: url("../fonts/Artifex/SVN-ArtifexCF-ExtraBold.otf") format("opentype");
}
@font-face {
  font-family: "SVN-Artifex CF";
  font-weight: 800;
  font-style: italic;
  src: url("../fonts/Artifex/SVN-ArtifexCF-BoldItalic.otf") format("opentype");
}
@font-face {
  font-family: "SVN-Artifex CF";
  font-weight: 900;
  font-style: normal;
  src: url("../fonts/Artifex/SVN-ArtifexCF-Heavy.otf") format("opentype");
}
@font-face {
  font-family: "SVN-Artifex CF";
  font-weight: 900;
  font-style: italic;
  src: url("../fonts/Artifex/SVN-ArtifexCF-HeavyItalic.otf") format("opentype");
}
@font-face {
  font-family: "Pinyon Script";
  font-weight: 900;
  font-style: italic;
  src: url("../fonts/Pinyon_Script/PinyonScript-Regular.ttf");
}
.mt0 {
  margin-top: 0px;
}

.pt0 {
  padding-top: 0px;
}

.mb0 {
  margin-bottom: 0px;
}

.pb0 {
  padding-bottom: 0px;
}

.ml0 {
  margin-left: 0px;
}

.pl0 {
  padding-left: 0px;
}

.mr0 {
  margin-right: 0px;
}

.pr0 {
  padding-right: 0px;
}

.m0 {
  margin: 0px;
}

.p0 {
  padding: 0px;
}

.mt10 {
  margin-top: 10px;
}

.pt10 {
  padding-top: 10px;
}

.mb10 {
  margin-bottom: 10px;
}

.pb10 {
  padding-bottom: 10px;
}

.ml10 {
  margin-left: 10px;
}

.pl10 {
  padding-left: 10px;
}

.mr10 {
  margin-right: 10px;
}

.pr10 {
  padding-right: 10px;
}

.m10 {
  margin: 10px;
}

.p10 {
  padding: 10px;
}

.mt20 {
  margin-top: 20px;
}

.pt20 {
  padding-top: 20px;
}

.mb20 {
  margin-bottom: 20px;
}

.pb20 {
  padding-bottom: 20px;
}

.ml20 {
  margin-left: 20px;
}

.pl20 {
  padding-left: 20px;
}

.mr20 {
  margin-right: 20px;
}

.pr20 {
  padding-right: 20px;
}

.m20 {
  margin: 20px;
}

.p20 {
  padding: 20px;
}

.mt30 {
  margin-top: 30px;
}

.pt30 {
  padding-top: 30px;
}

.mb30 {
  margin-bottom: 30px;
}

.pb30 {
  padding-bottom: 30px;
}

.ml30 {
  margin-left: 30px;
}

.pl30 {
  padding-left: 30px;
}

.mr30 {
  margin-right: 30px;
}

.pr30 {
  padding-right: 30px;
}

.m30 {
  margin: 30px;
}

.p30 {
  padding: 30px;
}

.mt50 {
  margin-top: 50px;
}

.pt50 {
  padding-top: 50px;
}

.mb50 {
  margin-bottom: 50px;
}

.pb50 {
  padding-bottom: 50px;
}

.ml50 {
  margin-left: 50px;
}

.pl50 {
  padding-left: 50px;
}

.mr50 {
  margin-right: 50px;
}

.pr50 {
  padding-right: 50px;
}

.m50 {
  margin: 50px;
}

.p50 {
  padding: 50px;
}

.fs-10 {
  font-size: 10px;
}

.fs-11 {
  font-size: 11px;
}

.fs-12 {
  font-size: 12px;
}

.fs-13 {
  font-size: 13px;
}

.fs-14 {
  font-size: 14px;
}

.fs-15 {
  font-size: 15px;
}

.fs-16 {
  font-size: 16px;
}

.fs-17 {
  font-size: 17px;
}

.fs-18 {
  font-size: 18px;
}

.fs-19 {
  font-size: 19px;
}

.fs-20 {
  font-size: 20px;
}

.fs-21 {
  font-size: 21px;
}

.fs-22 {
  font-size: 22px;
}

.fs-23 {
  font-size: 23px;
}

.fs-24 {
  font-size: 24px;
}

.fs-25 {
  font-size: 25px;
}

.fs-26 {
  font-size: 26px;
}

.fs-27 {
  font-size: 27px;
}

.fs-28 {
  font-size: 28px;
}

.fs-29 {
  font-size: 29px;
}

.fs-30 {
  font-size: 30px;
}

.fs-31 {
  font-size: 31px;
}

.fs-32 {
  font-size: 32px;
}

.fs-33 {
  font-size: 33px;
}

.fs-34 {
  font-size: 34px;
}

.fs-35 {
  font-size: 35px;
}

.fs-36 {
  font-size: 36px;
}

.fs-37 {
  font-size: 37px;
}

.fs-38 {
  font-size: 38px;
}

.fs-39 {
  font-size: 39px;
}

.fs-40 {
  font-size: 40px;
}

.fs-41 {
  font-size: 41px;
}

.fs-42 {
  font-size: 42px;
}

.fs-43 {
  font-size: 43px;
}

.fs-44 {
  font-size: 44px;
}

.fs-45 {
  font-size: 45px;
}

.fs-46 {
  font-size: 46px;
}

.fs-47 {
  font-size: 47px;
}

.fs-48 {
  font-size: 48px;
}

.fs-49 {
  font-size: 49px;
}

.fs-50 {
  font-size: 50px;
}

/*!
 * Bootstrap v5.0.2 (https://getbootstrap.com/)
 * Copyright 2011-2021 The Bootstrap Authors
 * Copyright 2011-2021 Twitter, Inc.
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)
 */
:root {
  --bs-blue: #0d6efd;
  --bs-indigo: #6610f2;
  --bs-purple: #6f42c1;
  --bs-pink: #d63384;
  --bs-red: #dc3545;
  --bs-orange: #fd7e14;
  --bs-yellow: #ffc107;
  --bs-green: #198754;
  --bs-teal: #20c997;
  --bs-cyan: #0dcaf0;
  --bs-white: #fff;
  --bs-gray: #6c757d;
  --bs-gray-dark: #343a40;
  --bs-primary: #0d6efd;
  --bs-secondary: #6c757d;
  --bs-success: #198754;
  --bs-info: #0dcaf0;
  --bs-warning: #ffc107;
  --bs-danger: #dc3545;
  --bs-light: #f8f9fa;
  --bs-dark: #212529;
  --bs-font-sans-serif: system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", "Liberation Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --bs-font-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  --bs-gradient: linear-gradient(180deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0));
}

*,
*::before,
*::after {
  box-sizing: border-box;
}

@media (prefers-reduced-motion: no-preference) {
  :root {
    scroll-behavior: smooth;
  }
}

body {
  margin: 0;
  font-family: var(--bs-font-sans-serif);
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #212529;
  background-color: #fff;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

hr {
  margin: 1rem 0;
  color: inherit;
  background-color: currentColor;
  border: 0;
  opacity: 0.25;
}

hr:not([size]) {
  height: 1px;
}

h6, .h6, h5, .h5, h4, .h4, h3, .h3, h2, .h2, h1, .h1 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-weight: 500;
  line-height: 1.2;
}

h1, .h1 {
  font-size: calc(1.375rem + 1.5vw);
}
@media (min-width: 1200px) {
  h1, .h1 {
    font-size: 2.5rem;
  }
}

h2, .h2 {
  font-size: calc(1.325rem + 0.9vw);
}
@media (min-width: 1200px) {
  h2, .h2 {
    font-size: 2rem;
  }
}

h3, .h3 {
  font-size: calc(1.3rem + 0.6vw);
}
@media (min-width: 1200px) {
  h3, .h3 {
    font-size: 1.75rem;
  }
}

h4, .h4 {
  font-size: calc(1.275rem + 0.3vw);
}
@media (min-width: 1200px) {
  h4, .h4 {
    font-size: 1.5rem;
  }
}

h5, .h5 {
  font-size: 1.25rem;
}

h6, .h6 {
  font-size: 1rem;
}

p {
  margin-top: 0;
  margin-bottom: 1rem;
}

abbr[title],
abbr[data-bs-original-title] {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
  cursor: help;
  -webkit-text-decoration-skip-ink: none;
          text-decoration-skip-ink: none;
}

address {
  margin-bottom: 1rem;
  font-style: normal;
  line-height: inherit;
}

ol,
ul {
  padding-left: 2rem;
}

ol,
ul,
dl {
  margin-top: 0;
  margin-bottom: 1rem;
}

ol ol,
ul ul,
ol ul,
ul ol {
  margin-bottom: 0;
}

dt {
  font-weight: 700;
}

dd {
  margin-bottom: 0.5rem;
  margin-left: 0;
}

blockquote {
  margin: 0 0 1rem;
}

b,
strong {
  font-weight: bolder;
}

small, .small {
  font-size: 0.875em;
}

mark, .mark {
  padding: 0.2em;
  background-color: #fcf8e3;
}

sub,
sup {
  position: relative;
  font-size: 0.75em;
  line-height: 0;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

a {
  color: #0d6efd;
  text-decoration: underline;
}
a:hover {
  color: rgb(10.4, 88, 202.4);
}

a:not([href]):not([class]), a:not([href]):not([class]):hover {
  color: inherit;
  text-decoration: none;
}

pre,
code,
kbd,
samp {
  font-family: var(--bs-font-monospace);
  font-size: 1em;
  direction: ltr /* rtl:ignore */;
  unicode-bidi: bidi-override;
}

pre {
  display: block;
  margin-top: 0;
  margin-bottom: 1rem;
  overflow: auto;
  font-size: 0.875em;
}
pre code {
  font-size: inherit;
  color: inherit;
  word-break: normal;
}

code {
  font-size: 0.875em;
  color: #d63384;
  word-wrap: break-word;
}
a > code {
  color: inherit;
}

kbd {
  padding: 0.2rem 0.4rem;
  font-size: 0.875em;
  color: #fff;
  background-color: #212529;
  border-radius: 0.2rem;
}
kbd kbd {
  padding: 0;
  font-size: 1em;
  font-weight: 700;
}

figure {
  margin: 0 0 1rem;
}

img,
svg {
  vertical-align: middle;
}

table {
  caption-side: bottom;
  border-collapse: collapse;
}

caption {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  color: #6c757d;
  text-align: left;
}

th {
  text-align: inherit;
  text-align: -webkit-match-parent;
}

thead,
tbody,
tfoot,
tr,
td,
th {
  border-color: inherit;
  border-style: solid;
  border-width: 0;
}

label {
  display: inline-block;
}

button {
  border-radius: 0;
}

button:focus:not(:focus-visible) {
  outline: 0;
}

input,
button,
select,
optgroup,
textarea {
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

button,
select {
  text-transform: none;
}

[role=button] {
  cursor: pointer;
}

select {
  word-wrap: normal;
}
select:disabled {
  opacity: 1;
}

[list]::-webkit-calendar-picker-indicator {
  display: none;
}

button,
[type=button],
[type=reset],
[type=submit] {
  -webkit-appearance: button;
}
button:not(:disabled),
[type=button]:not(:disabled),
[type=reset]:not(:disabled),
[type=submit]:not(:disabled) {
  cursor: pointer;
}

::-moz-focus-inner {
  padding: 0;
  border-style: none;
}

textarea {
  resize: vertical;
}

fieldset {
  min-width: 0;
  padding: 0;
  margin: 0;
  border: 0;
}

legend {
  float: left;
  width: 100%;
  padding: 0;
  margin-bottom: 0.5rem;
  font-size: calc(1.275rem + 0.3vw);
  line-height: inherit;
}
@media (min-width: 1200px) {
  legend {
    font-size: 1.5rem;
  }
}
legend + * {
  clear: left;
}

::-webkit-datetime-edit-fields-wrapper,
::-webkit-datetime-edit-text,
::-webkit-datetime-edit-minute,
::-webkit-datetime-edit-hour-field,
::-webkit-datetime-edit-day-field,
::-webkit-datetime-edit-month-field,
::-webkit-datetime-edit-year-field {
  padding: 0;
}

::-webkit-inner-spin-button {
  height: auto;
}

[type=search] {
  outline-offset: -2px;
  -webkit-appearance: textfield;
}

/* rtl:raw:
[type="tel"],
[type="url"],
[type="email"],
[type="number"] {
  direction: ltr;
}
*/
::-webkit-search-decoration {
  -webkit-appearance: none;
}

::-webkit-color-swatch-wrapper {
  padding: 0;
}

::file-selector-button {
  font: inherit;
}

::-webkit-file-upload-button {
  font: inherit;
  -webkit-appearance: button;
}

output {
  display: inline-block;
}

iframe {
  border: 0;
}

summary {
  display: list-item;
  cursor: pointer;
}

progress {
  vertical-align: baseline;
}

[hidden] {
  display: none !important;
}

.lead {
  font-size: 1.25rem;
  font-weight: 300;
}

.display-1 {
  font-size: calc(1.625rem + 4.5vw);
  font-weight: 300;
  line-height: 1.2;
}
@media (min-width: 1200px) {
  .display-1 {
    font-size: 5rem;
  }
}

.display-2 {
  font-size: calc(1.575rem + 3.9vw);
  font-weight: 300;
  line-height: 1.2;
}
@media (min-width: 1200px) {
  .display-2 {
    font-size: 4.5rem;
  }
}

.display-3 {
  font-size: calc(1.525rem + 3.3vw);
  font-weight: 300;
  line-height: 1.2;
}
@media (min-width: 1200px) {
  .display-3 {
    font-size: 4rem;
  }
}

.display-4 {
  font-size: calc(1.475rem + 2.7vw);
  font-weight: 300;
  line-height: 1.2;
}
@media (min-width: 1200px) {
  .display-4 {
    font-size: 3.5rem;
  }
}

.display-5 {
  font-size: calc(1.425rem + 2.1vw);
  font-weight: 300;
  line-height: 1.2;
}
@media (min-width: 1200px) {
  .display-5 {
    font-size: 3rem;
  }
}

.display-6 {
  font-size: calc(1.375rem + 1.5vw);
  font-weight: 300;
  line-height: 1.2;
}
@media (min-width: 1200px) {
  .display-6 {
    font-size: 2.5rem;
  }
}

.list-unstyled {
  padding-left: 0;
  list-style: none;
}

.list-inline {
  padding-left: 0;
  list-style: none;
}

.list-inline-item {
  display: inline-block;
}
.list-inline-item:not(:last-child) {
  margin-right: 0.5rem;
}

.initialism {
  font-size: 0.875em;
  text-transform: uppercase;
}

.blockquote {
  margin-bottom: 1rem;
  font-size: 1.25rem;
}
.blockquote > :last-child {
  margin-bottom: 0;
}

.blockquote-footer {
  margin-top: -1rem;
  margin-bottom: 1rem;
  font-size: 0.875em;
  color: #6c757d;
}
.blockquote-footer::before {
  content: "— ";
}

.img-fluid {
  max-width: 100%;
  height: auto;
}

.img-thumbnail {
  padding: 0.25rem;
  background-color: #fff;
  border: 1px solid #dee2e6;
  border-radius: 0.25rem;
  max-width: 100%;
  height: auto;
}

.figure {
  display: inline-block;
}

.figure-img {
  margin-bottom: 0.5rem;
  line-height: 1;
}

.figure-caption {
  font-size: 0.875em;
  color: #6c757d;
}

.container,
.container-fluid,
.container-xxl,
.container-xl,
.container-lg,
.container-md,
.container-sm {
  width: 100%;
  padding-right: var(--bs-gutter-x, 15px);
  padding-left: var(--bs-gutter-x, 15px);
  margin-right: auto;
  margin-left: auto;
}

@media (min-width: 576px) {
  .container-sm, .container {
    max-width: 540px;
  }
}
@media (min-width: 768px) {
  .container-md, .container-sm, .container {
    max-width: 720px;
  }
}
@media (min-width: 992px) {
  .container-lg, .container-md, .container-sm, .container {
    max-width: 960px;
  }
}
@media (min-width: 1200px) {
  .container-xl, .container-lg, .container-md, .container-sm, .container {
    max-width: 1140px;
  }
}
@media (min-width: 1400px) {
  .container-xxl, .container-xl, .container-lg, .container-md, .container-sm, .container {
    max-width: 1140px;
  }
}
.row {
  --bs-gutter-x: 30px;
  --bs-gutter-y: 0;
  display: flex;
  flex-wrap: wrap;
  margin-top: calc(var(--bs-gutter-y) * -1);
  margin-right: calc(var(--bs-gutter-x) * -0.5);
  margin-left: calc(var(--bs-gutter-x) * -0.5);
}
.row > * {
  flex-shrink: 0;
  width: 100%;
  max-width: 100%;
  padding-right: calc(var(--bs-gutter-x) * 0.5);
  padding-left: calc(var(--bs-gutter-x) * 0.5);
  margin-top: var(--bs-gutter-y);
}

.col {
  flex: 1 0 0%;
}

.row-cols-auto > * {
  flex: 0 0 auto;
  width: auto;
}

.row-cols-1 > * {
  flex: 0 0 auto;
  width: 100%;
}

.row-cols-2 > * {
  flex: 0 0 auto;
  width: 50%;
}

.row-cols-3 > * {
  flex: 0 0 auto;
  width: 33.3333333333%;
}

.row-cols-4 > * {
  flex: 0 0 auto;
  width: 25%;
}

.row-cols-5 > * {
  flex: 0 0 auto;
  width: 20%;
}

.row-cols-6 > * {
  flex: 0 0 auto;
  width: 16.6666666667%;
}

@media (min-width: 576px) {
  .col-sm {
    flex: 1 0 0%;
  }
  .row-cols-sm-auto > * {
    flex: 0 0 auto;
    width: auto;
  }
  .row-cols-sm-1 > * {
    flex: 0 0 auto;
    width: 100%;
  }
  .row-cols-sm-2 > * {
    flex: 0 0 auto;
    width: 50%;
  }
  .row-cols-sm-3 > * {
    flex: 0 0 auto;
    width: 33.3333333333%;
  }
  .row-cols-sm-4 > * {
    flex: 0 0 auto;
    width: 25%;
  }
  .row-cols-sm-5 > * {
    flex: 0 0 auto;
    width: 20%;
  }
  .row-cols-sm-6 > * {
    flex: 0 0 auto;
    width: 16.6666666667%;
  }
}
@media (min-width: 768px) {
  .col-md {
    flex: 1 0 0%;
  }
  .row-cols-md-auto > * {
    flex: 0 0 auto;
    width: auto;
  }
  .row-cols-md-1 > * {
    flex: 0 0 auto;
    width: 100%;
  }
  .row-cols-md-2 > * {
    flex: 0 0 auto;
    width: 50%;
  }
  .row-cols-md-3 > * {
    flex: 0 0 auto;
    width: 33.3333333333%;
  }
  .row-cols-md-4 > * {
    flex: 0 0 auto;
    width: 25%;
  }
  .row-cols-md-5 > * {
    flex: 0 0 auto;
    width: 20%;
  }
  .row-cols-md-6 > * {
    flex: 0 0 auto;
    width: 16.6666666667%;
  }
}
@media (min-width: 992px) {
  .col-lg {
    flex: 1 0 0%;
  }
  .row-cols-lg-auto > * {
    flex: 0 0 auto;
    width: auto;
  }
  .row-cols-lg-1 > * {
    flex: 0 0 auto;
    width: 100%;
  }
  .row-cols-lg-2 > * {
    flex: 0 0 auto;
    width: 50%;
  }
  .row-cols-lg-3 > * {
    flex: 0 0 auto;
    width: 33.3333333333%;
  }
  .row-cols-lg-4 > * {
    flex: 0 0 auto;
    width: 25%;
  }
  .row-cols-lg-5 > * {
    flex: 0 0 auto;
    width: 20%;
  }
  .row-cols-lg-6 > * {
    flex: 0 0 auto;
    width: 16.6666666667%;
  }
}
@media (min-width: 1200px) {
  .col-xl {
    flex: 1 0 0%;
  }
  .row-cols-xl-auto > * {
    flex: 0 0 auto;
    width: auto;
  }
  .row-cols-xl-1 > * {
    flex: 0 0 auto;
    width: 100%;
  }
  .row-cols-xl-2 > * {
    flex: 0 0 auto;
    width: 50%;
  }
  .row-cols-xl-3 > * {
    flex: 0 0 auto;
    width: 33.3333333333%;
  }
  .row-cols-xl-4 > * {
    flex: 0 0 auto;
    width: 25%;
  }
  .row-cols-xl-5 > * {
    flex: 0 0 auto;
    width: 20%;
  }
  .row-cols-xl-6 > * {
    flex: 0 0 auto;
    width: 16.6666666667%;
  }
}
@media (min-width: 1400px) {
  .col-xxl {
    flex: 1 0 0%;
  }
  .row-cols-xxl-auto > * {
    flex: 0 0 auto;
    width: auto;
  }
  .row-cols-xxl-1 > * {
    flex: 0 0 auto;
    width: 100%;
  }
  .row-cols-xxl-2 > * {
    flex: 0 0 auto;
    width: 50%;
  }
  .row-cols-xxl-3 > * {
    flex: 0 0 auto;
    width: 33.3333333333%;
  }
  .row-cols-xxl-4 > * {
    flex: 0 0 auto;
    width: 25%;
  }
  .row-cols-xxl-5 > * {
    flex: 0 0 auto;
    width: 20%;
  }
  .row-cols-xxl-6 > * {
    flex: 0 0 auto;
    width: 16.6666666667%;
  }
}
.col-auto {
  flex: 0 0 auto;
  width: auto;
}

.col-1 {
  flex: 0 0 auto;
  width: 8.33333333%;
}

.col-2 {
  flex: 0 0 auto;
  width: 16.66666667%;
}

.col-3 {
  flex: 0 0 auto;
  width: 25%;
}

.col-4 {
  flex: 0 0 auto;
  width: 33.33333333%;
}

.col-5 {
  flex: 0 0 auto;
  width: 41.66666667%;
}

.col-6 {
  flex: 0 0 auto;
  width: 50%;
}

.col-7 {
  flex: 0 0 auto;
  width: 58.33333333%;
}

.col-8 {
  flex: 0 0 auto;
  width: 66.66666667%;
}

.col-9 {
  flex: 0 0 auto;
  width: 75%;
}

.col-10 {
  flex: 0 0 auto;
  width: 83.33333333%;
}

.col-11 {
  flex: 0 0 auto;
  width: 91.66666667%;
}

.col-12 {
  flex: 0 0 auto;
  width: 100%;
}

.offset-1 {
  margin-left: 8.33333333%;
}

.offset-2 {
  margin-left: 16.66666667%;
}

.offset-3 {
  margin-left: 25%;
}

.offset-4 {
  margin-left: 33.33333333%;
}

.offset-5 {
  margin-left: 41.66666667%;
}

.offset-6 {
  margin-left: 50%;
}

.offset-7 {
  margin-left: 58.33333333%;
}

.offset-8 {
  margin-left: 66.66666667%;
}

.offset-9 {
  margin-left: 75%;
}

.offset-10 {
  margin-left: 83.33333333%;
}

.offset-11 {
  margin-left: 91.66666667%;
}

.g-0,
.gx-0 {
  --bs-gutter-x: 0;
}

.g-0,
.gy-0 {
  --bs-gutter-y: 0;
}

.g-1,
.gx-1 {
  --bs-gutter-x: 0.25rem;
}

.g-1,
.gy-1 {
  --bs-gutter-y: 0.25rem;
}

.g-2,
.gx-2 {
  --bs-gutter-x: 0.5rem;
}

.g-2,
.gy-2 {
  --bs-gutter-y: 0.5rem;
}

.g-3,
.gx-3 {
  --bs-gutter-x: 1rem;
}

.g-3,
.gy-3 {
  --bs-gutter-y: 1rem;
}

.g-4,
.gx-4 {
  --bs-gutter-x: 1.5rem;
}

.g-4,
.gy-4 {
  --bs-gutter-y: 1.5rem;
}

.g-5,
.gx-5 {
  --bs-gutter-x: 3rem;
}

.g-5,
.gy-5 {
  --bs-gutter-y: 3rem;
}

@media (min-width: 576px) {
  .col-sm-auto {
    flex: 0 0 auto;
    width: auto;
  }
  .col-sm-1 {
    flex: 0 0 auto;
    width: 8.33333333%;
  }
  .col-sm-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
  }
  .col-sm-3 {
    flex: 0 0 auto;
    width: 25%;
  }
  .col-sm-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
  }
  .col-sm-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
  }
  .col-sm-6 {
    flex: 0 0 auto;
    width: 50%;
  }
  .col-sm-7 {
    flex: 0 0 auto;
    width: 58.33333333%;
  }
  .col-sm-8 {
    flex: 0 0 auto;
    width: 66.66666667%;
  }
  .col-sm-9 {
    flex: 0 0 auto;
    width: 75%;
  }
  .col-sm-10 {
    flex: 0 0 auto;
    width: 83.33333333%;
  }
  .col-sm-11 {
    flex: 0 0 auto;
    width: 91.66666667%;
  }
  .col-sm-12 {
    flex: 0 0 auto;
    width: 100%;
  }
  .offset-sm-0 {
    margin-left: 0;
  }
  .offset-sm-1 {
    margin-left: 8.33333333%;
  }
  .offset-sm-2 {
    margin-left: 16.66666667%;
  }
  .offset-sm-3 {
    margin-left: 25%;
  }
  .offset-sm-4 {
    margin-left: 33.33333333%;
  }
  .offset-sm-5 {
    margin-left: 41.66666667%;
  }
  .offset-sm-6 {
    margin-left: 50%;
  }
  .offset-sm-7 {
    margin-left: 58.33333333%;
  }
  .offset-sm-8 {
    margin-left: 66.66666667%;
  }
  .offset-sm-9 {
    margin-left: 75%;
  }
  .offset-sm-10 {
    margin-left: 83.33333333%;
  }
  .offset-sm-11 {
    margin-left: 91.66666667%;
  }
  .g-sm-0,
  .gx-sm-0 {
    --bs-gutter-x: 0;
  }
  .g-sm-0,
  .gy-sm-0 {
    --bs-gutter-y: 0;
  }
  .g-sm-1,
  .gx-sm-1 {
    --bs-gutter-x: 0.25rem;
  }
  .g-sm-1,
  .gy-sm-1 {
    --bs-gutter-y: 0.25rem;
  }
  .g-sm-2,
  .gx-sm-2 {
    --bs-gutter-x: 0.5rem;
  }
  .g-sm-2,
  .gy-sm-2 {
    --bs-gutter-y: 0.5rem;
  }
  .g-sm-3,
  .gx-sm-3 {
    --bs-gutter-x: 1rem;
  }
  .g-sm-3,
  .gy-sm-3 {
    --bs-gutter-y: 1rem;
  }
  .g-sm-4,
  .gx-sm-4 {
    --bs-gutter-x: 1.5rem;
  }
  .g-sm-4,
  .gy-sm-4 {
    --bs-gutter-y: 1.5rem;
  }
  .g-sm-5,
  .gx-sm-5 {
    --bs-gutter-x: 3rem;
  }
  .g-sm-5,
  .gy-sm-5 {
    --bs-gutter-y: 3rem;
  }
}
@media (min-width: 768px) {
  .col-md-auto {
    flex: 0 0 auto;
    width: auto;
  }
  .col-md-1 {
    flex: 0 0 auto;
    width: 8.33333333%;
  }
  .col-md-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
  }
  .col-md-3 {
    flex: 0 0 auto;
    width: 25%;
  }
  .col-md-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
  }
  .col-md-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
  }
  .col-md-6 {
    flex: 0 0 auto;
    width: 50%;
  }
  .col-md-7 {
    flex: 0 0 auto;
    width: 58.33333333%;
  }
  .col-md-8 {
    flex: 0 0 auto;
    width: 66.66666667%;
  }
  .col-md-9 {
    flex: 0 0 auto;
    width: 75%;
  }
  .col-md-10 {
    flex: 0 0 auto;
    width: 83.33333333%;
  }
  .col-md-11 {
    flex: 0 0 auto;
    width: 91.66666667%;
  }
  .col-md-12 {
    flex: 0 0 auto;
    width: 100%;
  }
  .offset-md-0 {
    margin-left: 0;
  }
  .offset-md-1 {
    margin-left: 8.33333333%;
  }
  .offset-md-2 {
    margin-left: 16.66666667%;
  }
  .offset-md-3 {
    margin-left: 25%;
  }
  .offset-md-4 {
    margin-left: 33.33333333%;
  }
  .offset-md-5 {
    margin-left: 41.66666667%;
  }
  .offset-md-6 {
    margin-left: 50%;
  }
  .offset-md-7 {
    margin-left: 58.33333333%;
  }
  .offset-md-8 {
    margin-left: 66.66666667%;
  }
  .offset-md-9 {
    margin-left: 75%;
  }
  .offset-md-10 {
    margin-left: 83.33333333%;
  }
  .offset-md-11 {
    margin-left: 91.66666667%;
  }
  .g-md-0,
  .gx-md-0 {
    --bs-gutter-x: 0;
  }
  .g-md-0,
  .gy-md-0 {
    --bs-gutter-y: 0;
  }
  .g-md-1,
  .gx-md-1 {
    --bs-gutter-x: 0.25rem;
  }
  .g-md-1,
  .gy-md-1 {
    --bs-gutter-y: 0.25rem;
  }
  .g-md-2,
  .gx-md-2 {
    --bs-gutter-x: 0.5rem;
  }
  .g-md-2,
  .gy-md-2 {
    --bs-gutter-y: 0.5rem;
  }
  .g-md-3,
  .gx-md-3 {
    --bs-gutter-x: 1rem;
  }
  .g-md-3,
  .gy-md-3 {
    --bs-gutter-y: 1rem;
  }
  .g-md-4,
  .gx-md-4 {
    --bs-gutter-x: 1.5rem;
  }
  .g-md-4,
  .gy-md-4 {
    --bs-gutter-y: 1.5rem;
  }
  .g-md-5,
  .gx-md-5 {
    --bs-gutter-x: 3rem;
  }
  .g-md-5,
  .gy-md-5 {
    --bs-gutter-y: 3rem;
  }
}
@media (min-width: 992px) {
  .col-lg-auto {
    flex: 0 0 auto;
    width: auto;
  }
  .col-lg-1 {
    flex: 0 0 auto;
    width: 8.33333333%;
  }
  .col-lg-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
  }
  .col-lg-3 {
    flex: 0 0 auto;
    width: 25%;
  }
  .col-lg-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
  }
  .col-lg-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
  }
  .col-lg-6 {
    flex: 0 0 auto;
    width: 50%;
  }
  .col-lg-7 {
    flex: 0 0 auto;
    width: 58.33333333%;
  }
  .col-lg-8 {
    flex: 0 0 auto;
    width: 66.66666667%;
  }
  .col-lg-9 {
    flex: 0 0 auto;
    width: 75%;
  }
  .col-lg-10 {
    flex: 0 0 auto;
    width: 83.33333333%;
  }
  .col-lg-11 {
    flex: 0 0 auto;
    width: 91.66666667%;
  }
  .col-lg-12 {
    flex: 0 0 auto;
    width: 100%;
  }
  .offset-lg-0 {
    margin-left: 0;
  }
  .offset-lg-1 {
    margin-left: 8.33333333%;
  }
  .offset-lg-2 {
    margin-left: 16.66666667%;
  }
  .offset-lg-3 {
    margin-left: 25%;
  }
  .offset-lg-4 {
    margin-left: 33.33333333%;
  }
  .offset-lg-5 {
    margin-left: 41.66666667%;
  }
  .offset-lg-6 {
    margin-left: 50%;
  }
  .offset-lg-7 {
    margin-left: 58.33333333%;
  }
  .offset-lg-8 {
    margin-left: 66.66666667%;
  }
  .offset-lg-9 {
    margin-left: 75%;
  }
  .offset-lg-10 {
    margin-left: 83.33333333%;
  }
  .offset-lg-11 {
    margin-left: 91.66666667%;
  }
  .g-lg-0,
  .gx-lg-0 {
    --bs-gutter-x: 0;
  }
  .g-lg-0,
  .gy-lg-0 {
    --bs-gutter-y: 0;
  }
  .g-lg-1,
  .gx-lg-1 {
    --bs-gutter-x: 0.25rem;
  }
  .g-lg-1,
  .gy-lg-1 {
    --bs-gutter-y: 0.25rem;
  }
  .g-lg-2,
  .gx-lg-2 {
    --bs-gutter-x: 0.5rem;
  }
  .g-lg-2,
  .gy-lg-2 {
    --bs-gutter-y: 0.5rem;
  }
  .g-lg-3,
  .gx-lg-3 {
    --bs-gutter-x: 1rem;
  }
  .g-lg-3,
  .gy-lg-3 {
    --bs-gutter-y: 1rem;
  }
  .g-lg-4,
  .gx-lg-4 {
    --bs-gutter-x: 1.5rem;
  }
  .g-lg-4,
  .gy-lg-4 {
    --bs-gutter-y: 1.5rem;
  }
  .g-lg-5,
  .gx-lg-5 {
    --bs-gutter-x: 3rem;
  }
  .g-lg-5,
  .gy-lg-5 {
    --bs-gutter-y: 3rem;
  }
}
@media (min-width: 1200px) {
  .col-xl-auto {
    flex: 0 0 auto;
    width: auto;
  }
  .col-xl-1 {
    flex: 0 0 auto;
    width: 8.33333333%;
  }
  .col-xl-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
  }
  .col-xl-3 {
    flex: 0 0 auto;
    width: 25%;
  }
  .col-xl-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
  }
  .col-xl-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
  }
  .col-xl-6 {
    flex: 0 0 auto;
    width: 50%;
  }
  .col-xl-7 {
    flex: 0 0 auto;
    width: 58.33333333%;
  }
  .col-xl-8 {
    flex: 0 0 auto;
    width: 66.66666667%;
  }
  .col-xl-9 {
    flex: 0 0 auto;
    width: 75%;
  }
  .col-xl-10 {
    flex: 0 0 auto;
    width: 83.33333333%;
  }
  .col-xl-11 {
    flex: 0 0 auto;
    width: 91.66666667%;
  }
  .col-xl-12 {
    flex: 0 0 auto;
    width: 100%;
  }
  .offset-xl-0 {
    margin-left: 0;
  }
  .offset-xl-1 {
    margin-left: 8.33333333%;
  }
  .offset-xl-2 {
    margin-left: 16.66666667%;
  }
  .offset-xl-3 {
    margin-left: 25%;
  }
  .offset-xl-4 {
    margin-left: 33.33333333%;
  }
  .offset-xl-5 {
    margin-left: 41.66666667%;
  }
  .offset-xl-6 {
    margin-left: 50%;
  }
  .offset-xl-7 {
    margin-left: 58.33333333%;
  }
  .offset-xl-8 {
    margin-left: 66.66666667%;
  }
  .offset-xl-9 {
    margin-left: 75%;
  }
  .offset-xl-10 {
    margin-left: 83.33333333%;
  }
  .offset-xl-11 {
    margin-left: 91.66666667%;
  }
  .g-xl-0,
  .gx-xl-0 {
    --bs-gutter-x: 0;
  }
  .g-xl-0,
  .gy-xl-0 {
    --bs-gutter-y: 0;
  }
  .g-xl-1,
  .gx-xl-1 {
    --bs-gutter-x: 0.25rem;
  }
  .g-xl-1,
  .gy-xl-1 {
    --bs-gutter-y: 0.25rem;
  }
  .g-xl-2,
  .gx-xl-2 {
    --bs-gutter-x: 0.5rem;
  }
  .g-xl-2,
  .gy-xl-2 {
    --bs-gutter-y: 0.5rem;
  }
  .g-xl-3,
  .gx-xl-3 {
    --bs-gutter-x: 1rem;
  }
  .g-xl-3,
  .gy-xl-3 {
    --bs-gutter-y: 1rem;
  }
  .g-xl-4,
  .gx-xl-4 {
    --bs-gutter-x: 1.5rem;
  }
  .g-xl-4,
  .gy-xl-4 {
    --bs-gutter-y: 1.5rem;
  }
  .g-xl-5,
  .gx-xl-5 {
    --bs-gutter-x: 3rem;
  }
  .g-xl-5,
  .gy-xl-5 {
    --bs-gutter-y: 3rem;
  }
}
@media (min-width: 1400px) {
  .col-xxl-auto {
    flex: 0 0 auto;
    width: auto;
  }
  .col-xxl-1 {
    flex: 0 0 auto;
    width: 8.33333333%;
  }
  .col-xxl-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
  }
  .col-xxl-3 {
    flex: 0 0 auto;
    width: 25%;
  }
  .col-xxl-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
  }
  .col-xxl-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
  }
  .col-xxl-6 {
    flex: 0 0 auto;
    width: 50%;
  }
  .col-xxl-7 {
    flex: 0 0 auto;
    width: 58.33333333%;
  }
  .col-xxl-8 {
    flex: 0 0 auto;
    width: 66.66666667%;
  }
  .col-xxl-9 {
    flex: 0 0 auto;
    width: 75%;
  }
  .col-xxl-10 {
    flex: 0 0 auto;
    width: 83.33333333%;
  }
  .col-xxl-11 {
    flex: 0 0 auto;
    width: 91.66666667%;
  }
  .col-xxl-12 {
    flex: 0 0 auto;
    width: 100%;
  }
  .offset-xxl-0 {
    margin-left: 0;
  }
  .offset-xxl-1 {
    margin-left: 8.33333333%;
  }
  .offset-xxl-2 {
    margin-left: 16.66666667%;
  }
  .offset-xxl-3 {
    margin-left: 25%;
  }
  .offset-xxl-4 {
    margin-left: 33.33333333%;
  }
  .offset-xxl-5 {
    margin-left: 41.66666667%;
  }
  .offset-xxl-6 {
    margin-left: 50%;
  }
  .offset-xxl-7 {
    margin-left: 58.33333333%;
  }
  .offset-xxl-8 {
    margin-left: 66.66666667%;
  }
  .offset-xxl-9 {
    margin-left: 75%;
  }
  .offset-xxl-10 {
    margin-left: 83.33333333%;
  }
  .offset-xxl-11 {
    margin-left: 91.66666667%;
  }
  .g-xxl-0,
  .gx-xxl-0 {
    --bs-gutter-x: 0;
  }
  .g-xxl-0,
  .gy-xxl-0 {
    --bs-gutter-y: 0;
  }
  .g-xxl-1,
  .gx-xxl-1 {
    --bs-gutter-x: 0.25rem;
  }
  .g-xxl-1,
  .gy-xxl-1 {
    --bs-gutter-y: 0.25rem;
  }
  .g-xxl-2,
  .gx-xxl-2 {
    --bs-gutter-x: 0.5rem;
  }
  .g-xxl-2,
  .gy-xxl-2 {
    --bs-gutter-y: 0.5rem;
  }
  .g-xxl-3,
  .gx-xxl-3 {
    --bs-gutter-x: 1rem;
  }
  .g-xxl-3,
  .gy-xxl-3 {
    --bs-gutter-y: 1rem;
  }
  .g-xxl-4,
  .gx-xxl-4 {
    --bs-gutter-x: 1.5rem;
  }
  .g-xxl-4,
  .gy-xxl-4 {
    --bs-gutter-y: 1.5rem;
  }
  .g-xxl-5,
  .gx-xxl-5 {
    --bs-gutter-x: 3rem;
  }
  .g-xxl-5,
  .gy-xxl-5 {
    --bs-gutter-y: 3rem;
  }
}
.table {
  --bs-table-bg: transparent;
  --bs-table-accent-bg: transparent;
  --bs-table-striped-color: #212529;
  --bs-table-striped-bg: rgba(0, 0, 0, 0.05);
  --bs-table-active-color: #212529;
  --bs-table-active-bg: rgba(0, 0, 0, 0.1);
  --bs-table-hover-color: #212529;
  --bs-table-hover-bg: rgba(0, 0, 0, 0.075);
  width: 100%;
  margin-bottom: 1rem;
  color: #212529;
  vertical-align: top;
  border-color: #dee2e6;
}
.table > :not(caption) > * > * {
  padding: 0.5rem 0.5rem;
  background-color: var(--bs-table-bg);
  border-bottom-width: 1px;
  box-shadow: inset 0 0 0 9999px var(--bs-table-accent-bg);
}
.table > tbody {
  vertical-align: inherit;
}
.table > thead {
  vertical-align: bottom;
}
.table > :not(:last-child) > :last-child > * {
  border-bottom-color: currentColor;
}

.caption-top {
  caption-side: top;
}

.table-sm > :not(caption) > * > * {
  padding: 0.25rem 0.25rem;
}

.table-bordered > :not(caption) > * {
  border-width: 1px 0;
}
.table-bordered > :not(caption) > * > * {
  border-width: 0 1px;
}

.table-borderless > :not(caption) > * > * {
  border-bottom-width: 0;
}

.table-striped > tbody > tr:nth-of-type(odd) {
  --bs-table-accent-bg: var(--bs-table-striped-bg);
  color: var(--bs-table-striped-color);
}

.table-active {
  --bs-table-accent-bg: var(--bs-table-active-bg);
  color: var(--bs-table-active-color);
}

.table-hover > tbody > tr:hover {
  --bs-table-accent-bg: var(--bs-table-hover-bg);
  color: var(--bs-table-hover-color);
}

.table-primary {
  --bs-table-bg: rgb(206.6, 226, 254.6);
  --bs-table-striped-bg: rgb(196.27, 214.7, 241.87);
  --bs-table-striped-color: #000;
  --bs-table-active-bg: rgb(185.94, 203.4, 229.14);
  --bs-table-active-color: #000;
  --bs-table-hover-bg: rgb(191.105, 209.05, 235.505);
  --bs-table-hover-color: #000;
  color: #000;
  border-color: rgb(185.94, 203.4, 229.14);
}

.table-secondary {
  --bs-table-bg: rgb(225.6, 227.4, 229);
  --bs-table-striped-bg: rgb(214.32, 216.03, 217.55);
  --bs-table-striped-color: #000;
  --bs-table-active-bg: rgb(203.04, 204.66, 206.1);
  --bs-table-active-color: #000;
  --bs-table-hover-bg: rgb(208.68, 210.345, 211.825);
  --bs-table-hover-color: #000;
  color: #000;
  border-color: rgb(203.04, 204.66, 206.1);
}

.table-success {
  --bs-table-bg: rgb(209, 231, 220.8);
  --bs-table-striped-bg: rgb(198.55, 219.45, 209.76);
  --bs-table-striped-color: #000;
  --bs-table-active-bg: rgb(188.1, 207.9, 198.72);
  --bs-table-active-color: #000;
  --bs-table-hover-bg: rgb(193.325, 213.675, 204.24);
  --bs-table-hover-color: #000;
  color: #000;
  border-color: rgb(188.1, 207.9, 198.72);
}

.table-info {
  --bs-table-bg: rgb(206.6, 244.4, 252);
  --bs-table-striped-bg: rgb(196.27, 232.18, 239.4);
  --bs-table-striped-color: #000;
  --bs-table-active-bg: rgb(185.94, 219.96, 226.8);
  --bs-table-active-color: #000;
  --bs-table-hover-bg: rgb(191.105, 226.07, 233.1);
  --bs-table-hover-color: #000;
  color: #000;
  border-color: rgb(185.94, 219.96, 226.8);
}

.table-warning {
  --bs-table-bg: rgb(255, 242.6, 205.4);
  --bs-table-striped-bg: rgb(242.25, 230.47, 195.13);
  --bs-table-striped-color: #000;
  --bs-table-active-bg: rgb(229.5, 218.34, 184.86);
  --bs-table-active-color: #000;
  --bs-table-hover-bg: rgb(235.875, 224.405, 189.995);
  --bs-table-hover-color: #000;
  color: #000;
  border-color: rgb(229.5, 218.34, 184.86);
}

.table-danger {
  --bs-table-bg: rgb(248, 214.6, 217.8);
  --bs-table-striped-bg: rgb(235.6, 203.87, 206.91);
  --bs-table-striped-color: #000;
  --bs-table-active-bg: rgb(223.2, 193.14, 196.02);
  --bs-table-active-color: #000;
  --bs-table-hover-bg: rgb(229.4, 198.505, 201.465);
  --bs-table-hover-color: #000;
  color: #000;
  border-color: rgb(223.2, 193.14, 196.02);
}

.table-light {
  --bs-table-bg: #f8f9fa;
  --bs-table-striped-bg: rgb(235.6, 236.55, 237.5);
  --bs-table-striped-color: #000;
  --bs-table-active-bg: rgb(223.2, 224.1, 225);
  --bs-table-active-color: #000;
  --bs-table-hover-bg: rgb(229.4, 230.325, 231.25);
  --bs-table-hover-color: #000;
  color: #000;
  border-color: rgb(223.2, 224.1, 225);
}

.table-dark {
  --bs-table-bg: #212529;
  --bs-table-striped-bg: rgb(44.1, 47.9, 51.7);
  --bs-table-striped-color: #fff;
  --bs-table-active-bg: rgb(55.2, 58.8, 62.4);
  --bs-table-active-color: #fff;
  --bs-table-hover-bg: rgb(49.65, 53.35, 57.05);
  --bs-table-hover-color: #fff;
  color: #fff;
  border-color: rgb(55.2, 58.8, 62.4);
}

.table-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

@media (max-width: 575.98px) {
  .table-responsive-sm {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}
@media (max-width: 767.98px) {
  .table-responsive-md {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}
@media (max-width: 991.98px) {
  .table-responsive-lg {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}
@media (max-width: 1199.98px) {
  .table-responsive-xl {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}
@media (max-width: 1399.98px) {
  .table-responsive-xxl {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}
.form-label {
  margin-bottom: 0.5rem;
}

.col-form-label {
  padding-top: calc(0.375rem + 1px);
  padding-bottom: calc(0.375rem + 1px);
  margin-bottom: 0;
  font-size: inherit;
  line-height: 1.5;
}

.col-form-label-lg {
  padding-top: calc(0.5rem + 1px);
  padding-bottom: calc(0.5rem + 1px);
  font-size: 1.25rem;
}

.col-form-label-sm {
  padding-top: calc(0.25rem + 1px);
  padding-bottom: calc(0.25rem + 1px);
  font-size: 0.875rem;
}

.form-text {
  margin-top: 0.25rem;
  font-size: 0.875em;
  color: #6c757d;
}

.form-control {
  display: block;
  width: 100%;
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #212529;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ced4da;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  border-radius: 0.25rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-control {
    transition: none;
  }
}
.form-control[type=file] {
  overflow: hidden;
}
.form-control[type=file]:not(:disabled):not([readonly]) {
  cursor: pointer;
}
.form-control:focus {
  color: #212529;
  background-color: #fff;
  border-color: rgb(134, 182.5, 254);
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}
.form-control::-webkit-date-and-time-value {
  height: 1.5em;
}
.form-control::-moz-placeholder {
  color: #6c757d;
  opacity: 1;
}
.form-control::placeholder {
  color: #6c757d;
  opacity: 1;
}
.form-control:disabled, .form-control[readonly] {
  background-color: #e9ecef;
  opacity: 1;
}
.form-control::file-selector-button {
  padding: 0.375rem 0.75rem;
  margin: -0.375rem -0.75rem;
  margin-inline-end: 0.75rem;
  color: #212529;
  background-color: #e9ecef;
  pointer-events: none;
  border-color: inherit;
  border-style: solid;
  border-width: 0;
  border-inline-end-width: 1px;
  border-radius: 0;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-control::file-selector-button {
    transition: none;
  }
}
.form-control:hover:not(:disabled):not([readonly])::file-selector-button {
  background-color: rgb(221.35, 224.2, 227.05);
}
.form-control::-webkit-file-upload-button {
  padding: 0.375rem 0.75rem;
  margin: -0.375rem -0.75rem;
  margin-inline-end: 0.75rem;
  color: #212529;
  background-color: #e9ecef;
  pointer-events: none;
  border-color: inherit;
  border-style: solid;
  border-width: 0;
  border-inline-end-width: 1px;
  border-radius: 0;
  -webkit-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-control::-webkit-file-upload-button {
    -webkit-transition: none;
    transition: none;
  }
}
.form-control:hover:not(:disabled):not([readonly])::-webkit-file-upload-button {
  background-color: rgb(221.35, 224.2, 227.05);
}

.form-control-plaintext {
  display: block;
  width: 100%;
  padding: 0.375rem 0;
  margin-bottom: 0;
  line-height: 1.5;
  color: #212529;
  background-color: transparent;
  border: solid transparent;
  border-width: 1px 0;
}
.form-control-plaintext.form-control-sm, .form-control-plaintext.form-control-lg {
  padding-right: 0;
  padding-left: 0;
}

.form-control-sm {
  min-height: calc(1.5em + (0.5rem + 2px));
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  border-radius: 0.2rem;
}
.form-control-sm::file-selector-button {
  padding: 0.25rem 0.5rem;
  margin: -0.25rem -0.5rem;
  margin-inline-end: 0.5rem;
}
.form-control-sm::-webkit-file-upload-button {
  padding: 0.25rem 0.5rem;
  margin: -0.25rem -0.5rem;
  margin-inline-end: 0.5rem;
}

.form-control-lg {
  min-height: calc(1.5em + (1rem + 2px));
  padding: 0.5rem 1rem;
  font-size: 1.25rem;
  border-radius: 0.3rem;
}
.form-control-lg::file-selector-button {
  padding: 0.5rem 1rem;
  margin: -0.5rem -1rem;
  margin-inline-end: 1rem;
}
.form-control-lg::-webkit-file-upload-button {
  padding: 0.5rem 1rem;
  margin: -0.5rem -1rem;
  margin-inline-end: 1rem;
}

textarea.form-control {
  min-height: calc(1.5em + (0.75rem + 2px));
}
textarea.form-control-sm {
  min-height: calc(1.5em + (0.5rem + 2px));
}
textarea.form-control-lg {
  min-height: calc(1.5em + (1rem + 2px));
}

.form-control-color {
  max-width: 3rem;
  height: auto;
  padding: 0.375rem;
}
.form-control-color:not(:disabled):not([readonly]) {
  cursor: pointer;
}
.form-control-color::-moz-color-swatch {
  height: 1.5em;
  border-radius: 0.25rem;
}
.form-control-color::-webkit-color-swatch {
  height: 1.5em;
  border-radius: 0.25rem;
}

.form-select {
  display: block;
  width: 100%;
  padding: 0.375rem 2.25rem 0.375rem 0.75rem;
  -moz-padding-start: calc(0.75rem - 3px);
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #212529;
  background-color: #fff;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 16px 12px;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}
@media (prefers-reduced-motion: reduce) {
  .form-select {
    transition: none;
  }
}
.form-select:focus {
  border-color: rgb(134, 182.5, 254);
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}
.form-select[multiple], .form-select[size]:not([size="1"]) {
  padding-right: 0.75rem;
  background-image: none;
}
.form-select:disabled {
  background-color: #e9ecef;
}
.form-select:-moz-focusring {
  color: transparent;
  text-shadow: 0 0 0 #212529;
}

.form-select-sm {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  padding-left: 0.5rem;
  font-size: 0.875rem;
}

.form-select-lg {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  padding-left: 1rem;
  font-size: 1.25rem;
}

.form-check {
  display: block;
  min-height: 1.5rem;
  padding-left: 1.5em;
  margin-bottom: 0.125rem;
}
.form-check .form-check-input {
  float: left;
  margin-left: -1.5em;
}

.form-check-input {
  width: 1em;
  height: 1em;
  margin-top: 0.25em;
  vertical-align: top;
  background-color: #fff;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  border: 1px solid rgba(0, 0, 0, 0.25);
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  -webkit-print-color-adjust: exact;
          color-adjust: exact;
}
.form-check-input[type=checkbox] {
  border-radius: 0.25em;
}
.form-check-input[type=radio] {
  border-radius: 50%;
}
.form-check-input:active {
  filter: brightness(90%);
}
.form-check-input:focus {
  border-color: rgb(134, 182.5, 254);
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}
.form-check-input:checked {
  background-color: #0d6efd;
  border-color: #0d6efd;
}
.form-check-input:checked[type=checkbox] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/%3e%3c/svg%3e");
}
.form-check-input:checked[type=radio] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%23fff'/%3e%3c/svg%3e");
}
.form-check-input[type=checkbox]:indeterminate {
  background-color: #0d6efd;
  border-color: #0d6efd;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/%3e%3c/svg%3e");
}
.form-check-input:disabled {
  pointer-events: none;
  filter: none;
  opacity: 0.5;
}
.form-check-input[disabled] ~ .form-check-label, .form-check-input:disabled ~ .form-check-label {
  opacity: 0.5;
}

.form-switch {
  padding-left: 2.5em;
}
.form-switch .form-check-input {
  width: 2em;
  margin-left: -2.5em;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%280, 0, 0, 0.25%29'/%3e%3c/svg%3e");
  background-position: left center;
  border-radius: 2em;
  transition: background-position 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-switch .form-check-input {
    transition: none;
  }
}
.form-switch .form-check-input:focus {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgb%28134, 182.5, 254%29'/%3e%3c/svg%3e");
}
.form-switch .form-check-input:checked {
  background-position: right center;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e");
}

.form-check-inline {
  display: inline-block;
  margin-right: 1rem;
}

.btn-check {
  position: absolute;
  clip: rect(0, 0, 0, 0);
  pointer-events: none;
}
.btn-check[disabled] + .btn, .btn-check:disabled + .btn {
  pointer-events: none;
  filter: none;
  opacity: 0.65;
}

.form-range {
  width: 100%;
  height: 1.5rem;
  padding: 0;
  background-color: transparent;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}
.form-range:focus {
  outline: 0;
}
.form-range:focus::-webkit-slider-thumb {
  box-shadow: 0 0 0 1px #fff, 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}
.form-range:focus::-moz-range-thumb {
  box-shadow: 0 0 0 1px #fff, 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}
.form-range::-moz-focus-outer {
  border: 0;
}
.form-range::-webkit-slider-thumb {
  width: 1rem;
  height: 1rem;
  margin-top: -0.25rem;
  background-color: #0d6efd;
  border: 0;
  border-radius: 1rem;
  -webkit-transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  -webkit-appearance: none;
          appearance: none;
}
@media (prefers-reduced-motion: reduce) {
  .form-range::-webkit-slider-thumb {
    -webkit-transition: none;
    transition: none;
  }
}
.form-range::-webkit-slider-thumb:active {
  background-color: rgb(182.4, 211.5, 254.4);
}
.form-range::-webkit-slider-runnable-track {
  width: 100%;
  height: 0.5rem;
  color: transparent;
  cursor: pointer;
  background-color: #dee2e6;
  border-color: transparent;
  border-radius: 1rem;
}
.form-range::-moz-range-thumb {
  width: 1rem;
  height: 1rem;
  background-color: #0d6efd;
  border: 0;
  border-radius: 1rem;
  -moz-transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  -moz-appearance: none;
       appearance: none;
}
@media (prefers-reduced-motion: reduce) {
  .form-range::-moz-range-thumb {
    -moz-transition: none;
    transition: none;
  }
}
.form-range::-moz-range-thumb:active {
  background-color: rgb(182.4, 211.5, 254.4);
}
.form-range::-moz-range-track {
  width: 100%;
  height: 0.5rem;
  color: transparent;
  cursor: pointer;
  background-color: #dee2e6;
  border-color: transparent;
  border-radius: 1rem;
}
.form-range:disabled {
  pointer-events: none;
}
.form-range:disabled::-webkit-slider-thumb {
  background-color: #adb5bd;
}
.form-range:disabled::-moz-range-thumb {
  background-color: #adb5bd;
}

.form-floating {
  position: relative;
}
.form-floating > .form-control,
.form-floating > .form-select {
  height: calc(3.5rem + 2px);
  line-height: 1.25;
}
.form-floating > label {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  padding: 1rem 0.75rem;
  pointer-events: none;
  border: 1px solid transparent;
  transform-origin: 0 0;
  transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-floating > label {
    transition: none;
  }
}
.form-floating > .form-control {
  padding: 1rem 0.75rem;
}
.form-floating > .form-control::-moz-placeholder {
  color: transparent;
}
.form-floating > .form-control::placeholder {
  color: transparent;
}
.form-floating > .form-control:not(:-moz-placeholder) {
  padding-top: 1.625rem;
  padding-bottom: 0.625rem;
}
.form-floating > .form-control:focus, .form-floating > .form-control:not(:placeholder-shown) {
  padding-top: 1.625rem;
  padding-bottom: 0.625rem;
}
.form-floating > .form-control:-webkit-autofill {
  padding-top: 1.625rem;
  padding-bottom: 0.625rem;
}
.form-floating > .form-select {
  padding-top: 1.625rem;
  padding-bottom: 0.625rem;
}
.form-floating > .form-control:not(:-moz-placeholder) ~ label {
  opacity: 0.65;
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}
.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label,
.form-floating > .form-select ~ label {
  opacity: 0.65;
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}
.form-floating > .form-control:-webkit-autofill ~ label {
  opacity: 0.65;
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}

.input-group {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: stretch;
  width: 100%;
}
.input-group > .form-control,
.input-group > .form-select {
  position: relative;
  flex: 1 1 auto;
  width: 1%;
  min-width: 0;
}
.input-group > .form-control:focus,
.input-group > .form-select:focus {
  z-index: 3;
}
.input-group .btn {
  position: relative;
  z-index: 2;
}
.input-group .btn:focus {
  z-index: 3;
}

.input-group-text {
  display: flex;
  align-items: center;
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #212529;
  text-align: center;
  white-space: nowrap;
  background-color: #e9ecef;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
}

.input-group-lg > .form-control,
.input-group-lg > .form-select,
.input-group-lg > .input-group-text,
.input-group-lg > .btn {
  padding: 0.5rem 1rem;
  font-size: 1.25rem;
  border-radius: 0.3rem;
}

.input-group-sm > .form-control,
.input-group-sm > .form-select,
.input-group-sm > .input-group-text,
.input-group-sm > .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  border-radius: 0.2rem;
}

.input-group-lg > .form-select,
.input-group-sm > .form-select {
  padding-right: 3rem;
}

.input-group:not(.has-validation) > :not(:last-child):not(.dropdown-toggle):not(.dropdown-menu),
.input-group:not(.has-validation) > .dropdown-toggle:nth-last-child(n+3) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.input-group.has-validation > :nth-last-child(n+3):not(.dropdown-toggle):not(.dropdown-menu),
.input-group.has-validation > .dropdown-toggle:nth-last-child(n+4) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.input-group > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
  margin-left: -1px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.valid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875em;
  color: #198754;
}

.valid-tooltip {
  position: absolute;
  top: 100%;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: 0.25rem 0.5rem;
  margin-top: 0.1rem;
  font-size: 0.875rem;
  color: #fff;
  background-color: rgba(25, 135, 84, 0.9);
  border-radius: 0.25rem;
}

.was-validated :valid ~ .valid-feedback,
.was-validated :valid ~ .valid-tooltip,
.is-valid ~ .valid-feedback,
.is-valid ~ .valid-tooltip {
  display: block;
}

.was-validated .form-control:valid, .form-control.is-valid {
  border-color: #198754;
  padding-right: calc(1.5em + 0.75rem);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 0.1875rem) center;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}
.was-validated .form-control:valid:focus, .form-control.is-valid:focus {
  border-color: #198754;
  box-shadow: 0 0 0 0.25rem rgba(25, 135, 84, 0.25);
}

.was-validated textarea.form-control:valid, textarea.form-control.is-valid {
  padding-right: calc(1.5em + 0.75rem);
  background-position: top calc(0.375em + 0.1875rem) right calc(0.375em + 0.1875rem);
}

.was-validated .form-select:valid, .form-select.is-valid {
  border-color: #198754;
}
.was-validated .form-select:valid:not([multiple]):not([size]), .was-validated .form-select:valid:not([multiple])[size="1"], .form-select.is-valid:not([multiple]):not([size]), .form-select.is-valid:not([multiple])[size="1"] {
  padding-right: 4.125rem;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e"), url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
  background-position: right 0.75rem center, center right 2.25rem;
  background-size: 16px 12px, calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}
.was-validated .form-select:valid:focus, .form-select.is-valid:focus {
  border-color: #198754;
  box-shadow: 0 0 0 0.25rem rgba(25, 135, 84, 0.25);
}

.was-validated .form-check-input:valid, .form-check-input.is-valid {
  border-color: #198754;
}
.was-validated .form-check-input:valid:checked, .form-check-input.is-valid:checked {
  background-color: #198754;
}
.was-validated .form-check-input:valid:focus, .form-check-input.is-valid:focus {
  box-shadow: 0 0 0 0.25rem rgba(25, 135, 84, 0.25);
}
.was-validated .form-check-input:valid ~ .form-check-label, .form-check-input.is-valid ~ .form-check-label {
  color: #198754;
}

.form-check-inline .form-check-input ~ .valid-feedback {
  margin-left: 0.5em;
}

.was-validated .input-group .form-control:valid, .input-group .form-control.is-valid,
.was-validated .input-group .form-select:valid,
.input-group .form-select.is-valid {
  z-index: 1;
}
.was-validated .input-group .form-control:valid:focus, .input-group .form-control.is-valid:focus,
.was-validated .input-group .form-select:valid:focus,
.input-group .form-select.is-valid:focus {
  z-index: 3;
}

.invalid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875em;
  color: #dc3545;
}

.invalid-tooltip {
  position: absolute;
  top: 100%;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: 0.25rem 0.5rem;
  margin-top: 0.1rem;
  font-size: 0.875rem;
  color: #fff;
  background-color: rgba(220, 53, 69, 0.9);
  border-radius: 0.25rem;
}

.was-validated :invalid ~ .invalid-feedback,
.was-validated :invalid ~ .invalid-tooltip,
.is-invalid ~ .invalid-feedback,
.is-invalid ~ .invalid-tooltip {
  display: block;
}

.was-validated .form-control:invalid, .form-control.is-invalid {
  border-color: #dc3545;
  padding-right: calc(1.5em + 0.75rem);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 0.1875rem) center;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}
.was-validated .form-control:invalid:focus, .form-control.is-invalid:focus {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
}

.was-validated textarea.form-control:invalid, textarea.form-control.is-invalid {
  padding-right: calc(1.5em + 0.75rem);
  background-position: top calc(0.375em + 0.1875rem) right calc(0.375em + 0.1875rem);
}

.was-validated .form-select:invalid, .form-select.is-invalid {
  border-color: #dc3545;
}
.was-validated .form-select:invalid:not([multiple]):not([size]), .was-validated .form-select:invalid:not([multiple])[size="1"], .form-select.is-invalid:not([multiple]):not([size]), .form-select.is-invalid:not([multiple])[size="1"] {
  padding-right: 4.125rem;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e"), url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
  background-position: right 0.75rem center, center right 2.25rem;
  background-size: 16px 12px, calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}
.was-validated .form-select:invalid:focus, .form-select.is-invalid:focus {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
}

.was-validated .form-check-input:invalid, .form-check-input.is-invalid {
  border-color: #dc3545;
}
.was-validated .form-check-input:invalid:checked, .form-check-input.is-invalid:checked {
  background-color: #dc3545;
}
.was-validated .form-check-input:invalid:focus, .form-check-input.is-invalid:focus {
  box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
}
.was-validated .form-check-input:invalid ~ .form-check-label, .form-check-input.is-invalid ~ .form-check-label {
  color: #dc3545;
}

.form-check-inline .form-check-input ~ .invalid-feedback {
  margin-left: 0.5em;
}

.was-validated .input-group .form-control:invalid, .input-group .form-control.is-invalid,
.was-validated .input-group .form-select:invalid,
.input-group .form-select.is-invalid {
  z-index: 2;
}
.was-validated .input-group .form-control:invalid:focus, .input-group .form-control.is-invalid:focus,
.was-validated .input-group .form-select:invalid:focus,
.input-group .form-select.is-invalid:focus {
  z-index: 3;
}

.btn {
  display: inline-block;
  font-weight: 400;
  line-height: 1.5;
  color: #212529;
  text-align: center;
  text-decoration: none;
  vertical-align: middle;
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  background-color: transparent;
  border: 1px solid transparent;
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  border-radius: 0.25rem;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .btn {
    transition: none;
  }
}
.btn:hover {
  color: #212529;
}
.btn-check:focus + .btn, .btn:focus {
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}
.btn:disabled, .btn.disabled, fieldset:disabled .btn {
  pointer-events: none;
  opacity: 0.65;
}

.btn-primary {
  color: #fff;
  background-color: #0d6efd;
  border-color: #0d6efd;
}
.btn-primary:hover {
  color: #fff;
  background-color: rgb(11.05, 93.5, 215.05);
  border-color: rgb(10.4, 88, 202.4);
}
.btn-check:focus + .btn-primary, .btn-primary:focus {
  color: #fff;
  background-color: rgb(11.05, 93.5, 215.05);
  border-color: rgb(10.4, 88, 202.4);
  box-shadow: 0 0 0 0.25rem rgba(49.3, 131.75, 253.3, 0.5);
}
.btn-check:checked + .btn-primary, .btn-check:active + .btn-primary, .btn-primary:active, .btn-primary.active, .show > .btn-primary.dropdown-toggle {
  color: #fff;
  background-color: rgb(10.4, 88, 202.4);
  border-color: rgb(9.75, 82.5, 189.75);
}
.btn-check:checked + .btn-primary:focus, .btn-check:active + .btn-primary:focus, .btn-primary:active:focus, .btn-primary.active:focus, .show > .btn-primary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.25rem rgba(49.3, 131.75, 253.3, 0.5);
}
.btn-primary:disabled, .btn-primary.disabled {
  color: #fff;
  background-color: #0d6efd;
  border-color: #0d6efd;
}

.btn-secondary {
  color: #fff;
  background-color: #6c757d;
  border-color: #6c757d;
}
.btn-secondary:hover {
  color: #fff;
  background-color: rgb(91.8, 99.45, 106.25);
  border-color: rgb(86.4, 93.6, 100);
}
.btn-check:focus + .btn-secondary, .btn-secondary:focus {
  color: #fff;
  background-color: rgb(91.8, 99.45, 106.25);
  border-color: rgb(86.4, 93.6, 100);
  box-shadow: 0 0 0 0.25rem rgba(130.05, 137.7, 144.5, 0.5);
}
.btn-check:checked + .btn-secondary, .btn-check:active + .btn-secondary, .btn-secondary:active, .btn-secondary.active, .show > .btn-secondary.dropdown-toggle {
  color: #fff;
  background-color: rgb(86.4, 93.6, 100);
  border-color: rgb(81, 87.75, 93.75);
}
.btn-check:checked + .btn-secondary:focus, .btn-check:active + .btn-secondary:focus, .btn-secondary:active:focus, .btn-secondary.active:focus, .show > .btn-secondary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.25rem rgba(130.05, 137.7, 144.5, 0.5);
}
.btn-secondary:disabled, .btn-secondary.disabled {
  color: #fff;
  background-color: #6c757d;
  border-color: #6c757d;
}

.btn-success {
  color: #fff;
  background-color: #198754;
  border-color: #198754;
}
.btn-success:hover {
  color: #fff;
  background-color: rgb(21.25, 114.75, 71.4);
  border-color: rgb(20, 108, 67.2);
}
.btn-check:focus + .btn-success, .btn-success:focus {
  color: #fff;
  background-color: rgb(21.25, 114.75, 71.4);
  border-color: rgb(20, 108, 67.2);
  box-shadow: 0 0 0 0.25rem rgba(59.5, 153, 109.65, 0.5);
}
.btn-check:checked + .btn-success, .btn-check:active + .btn-success, .btn-success:active, .btn-success.active, .show > .btn-success.dropdown-toggle {
  color: #fff;
  background-color: rgb(20, 108, 67.2);
  border-color: rgb(18.75, 101.25, 63);
}
.btn-check:checked + .btn-success:focus, .btn-check:active + .btn-success:focus, .btn-success:active:focus, .btn-success.active:focus, .show > .btn-success.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.25rem rgba(59.5, 153, 109.65, 0.5);
}
.btn-success:disabled, .btn-success.disabled {
  color: #fff;
  background-color: #198754;
  border-color: #198754;
}

.btn-info {
  color: #000;
  background-color: #0dcaf0;
  border-color: #0dcaf0;
}
.btn-info:hover {
  color: #000;
  background-color: rgb(49.3, 209.95, 242.25);
  border-color: rgb(37.2, 207.3, 241.5);
}
.btn-check:focus + .btn-info, .btn-info:focus {
  color: #000;
  background-color: rgb(49.3, 209.95, 242.25);
  border-color: rgb(37.2, 207.3, 241.5);
  box-shadow: 0 0 0 0.25rem rgba(11.05, 171.7, 204, 0.5);
}
.btn-check:checked + .btn-info, .btn-check:active + .btn-info, .btn-info:active, .btn-info.active, .show > .btn-info.dropdown-toggle {
  color: #000;
  background-color: rgb(61.4, 212.6, 243);
  border-color: rgb(37.2, 207.3, 241.5);
}
.btn-check:checked + .btn-info:focus, .btn-check:active + .btn-info:focus, .btn-info:active:focus, .btn-info.active:focus, .show > .btn-info.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.25rem rgba(11.05, 171.7, 204, 0.5);
}
.btn-info:disabled, .btn-info.disabled {
  color: #000;
  background-color: #0dcaf0;
  border-color: #0dcaf0;
}

.btn-warning {
  color: #000;
  background-color: #ffc107;
  border-color: #ffc107;
}
.btn-warning:hover {
  color: #000;
  background-color: rgb(255, 202.3, 44.2);
  border-color: rgb(255, 199.2, 31.8);
}
.btn-check:focus + .btn-warning, .btn-warning:focus {
  color: #000;
  background-color: rgb(255, 202.3, 44.2);
  border-color: rgb(255, 199.2, 31.8);
  box-shadow: 0 0 0 0.25rem rgba(216.75, 164.05, 5.95, 0.5);
}
.btn-check:checked + .btn-warning, .btn-check:active + .btn-warning, .btn-warning:active, .btn-warning.active, .show > .btn-warning.dropdown-toggle {
  color: #000;
  background-color: rgb(255, 205.4, 56.6);
  border-color: rgb(255, 199.2, 31.8);
}
.btn-check:checked + .btn-warning:focus, .btn-check:active + .btn-warning:focus, .btn-warning:active:focus, .btn-warning.active:focus, .show > .btn-warning.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.25rem rgba(216.75, 164.05, 5.95, 0.5);
}
.btn-warning:disabled, .btn-warning.disabled {
  color: #000;
  background-color: #ffc107;
  border-color: #ffc107;
}

.btn-danger {
  color: #fff;
  background-color: #dc3545;
  border-color: #dc3545;
}
.btn-danger:hover {
  color: #fff;
  background-color: rgb(187, 45.05, 58.65);
  border-color: rgb(176, 42.4, 55.2);
}
.btn-check:focus + .btn-danger, .btn-danger:focus {
  color: #fff;
  background-color: rgb(187, 45.05, 58.65);
  border-color: rgb(176, 42.4, 55.2);
  box-shadow: 0 0 0 0.25rem rgba(225.25, 83.3, 96.9, 0.5);
}
.btn-check:checked + .btn-danger, .btn-check:active + .btn-danger, .btn-danger:active, .btn-danger.active, .show > .btn-danger.dropdown-toggle {
  color: #fff;
  background-color: rgb(176, 42.4, 55.2);
  border-color: rgb(165, 39.75, 51.75);
}
.btn-check:checked + .btn-danger:focus, .btn-check:active + .btn-danger:focus, .btn-danger:active:focus, .btn-danger.active:focus, .show > .btn-danger.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.25rem rgba(225.25, 83.3, 96.9, 0.5);
}
.btn-danger:disabled, .btn-danger.disabled {
  color: #fff;
  background-color: #dc3545;
  border-color: #dc3545;
}

.btn-light {
  color: #000;
  background-color: #f8f9fa;
  border-color: #f8f9fa;
}
.btn-light:hover {
  color: #000;
  background-color: rgb(249.05, 249.9, 250.75);
  border-color: rgb(248.7, 249.6, 250.5);
}
.btn-check:focus + .btn-light, .btn-light:focus {
  color: #000;
  background-color: rgb(249.05, 249.9, 250.75);
  border-color: rgb(248.7, 249.6, 250.5);
  box-shadow: 0 0 0 0.25rem rgba(210.8, 211.65, 212.5, 0.5);
}
.btn-check:checked + .btn-light, .btn-check:active + .btn-light, .btn-light:active, .btn-light.active, .show > .btn-light.dropdown-toggle {
  color: #000;
  background-color: rgb(249.4, 250.2, 251);
  border-color: rgb(248.7, 249.6, 250.5);
}
.btn-check:checked + .btn-light:focus, .btn-check:active + .btn-light:focus, .btn-light:active:focus, .btn-light.active:focus, .show > .btn-light.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.25rem rgba(210.8, 211.65, 212.5, 0.5);
}
.btn-light:disabled, .btn-light.disabled {
  color: #000;
  background-color: #f8f9fa;
  border-color: #f8f9fa;
}

.btn-dark {
  color: #fff;
  background-color: #212529;
  border-color: #212529;
}
.btn-dark:hover {
  color: #fff;
  background-color: rgb(28.05, 31.45, 34.85);
  border-color: rgb(26.4, 29.6, 32.8);
}
.btn-check:focus + .btn-dark, .btn-dark:focus {
  color: #fff;
  background-color: rgb(28.05, 31.45, 34.85);
  border-color: rgb(26.4, 29.6, 32.8);
  box-shadow: 0 0 0 0.25rem rgba(66.3, 69.7, 73.1, 0.5);
}
.btn-check:checked + .btn-dark, .btn-check:active + .btn-dark, .btn-dark:active, .btn-dark.active, .show > .btn-dark.dropdown-toggle {
  color: #fff;
  background-color: rgb(26.4, 29.6, 32.8);
  border-color: rgb(24.75, 27.75, 30.75);
}
.btn-check:checked + .btn-dark:focus, .btn-check:active + .btn-dark:focus, .btn-dark:active:focus, .btn-dark.active:focus, .show > .btn-dark.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.25rem rgba(66.3, 69.7, 73.1, 0.5);
}
.btn-dark:disabled, .btn-dark.disabled {
  color: #fff;
  background-color: #212529;
  border-color: #212529;
}

.btn-outline-primary {
  color: #0d6efd;
  border-color: #0d6efd;
}
.btn-outline-primary:hover {
  color: #fff;
  background-color: #0d6efd;
  border-color: #0d6efd;
}
.btn-check:focus + .btn-outline-primary, .btn-outline-primary:focus {
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.5);
}
.btn-check:checked + .btn-outline-primary, .btn-check:active + .btn-outline-primary, .btn-outline-primary:active, .btn-outline-primary.active, .btn-outline-primary.dropdown-toggle.show {
  color: #fff;
  background-color: #0d6efd;
  border-color: #0d6efd;
}
.btn-check:checked + .btn-outline-primary:focus, .btn-check:active + .btn-outline-primary:focus, .btn-outline-primary:active:focus, .btn-outline-primary.active:focus, .btn-outline-primary.dropdown-toggle.show:focus {
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.5);
}
.btn-outline-primary:disabled, .btn-outline-primary.disabled {
  color: #0d6efd;
  background-color: transparent;
}

.btn-outline-secondary {
  color: #6c757d;
  border-color: #6c757d;
}
.btn-outline-secondary:hover {
  color: #fff;
  background-color: #6c757d;
  border-color: #6c757d;
}
.btn-check:focus + .btn-outline-secondary, .btn-outline-secondary:focus {
  box-shadow: 0 0 0 0.25rem rgba(108, 117, 125, 0.5);
}
.btn-check:checked + .btn-outline-secondary, .btn-check:active + .btn-outline-secondary, .btn-outline-secondary:active, .btn-outline-secondary.active, .btn-outline-secondary.dropdown-toggle.show {
  color: #fff;
  background-color: #6c757d;
  border-color: #6c757d;
}
.btn-check:checked + .btn-outline-secondary:focus, .btn-check:active + .btn-outline-secondary:focus, .btn-outline-secondary:active:focus, .btn-outline-secondary.active:focus, .btn-outline-secondary.dropdown-toggle.show:focus {
  box-shadow: 0 0 0 0.25rem rgba(108, 117, 125, 0.5);
}
.btn-outline-secondary:disabled, .btn-outline-secondary.disabled {
  color: #6c757d;
  background-color: transparent;
}

.btn-outline-success {
  color: #198754;
  border-color: #198754;
}
.btn-outline-success:hover {
  color: #fff;
  background-color: #198754;
  border-color: #198754;
}
.btn-check:focus + .btn-outline-success, .btn-outline-success:focus {
  box-shadow: 0 0 0 0.25rem rgba(25, 135, 84, 0.5);
}
.btn-check:checked + .btn-outline-success, .btn-check:active + .btn-outline-success, .btn-outline-success:active, .btn-outline-success.active, .btn-outline-success.dropdown-toggle.show {
  color: #fff;
  background-color: #198754;
  border-color: #198754;
}
.btn-check:checked + .btn-outline-success:focus, .btn-check:active + .btn-outline-success:focus, .btn-outline-success:active:focus, .btn-outline-success.active:focus, .btn-outline-success.dropdown-toggle.show:focus {
  box-shadow: 0 0 0 0.25rem rgba(25, 135, 84, 0.5);
}
.btn-outline-success:disabled, .btn-outline-success.disabled {
  color: #198754;
  background-color: transparent;
}

.btn-outline-info {
  color: #0dcaf0;
  border-color: #0dcaf0;
}
.btn-outline-info:hover {
  color: #000;
  background-color: #0dcaf0;
  border-color: #0dcaf0;
}
.btn-check:focus + .btn-outline-info, .btn-outline-info:focus {
  box-shadow: 0 0 0 0.25rem rgba(13, 202, 240, 0.5);
}
.btn-check:checked + .btn-outline-info, .btn-check:active + .btn-outline-info, .btn-outline-info:active, .btn-outline-info.active, .btn-outline-info.dropdown-toggle.show {
  color: #000;
  background-color: #0dcaf0;
  border-color: #0dcaf0;
}
.btn-check:checked + .btn-outline-info:focus, .btn-check:active + .btn-outline-info:focus, .btn-outline-info:active:focus, .btn-outline-info.active:focus, .btn-outline-info.dropdown-toggle.show:focus {
  box-shadow: 0 0 0 0.25rem rgba(13, 202, 240, 0.5);
}
.btn-outline-info:disabled, .btn-outline-info.disabled {
  color: #0dcaf0;
  background-color: transparent;
}

.btn-outline-warning {
  color: #ffc107;
  border-color: #ffc107;
}
.btn-outline-warning:hover {
  color: #000;
  background-color: #ffc107;
  border-color: #ffc107;
}
.btn-check:focus + .btn-outline-warning, .btn-outline-warning:focus {
  box-shadow: 0 0 0 0.25rem rgba(255, 193, 7, 0.5);
}
.btn-check:checked + .btn-outline-warning, .btn-check:active + .btn-outline-warning, .btn-outline-warning:active, .btn-outline-warning.active, .btn-outline-warning.dropdown-toggle.show {
  color: #000;
  background-color: #ffc107;
  border-color: #ffc107;
}
.btn-check:checked + .btn-outline-warning:focus, .btn-check:active + .btn-outline-warning:focus, .btn-outline-warning:active:focus, .btn-outline-warning.active:focus, .btn-outline-warning.dropdown-toggle.show:focus {
  box-shadow: 0 0 0 0.25rem rgba(255, 193, 7, 0.5);
}
.btn-outline-warning:disabled, .btn-outline-warning.disabled {
  color: #ffc107;
  background-color: transparent;
}

.btn-outline-danger {
  color: #dc3545;
  border-color: #dc3545;
}
.btn-outline-danger:hover {
  color: #fff;
  background-color: #dc3545;
  border-color: #dc3545;
}
.btn-check:focus + .btn-outline-danger, .btn-outline-danger:focus {
  box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.5);
}
.btn-check:checked + .btn-outline-danger, .btn-check:active + .btn-outline-danger, .btn-outline-danger:active, .btn-outline-danger.active, .btn-outline-danger.dropdown-toggle.show {
  color: #fff;
  background-color: #dc3545;
  border-color: #dc3545;
}
.btn-check:checked + .btn-outline-danger:focus, .btn-check:active + .btn-outline-danger:focus, .btn-outline-danger:active:focus, .btn-outline-danger.active:focus, .btn-outline-danger.dropdown-toggle.show:focus {
  box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.5);
}
.btn-outline-danger:disabled, .btn-outline-danger.disabled {
  color: #dc3545;
  background-color: transparent;
}

.btn-outline-light {
  color: #f8f9fa;
  border-color: #f8f9fa;
}
.btn-outline-light:hover {
  color: #000;
  background-color: #f8f9fa;
  border-color: #f8f9fa;
}
.btn-check:focus + .btn-outline-light, .btn-outline-light:focus {
  box-shadow: 0 0 0 0.25rem rgba(248, 249, 250, 0.5);
}
.btn-check:checked + .btn-outline-light, .btn-check:active + .btn-outline-light, .btn-outline-light:active, .btn-outline-light.active, .btn-outline-light.dropdown-toggle.show {
  color: #000;
  background-color: #f8f9fa;
  border-color: #f8f9fa;
}
.btn-check:checked + .btn-outline-light:focus, .btn-check:active + .btn-outline-light:focus, .btn-outline-light:active:focus, .btn-outline-light.active:focus, .btn-outline-light.dropdown-toggle.show:focus {
  box-shadow: 0 0 0 0.25rem rgba(248, 249, 250, 0.5);
}
.btn-outline-light:disabled, .btn-outline-light.disabled {
  color: #f8f9fa;
  background-color: transparent;
}

.btn-outline-dark {
  color: #212529;
  border-color: #212529;
}
.btn-outline-dark:hover {
  color: #fff;
  background-color: #212529;
  border-color: #212529;
}
.btn-check:focus + .btn-outline-dark, .btn-outline-dark:focus {
  box-shadow: 0 0 0 0.25rem rgba(33, 37, 41, 0.5);
}
.btn-check:checked + .btn-outline-dark, .btn-check:active + .btn-outline-dark, .btn-outline-dark:active, .btn-outline-dark.active, .btn-outline-dark.dropdown-toggle.show {
  color: #fff;
  background-color: #212529;
  border-color: #212529;
}
.btn-check:checked + .btn-outline-dark:focus, .btn-check:active + .btn-outline-dark:focus, .btn-outline-dark:active:focus, .btn-outline-dark.active:focus, .btn-outline-dark.dropdown-toggle.show:focus {
  box-shadow: 0 0 0 0.25rem rgba(33, 37, 41, 0.5);
}
.btn-outline-dark:disabled, .btn-outline-dark.disabled {
  color: #212529;
  background-color: transparent;
}

.btn-link {
  font-weight: 400;
  color: #0d6efd;
  text-decoration: underline;
}
.btn-link:hover {
  color: rgb(10.4, 88, 202.4);
}
.btn-link:disabled, .btn-link.disabled {
  color: #6c757d;
}

.btn-lg, .btn-group-lg > .btn {
  padding: 0.5rem 1rem;
  font-size: 1.25rem;
  border-radius: 0.3rem;
}

.btn-sm, .btn-group-sm > .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  border-radius: 0.2rem;
}

.fade {
  transition: opacity 0.15s linear;
}
@media (prefers-reduced-motion: reduce) {
  .fade {
    transition: none;
  }
}
.fade:not(.show) {
  opacity: 0;
}

.collapse:not(.show) {
  display: none;
}

.collapsing {
  height: 0;
  overflow: hidden;
  transition: height 0.35s ease;
}
@media (prefers-reduced-motion: reduce) {
  .collapsing {
    transition: none;
  }
}

.dropup,
.dropend,
.dropdown,
.dropstart {
  position: relative;
}

.dropdown-toggle {
  white-space: nowrap;
}
.dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid;
  border-right: 0.3em solid transparent;
  border-bottom: 0;
  border-left: 0.3em solid transparent;
}
.dropdown-toggle:empty::after {
  margin-left: 0;
}

.dropdown-menu {
  position: absolute;
  z-index: 1000;
  display: none;
  min-width: 10rem;
  padding: 0.5rem 0;
  margin: 0;
  font-size: 1rem;
  color: #212529;
  text-align: left;
  list-style: none;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 0.25rem;
}
.dropdown-menu[data-bs-popper] {
  top: 100%;
  left: 0;
  margin-top: 0.125rem;
}

.dropdown-menu-start {
  --bs-position: start;
}
.dropdown-menu-start[data-bs-popper] {
  right: auto;
  left: 0;
}

.dropdown-menu-end {
  --bs-position: end;
}
.dropdown-menu-end[data-bs-popper] {
  right: 0;
  left: auto;
}

@media (min-width: 576px) {
  .dropdown-menu-sm-start {
    --bs-position: start;
  }
  .dropdown-menu-sm-start[data-bs-popper] {
    right: auto;
    left: 0;
  }
  .dropdown-menu-sm-end {
    --bs-position: end;
  }
  .dropdown-menu-sm-end[data-bs-popper] {
    right: 0;
    left: auto;
  }
}
@media (min-width: 768px) {
  .dropdown-menu-md-start {
    --bs-position: start;
  }
  .dropdown-menu-md-start[data-bs-popper] {
    right: auto;
    left: 0;
  }
  .dropdown-menu-md-end {
    --bs-position: end;
  }
  .dropdown-menu-md-end[data-bs-popper] {
    right: 0;
    left: auto;
  }
}
@media (min-width: 992px) {
  .dropdown-menu-lg-start {
    --bs-position: start;
  }
  .dropdown-menu-lg-start[data-bs-popper] {
    right: auto;
    left: 0;
  }
  .dropdown-menu-lg-end {
    --bs-position: end;
  }
  .dropdown-menu-lg-end[data-bs-popper] {
    right: 0;
    left: auto;
  }
}
@media (min-width: 1200px) {
  .dropdown-menu-xl-start {
    --bs-position: start;
  }
  .dropdown-menu-xl-start[data-bs-popper] {
    right: auto;
    left: 0;
  }
  .dropdown-menu-xl-end {
    --bs-position: end;
  }
  .dropdown-menu-xl-end[data-bs-popper] {
    right: 0;
    left: auto;
  }
}
@media (min-width: 1400px) {
  .dropdown-menu-xxl-start {
    --bs-position: start;
  }
  .dropdown-menu-xxl-start[data-bs-popper] {
    right: auto;
    left: 0;
  }
  .dropdown-menu-xxl-end {
    --bs-position: end;
  }
  .dropdown-menu-xxl-end[data-bs-popper] {
    right: 0;
    left: auto;
  }
}
.dropup .dropdown-menu[data-bs-popper] {
  top: auto;
  bottom: 100%;
  margin-top: 0;
  margin-bottom: 0.125rem;
}
.dropup .dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0;
  border-right: 0.3em solid transparent;
  border-bottom: 0.3em solid;
  border-left: 0.3em solid transparent;
}
.dropup .dropdown-toggle:empty::after {
  margin-left: 0;
}

.dropend .dropdown-menu[data-bs-popper] {
  top: 0;
  right: auto;
  left: 100%;
  margin-top: 0;
  margin-left: 0.125rem;
}
.dropend .dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid transparent;
  border-right: 0;
  border-bottom: 0.3em solid transparent;
  border-left: 0.3em solid;
}
.dropend .dropdown-toggle:empty::after {
  margin-left: 0;
}
.dropend .dropdown-toggle::after {
  vertical-align: 0;
}

.dropstart .dropdown-menu[data-bs-popper] {
  top: 0;
  right: 100%;
  left: auto;
  margin-top: 0;
  margin-right: 0.125rem;
}
.dropstart .dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
}
.dropstart .dropdown-toggle::after {
  display: none;
}
.dropstart .dropdown-toggle::before {
  display: inline-block;
  margin-right: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid transparent;
  border-right: 0.3em solid;
  border-bottom: 0.3em solid transparent;
}
.dropstart .dropdown-toggle:empty::after {
  margin-left: 0;
}
.dropstart .dropdown-toggle::before {
  vertical-align: 0;
}

.dropdown-divider {
  height: 0;
  margin: 0.5rem 0;
  overflow: hidden;
  border-top: 1px solid rgba(0, 0, 0, 0.15);
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: 0.25rem 1rem;
  clear: both;
  font-weight: 400;
  color: #212529;
  text-align: inherit;
  text-decoration: none;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
}
.dropdown-item:hover, .dropdown-item:focus {
  color: rgb(29.7, 33.3, 36.9);
  background-color: #e9ecef;
}
.dropdown-item.active, .dropdown-item:active {
  color: #fff;
  text-decoration: none;
  background-color: #0d6efd;
}
.dropdown-item.disabled, .dropdown-item:disabled {
  color: #adb5bd;
  pointer-events: none;
  background-color: transparent;
}

.dropdown-menu.show {
  display: block;
}

.dropdown-header {
  display: block;
  padding: 0.5rem 1rem;
  margin-bottom: 0;
  font-size: 0.875rem;
  color: #6c757d;
  white-space: nowrap;
}

.dropdown-item-text {
  display: block;
  padding: 0.25rem 1rem;
  color: #212529;
}

.dropdown-menu-dark {
  color: #dee2e6;
  background-color: #343a40;
  border-color: rgba(0, 0, 0, 0.15);
}
.dropdown-menu-dark .dropdown-item {
  color: #dee2e6;
}
.dropdown-menu-dark .dropdown-item:hover, .dropdown-menu-dark .dropdown-item:focus {
  color: #fff;
  background-color: rgba(255, 255, 255, 0.15);
}
.dropdown-menu-dark .dropdown-item.active, .dropdown-menu-dark .dropdown-item:active {
  color: #fff;
  background-color: #0d6efd;
}
.dropdown-menu-dark .dropdown-item.disabled, .dropdown-menu-dark .dropdown-item:disabled {
  color: #adb5bd;
}
.dropdown-menu-dark .dropdown-divider {
  border-color: rgba(0, 0, 0, 0.15);
}
.dropdown-menu-dark .dropdown-item-text {
  color: #dee2e6;
}
.dropdown-menu-dark .dropdown-header {
  color: #adb5bd;
}

.btn-group,
.btn-group-vertical {
  position: relative;
  display: inline-flex;
  vertical-align: middle;
}
.btn-group > .btn,
.btn-group-vertical > .btn {
  position: relative;
  flex: 1 1 auto;
}
.btn-group > .btn-check:checked + .btn,
.btn-group > .btn-check:focus + .btn,
.btn-group > .btn:hover,
.btn-group > .btn:focus,
.btn-group > .btn:active,
.btn-group > .btn.active,
.btn-group-vertical > .btn-check:checked + .btn,
.btn-group-vertical > .btn-check:focus + .btn,
.btn-group-vertical > .btn:hover,
.btn-group-vertical > .btn:focus,
.btn-group-vertical > .btn:active,
.btn-group-vertical > .btn.active {
  z-index: 1;
}

.btn-toolbar {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
}
.btn-toolbar .input-group {
  width: auto;
}

.btn-group > .btn:not(:first-child),
.btn-group > .btn-group:not(:first-child) {
  margin-left: -1px;
}
.btn-group > .btn:not(:last-child):not(.dropdown-toggle),
.btn-group > .btn-group:not(:last-child) > .btn {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.btn-group > .btn:nth-child(n+3),
.btn-group > :not(.btn-check) + .btn,
.btn-group > .btn-group:not(:first-child) > .btn {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.dropdown-toggle-split {
  padding-right: 0.5625rem;
  padding-left: 0.5625rem;
}
.dropdown-toggle-split::after, .dropup .dropdown-toggle-split::after, .dropend .dropdown-toggle-split::after {
  margin-left: 0;
}
.dropstart .dropdown-toggle-split::before {
  margin-right: 0;
}

.btn-sm + .dropdown-toggle-split, .btn-group-sm > .btn + .dropdown-toggle-split {
  padding-right: 0.375rem;
  padding-left: 0.375rem;
}

.btn-lg + .dropdown-toggle-split, .btn-group-lg > .btn + .dropdown-toggle-split {
  padding-right: 0.75rem;
  padding-left: 0.75rem;
}

.btn-group-vertical {
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
}
.btn-group-vertical > .btn,
.btn-group-vertical > .btn-group {
  width: 100%;
}
.btn-group-vertical > .btn:not(:first-child),
.btn-group-vertical > .btn-group:not(:first-child) {
  margin-top: -1px;
}
.btn-group-vertical > .btn:not(:last-child):not(.dropdown-toggle),
.btn-group-vertical > .btn-group:not(:last-child) > .btn {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.btn-group-vertical > .btn ~ .btn,
.btn-group-vertical > .btn-group:not(:first-child) > .btn {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.nav {
  display: flex;
  flex-wrap: wrap;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}

.nav-link {
  display: block;
  padding: 0.5rem 1rem;
  color: #0d6efd;
  text-decoration: none;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .nav-link {
    transition: none;
  }
}
.nav-link:hover, .nav-link:focus {
  color: rgb(10.4, 88, 202.4);
}
.nav-link.disabled {
  color: #6c757d;
  pointer-events: none;
  cursor: default;
}

.nav-tabs {
  border-bottom: 1px solid #dee2e6;
}
.nav-tabs .nav-link {
  margin-bottom: -1px;
  background: none;
  border: 1px solid transparent;
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}
.nav-tabs .nav-link:hover, .nav-tabs .nav-link:focus {
  border-color: #e9ecef #e9ecef #dee2e6;
  isolation: isolate;
}
.nav-tabs .nav-link.disabled {
  color: #6c757d;
  background-color: transparent;
  border-color: transparent;
}
.nav-tabs .nav-link.active,
.nav-tabs .nav-item.show .nav-link {
  color: #495057;
  background-color: #fff;
  border-color: #dee2e6 #dee2e6 #fff;
}
.nav-tabs .dropdown-menu {
  margin-top: -1px;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.nav-pills .nav-link {
  background: none;
  border: 0;
  border-radius: 0.25rem;
}
.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
  color: #fff;
  background-color: #0d6efd;
}

.nav-fill > .nav-link,
.nav-fill .nav-item {
  flex: 1 1 auto;
  text-align: center;
}

.nav-justified > .nav-link,
.nav-justified .nav-item {
  flex-basis: 0;
  flex-grow: 1;
  text-align: center;
}

.nav-fill .nav-item .nav-link,
.nav-justified .nav-item .nav-link {
  width: 100%;
}

.tab-content > .tab-pane {
  display: none;
}
.tab-content > .active {
  display: block;
}

.navbar {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.navbar > .container,
.navbar > .container-fluid,
.navbar > .container-sm,
.navbar > .container-md,
.navbar > .container-lg,
.navbar > .container-xl,
.navbar > .container-xxl {
  display: flex;
  flex-wrap: inherit;
  align-items: center;
  justify-content: space-between;
}
.navbar-brand {
  padding-top: 0.3125rem;
  padding-bottom: 0.3125rem;
  margin-right: 1rem;
  font-size: 1.25rem;
  text-decoration: none;
  white-space: nowrap;
}
.navbar-nav {
  display: flex;
  flex-direction: column;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}
.navbar-nav .nav-link {
  padding-right: 0;
  padding-left: 0;
}
.navbar-nav .dropdown-menu {
  position: static;
}

.navbar-text {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.navbar-collapse {
  flex-basis: 100%;
  flex-grow: 1;
  align-items: center;
}

.navbar-toggler {
  padding: 0.25rem 0.75rem;
  font-size: 1.25rem;
  line-height: 1;
  background-color: transparent;
  border: 1px solid transparent;
  border-radius: 0.25rem;
  transition: box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .navbar-toggler {
    transition: none;
  }
}
.navbar-toggler:hover {
  text-decoration: none;
}
.navbar-toggler:focus {
  text-decoration: none;
  outline: 0;
  box-shadow: 0 0 0 0.25rem;
}

.navbar-toggler-icon {
  display: inline-block;
  width: 1.5em;
  height: 1.5em;
  vertical-align: middle;
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100%;
}

.navbar-nav-scroll {
  max-height: var(--bs-scroll-height, 75vh);
  overflow-y: auto;
}

@media (min-width: 576px) {
  .navbar-expand-sm {
    flex-wrap: nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-sm .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-sm .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-sm .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .navbar-expand-sm .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-sm .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-sm .navbar-toggler {
    display: none;
  }
}
@media (min-width: 768px) {
  .navbar-expand-md {
    flex-wrap: nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-md .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-md .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-md .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .navbar-expand-md .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-md .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-md .navbar-toggler {
    display: none;
  }
}
@media (min-width: 992px) {
  .navbar-expand-lg {
    flex-wrap: nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-lg .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-lg .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-lg .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .navbar-expand-lg .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-lg .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-lg .navbar-toggler {
    display: none;
  }
}
@media (min-width: 1200px) {
  .navbar-expand-xl {
    flex-wrap: nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-xl .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-xl .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-xl .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .navbar-expand-xl .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-xl .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-xl .navbar-toggler {
    display: none;
  }
}
@media (min-width: 1400px) {
  .navbar-expand-xxl {
    flex-wrap: nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-xxl .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-xxl .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-xxl .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .navbar-expand-xxl .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-xxl .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-xxl .navbar-toggler {
    display: none;
  }
}
.navbar-expand {
  flex-wrap: nowrap;
  justify-content: flex-start;
}
.navbar-expand .navbar-nav {
  flex-direction: row;
}
.navbar-expand .navbar-nav .dropdown-menu {
  position: absolute;
}
.navbar-expand .navbar-nav .nav-link {
  padding-right: 0.5rem;
  padding-left: 0.5rem;
}
.navbar-expand .navbar-nav-scroll {
  overflow: visible;
}
.navbar-expand .navbar-collapse {
  display: flex !important;
  flex-basis: auto;
}
.navbar-expand .navbar-toggler {
  display: none;
}

.navbar-light .navbar-brand {
  color: rgba(0, 0, 0, 0.9);
}
.navbar-light .navbar-brand:hover, .navbar-light .navbar-brand:focus {
  color: rgba(0, 0, 0, 0.9);
}
.navbar-light .navbar-nav .nav-link {
  color: rgba(0, 0, 0, 0.55);
}
.navbar-light .navbar-nav .nav-link:hover, .navbar-light .navbar-nav .nav-link:focus {
  color: rgba(0, 0, 0, 0.7);
}
.navbar-light .navbar-nav .nav-link.disabled {
  color: rgba(0, 0, 0, 0.3);
}
.navbar-light .navbar-nav .show > .nav-link,
.navbar-light .navbar-nav .nav-link.active {
  color: rgba(0, 0, 0, 0.9);
}
.navbar-light .navbar-toggler {
  color: rgba(0, 0, 0, 0.55);
  border-color: rgba(0, 0, 0, 0.1);
}
.navbar-light .navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%280, 0, 0, 0.55%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}
.navbar-light .navbar-text {
  color: rgba(0, 0, 0, 0.55);
}
.navbar-light .navbar-text a,
.navbar-light .navbar-text a:hover,
.navbar-light .navbar-text a:focus {
  color: rgba(0, 0, 0, 0.9);
}

.navbar-dark .navbar-brand {
  color: #fff;
}
.navbar-dark .navbar-brand:hover, .navbar-dark .navbar-brand:focus {
  color: #fff;
}
.navbar-dark .navbar-nav .nav-link {
  color: rgba(255, 255, 255, 0.55);
}
.navbar-dark .navbar-nav .nav-link:hover, .navbar-dark .navbar-nav .nav-link:focus {
  color: rgba(255, 255, 255, 0.75);
}
.navbar-dark .navbar-nav .nav-link.disabled {
  color: rgba(255, 255, 255, 0.25);
}
.navbar-dark .navbar-nav .show > .nav-link,
.navbar-dark .navbar-nav .nav-link.active {
  color: #fff;
}
.navbar-dark .navbar-toggler {
  color: rgba(255, 255, 255, 0.55);
  border-color: rgba(255, 255, 255, 0.1);
}
.navbar-dark .navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.55%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}
.navbar-dark .navbar-text {
  color: rgba(255, 255, 255, 0.55);
}
.navbar-dark .navbar-text a,
.navbar-dark .navbar-text a:hover,
.navbar-dark .navbar-text a:focus {
  color: #fff;
}

.card {
  position: relative;
  display: flex;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  background-color: #fff;
  background-clip: border-box;
  border: 1px solid rgba(0, 0, 0, 0.125);
  border-radius: 0.25rem;
}
.card > hr {
  margin-right: 0;
  margin-left: 0;
}
.card > .list-group {
  border-top: inherit;
  border-bottom: inherit;
}
.card > .list-group:first-child {
  border-top-width: 0;
  border-top-left-radius: calc(0.25rem - 1px);
  border-top-right-radius: calc(0.25rem - 1px);
}
.card > .list-group:last-child {
  border-bottom-width: 0;
  border-bottom-right-radius: calc(0.25rem - 1px);
  border-bottom-left-radius: calc(0.25rem - 1px);
}
.card > .card-header + .list-group,
.card > .list-group + .card-footer {
  border-top: 0;
}

.card-body {
  flex: 1 1 auto;
  padding: 1rem 1rem;
}

.card-title {
  margin-bottom: 0.5rem;
}

.card-subtitle {
  margin-top: -0.25rem;
  margin-bottom: 0;
}

.card-text:last-child {
  margin-bottom: 0;
}

.card-link:hover {
  text-decoration: none;
}
.card-link + .card-link {
  margin-left: 1rem;
}

.card-header {
  padding: 0.5rem 1rem;
  margin-bottom: 0;
  background-color: rgba(0, 0, 0, 0.03);
  border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}
.card-header:first-child {
  border-radius: calc(0.25rem - 1px) calc(0.25rem - 1px) 0 0;
}

.card-footer {
  padding: 0.5rem 1rem;
  background-color: rgba(0, 0, 0, 0.03);
  border-top: 1px solid rgba(0, 0, 0, 0.125);
}
.card-footer:last-child {
  border-radius: 0 0 calc(0.25rem - 1px) calc(0.25rem - 1px);
}

.card-header-tabs {
  margin-right: -0.5rem;
  margin-bottom: -0.5rem;
  margin-left: -0.5rem;
  border-bottom: 0;
}

.card-header-pills {
  margin-right: -0.5rem;
  margin-left: -0.5rem;
}

.card-img-overlay {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  padding: 1rem;
  border-radius: calc(0.25rem - 1px);
}

.card-img,
.card-img-top,
.card-img-bottom {
  width: 100%;
}

.card-img,
.card-img-top {
  border-top-left-radius: calc(0.25rem - 1px);
  border-top-right-radius: calc(0.25rem - 1px);
}

.card-img,
.card-img-bottom {
  border-bottom-right-radius: calc(0.25rem - 1px);
  border-bottom-left-radius: calc(0.25rem - 1px);
}

.card-group > .card {
  margin-bottom: 15px;
}
@media (min-width: 576px) {
  .card-group {
    display: flex;
    flex-flow: row wrap;
  }
  .card-group > .card {
    flex: 1 0 0%;
    margin-bottom: 0;
  }
  .card-group > .card + .card {
    margin-left: 0;
    border-left: 0;
  }
  .card-group > .card:not(:last-child) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
  .card-group > .card:not(:last-child) .card-img-top,
  .card-group > .card:not(:last-child) .card-header {
    border-top-right-radius: 0;
  }
  .card-group > .card:not(:last-child) .card-img-bottom,
  .card-group > .card:not(:last-child) .card-footer {
    border-bottom-right-radius: 0;
  }
  .card-group > .card:not(:first-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
  .card-group > .card:not(:first-child) .card-img-top,
  .card-group > .card:not(:first-child) .card-header {
    border-top-left-radius: 0;
  }
  .card-group > .card:not(:first-child) .card-img-bottom,
  .card-group > .card:not(:first-child) .card-footer {
    border-bottom-left-radius: 0;
  }
}

.accordion-button {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  padding: 1rem 1.25rem;
  font-size: 1rem;
  color: #212529;
  text-align: left;
  background-color: #fff;
  border: 0;
  border-radius: 0;
  overflow-anchor: none;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, border-radius 0.15s ease;
}
@media (prefers-reduced-motion: reduce) {
  .accordion-button {
    transition: none;
  }
}
.accordion-button:not(.collapsed) {
  color: rgb(11.7, 99, 227.7);
  background-color: rgb(230.8, 240.5, 254.8);
  box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.125);
}
.accordion-button:not(.collapsed)::after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='rgb%2811.7, 99, 227.7%29'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
  transform: rotate(-180deg);
}
.accordion-button::after {
  flex-shrink: 0;
  width: 1.25rem;
  height: 1.25rem;
  margin-left: auto;
  content: "";
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23212529'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-size: 1.25rem;
  transition: transform 0.2s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .accordion-button::after {
    transition: none;
  }
}
.accordion-button:hover {
  z-index: 2;
}
.accordion-button:focus {
  z-index: 3;
  border-color: rgb(134, 182.5, 254);
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.accordion-header {
  margin-bottom: 0;
}

.accordion-item {
  background-color: #fff;
  border: 1px solid rgba(0, 0, 0, 0.125);
}
.accordion-item:first-of-type {
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}
.accordion-item:first-of-type .accordion-button {
  border-top-left-radius: calc(0.25rem - 1px);
  border-top-right-radius: calc(0.25rem - 1px);
}
.accordion-item:not(:first-of-type) {
  border-top: 0;
}
.accordion-item:last-of-type {
  border-bottom-right-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}
.accordion-item:last-of-type .accordion-button.collapsed {
  border-bottom-right-radius: calc(0.25rem - 1px);
  border-bottom-left-radius: calc(0.25rem - 1px);
}
.accordion-item:last-of-type .accordion-collapse {
  border-bottom-right-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}

.accordion-body {
  padding: 1rem 1.25rem;
}

.accordion-flush .accordion-collapse {
  border-width: 0;
}
.accordion-flush .accordion-item {
  border-right: 0;
  border-left: 0;
  border-radius: 0;
}
.accordion-flush .accordion-item:first-child {
  border-top: 0;
}
.accordion-flush .accordion-item:last-child {
  border-bottom: 0;
}
.accordion-flush .accordion-item .accordion-button {
  border-radius: 0;
}

.breadcrumb {
  display: flex;
  flex-wrap: wrap;
  padding: 0 0;
  margin-bottom: 1rem;
  list-style: none;
}

.breadcrumb-item + .breadcrumb-item {
  padding-left: 0.5rem;
}
.breadcrumb-item + .breadcrumb-item::before {
  float: left;
  padding-right: 0.5rem;
  color: #6c757d;
  content: var(--bs-breadcrumb-divider, "/") /* rtl: var(--bs-breadcrumb-divider, "/") */;
}
.breadcrumb-item.active {
  color: #6c757d;
}

.pagination {
  display: flex;
  padding-left: 0;
  list-style: none;
}

.page-link {
  position: relative;
  display: block;
  color: #0d6efd;
  text-decoration: none;
  background-color: #fff;
  border: 1px solid #dee2e6;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .page-link {
    transition: none;
  }
}
.page-link:hover {
  z-index: 2;
  color: rgb(10.4, 88, 202.4);
  background-color: #e9ecef;
  border-color: #dee2e6;
}
.page-link:focus {
  z-index: 3;
  color: rgb(10.4, 88, 202.4);
  background-color: #e9ecef;
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.page-item:not(:first-child) .page-link {
  margin-left: -1px;
}
.page-item.active .page-link {
  z-index: 3;
  color: #fff;
  background-color: #0d6efd;
  border-color: #0d6efd;
}
.page-item.disabled .page-link {
  color: #6c757d;
  pointer-events: none;
  background-color: #fff;
  border-color: #dee2e6;
}

.page-link {
  padding: 0.375rem 0.75rem;
}

.page-item:first-child .page-link {
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}
.page-item:last-child .page-link {
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}

.pagination-lg .page-link {
  padding: 0.75rem 1.5rem;
  font-size: 1.25rem;
}
.pagination-lg .page-item:first-child .page-link {
  border-top-left-radius: 0.3rem;
  border-bottom-left-radius: 0.3rem;
}
.pagination-lg .page-item:last-child .page-link {
  border-top-right-radius: 0.3rem;
  border-bottom-right-radius: 0.3rem;
}

.pagination-sm .page-link {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}
.pagination-sm .page-item:first-child .page-link {
  border-top-left-radius: 0.2rem;
  border-bottom-left-radius: 0.2rem;
}
.pagination-sm .page-item:last-child .page-link {
  border-top-right-radius: 0.2rem;
  border-bottom-right-radius: 0.2rem;
}

.badge {
  display: inline-block;
  padding: 0.35em 0.65em;
  font-size: 0.75em;
  font-weight: 700;
  line-height: 1;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.25rem;
}
.badge:empty {
  display: none;
}

.btn .badge {
  position: relative;
  top: -1px;
}

.alert {
  position: relative;
  padding: 1rem 1rem;
  margin-bottom: 1rem;
  border: 1px solid transparent;
  border-radius: 0.25rem;
}

.alert-heading {
  color: inherit;
}

.alert-link {
  font-weight: 700;
}

.alert-dismissible {
  padding-right: 3rem;
}
.alert-dismissible .btn-close {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 2;
  padding: 1.25rem 1rem;
}

.alert-primary {
  color: rgb(7.8, 66, 151.8);
  background-color: rgb(206.6, 226, 254.6);
  border-color: rgb(182.4, 211.5, 254.4);
}
.alert-primary .alert-link {
  color: rgb(6.24, 52.8, 121.44);
}

.alert-secondary {
  color: rgb(64.8, 70.2, 75);
  background-color: rgb(225.6, 227.4, 229);
  border-color: rgb(210.9, 213.6, 216);
}
.alert-secondary .alert-link {
  color: rgb(51.84, 56.16, 60);
}

.alert-success {
  color: rgb(15, 81, 50.4);
  background-color: rgb(209, 231, 220.8);
  border-color: rgb(186, 219, 203.7);
}
.alert-success .alert-link {
  color: rgb(12, 64.8, 40.32);
}

.alert-info {
  color: rgb(5.2, 80.8, 96);
  background-color: rgb(206.6, 244.4, 252);
  border-color: rgb(182.4, 239.1, 250.5);
}
.alert-info .alert-link {
  color: rgb(4.16, 64.64, 76.8);
}

.alert-warning {
  color: rgb(102, 77.2, 2.8);
  background-color: rgb(255, 242.6, 205.4);
  border-color: rgb(255, 236.4, 180.6);
}
.alert-warning .alert-link {
  color: rgb(81.6, 61.76, 2.24);
}

.alert-danger {
  color: rgb(132, 31.8, 41.4);
  background-color: rgb(248, 214.6, 217.8);
  border-color: rgb(244.5, 194.4, 199.2);
}
.alert-danger .alert-link {
  color: rgb(105.6, 25.44, 33.12);
}

.alert-light {
  color: rgb(99.2, 99.6, 100);
  background-color: rgb(253.6, 253.8, 254);
  border-color: rgb(252.9, 253.2, 253.5);
}
.alert-light .alert-link {
  color: rgb(79.36, 79.68, 80);
}

.alert-dark {
  color: rgb(19.8, 22.2, 24.6);
  background-color: rgb(210.6, 211.4, 212.2);
  border-color: rgb(188.4, 189.6, 190.8);
}
.alert-dark .alert-link {
  color: rgb(15.84, 17.76, 19.68);
}

@keyframes progress-bar-stripes {
  0% {
    background-position-x: 1rem;
  }
}
.progress {
  display: flex;
  height: 1rem;
  overflow: hidden;
  font-size: 0.75rem;
  background-color: #e9ecef;
  border-radius: 0.25rem;
}

.progress-bar {
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow: hidden;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  background-color: #0d6efd;
  transition: width 0.6s ease;
}
@media (prefers-reduced-motion: reduce) {
  .progress-bar {
    transition: none;
  }
}

.progress-bar-striped {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 1rem 1rem;
}

.progress-bar-animated {
  animation: 1s linear infinite progress-bar-stripes;
}
@media (prefers-reduced-motion: reduce) {
  .progress-bar-animated {
    animation: none;
  }
}

.list-group {
  display: flex;
  flex-direction: column;
  padding-left: 0;
  margin-bottom: 0;
  border-radius: 0.25rem;
}

.list-group-numbered {
  list-style-type: none;
  counter-reset: section;
}
.list-group-numbered > li::before {
  content: counters(section, ".") ". ";
  counter-increment: section;
}

.list-group-item-action {
  width: 100%;
  color: #495057;
  text-align: inherit;
}
.list-group-item-action:hover, .list-group-item-action:focus {
  z-index: 1;
  color: #495057;
  text-decoration: none;
  background-color: #f8f9fa;
}
.list-group-item-action:active {
  color: #212529;
  background-color: #e9ecef;
}

.list-group-item {
  position: relative;
  display: block;
  padding: 0.5rem 1rem;
  color: #212529;
  text-decoration: none;
  background-color: #fff;
  border: 1px solid rgba(0, 0, 0, 0.125);
}
.list-group-item:first-child {
  border-top-left-radius: inherit;
  border-top-right-radius: inherit;
}
.list-group-item:last-child {
  border-bottom-right-radius: inherit;
  border-bottom-left-radius: inherit;
}
.list-group-item.disabled, .list-group-item:disabled {
  color: #6c757d;
  pointer-events: none;
  background-color: #fff;
}
.list-group-item.active {
  z-index: 2;
  color: #fff;
  background-color: #0d6efd;
  border-color: #0d6efd;
}
.list-group-item + .list-group-item {
  border-top-width: 0;
}
.list-group-item + .list-group-item.active {
  margin-top: -1px;
  border-top-width: 1px;
}

.list-group-horizontal {
  flex-direction: row;
}
.list-group-horizontal > .list-group-item:first-child {
  border-bottom-left-radius: 0.25rem;
  border-top-right-radius: 0;
}
.list-group-horizontal > .list-group-item:last-child {
  border-top-right-radius: 0.25rem;
  border-bottom-left-radius: 0;
}
.list-group-horizontal > .list-group-item.active {
  margin-top: 0;
}
.list-group-horizontal > .list-group-item + .list-group-item {
  border-top-width: 1px;
  border-left-width: 0;
}
.list-group-horizontal > .list-group-item + .list-group-item.active {
  margin-left: -1px;
  border-left-width: 1px;
}

@media (min-width: 576px) {
  .list-group-horizontal-sm {
    flex-direction: row;
  }
  .list-group-horizontal-sm > .list-group-item:first-child {
    border-bottom-left-radius: 0.25rem;
    border-top-right-radius: 0;
  }
  .list-group-horizontal-sm > .list-group-item:last-child {
    border-top-right-radius: 0.25rem;
    border-bottom-left-radius: 0;
  }
  .list-group-horizontal-sm > .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-sm > .list-group-item + .list-group-item {
    border-top-width: 1px;
    border-left-width: 0;
  }
  .list-group-horizontal-sm > .list-group-item + .list-group-item.active {
    margin-left: -1px;
    border-left-width: 1px;
  }
}
@media (min-width: 768px) {
  .list-group-horizontal-md {
    flex-direction: row;
  }
  .list-group-horizontal-md > .list-group-item:first-child {
    border-bottom-left-radius: 0.25rem;
    border-top-right-radius: 0;
  }
  .list-group-horizontal-md > .list-group-item:last-child {
    border-top-right-radius: 0.25rem;
    border-bottom-left-radius: 0;
  }
  .list-group-horizontal-md > .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-md > .list-group-item + .list-group-item {
    border-top-width: 1px;
    border-left-width: 0;
  }
  .list-group-horizontal-md > .list-group-item + .list-group-item.active {
    margin-left: -1px;
    border-left-width: 1px;
  }
}
@media (min-width: 992px) {
  .list-group-horizontal-lg {
    flex-direction: row;
  }
  .list-group-horizontal-lg > .list-group-item:first-child {
    border-bottom-left-radius: 0.25rem;
    border-top-right-radius: 0;
  }
  .list-group-horizontal-lg > .list-group-item:last-child {
    border-top-right-radius: 0.25rem;
    border-bottom-left-radius: 0;
  }
  .list-group-horizontal-lg > .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-lg > .list-group-item + .list-group-item {
    border-top-width: 1px;
    border-left-width: 0;
  }
  .list-group-horizontal-lg > .list-group-item + .list-group-item.active {
    margin-left: -1px;
    border-left-width: 1px;
  }
}
@media (min-width: 1200px) {
  .list-group-horizontal-xl {
    flex-direction: row;
  }
  .list-group-horizontal-xl > .list-group-item:first-child {
    border-bottom-left-radius: 0.25rem;
    border-top-right-radius: 0;
  }
  .list-group-horizontal-xl > .list-group-item:last-child {
    border-top-right-radius: 0.25rem;
    border-bottom-left-radius: 0;
  }
  .list-group-horizontal-xl > .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-xl > .list-group-item + .list-group-item {
    border-top-width: 1px;
    border-left-width: 0;
  }
  .list-group-horizontal-xl > .list-group-item + .list-group-item.active {
    margin-left: -1px;
    border-left-width: 1px;
  }
}
@media (min-width: 1400px) {
  .list-group-horizontal-xxl {
    flex-direction: row;
  }
  .list-group-horizontal-xxl > .list-group-item:first-child {
    border-bottom-left-radius: 0.25rem;
    border-top-right-radius: 0;
  }
  .list-group-horizontal-xxl > .list-group-item:last-child {
    border-top-right-radius: 0.25rem;
    border-bottom-left-radius: 0;
  }
  .list-group-horizontal-xxl > .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-xxl > .list-group-item + .list-group-item {
    border-top-width: 1px;
    border-left-width: 0;
  }
  .list-group-horizontal-xxl > .list-group-item + .list-group-item.active {
    margin-left: -1px;
    border-left-width: 1px;
  }
}
.list-group-flush {
  border-radius: 0;
}
.list-group-flush > .list-group-item {
  border-width: 0 0 1px;
}
.list-group-flush > .list-group-item:last-child {
  border-bottom-width: 0;
}

.list-group-item-primary {
  color: rgb(7.8, 66, 151.8);
  background-color: rgb(206.6, 226, 254.6);
}
.list-group-item-primary.list-group-item-action:hover, .list-group-item-primary.list-group-item-action:focus {
  color: rgb(7.8, 66, 151.8);
  background-color: rgb(185.94, 203.4, 229.14);
}
.list-group-item-primary.list-group-item-action.active {
  color: #fff;
  background-color: rgb(7.8, 66, 151.8);
  border-color: rgb(7.8, 66, 151.8);
}

.list-group-item-secondary {
  color: rgb(64.8, 70.2, 75);
  background-color: rgb(225.6, 227.4, 229);
}
.list-group-item-secondary.list-group-item-action:hover, .list-group-item-secondary.list-group-item-action:focus {
  color: rgb(64.8, 70.2, 75);
  background-color: rgb(203.04, 204.66, 206.1);
}
.list-group-item-secondary.list-group-item-action.active {
  color: #fff;
  background-color: rgb(64.8, 70.2, 75);
  border-color: rgb(64.8, 70.2, 75);
}

.list-group-item-success {
  color: rgb(15, 81, 50.4);
  background-color: rgb(209, 231, 220.8);
}
.list-group-item-success.list-group-item-action:hover, .list-group-item-success.list-group-item-action:focus {
  color: rgb(15, 81, 50.4);
  background-color: rgb(188.1, 207.9, 198.72);
}
.list-group-item-success.list-group-item-action.active {
  color: #fff;
  background-color: rgb(15, 81, 50.4);
  border-color: rgb(15, 81, 50.4);
}

.list-group-item-info {
  color: rgb(5.2, 80.8, 96);
  background-color: rgb(206.6, 244.4, 252);
}
.list-group-item-info.list-group-item-action:hover, .list-group-item-info.list-group-item-action:focus {
  color: rgb(5.2, 80.8, 96);
  background-color: rgb(185.94, 219.96, 226.8);
}
.list-group-item-info.list-group-item-action.active {
  color: #fff;
  background-color: rgb(5.2, 80.8, 96);
  border-color: rgb(5.2, 80.8, 96);
}

.list-group-item-warning {
  color: rgb(102, 77.2, 2.8);
  background-color: rgb(255, 242.6, 205.4);
}
.list-group-item-warning.list-group-item-action:hover, .list-group-item-warning.list-group-item-action:focus {
  color: rgb(102, 77.2, 2.8);
  background-color: rgb(229.5, 218.34, 184.86);
}
.list-group-item-warning.list-group-item-action.active {
  color: #fff;
  background-color: rgb(102, 77.2, 2.8);
  border-color: rgb(102, 77.2, 2.8);
}

.list-group-item-danger {
  color: rgb(132, 31.8, 41.4);
  background-color: rgb(248, 214.6, 217.8);
}
.list-group-item-danger.list-group-item-action:hover, .list-group-item-danger.list-group-item-action:focus {
  color: rgb(132, 31.8, 41.4);
  background-color: rgb(223.2, 193.14, 196.02);
}
.list-group-item-danger.list-group-item-action.active {
  color: #fff;
  background-color: rgb(132, 31.8, 41.4);
  border-color: rgb(132, 31.8, 41.4);
}

.list-group-item-light {
  color: rgb(99.2, 99.6, 100);
  background-color: rgb(253.6, 253.8, 254);
}
.list-group-item-light.list-group-item-action:hover, .list-group-item-light.list-group-item-action:focus {
  color: rgb(99.2, 99.6, 100);
  background-color: rgb(228.24, 228.42, 228.6);
}
.list-group-item-light.list-group-item-action.active {
  color: #fff;
  background-color: rgb(99.2, 99.6, 100);
  border-color: rgb(99.2, 99.6, 100);
}

.list-group-item-dark {
  color: rgb(19.8, 22.2, 24.6);
  background-color: rgb(210.6, 211.4, 212.2);
}
.list-group-item-dark.list-group-item-action:hover, .list-group-item-dark.list-group-item-action:focus {
  color: rgb(19.8, 22.2, 24.6);
  background-color: rgb(189.54, 190.26, 190.98);
}
.list-group-item-dark.list-group-item-action.active {
  color: #fff;
  background-color: rgb(19.8, 22.2, 24.6);
  border-color: rgb(19.8, 22.2, 24.6);
}

.btn-close {
  box-sizing: content-box;
  width: 1em;
  height: 1em;
  padding: 0.25em 0.25em;
  color: #000;
  background: transparent url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23000'%3e%3cpath d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/%3e%3c/svg%3e") center/1em auto no-repeat;
  border: 0;
  border-radius: 0.25rem;
  opacity: 0.5;
}
.btn-close:hover {
  color: #000;
  text-decoration: none;
  opacity: 0.75;
}
.btn-close:focus {
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
  opacity: 1;
}
.btn-close:disabled, .btn-close.disabled {
  pointer-events: none;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  opacity: 0.25;
}

.btn-close-white {
  filter: invert(1) grayscale(100%) brightness(200%);
}

.toast {
  width: 350px;
  max-width: 100%;
  font-size: 0.875rem;
  pointer-events: auto;
  background-color: rgba(255, 255, 255, 0.85);
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  border-radius: 0.25rem;
}
.toast:not(.showing):not(.show) {
  opacity: 0;
}
.toast.hide {
  display: none;
}

.toast-container {
  width: -moz-max-content;
  width: max-content;
  max-width: 100%;
  pointer-events: none;
}
.toast-container > :not(:last-child) {
  margin-bottom: 15px;
}

.toast-header {
  display: flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
  color: #6c757d;
  background-color: rgba(255, 255, 255, 0.85);
  background-clip: padding-box;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  border-top-left-radius: calc(0.25rem - 1px);
  border-top-right-radius: calc(0.25rem - 1px);
}
.toast-header .btn-close {
  margin-right: -0.375rem;
  margin-left: 0.75rem;
}

.toast-body {
  padding: 0.75rem;
  word-wrap: break-word;
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1060;
  display: none;
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  outline: 0;
}

.modal-dialog {
  position: relative;
  width: auto;
  margin: 0.5rem;
  pointer-events: none;
}
.modal.fade .modal-dialog {
  transition: transform 0.3s ease-out;
  transform: translate(0, -50px);
}
@media (prefers-reduced-motion: reduce) {
  .modal.fade .modal-dialog {
    transition: none;
  }
}
.modal.show .modal-dialog {
  transform: none;
}
.modal.modal-static .modal-dialog {
  transform: scale(1.02);
}

.modal-dialog-scrollable {
  height: calc(100% - 1rem);
}
.modal-dialog-scrollable .modal-content {
  max-height: 100%;
  overflow: hidden;
}
.modal-dialog-scrollable .modal-body {
  overflow-y: auto;
}

.modal-dialog-centered {
  display: flex;
  align-items: center;
  min-height: calc(100% - 1rem);
}

.modal-content {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  pointer-events: auto;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 0.3rem;
  outline: 0;
}

.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1040;
  width: 100vw;
  height: 100vh;
  background-color: #000;
}
.modal-backdrop.fade {
  opacity: 0;
}
.modal-backdrop.show {
  opacity: 0.5;
}

.modal-header {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1rem;
  border-bottom: 1px solid #dee2e6;
  border-top-left-radius: calc(0.3rem - 1px);
  border-top-right-radius: calc(0.3rem - 1px);
}
.modal-header .btn-close {
  padding: 0.5rem 0.5rem;
  margin: -0.5rem -0.5rem -0.5rem auto;
}

.modal-title {
  margin-bottom: 0;
  line-height: 1.5;
}

.modal-body {
  position: relative;
  flex: 1 1 auto;
  padding: 1rem;
}

.modal-footer {
  display: flex;
  flex-wrap: wrap;
  flex-shrink: 0;
  align-items: center;
  justify-content: flex-end;
  padding: 0.75rem;
  border-top: 1px solid #dee2e6;
  border-bottom-right-radius: calc(0.3rem - 1px);
  border-bottom-left-radius: calc(0.3rem - 1px);
}
.modal-footer > * {
  margin: 0.25rem;
}

@media (min-width: 576px) {
  .modal-dialog {
    max-width: 500px;
    margin: 1.75rem auto;
  }
  .modal-dialog-scrollable {
    height: calc(100% - 3.5rem);
  }
  .modal-dialog-centered {
    min-height: calc(100% - 3.5rem);
  }
  .modal-sm {
    max-width: 300px;
  }
}
@media (min-width: 992px) {
  .modal-lg,
  .modal-xl {
    max-width: 800px;
  }
}
@media (min-width: 1200px) {
  .modal-xl {
    max-width: 1140px;
  }
}
.modal-fullscreen {
  width: 100vw;
  max-width: none;
  height: 100%;
  margin: 0;
}
.modal-fullscreen .modal-content {
  height: 100%;
  border: 0;
  border-radius: 0;
}
.modal-fullscreen .modal-header {
  border-radius: 0;
}
.modal-fullscreen .modal-body {
  overflow-y: auto;
}
.modal-fullscreen .modal-footer {
  border-radius: 0;
}

@media (max-width: 575.98px) {
  .modal-fullscreen-sm-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }
  .modal-fullscreen-sm-down .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
  }
  .modal-fullscreen-sm-down .modal-header {
    border-radius: 0;
  }
  .modal-fullscreen-sm-down .modal-body {
    overflow-y: auto;
  }
  .modal-fullscreen-sm-down .modal-footer {
    border-radius: 0;
  }
}
@media (max-width: 767.98px) {
  .modal-fullscreen-md-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }
  .modal-fullscreen-md-down .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
  }
  .modal-fullscreen-md-down .modal-header {
    border-radius: 0;
  }
  .modal-fullscreen-md-down .modal-body {
    overflow-y: auto;
  }
  .modal-fullscreen-md-down .modal-footer {
    border-radius: 0;
  }
}
@media (max-width: 991.98px) {
  .modal-fullscreen-lg-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }
  .modal-fullscreen-lg-down .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
  }
  .modal-fullscreen-lg-down .modal-header {
    border-radius: 0;
  }
  .modal-fullscreen-lg-down .modal-body {
    overflow-y: auto;
  }
  .modal-fullscreen-lg-down .modal-footer {
    border-radius: 0;
  }
}
@media (max-width: 1199.98px) {
  .modal-fullscreen-xl-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }
  .modal-fullscreen-xl-down .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
  }
  .modal-fullscreen-xl-down .modal-header {
    border-radius: 0;
  }
  .modal-fullscreen-xl-down .modal-body {
    overflow-y: auto;
  }
  .modal-fullscreen-xl-down .modal-footer {
    border-radius: 0;
  }
}
@media (max-width: 1399.98px) {
  .modal-fullscreen-xxl-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }
  .modal-fullscreen-xxl-down .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
  }
  .modal-fullscreen-xxl-down .modal-header {
    border-radius: 0;
  }
  .modal-fullscreen-xxl-down .modal-body {
    overflow-y: auto;
  }
  .modal-fullscreen-xxl-down .modal-footer {
    border-radius: 0;
  }
}
.tooltip {
  position: absolute;
  z-index: 1080;
  display: block;
  margin: 0;
  font-family: var(--bs-font-sans-serif);
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  word-spacing: normal;
  white-space: normal;
  line-break: auto;
  font-size: 0.875rem;
  word-wrap: break-word;
  opacity: 0;
}
.tooltip.show {
  opacity: 0.9;
}
.tooltip .tooltip-arrow {
  position: absolute;
  display: block;
  width: 0.8rem;
  height: 0.4rem;
}
.tooltip .tooltip-arrow::before {
  position: absolute;
  content: "";
  border-color: transparent;
  border-style: solid;
}

.bs-tooltip-top, .bs-tooltip-auto[data-popper-placement^=top] {
  padding: 0.4rem 0;
}
.bs-tooltip-top .tooltip-arrow, .bs-tooltip-auto[data-popper-placement^=top] .tooltip-arrow {
  bottom: 0;
}
.bs-tooltip-top .tooltip-arrow::before, .bs-tooltip-auto[data-popper-placement^=top] .tooltip-arrow::before {
  top: -1px;
  border-width: 0.4rem 0.4rem 0;
  border-top-color: #000;
}

.bs-tooltip-end, .bs-tooltip-auto[data-popper-placement^=right] {
  padding: 0 0.4rem;
}
.bs-tooltip-end .tooltip-arrow, .bs-tooltip-auto[data-popper-placement^=right] .tooltip-arrow {
  left: 0;
  width: 0.4rem;
  height: 0.8rem;
}
.bs-tooltip-end .tooltip-arrow::before, .bs-tooltip-auto[data-popper-placement^=right] .tooltip-arrow::before {
  right: -1px;
  border-width: 0.4rem 0.4rem 0.4rem 0;
  border-right-color: #000;
}

.bs-tooltip-bottom, .bs-tooltip-auto[data-popper-placement^=bottom] {
  padding: 0.4rem 0;
}
.bs-tooltip-bottom .tooltip-arrow, .bs-tooltip-auto[data-popper-placement^=bottom] .tooltip-arrow {
  top: 0;
}
.bs-tooltip-bottom .tooltip-arrow::before, .bs-tooltip-auto[data-popper-placement^=bottom] .tooltip-arrow::before {
  bottom: -1px;
  border-width: 0 0.4rem 0.4rem;
  border-bottom-color: #000;
}

.bs-tooltip-start, .bs-tooltip-auto[data-popper-placement^=left] {
  padding: 0 0.4rem;
}
.bs-tooltip-start .tooltip-arrow, .bs-tooltip-auto[data-popper-placement^=left] .tooltip-arrow {
  right: 0;
  width: 0.4rem;
  height: 0.8rem;
}
.bs-tooltip-start .tooltip-arrow::before, .bs-tooltip-auto[data-popper-placement^=left] .tooltip-arrow::before {
  left: -1px;
  border-width: 0.4rem 0 0.4rem 0.4rem;
  border-left-color: #000;
}

.tooltip-inner {
  max-width: 200px;
  padding: 0.25rem 0.5rem;
  color: #fff;
  text-align: center;
  background-color: #000;
  border-radius: 0.25rem;
}

.popover {
  position: absolute;
  top: 0;
  left: 0 /* rtl:ignore */;
  z-index: 1070;
  display: block;
  max-width: 276px;
  font-family: var(--bs-font-sans-serif);
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  word-spacing: normal;
  white-space: normal;
  line-break: auto;
  font-size: 0.875rem;
  word-wrap: break-word;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 0.3rem;
}
.popover .popover-arrow {
  position: absolute;
  display: block;
  width: 1rem;
  height: 0.5rem;
}
.popover .popover-arrow::before, .popover .popover-arrow::after {
  position: absolute;
  display: block;
  content: "";
  border-color: transparent;
  border-style: solid;
}

.bs-popover-top > .popover-arrow, .bs-popover-auto[data-popper-placement^=top] > .popover-arrow {
  bottom: calc(-0.5rem - 1px);
}
.bs-popover-top > .popover-arrow::before, .bs-popover-auto[data-popper-placement^=top] > .popover-arrow::before {
  bottom: 0;
  border-width: 0.5rem 0.5rem 0;
  border-top-color: rgba(0, 0, 0, 0.25);
}
.bs-popover-top > .popover-arrow::after, .bs-popover-auto[data-popper-placement^=top] > .popover-arrow::after {
  bottom: 1px;
  border-width: 0.5rem 0.5rem 0;
  border-top-color: #fff;
}

.bs-popover-end > .popover-arrow, .bs-popover-auto[data-popper-placement^=right] > .popover-arrow {
  left: calc(-0.5rem - 1px);
  width: 0.5rem;
  height: 1rem;
}
.bs-popover-end > .popover-arrow::before, .bs-popover-auto[data-popper-placement^=right] > .popover-arrow::before {
  left: 0;
  border-width: 0.5rem 0.5rem 0.5rem 0;
  border-right-color: rgba(0, 0, 0, 0.25);
}
.bs-popover-end > .popover-arrow::after, .bs-popover-auto[data-popper-placement^=right] > .popover-arrow::after {
  left: 1px;
  border-width: 0.5rem 0.5rem 0.5rem 0;
  border-right-color: #fff;
}

.bs-popover-bottom > .popover-arrow, .bs-popover-auto[data-popper-placement^=bottom] > .popover-arrow {
  top: calc(-0.5rem - 1px);
}
.bs-popover-bottom > .popover-arrow::before, .bs-popover-auto[data-popper-placement^=bottom] > .popover-arrow::before {
  top: 0;
  border-width: 0 0.5rem 0.5rem 0.5rem;
  border-bottom-color: rgba(0, 0, 0, 0.25);
}
.bs-popover-bottom > .popover-arrow::after, .bs-popover-auto[data-popper-placement^=bottom] > .popover-arrow::after {
  top: 1px;
  border-width: 0 0.5rem 0.5rem 0.5rem;
  border-bottom-color: #fff;
}
.bs-popover-bottom .popover-header::before, .bs-popover-auto[data-popper-placement^=bottom] .popover-header::before {
  position: absolute;
  top: 0;
  left: 50%;
  display: block;
  width: 1rem;
  margin-left: -0.5rem;
  content: "";
  border-bottom: 1px solid rgb(239.7, 239.7, 239.7);
}

.bs-popover-start > .popover-arrow, .bs-popover-auto[data-popper-placement^=left] > .popover-arrow {
  right: calc(-0.5rem - 1px);
  width: 0.5rem;
  height: 1rem;
}
.bs-popover-start > .popover-arrow::before, .bs-popover-auto[data-popper-placement^=left] > .popover-arrow::before {
  right: 0;
  border-width: 0.5rem 0 0.5rem 0.5rem;
  border-left-color: rgba(0, 0, 0, 0.25);
}
.bs-popover-start > .popover-arrow::after, .bs-popover-auto[data-popper-placement^=left] > .popover-arrow::after {
  right: 1px;
  border-width: 0.5rem 0 0.5rem 0.5rem;
  border-left-color: #fff;
}

.popover-header {
  padding: 0.5rem 1rem;
  margin-bottom: 0;
  font-size: 1rem;
  background-color: rgb(239.7, 239.7, 239.7);
  border-bottom: 1px solid rgba(0, 0, 0, 0.2);
  border-top-left-radius: calc(0.3rem - 1px);
  border-top-right-radius: calc(0.3rem - 1px);
}
.popover-header:empty {
  display: none;
}

.popover-body {
  padding: 1rem 1rem;
  color: #212529;
}

.carousel {
  position: relative;
}

.carousel.pointer-event {
  touch-action: pan-y;
}

.carousel-inner {
  position: relative;
  width: 100%;
  overflow: hidden;
}
.carousel-inner::after {
  display: block;
  clear: both;
  content: "";
}

.carousel-item {
  position: relative;
  display: none;
  float: left;
  width: 100%;
  margin-right: -100%;
  backface-visibility: hidden;
  transition: transform 0.6s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .carousel-item {
    transition: none;
  }
}

.carousel-item.active,
.carousel-item-next,
.carousel-item-prev {
  display: block;
}

/* rtl:begin:ignore */
.carousel-item-next:not(.carousel-item-start),
.active.carousel-item-end {
  transform: translateX(100%);
}

.carousel-item-prev:not(.carousel-item-end),
.active.carousel-item-start {
  transform: translateX(-100%);
}

/* rtl:end:ignore */
.carousel-fade .carousel-item {
  opacity: 0;
  transition-property: opacity;
  transform: none;
}
.carousel-fade .carousel-item.active,
.carousel-fade .carousel-item-next.carousel-item-start,
.carousel-fade .carousel-item-prev.carousel-item-end {
  z-index: 1;
  opacity: 1;
}
.carousel-fade .active.carousel-item-start,
.carousel-fade .active.carousel-item-end {
  z-index: 0;
  opacity: 0;
  transition: opacity 0s 0.6s;
}
@media (prefers-reduced-motion: reduce) {
  .carousel-fade .active.carousel-item-start,
  .carousel-fade .active.carousel-item-end {
    transition: none;
  }
}

.carousel-control-prev,
.carousel-control-next {
  position: absolute;
  top: 0;
  bottom: 0;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 15%;
  padding: 0;
  color: #fff;
  text-align: center;
  background: none;
  border: 0;
  opacity: 0.5;
  transition: opacity 0.15s ease;
}
@media (prefers-reduced-motion: reduce) {
  .carousel-control-prev,
  .carousel-control-next {
    transition: none;
  }
}
.carousel-control-prev:hover, .carousel-control-prev:focus,
.carousel-control-next:hover,
.carousel-control-next:focus {
  color: #fff;
  text-decoration: none;
  outline: 0;
  opacity: 0.9;
}

.carousel-control-prev {
  left: 0;
}

.carousel-control-next {
  right: 0;
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  background-repeat: no-repeat;
  background-position: 50%;
  background-size: 100% 100%;
}

/* rtl:options: {
  "autoRename": true,
  "stringMap":[ {
    "name"    : "prev-next",
    "search"  : "prev",
    "replace" : "next"
  } ]
} */
.carousel-control-prev-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fff'%3e%3cpath d='M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z'/%3e%3c/svg%3e");
}

.carousel-control-next-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fff'%3e%3cpath d='M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}

.carousel-indicators {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 2;
  display: flex;
  justify-content: center;
  padding: 0;
  margin-right: 15%;
  margin-bottom: 1rem;
  margin-left: 15%;
  list-style: none;
}
.carousel-indicators [data-bs-target] {
  box-sizing: content-box;
  flex: 0 1 auto;
  width: 30px;
  height: 3px;
  padding: 0;
  margin-right: 3px;
  margin-left: 3px;
  text-indent: -999px;
  cursor: pointer;
  background-color: #fff;
  background-clip: padding-box;
  border: 0;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  opacity: 0.5;
  transition: opacity 0.6s ease;
}
@media (prefers-reduced-motion: reduce) {
  .carousel-indicators [data-bs-target] {
    transition: none;
  }
}
.carousel-indicators .active {
  opacity: 1;
}

.carousel-caption {
  position: absolute;
  right: 15%;
  bottom: 1.25rem;
  left: 15%;
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
  color: #fff;
  text-align: center;
}

.carousel-dark .carousel-control-prev-icon,
.carousel-dark .carousel-control-next-icon {
  filter: invert(1) grayscale(100);
}
.carousel-dark .carousel-indicators [data-bs-target] {
  background-color: #000;
}
.carousel-dark .carousel-caption {
  color: #000;
}

@keyframes spinner-border {
  to {
    transform: rotate(360deg) /* rtl:ignore */;
  }
}
.spinner-border {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  vertical-align: -0.125em;
  border: 0.25em solid currentColor;
  border-right-color: transparent;
  border-radius: 50%;
  animation: 0.75s linear infinite spinner-border;
}

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
  border-width: 0.2em;
}

@keyframes spinner-grow {
  0% {
    transform: scale(0);
  }
  50% {
    opacity: 1;
    transform: none;
  }
}
.spinner-grow {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  vertical-align: -0.125em;
  background-color: currentColor;
  border-radius: 50%;
  opacity: 0;
  animation: 0.75s linear infinite spinner-grow;
}

.spinner-grow-sm {
  width: 1rem;
  height: 1rem;
}

@media (prefers-reduced-motion: reduce) {
  .spinner-border,
  .spinner-grow {
    animation-duration: 1.5s;
  }
}
.offcanvas {
  position: fixed;
  bottom: 0;
  z-index: 1050;
  display: flex;
  flex-direction: column;
  max-width: 100%;
  visibility: hidden;
  background-color: #fff;
  background-clip: padding-box;
  outline: 0;
  transition: transform 0.3s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .offcanvas {
    transition: none;
  }
}

.offcanvas-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1rem;
}
.offcanvas-header .btn-close {
  padding: 0.5rem 0.5rem;
  margin-top: -0.5rem;
  margin-right: -0.5rem;
  margin-bottom: -0.5rem;
}

.offcanvas-title {
  margin-bottom: 0;
  line-height: 1.5;
}

.offcanvas-body {
  flex-grow: 1;
  padding: 1rem 1rem;
  overflow-y: auto;
}

.offcanvas-start {
  top: 0;
  left: 0;
  width: 400px;
  border-right: 1px solid rgba(0, 0, 0, 0.2);
  transform: translateX(-100%);
}

.offcanvas-end {
  top: 0;
  right: 0;
  width: 400px;
  border-left: 1px solid rgba(0, 0, 0, 0.2);
  transform: translateX(100%);
}

.offcanvas-top {
  top: 0;
  right: 0;
  left: 0;
  height: 30vh;
  max-height: 100%;
  border-bottom: 1px solid rgba(0, 0, 0, 0.2);
  transform: translateY(-100%);
}

.offcanvas-bottom {
  right: 0;
  left: 0;
  height: 30vh;
  max-height: 100%;
  border-top: 1px solid rgba(0, 0, 0, 0.2);
  transform: translateY(100%);
}

.offcanvas.show {
  transform: none;
}

.clearfix::after {
  display: block;
  clear: both;
  content: "";
}

.link-primary {
  color: #0d6efd;
}
.link-primary:hover, .link-primary:focus {
  color: rgb(10.4, 88, 202.4);
}

.link-secondary {
  color: #6c757d;
}
.link-secondary:hover, .link-secondary:focus {
  color: rgb(86.4, 93.6, 100);
}

.link-success {
  color: #198754;
}
.link-success:hover, .link-success:focus {
  color: rgb(20, 108, 67.2);
}

.link-info {
  color: #0dcaf0;
}
.link-info:hover, .link-info:focus {
  color: rgb(61.4, 212.6, 243);
}

.link-warning {
  color: #ffc107;
}
.link-warning:hover, .link-warning:focus {
  color: rgb(255, 205.4, 56.6);
}

.link-danger {
  color: #dc3545;
}
.link-danger:hover, .link-danger:focus {
  color: rgb(176, 42.4, 55.2);
}

.link-light {
  color: #f8f9fa;
}
.link-light:hover, .link-light:focus {
  color: rgb(249.4, 250.2, 251);
}

.link-dark {
  color: #212529;
}
.link-dark:hover, .link-dark:focus {
  color: rgb(26.4, 29.6, 32.8);
}

.ratio {
  position: relative;
  width: 100%;
}
.ratio::before {
  display: block;
  padding-top: var(--bs-aspect-ratio);
  content: "";
}
.ratio > * {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.ratio-1x1 {
  --bs-aspect-ratio: 100%;
}

.ratio-4x3 {
  --bs-aspect-ratio: 75%;
}

.ratio-16x9 {
  --bs-aspect-ratio: 56.25%;
}

.ratio-21x9 {
  --bs-aspect-ratio: 42.8571428571%;
}

.fixed-top {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1030;
}

.fixed-bottom {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1030;
}

.sticky-top {
  position: sticky;
  top: 0;
  z-index: 1020;
}

@media (min-width: 576px) {
  .sticky-sm-top {
    position: sticky;
    top: 0;
    z-index: 1020;
  }
}
@media (min-width: 768px) {
  .sticky-md-top {
    position: sticky;
    top: 0;
    z-index: 1020;
  }
}
@media (min-width: 992px) {
  .sticky-lg-top {
    position: sticky;
    top: 0;
    z-index: 1020;
  }
}
@media (min-width: 1200px) {
  .sticky-xl-top {
    position: sticky;
    top: 0;
    z-index: 1020;
  }
}
@media (min-width: 1400px) {
  .sticky-xxl-top {
    position: sticky;
    top: 0;
    z-index: 1020;
  }
}
.visually-hidden,
.visually-hidden-focusable:not(:focus):not(:focus-within) {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

.stretched-link::after {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1;
  content: "";
}

.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.align-baseline {
  vertical-align: baseline !important;
}

.align-top {
  vertical-align: top !important;
}

.align-middle {
  vertical-align: middle !important;
}

.align-bottom {
  vertical-align: bottom !important;
}

.align-text-bottom {
  vertical-align: text-bottom !important;
}

.align-text-top {
  vertical-align: text-top !important;
}

.float-start {
  float: left !important;
}

.float-end {
  float: right !important;
}

.float-none {
  float: none !important;
}

.overflow-auto {
  overflow: auto !important;
}

.overflow-hidden {
  overflow: hidden !important;
}

.overflow-visible {
  overflow: visible !important;
}

.overflow-scroll {
  overflow: scroll !important;
}

.d-inline {
  display: inline !important;
}

.d-inline-block {
  display: inline-block !important;
}

.d-block {
  display: block !important;
}

.d-grid {
  display: grid !important;
}

.d-table {
  display: table !important;
}

.d-table-row {
  display: table-row !important;
}

.d-table-cell {
  display: table-cell !important;
}

.d-flex, .page_404 {
  display: flex !important;
}

.d-inline-flex {
  display: inline-flex !important;
}

.d-none {
  display: none !important;
}

.shadow {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.shadow-sm {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.shadow-lg {
  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
}

.shadow-none {
  box-shadow: none !important;
}

.position-static {
  position: static !important;
}

.position-relative {
  position: relative !important;
}

.position-absolute {
  position: absolute !important;
}

.position-fixed {
  position: fixed !important;
}

.position-sticky {
  position: sticky !important;
}

.top-0 {
  top: 0 !important;
}

.top-50 {
  top: 50% !important;
}

.top-100 {
  top: 100% !important;
}

.bottom-0 {
  bottom: 0 !important;
}

.bottom-50 {
  bottom: 50% !important;
}

.bottom-100 {
  bottom: 100% !important;
}

.start-0 {
  left: 0 !important;
}

.start-50 {
  left: 50% !important;
}

.start-100 {
  left: 100% !important;
}

.end-0 {
  right: 0 !important;
}

.end-50 {
  right: 50% !important;
}

.end-100 {
  right: 100% !important;
}

.translate-middle {
  transform: translate(-50%, -50%) !important;
}

.translate-middle-x {
  transform: translateX(-50%) !important;
}

.translate-middle-y {
  transform: translateY(-50%) !important;
}

.border {
  border: 1px solid #dee2e6 !important;
}

.border-0 {
  border: 0 !important;
}

.border-top {
  border-top: 1px solid #dee2e6 !important;
}

.border-top-0 {
  border-top: 0 !important;
}

.border-end {
  border-right: 1px solid #dee2e6 !important;
}

.border-end-0 {
  border-right: 0 !important;
}

.border-bottom {
  border-bottom: 1px solid #dee2e6 !important;
}

.border-bottom-0 {
  border-bottom: 0 !important;
}

.border-start {
  border-left: 1px solid #dee2e6 !important;
}

.border-start-0 {
  border-left: 0 !important;
}

.border-primary {
  border-color: #0d6efd !important;
}

.border-secondary {
  border-color: #6c757d !important;
}

.border-success {
  border-color: #198754 !important;
}

.border-info {
  border-color: #0dcaf0 !important;
}

.border-warning {
  border-color: #ffc107 !important;
}

.border-danger {
  border-color: #dc3545 !important;
}

.border-light {
  border-color: #f8f9fa !important;
}

.border-dark {
  border-color: #212529 !important;
}

.border-white {
  border-color: #fff !important;
}

.border-1 {
  border-width: 1px !important;
}

.border-2 {
  border-width: 2px !important;
}

.border-3 {
  border-width: 3px !important;
}

.border-4 {
  border-width: 4px !important;
}

.border-5 {
  border-width: 5px !important;
}

.w-25 {
  width: 25% !important;
}

.w-50 {
  width: 50% !important;
}

.w-75 {
  width: 75% !important;
}

.w-100 {
  width: 100% !important;
}

.w-auto {
  width: auto !important;
}

.mw-100 {
  max-width: 100% !important;
}

.vw-100 {
  width: 100vw !important;
}

.min-vw-100 {
  min-width: 100vw !important;
}

.h-25 {
  height: 25% !important;
}

.h-50 {
  height: 50% !important;
}

.h-75 {
  height: 75% !important;
}

.h-100 {
  height: 100% !important;
}

.h-auto {
  height: auto !important;
}

.mh-100 {
  max-height: 100% !important;
}

.vh-100 {
  height: 100vh !important;
}

.min-vh-100 {
  min-height: 100vh !important;
}

.flex-fill {
  flex: 1 1 auto !important;
}

.flex-row {
  flex-direction: row !important;
}

.flex-column {
  flex-direction: column !important;
}

.flex-row-reverse {
  flex-direction: row-reverse !important;
}

.flex-column-reverse {
  flex-direction: column-reverse !important;
}

.flex-grow-0 {
  flex-grow: 0 !important;
}

.flex-grow-1 {
  flex-grow: 1 !important;
}

.flex-shrink-0 {
  flex-shrink: 0 !important;
}

.flex-shrink-1 {
  flex-shrink: 1 !important;
}

.flex-wrap {
  flex-wrap: wrap !important;
}

.flex-nowrap {
  flex-wrap: nowrap !important;
}

.flex-wrap-reverse {
  flex-wrap: wrap-reverse !important;
}

.gap-0 {
  gap: 0 !important;
}

.gap-1 {
  gap: 0.25rem !important;
}

.gap-2 {
  gap: 0.5rem !important;
}

.gap-3 {
  gap: 1rem !important;
}

.gap-4 {
  gap: 1.5rem !important;
}

.gap-5 {
  gap: 3rem !important;
}

.justify-content-start {
  justify-content: flex-start !important;
}

.justify-content-end {
  justify-content: flex-end !important;
}

.justify-content-center {
  justify-content: center !important;
}

.justify-content-between {
  justify-content: space-between !important;
}

.justify-content-around {
  justify-content: space-around !important;
}

.justify-content-evenly {
  justify-content: space-evenly !important;
}

.align-items-start {
  align-items: flex-start !important;
}

.align-items-end {
  align-items: flex-end !important;
}

.align-items-center, .page_404 {
  align-items: center !important;
}

.align-items-baseline {
  align-items: baseline !important;
}

.align-items-stretch {
  align-items: stretch !important;
}

.align-content-start {
  align-content: flex-start !important;
}

.align-content-end {
  align-content: flex-end !important;
}

.align-content-center {
  align-content: center !important;
}

.align-content-between {
  align-content: space-between !important;
}

.align-content-around {
  align-content: space-around !important;
}

.align-content-stretch {
  align-content: stretch !important;
}

.align-self-auto {
  align-self: auto !important;
}

.align-self-start {
  align-self: flex-start !important;
}

.align-self-end {
  align-self: flex-end !important;
}

.align-self-center {
  align-self: center !important;
}

.align-self-baseline {
  align-self: baseline !important;
}

.align-self-stretch {
  align-self: stretch !important;
}

.order-first {
  order: -1 !important;
}

.order-0 {
  order: 0 !important;
}

.order-1 {
  order: 1 !important;
}

.order-2 {
  order: 2 !important;
}

.order-3 {
  order: 3 !important;
}

.order-4 {
  order: 4 !important;
}

.order-5 {
  order: 5 !important;
}

.order-last {
  order: 6 !important;
}

.m-0 {
  margin: 0 !important;
}

.m-1 {
  margin: 0.25rem !important;
}

.m-2 {
  margin: 0.5rem !important;
}

.m-3 {
  margin: 1rem !important;
}

.m-4 {
  margin: 1.5rem !important;
}

.m-5 {
  margin: 3rem !important;
}

.m-auto {
  margin: auto !important;
}

.mx-0 {
  margin-right: 0 !important;
  margin-left: 0 !important;
}

.mx-1 {
  margin-right: 0.25rem !important;
  margin-left: 0.25rem !important;
}

.mx-2 {
  margin-right: 0.5rem !important;
  margin-left: 0.5rem !important;
}

.mx-3 {
  margin-right: 1rem !important;
  margin-left: 1rem !important;
}

.mx-4 {
  margin-right: 1.5rem !important;
  margin-left: 1.5rem !important;
}

.mx-5 {
  margin-right: 3rem !important;
  margin-left: 3rem !important;
}

.mx-auto {
  margin-right: auto !important;
  margin-left: auto !important;
}

.my-0 {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

.my-1 {
  margin-top: 0.25rem !important;
  margin-bottom: 0.25rem !important;
}

.my-2 {
  margin-top: 0.5rem !important;
  margin-bottom: 0.5rem !important;
}

.my-3 {
  margin-top: 1rem !important;
  margin-bottom: 1rem !important;
}

.my-4 {
  margin-top: 1.5rem !important;
  margin-bottom: 1.5rem !important;
}

.my-5 {
  margin-top: 3rem !important;
  margin-bottom: 3rem !important;
}

.my-auto {
  margin-top: auto !important;
  margin-bottom: auto !important;
}

.mt-0 {
  margin-top: 0 !important;
}

.mt-1 {
  margin-top: 0.25rem !important;
}

.mt-2 {
  margin-top: 0.5rem !important;
}

.mt-3 {
  margin-top: 1rem !important;
}

.mt-4 {
  margin-top: 1.5rem !important;
}

.mt-5 {
  margin-top: 3rem !important;
}

.mt-auto {
  margin-top: auto !important;
}

.me-0 {
  margin-right: 0 !important;
}

.me-1 {
  margin-right: 0.25rem !important;
}

.me-2 {
  margin-right: 0.5rem !important;
}

.me-3 {
  margin-right: 1rem !important;
}

.me-4 {
  margin-right: 1.5rem !important;
}

.me-5 {
  margin-right: 3rem !important;
}

.me-auto {
  margin-right: auto !important;
}

.mb-0 {
  margin-bottom: 0 !important;
}

.mb-1 {
  margin-bottom: 0.25rem !important;
}

.mb-2 {
  margin-bottom: 0.5rem !important;
}

.mb-3 {
  margin-bottom: 1rem !important;
}

.mb-4 {
  margin-bottom: 1.5rem !important;
}

.mb-5 {
  margin-bottom: 3rem !important;
}

.mb-auto {
  margin-bottom: auto !important;
}

.ms-0 {
  margin-left: 0 !important;
}

.ms-1 {
  margin-left: 0.25rem !important;
}

.ms-2 {
  margin-left: 0.5rem !important;
}

.ms-3 {
  margin-left: 1rem !important;
}

.ms-4 {
  margin-left: 1.5rem !important;
}

.ms-5 {
  margin-left: 3rem !important;
}

.ms-auto {
  margin-left: auto !important;
}

.p-0 {
  padding: 0 !important;
}

.p-1 {
  padding: 0.25rem !important;
}

.p-2 {
  padding: 0.5rem !important;
}

.p-3 {
  padding: 1rem !important;
}

.p-4 {
  padding: 1.5rem !important;
}

.p-5 {
  padding: 3rem !important;
}

.px-0 {
  padding-right: 0 !important;
  padding-left: 0 !important;
}

.px-1 {
  padding-right: 0.25rem !important;
  padding-left: 0.25rem !important;
}

.px-2 {
  padding-right: 0.5rem !important;
  padding-left: 0.5rem !important;
}

.px-3 {
  padding-right: 1rem !important;
  padding-left: 1rem !important;
}

.px-4 {
  padding-right: 1.5rem !important;
  padding-left: 1.5rem !important;
}

.px-5 {
  padding-right: 3rem !important;
  padding-left: 3rem !important;
}

.py-0 {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

.py-1 {
  padding-top: 0.25rem !important;
  padding-bottom: 0.25rem !important;
}

.py-2 {
  padding-top: 0.5rem !important;
  padding-bottom: 0.5rem !important;
}

.py-3 {
  padding-top: 1rem !important;
  padding-bottom: 1rem !important;
}

.py-4 {
  padding-top: 1.5rem !important;
  padding-bottom: 1.5rem !important;
}

.py-5 {
  padding-top: 3rem !important;
  padding-bottom: 3rem !important;
}

.pt-0 {
  padding-top: 0 !important;
}

.pt-1 {
  padding-top: 0.25rem !important;
}

.pt-2 {
  padding-top: 0.5rem !important;
}

.pt-3 {
  padding-top: 1rem !important;
}

.pt-4 {
  padding-top: 1.5rem !important;
}

.pt-5 {
  padding-top: 3rem !important;
}

.pe-0 {
  padding-right: 0 !important;
}

.pe-1 {
  padding-right: 0.25rem !important;
}

.pe-2 {
  padding-right: 0.5rem !important;
}

.pe-3 {
  padding-right: 1rem !important;
}

.pe-4 {
  padding-right: 1.5rem !important;
}

.pe-5 {
  padding-right: 3rem !important;
}

.pb-0 {
  padding-bottom: 0 !important;
}

.pb-1 {
  padding-bottom: 0.25rem !important;
}

.pb-2 {
  padding-bottom: 0.5rem !important;
}

.pb-3 {
  padding-bottom: 1rem !important;
}

.pb-4 {
  padding-bottom: 1.5rem !important;
}

.pb-5 {
  padding-bottom: 3rem !important;
}

.ps-0 {
  padding-left: 0 !important;
}

.ps-1 {
  padding-left: 0.25rem !important;
}

.ps-2 {
  padding-left: 0.5rem !important;
}

.ps-3 {
  padding-left: 1rem !important;
}

.ps-4 {
  padding-left: 1.5rem !important;
}

.ps-5 {
  padding-left: 3rem !important;
}

.font-monospace {
  font-family: var(--bs-font-monospace) !important;
}

.fs-1 {
  font-size: calc(1.375rem + 1.5vw) !important;
}

.fs-2 {
  font-size: calc(1.325rem + 0.9vw) !important;
}

.fs-3 {
  font-size: calc(1.3rem + 0.6vw) !important;
}

.fs-4 {
  font-size: calc(1.275rem + 0.3vw) !important;
}

.fs-5 {
  font-size: 1.25rem !important;
}

.fs-6 {
  font-size: 1rem !important;
}

.fst-italic {
  font-style: italic !important;
}

.fst-normal {
  font-style: normal !important;
}

.fw-light {
  font-weight: 300 !important;
}

.fw-lighter {
  font-weight: lighter !important;
}

.fw-normal {
  font-weight: 400 !important;
}

.fw-bold {
  font-weight: 700 !important;
}

.fw-bolder {
  font-weight: bolder !important;
}

.lh-1 {
  line-height: 1 !important;
}

.lh-sm {
  line-height: 1.25 !important;
}

.lh-base {
  line-height: 1.5 !important;
}

.lh-lg {
  line-height: 2 !important;
}

.text-start {
  text-align: left !important;
}

.text-end {
  text-align: right !important;
}

.text-center {
  text-align: center !important;
}

.text-decoration-none {
  text-decoration: none !important;
}

.text-decoration-underline {
  text-decoration: underline !important;
}

.text-decoration-line-through {
  text-decoration: line-through !important;
}

.text-lowercase {
  text-transform: lowercase !important;
}

.text-uppercase {
  text-transform: uppercase !important;
}

.text-capitalize {
  text-transform: capitalize !important;
}

.text-wrap {
  white-space: normal !important;
}

.text-nowrap {
  white-space: nowrap !important;
}

/* rtl:begin:remove */
.text-break {
  word-wrap: break-word !important;
  word-break: break-word !important;
}

/* rtl:end:remove */
.text-primary {
  color: #0d6efd !important;
}

.text-secondary {
  color: #6c757d !important;
}

.text-success {
  color: #198754 !important;
}

.text-info {
  color: #0dcaf0 !important;
}

.text-warning {
  color: #ffc107 !important;
}

.text-danger {
  color: #dc3545 !important;
}

.text-light {
  color: #f8f9fa !important;
}

.text-dark {
  color: #212529 !important;
}

.text-white {
  color: #fff !important;
}

.text-body {
  color: #212529 !important;
}

.text-muted {
  color: #6c757d !important;
}

.text-black-50 {
  color: rgba(0, 0, 0, 0.5) !important;
}

.text-white-50 {
  color: rgba(255, 255, 255, 0.5) !important;
}

.text-reset {
  color: inherit !important;
}

.bg-primary {
  background-color: #0d6efd !important;
}

.bg-secondary {
  background-color: #6c757d !important;
}

.bg-success {
  background-color: #198754 !important;
}

.bg-info {
  background-color: #0dcaf0 !important;
}

.bg-warning {
  background-color: #ffc107 !important;
}

.bg-danger {
  background-color: #dc3545 !important;
}

.bg-light {
  background-color: #f8f9fa !important;
}

.bg-dark {
  background-color: #212529 !important;
}

.bg-body {
  background-color: #fff !important;
}

.bg-white {
  background-color: #fff !important;
}

.bg-transparent {
  background-color: transparent !important;
}

.bg-gradient {
  background-image: var(--bs-gradient) !important;
}

.user-select-all {
  -webkit-user-select: all !important;
     -moz-user-select: all !important;
          user-select: all !important;
}

.user-select-auto {
  -webkit-user-select: auto !important;
     -moz-user-select: auto !important;
          user-select: auto !important;
}

.user-select-none {
  -webkit-user-select: none !important;
     -moz-user-select: none !important;
          user-select: none !important;
}

.pe-none {
  pointer-events: none !important;
}

.pe-auto {
  pointer-events: auto !important;
}

.rounded {
  border-radius: 0.25rem !important;
}

.rounded-0 {
  border-radius: 0 !important;
}

.rounded-1 {
  border-radius: 0.2rem !important;
}

.rounded-2 {
  border-radius: 0.25rem !important;
}

.rounded-3 {
  border-radius: 0.3rem !important;
}

.rounded-circle {
  border-radius: 50% !important;
}

.rounded-pill {
  border-radius: 50rem !important;
}

.rounded-top {
  border-top-left-radius: 0.25rem !important;
  border-top-right-radius: 0.25rem !important;
}

.rounded-end {
  border-top-right-radius: 0.25rem !important;
  border-bottom-right-radius: 0.25rem !important;
}

.rounded-bottom {
  border-bottom-right-radius: 0.25rem !important;
  border-bottom-left-radius: 0.25rem !important;
}

.rounded-start {
  border-bottom-left-radius: 0.25rem !important;
  border-top-left-radius: 0.25rem !important;
}

.visible {
  visibility: visible !important;
}

.invisible {
  visibility: hidden !important;
}

@media (min-width: 576px) {
  .float-sm-start {
    float: left !important;
  }
  .float-sm-end {
    float: right !important;
  }
  .float-sm-none {
    float: none !important;
  }
  .d-sm-inline {
    display: inline !important;
  }
  .d-sm-inline-block {
    display: inline-block !important;
  }
  .d-sm-block {
    display: block !important;
  }
  .d-sm-grid {
    display: grid !important;
  }
  .d-sm-table {
    display: table !important;
  }
  .d-sm-table-row {
    display: table-row !important;
  }
  .d-sm-table-cell {
    display: table-cell !important;
  }
  .d-sm-flex {
    display: flex !important;
  }
  .d-sm-inline-flex {
    display: inline-flex !important;
  }
  .d-sm-none {
    display: none !important;
  }
  .flex-sm-fill {
    flex: 1 1 auto !important;
  }
  .flex-sm-row {
    flex-direction: row !important;
  }
  .flex-sm-column {
    flex-direction: column !important;
  }
  .flex-sm-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-sm-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-sm-grow-0 {
    flex-grow: 0 !important;
  }
  .flex-sm-grow-1 {
    flex-grow: 1 !important;
  }
  .flex-sm-shrink-0 {
    flex-shrink: 0 !important;
  }
  .flex-sm-shrink-1 {
    flex-shrink: 1 !important;
  }
  .flex-sm-wrap {
    flex-wrap: wrap !important;
  }
  .flex-sm-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-sm-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .gap-sm-0 {
    gap: 0 !important;
  }
  .gap-sm-1 {
    gap: 0.25rem !important;
  }
  .gap-sm-2 {
    gap: 0.5rem !important;
  }
  .gap-sm-3 {
    gap: 1rem !important;
  }
  .gap-sm-4 {
    gap: 1.5rem !important;
  }
  .gap-sm-5 {
    gap: 3rem !important;
  }
  .justify-content-sm-start {
    justify-content: flex-start !important;
  }
  .justify-content-sm-end {
    justify-content: flex-end !important;
  }
  .justify-content-sm-center {
    justify-content: center !important;
  }
  .justify-content-sm-between {
    justify-content: space-between !important;
  }
  .justify-content-sm-around {
    justify-content: space-around !important;
  }
  .justify-content-sm-evenly {
    justify-content: space-evenly !important;
  }
  .align-items-sm-start {
    align-items: flex-start !important;
  }
  .align-items-sm-end {
    align-items: flex-end !important;
  }
  .align-items-sm-center {
    align-items: center !important;
  }
  .align-items-sm-baseline {
    align-items: baseline !important;
  }
  .align-items-sm-stretch {
    align-items: stretch !important;
  }
  .align-content-sm-start {
    align-content: flex-start !important;
  }
  .align-content-sm-end {
    align-content: flex-end !important;
  }
  .align-content-sm-center {
    align-content: center !important;
  }
  .align-content-sm-between {
    align-content: space-between !important;
  }
  .align-content-sm-around {
    align-content: space-around !important;
  }
  .align-content-sm-stretch {
    align-content: stretch !important;
  }
  .align-self-sm-auto {
    align-self: auto !important;
  }
  .align-self-sm-start {
    align-self: flex-start !important;
  }
  .align-self-sm-end {
    align-self: flex-end !important;
  }
  .align-self-sm-center {
    align-self: center !important;
  }
  .align-self-sm-baseline {
    align-self: baseline !important;
  }
  .align-self-sm-stretch {
    align-self: stretch !important;
  }
  .order-sm-first {
    order: -1 !important;
  }
  .order-sm-0 {
    order: 0 !important;
  }
  .order-sm-1 {
    order: 1 !important;
  }
  .order-sm-2 {
    order: 2 !important;
  }
  .order-sm-3 {
    order: 3 !important;
  }
  .order-sm-4 {
    order: 4 !important;
  }
  .order-sm-5 {
    order: 5 !important;
  }
  .order-sm-last {
    order: 6 !important;
  }
  .m-sm-0 {
    margin: 0 !important;
  }
  .m-sm-1 {
    margin: 0.25rem !important;
  }
  .m-sm-2 {
    margin: 0.5rem !important;
  }
  .m-sm-3 {
    margin: 1rem !important;
  }
  .m-sm-4 {
    margin: 1.5rem !important;
  }
  .m-sm-5 {
    margin: 3rem !important;
  }
  .m-sm-auto {
    margin: auto !important;
  }
  .mx-sm-0 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }
  .mx-sm-1 {
    margin-right: 0.25rem !important;
    margin-left: 0.25rem !important;
  }
  .mx-sm-2 {
    margin-right: 0.5rem !important;
    margin-left: 0.5rem !important;
  }
  .mx-sm-3 {
    margin-right: 1rem !important;
    margin-left: 1rem !important;
  }
  .mx-sm-4 {
    margin-right: 1.5rem !important;
    margin-left: 1.5rem !important;
  }
  .mx-sm-5 {
    margin-right: 3rem !important;
    margin-left: 3rem !important;
  }
  .mx-sm-auto {
    margin-right: auto !important;
    margin-left: auto !important;
  }
  .my-sm-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }
  .my-sm-1 {
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important;
  }
  .my-sm-2 {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }
  .my-sm-3 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important;
  }
  .my-sm-4 {
    margin-top: 1.5rem !important;
    margin-bottom: 1.5rem !important;
  }
  .my-sm-5 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important;
  }
  .my-sm-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
  .mt-sm-0 {
    margin-top: 0 !important;
  }
  .mt-sm-1 {
    margin-top: 0.25rem !important;
  }
  .mt-sm-2 {
    margin-top: 0.5rem !important;
  }
  .mt-sm-3 {
    margin-top: 1rem !important;
  }
  .mt-sm-4 {
    margin-top: 1.5rem !important;
  }
  .mt-sm-5 {
    margin-top: 3rem !important;
  }
  .mt-sm-auto {
    margin-top: auto !important;
  }
  .me-sm-0 {
    margin-right: 0 !important;
  }
  .me-sm-1 {
    margin-right: 0.25rem !important;
  }
  .me-sm-2 {
    margin-right: 0.5rem !important;
  }
  .me-sm-3 {
    margin-right: 1rem !important;
  }
  .me-sm-4 {
    margin-right: 1.5rem !important;
  }
  .me-sm-5 {
    margin-right: 3rem !important;
  }
  .me-sm-auto {
    margin-right: auto !important;
  }
  .mb-sm-0 {
    margin-bottom: 0 !important;
  }
  .mb-sm-1 {
    margin-bottom: 0.25rem !important;
  }
  .mb-sm-2 {
    margin-bottom: 0.5rem !important;
  }
  .mb-sm-3 {
    margin-bottom: 1rem !important;
  }
  .mb-sm-4 {
    margin-bottom: 1.5rem !important;
  }
  .mb-sm-5 {
    margin-bottom: 3rem !important;
  }
  .mb-sm-auto {
    margin-bottom: auto !important;
  }
  .ms-sm-0 {
    margin-left: 0 !important;
  }
  .ms-sm-1 {
    margin-left: 0.25rem !important;
  }
  .ms-sm-2 {
    margin-left: 0.5rem !important;
  }
  .ms-sm-3 {
    margin-left: 1rem !important;
  }
  .ms-sm-4 {
    margin-left: 1.5rem !important;
  }
  .ms-sm-5 {
    margin-left: 3rem !important;
  }
  .ms-sm-auto {
    margin-left: auto !important;
  }
  .p-sm-0 {
    padding: 0 !important;
  }
  .p-sm-1 {
    padding: 0.25rem !important;
  }
  .p-sm-2 {
    padding: 0.5rem !important;
  }
  .p-sm-3 {
    padding: 1rem !important;
  }
  .p-sm-4 {
    padding: 1.5rem !important;
  }
  .p-sm-5 {
    padding: 3rem !important;
  }
  .px-sm-0 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }
  .px-sm-1 {
    padding-right: 0.25rem !important;
    padding-left: 0.25rem !important;
  }
  .px-sm-2 {
    padding-right: 0.5rem !important;
    padding-left: 0.5rem !important;
  }
  .px-sm-3 {
    padding-right: 1rem !important;
    padding-left: 1rem !important;
  }
  .px-sm-4 {
    padding-right: 1.5rem !important;
    padding-left: 1.5rem !important;
  }
  .px-sm-5 {
    padding-right: 3rem !important;
    padding-left: 3rem !important;
  }
  .py-sm-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }
  .py-sm-1 {
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important;
  }
  .py-sm-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }
  .py-sm-3 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }
  .py-sm-4 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important;
  }
  .py-sm-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }
  .pt-sm-0 {
    padding-top: 0 !important;
  }
  .pt-sm-1 {
    padding-top: 0.25rem !important;
  }
  .pt-sm-2 {
    padding-top: 0.5rem !important;
  }
  .pt-sm-3 {
    padding-top: 1rem !important;
  }
  .pt-sm-4 {
    padding-top: 1.5rem !important;
  }
  .pt-sm-5 {
    padding-top: 3rem !important;
  }
  .pe-sm-0 {
    padding-right: 0 !important;
  }
  .pe-sm-1 {
    padding-right: 0.25rem !important;
  }
  .pe-sm-2 {
    padding-right: 0.5rem !important;
  }
  .pe-sm-3 {
    padding-right: 1rem !important;
  }
  .pe-sm-4 {
    padding-right: 1.5rem !important;
  }
  .pe-sm-5 {
    padding-right: 3rem !important;
  }
  .pb-sm-0 {
    padding-bottom: 0 !important;
  }
  .pb-sm-1 {
    padding-bottom: 0.25rem !important;
  }
  .pb-sm-2 {
    padding-bottom: 0.5rem !important;
  }
  .pb-sm-3 {
    padding-bottom: 1rem !important;
  }
  .pb-sm-4 {
    padding-bottom: 1.5rem !important;
  }
  .pb-sm-5 {
    padding-bottom: 3rem !important;
  }
  .ps-sm-0 {
    padding-left: 0 !important;
  }
  .ps-sm-1 {
    padding-left: 0.25rem !important;
  }
  .ps-sm-2 {
    padding-left: 0.5rem !important;
  }
  .ps-sm-3 {
    padding-left: 1rem !important;
  }
  .ps-sm-4 {
    padding-left: 1.5rem !important;
  }
  .ps-sm-5 {
    padding-left: 3rem !important;
  }
  .text-sm-start {
    text-align: left !important;
  }
  .text-sm-end {
    text-align: right !important;
  }
  .text-sm-center {
    text-align: center !important;
  }
}
@media (min-width: 768px) {
  .float-md-start {
    float: left !important;
  }
  .float-md-end {
    float: right !important;
  }
  .float-md-none {
    float: none !important;
  }
  .d-md-inline {
    display: inline !important;
  }
  .d-md-inline-block {
    display: inline-block !important;
  }
  .d-md-block {
    display: block !important;
  }
  .d-md-grid {
    display: grid !important;
  }
  .d-md-table {
    display: table !important;
  }
  .d-md-table-row {
    display: table-row !important;
  }
  .d-md-table-cell {
    display: table-cell !important;
  }
  .d-md-flex {
    display: flex !important;
  }
  .d-md-inline-flex {
    display: inline-flex !important;
  }
  .d-md-none {
    display: none !important;
  }
  .flex-md-fill {
    flex: 1 1 auto !important;
  }
  .flex-md-row {
    flex-direction: row !important;
  }
  .flex-md-column {
    flex-direction: column !important;
  }
  .flex-md-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-md-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-md-grow-0 {
    flex-grow: 0 !important;
  }
  .flex-md-grow-1 {
    flex-grow: 1 !important;
  }
  .flex-md-shrink-0 {
    flex-shrink: 0 !important;
  }
  .flex-md-shrink-1 {
    flex-shrink: 1 !important;
  }
  .flex-md-wrap {
    flex-wrap: wrap !important;
  }
  .flex-md-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-md-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .gap-md-0 {
    gap: 0 !important;
  }
  .gap-md-1 {
    gap: 0.25rem !important;
  }
  .gap-md-2 {
    gap: 0.5rem !important;
  }
  .gap-md-3 {
    gap: 1rem !important;
  }
  .gap-md-4 {
    gap: 1.5rem !important;
  }
  .gap-md-5 {
    gap: 3rem !important;
  }
  .justify-content-md-start {
    justify-content: flex-start !important;
  }
  .justify-content-md-end {
    justify-content: flex-end !important;
  }
  .justify-content-md-center {
    justify-content: center !important;
  }
  .justify-content-md-between {
    justify-content: space-between !important;
  }
  .justify-content-md-around {
    justify-content: space-around !important;
  }
  .justify-content-md-evenly {
    justify-content: space-evenly !important;
  }
  .align-items-md-start {
    align-items: flex-start !important;
  }
  .align-items-md-end {
    align-items: flex-end !important;
  }
  .align-items-md-center {
    align-items: center !important;
  }
  .align-items-md-baseline {
    align-items: baseline !important;
  }
  .align-items-md-stretch {
    align-items: stretch !important;
  }
  .align-content-md-start {
    align-content: flex-start !important;
  }
  .align-content-md-end {
    align-content: flex-end !important;
  }
  .align-content-md-center {
    align-content: center !important;
  }
  .align-content-md-between {
    align-content: space-between !important;
  }
  .align-content-md-around {
    align-content: space-around !important;
  }
  .align-content-md-stretch {
    align-content: stretch !important;
  }
  .align-self-md-auto {
    align-self: auto !important;
  }
  .align-self-md-start {
    align-self: flex-start !important;
  }
  .align-self-md-end {
    align-self: flex-end !important;
  }
  .align-self-md-center {
    align-self: center !important;
  }
  .align-self-md-baseline {
    align-self: baseline !important;
  }
  .align-self-md-stretch {
    align-self: stretch !important;
  }
  .order-md-first {
    order: -1 !important;
  }
  .order-md-0 {
    order: 0 !important;
  }
  .order-md-1 {
    order: 1 !important;
  }
  .order-md-2 {
    order: 2 !important;
  }
  .order-md-3 {
    order: 3 !important;
  }
  .order-md-4 {
    order: 4 !important;
  }
  .order-md-5 {
    order: 5 !important;
  }
  .order-md-last {
    order: 6 !important;
  }
  .m-md-0 {
    margin: 0 !important;
  }
  .m-md-1 {
    margin: 0.25rem !important;
  }
  .m-md-2 {
    margin: 0.5rem !important;
  }
  .m-md-3 {
    margin: 1rem !important;
  }
  .m-md-4 {
    margin: 1.5rem !important;
  }
  .m-md-5 {
    margin: 3rem !important;
  }
  .m-md-auto {
    margin: auto !important;
  }
  .mx-md-0 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }
  .mx-md-1 {
    margin-right: 0.25rem !important;
    margin-left: 0.25rem !important;
  }
  .mx-md-2 {
    margin-right: 0.5rem !important;
    margin-left: 0.5rem !important;
  }
  .mx-md-3 {
    margin-right: 1rem !important;
    margin-left: 1rem !important;
  }
  .mx-md-4 {
    margin-right: 1.5rem !important;
    margin-left: 1.5rem !important;
  }
  .mx-md-5 {
    margin-right: 3rem !important;
    margin-left: 3rem !important;
  }
  .mx-md-auto {
    margin-right: auto !important;
    margin-left: auto !important;
  }
  .my-md-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }
  .my-md-1 {
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important;
  }
  .my-md-2 {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }
  .my-md-3 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important;
  }
  .my-md-4 {
    margin-top: 1.5rem !important;
    margin-bottom: 1.5rem !important;
  }
  .my-md-5 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important;
  }
  .my-md-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
  .mt-md-0 {
    margin-top: 0 !important;
  }
  .mt-md-1 {
    margin-top: 0.25rem !important;
  }
  .mt-md-2 {
    margin-top: 0.5rem !important;
  }
  .mt-md-3 {
    margin-top: 1rem !important;
  }
  .mt-md-4 {
    margin-top: 1.5rem !important;
  }
  .mt-md-5 {
    margin-top: 3rem !important;
  }
  .mt-md-auto {
    margin-top: auto !important;
  }
  .me-md-0 {
    margin-right: 0 !important;
  }
  .me-md-1 {
    margin-right: 0.25rem !important;
  }
  .me-md-2 {
    margin-right: 0.5rem !important;
  }
  .me-md-3 {
    margin-right: 1rem !important;
  }
  .me-md-4 {
    margin-right: 1.5rem !important;
  }
  .me-md-5 {
    margin-right: 3rem !important;
  }
  .me-md-auto {
    margin-right: auto !important;
  }
  .mb-md-0 {
    margin-bottom: 0 !important;
  }
  .mb-md-1 {
    margin-bottom: 0.25rem !important;
  }
  .mb-md-2 {
    margin-bottom: 0.5rem !important;
  }
  .mb-md-3 {
    margin-bottom: 1rem !important;
  }
  .mb-md-4 {
    margin-bottom: 1.5rem !important;
  }
  .mb-md-5 {
    margin-bottom: 3rem !important;
  }
  .mb-md-auto {
    margin-bottom: auto !important;
  }
  .ms-md-0 {
    margin-left: 0 !important;
  }
  .ms-md-1 {
    margin-left: 0.25rem !important;
  }
  .ms-md-2 {
    margin-left: 0.5rem !important;
  }
  .ms-md-3 {
    margin-left: 1rem !important;
  }
  .ms-md-4 {
    margin-left: 1.5rem !important;
  }
  .ms-md-5 {
    margin-left: 3rem !important;
  }
  .ms-md-auto {
    margin-left: auto !important;
  }
  .p-md-0 {
    padding: 0 !important;
  }
  .p-md-1 {
    padding: 0.25rem !important;
  }
  .p-md-2 {
    padding: 0.5rem !important;
  }
  .p-md-3 {
    padding: 1rem !important;
  }
  .p-md-4 {
    padding: 1.5rem !important;
  }
  .p-md-5 {
    padding: 3rem !important;
  }
  .px-md-0 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }
  .px-md-1 {
    padding-right: 0.25rem !important;
    padding-left: 0.25rem !important;
  }
  .px-md-2 {
    padding-right: 0.5rem !important;
    padding-left: 0.5rem !important;
  }
  .px-md-3 {
    padding-right: 1rem !important;
    padding-left: 1rem !important;
  }
  .px-md-4 {
    padding-right: 1.5rem !important;
    padding-left: 1.5rem !important;
  }
  .px-md-5 {
    padding-right: 3rem !important;
    padding-left: 3rem !important;
  }
  .py-md-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }
  .py-md-1 {
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important;
  }
  .py-md-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }
  .py-md-3 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }
  .py-md-4 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important;
  }
  .py-md-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }
  .pt-md-0 {
    padding-top: 0 !important;
  }
  .pt-md-1 {
    padding-top: 0.25rem !important;
  }
  .pt-md-2 {
    padding-top: 0.5rem !important;
  }
  .pt-md-3 {
    padding-top: 1rem !important;
  }
  .pt-md-4 {
    padding-top: 1.5rem !important;
  }
  .pt-md-5 {
    padding-top: 3rem !important;
  }
  .pe-md-0 {
    padding-right: 0 !important;
  }
  .pe-md-1 {
    padding-right: 0.25rem !important;
  }
  .pe-md-2 {
    padding-right: 0.5rem !important;
  }
  .pe-md-3 {
    padding-right: 1rem !important;
  }
  .pe-md-4 {
    padding-right: 1.5rem !important;
  }
  .pe-md-5 {
    padding-right: 3rem !important;
  }
  .pb-md-0 {
    padding-bottom: 0 !important;
  }
  .pb-md-1 {
    padding-bottom: 0.25rem !important;
  }
  .pb-md-2 {
    padding-bottom: 0.5rem !important;
  }
  .pb-md-3 {
    padding-bottom: 1rem !important;
  }
  .pb-md-4 {
    padding-bottom: 1.5rem !important;
  }
  .pb-md-5 {
    padding-bottom: 3rem !important;
  }
  .ps-md-0 {
    padding-left: 0 !important;
  }
  .ps-md-1 {
    padding-left: 0.25rem !important;
  }
  .ps-md-2 {
    padding-left: 0.5rem !important;
  }
  .ps-md-3 {
    padding-left: 1rem !important;
  }
  .ps-md-4 {
    padding-left: 1.5rem !important;
  }
  .ps-md-5 {
    padding-left: 3rem !important;
  }
  .text-md-start {
    text-align: left !important;
  }
  .text-md-end {
    text-align: right !important;
  }
  .text-md-center {
    text-align: center !important;
  }
}
@media (min-width: 992px) {
  .float-lg-start {
    float: left !important;
  }
  .float-lg-end {
    float: right !important;
  }
  .float-lg-none {
    float: none !important;
  }
  .d-lg-inline {
    display: inline !important;
  }
  .d-lg-inline-block {
    display: inline-block !important;
  }
  .d-lg-block {
    display: block !important;
  }
  .d-lg-grid {
    display: grid !important;
  }
  .d-lg-table {
    display: table !important;
  }
  .d-lg-table-row {
    display: table-row !important;
  }
  .d-lg-table-cell {
    display: table-cell !important;
  }
  .d-lg-flex {
    display: flex !important;
  }
  .d-lg-inline-flex {
    display: inline-flex !important;
  }
  .d-lg-none {
    display: none !important;
  }
  .flex-lg-fill {
    flex: 1 1 auto !important;
  }
  .flex-lg-row {
    flex-direction: row !important;
  }
  .flex-lg-column {
    flex-direction: column !important;
  }
  .flex-lg-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-lg-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-lg-grow-0 {
    flex-grow: 0 !important;
  }
  .flex-lg-grow-1 {
    flex-grow: 1 !important;
  }
  .flex-lg-shrink-0 {
    flex-shrink: 0 !important;
  }
  .flex-lg-shrink-1 {
    flex-shrink: 1 !important;
  }
  .flex-lg-wrap {
    flex-wrap: wrap !important;
  }
  .flex-lg-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-lg-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .gap-lg-0 {
    gap: 0 !important;
  }
  .gap-lg-1 {
    gap: 0.25rem !important;
  }
  .gap-lg-2 {
    gap: 0.5rem !important;
  }
  .gap-lg-3 {
    gap: 1rem !important;
  }
  .gap-lg-4 {
    gap: 1.5rem !important;
  }
  .gap-lg-5 {
    gap: 3rem !important;
  }
  .justify-content-lg-start {
    justify-content: flex-start !important;
  }
  .justify-content-lg-end {
    justify-content: flex-end !important;
  }
  .justify-content-lg-center {
    justify-content: center !important;
  }
  .justify-content-lg-between {
    justify-content: space-between !important;
  }
  .justify-content-lg-around {
    justify-content: space-around !important;
  }
  .justify-content-lg-evenly {
    justify-content: space-evenly !important;
  }
  .align-items-lg-start {
    align-items: flex-start !important;
  }
  .align-items-lg-end {
    align-items: flex-end !important;
  }
  .align-items-lg-center {
    align-items: center !important;
  }
  .align-items-lg-baseline {
    align-items: baseline !important;
  }
  .align-items-lg-stretch {
    align-items: stretch !important;
  }
  .align-content-lg-start {
    align-content: flex-start !important;
  }
  .align-content-lg-end {
    align-content: flex-end !important;
  }
  .align-content-lg-center {
    align-content: center !important;
  }
  .align-content-lg-between {
    align-content: space-between !important;
  }
  .align-content-lg-around {
    align-content: space-around !important;
  }
  .align-content-lg-stretch {
    align-content: stretch !important;
  }
  .align-self-lg-auto {
    align-self: auto !important;
  }
  .align-self-lg-start {
    align-self: flex-start !important;
  }
  .align-self-lg-end {
    align-self: flex-end !important;
  }
  .align-self-lg-center {
    align-self: center !important;
  }
  .align-self-lg-baseline {
    align-self: baseline !important;
  }
  .align-self-lg-stretch {
    align-self: stretch !important;
  }
  .order-lg-first {
    order: -1 !important;
  }
  .order-lg-0 {
    order: 0 !important;
  }
  .order-lg-1 {
    order: 1 !important;
  }
  .order-lg-2 {
    order: 2 !important;
  }
  .order-lg-3 {
    order: 3 !important;
  }
  .order-lg-4 {
    order: 4 !important;
  }
  .order-lg-5 {
    order: 5 !important;
  }
  .order-lg-last {
    order: 6 !important;
  }
  .m-lg-0 {
    margin: 0 !important;
  }
  .m-lg-1 {
    margin: 0.25rem !important;
  }
  .m-lg-2 {
    margin: 0.5rem !important;
  }
  .m-lg-3 {
    margin: 1rem !important;
  }
  .m-lg-4 {
    margin: 1.5rem !important;
  }
  .m-lg-5 {
    margin: 3rem !important;
  }
  .m-lg-auto {
    margin: auto !important;
  }
  .mx-lg-0 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }
  .mx-lg-1 {
    margin-right: 0.25rem !important;
    margin-left: 0.25rem !important;
  }
  .mx-lg-2 {
    margin-right: 0.5rem !important;
    margin-left: 0.5rem !important;
  }
  .mx-lg-3 {
    margin-right: 1rem !important;
    margin-left: 1rem !important;
  }
  .mx-lg-4 {
    margin-right: 1.5rem !important;
    margin-left: 1.5rem !important;
  }
  .mx-lg-5 {
    margin-right: 3rem !important;
    margin-left: 3rem !important;
  }
  .mx-lg-auto {
    margin-right: auto !important;
    margin-left: auto !important;
  }
  .my-lg-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }
  .my-lg-1 {
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important;
  }
  .my-lg-2 {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }
  .my-lg-3 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important;
  }
  .my-lg-4 {
    margin-top: 1.5rem !important;
    margin-bottom: 1.5rem !important;
  }
  .my-lg-5 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important;
  }
  .my-lg-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
  .mt-lg-0 {
    margin-top: 0 !important;
  }
  .mt-lg-1 {
    margin-top: 0.25rem !important;
  }
  .mt-lg-2 {
    margin-top: 0.5rem !important;
  }
  .mt-lg-3 {
    margin-top: 1rem !important;
  }
  .mt-lg-4 {
    margin-top: 1.5rem !important;
  }
  .mt-lg-5 {
    margin-top: 3rem !important;
  }
  .mt-lg-auto {
    margin-top: auto !important;
  }
  .me-lg-0 {
    margin-right: 0 !important;
  }
  .me-lg-1 {
    margin-right: 0.25rem !important;
  }
  .me-lg-2 {
    margin-right: 0.5rem !important;
  }
  .me-lg-3 {
    margin-right: 1rem !important;
  }
  .me-lg-4 {
    margin-right: 1.5rem !important;
  }
  .me-lg-5 {
    margin-right: 3rem !important;
  }
  .me-lg-auto {
    margin-right: auto !important;
  }
  .mb-lg-0 {
    margin-bottom: 0 !important;
  }
  .mb-lg-1 {
    margin-bottom: 0.25rem !important;
  }
  .mb-lg-2 {
    margin-bottom: 0.5rem !important;
  }
  .mb-lg-3 {
    margin-bottom: 1rem !important;
  }
  .mb-lg-4 {
    margin-bottom: 1.5rem !important;
  }
  .mb-lg-5 {
    margin-bottom: 3rem !important;
  }
  .mb-lg-auto {
    margin-bottom: auto !important;
  }
  .ms-lg-0 {
    margin-left: 0 !important;
  }
  .ms-lg-1 {
    margin-left: 0.25rem !important;
  }
  .ms-lg-2 {
    margin-left: 0.5rem !important;
  }
  .ms-lg-3 {
    margin-left: 1rem !important;
  }
  .ms-lg-4 {
    margin-left: 1.5rem !important;
  }
  .ms-lg-5 {
    margin-left: 3rem !important;
  }
  .ms-lg-auto {
    margin-left: auto !important;
  }
  .p-lg-0 {
    padding: 0 !important;
  }
  .p-lg-1 {
    padding: 0.25rem !important;
  }
  .p-lg-2 {
    padding: 0.5rem !important;
  }
  .p-lg-3 {
    padding: 1rem !important;
  }
  .p-lg-4 {
    padding: 1.5rem !important;
  }
  .p-lg-5 {
    padding: 3rem !important;
  }
  .px-lg-0 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }
  .px-lg-1 {
    padding-right: 0.25rem !important;
    padding-left: 0.25rem !important;
  }
  .px-lg-2 {
    padding-right: 0.5rem !important;
    padding-left: 0.5rem !important;
  }
  .px-lg-3 {
    padding-right: 1rem !important;
    padding-left: 1rem !important;
  }
  .px-lg-4 {
    padding-right: 1.5rem !important;
    padding-left: 1.5rem !important;
  }
  .px-lg-5 {
    padding-right: 3rem !important;
    padding-left: 3rem !important;
  }
  .py-lg-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }
  .py-lg-1 {
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important;
  }
  .py-lg-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }
  .py-lg-3 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }
  .py-lg-4 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important;
  }
  .py-lg-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }
  .pt-lg-0 {
    padding-top: 0 !important;
  }
  .pt-lg-1 {
    padding-top: 0.25rem !important;
  }
  .pt-lg-2 {
    padding-top: 0.5rem !important;
  }
  .pt-lg-3 {
    padding-top: 1rem !important;
  }
  .pt-lg-4 {
    padding-top: 1.5rem !important;
  }
  .pt-lg-5 {
    padding-top: 3rem !important;
  }
  .pe-lg-0 {
    padding-right: 0 !important;
  }
  .pe-lg-1 {
    padding-right: 0.25rem !important;
  }
  .pe-lg-2 {
    padding-right: 0.5rem !important;
  }
  .pe-lg-3 {
    padding-right: 1rem !important;
  }
  .pe-lg-4 {
    padding-right: 1.5rem !important;
  }
  .pe-lg-5 {
    padding-right: 3rem !important;
  }
  .pb-lg-0 {
    padding-bottom: 0 !important;
  }
  .pb-lg-1 {
    padding-bottom: 0.25rem !important;
  }
  .pb-lg-2 {
    padding-bottom: 0.5rem !important;
  }
  .pb-lg-3 {
    padding-bottom: 1rem !important;
  }
  .pb-lg-4 {
    padding-bottom: 1.5rem !important;
  }
  .pb-lg-5 {
    padding-bottom: 3rem !important;
  }
  .ps-lg-0 {
    padding-left: 0 !important;
  }
  .ps-lg-1 {
    padding-left: 0.25rem !important;
  }
  .ps-lg-2 {
    padding-left: 0.5rem !important;
  }
  .ps-lg-3 {
    padding-left: 1rem !important;
  }
  .ps-lg-4 {
    padding-left: 1.5rem !important;
  }
  .ps-lg-5 {
    padding-left: 3rem !important;
  }
  .text-lg-start {
    text-align: left !important;
  }
  .text-lg-end {
    text-align: right !important;
  }
  .text-lg-center {
    text-align: center !important;
  }
}
@media (min-width: 1200px) {
  .float-xl-start {
    float: left !important;
  }
  .float-xl-end {
    float: right !important;
  }
  .float-xl-none {
    float: none !important;
  }
  .d-xl-inline {
    display: inline !important;
  }
  .d-xl-inline-block {
    display: inline-block !important;
  }
  .d-xl-block {
    display: block !important;
  }
  .d-xl-grid {
    display: grid !important;
  }
  .d-xl-table {
    display: table !important;
  }
  .d-xl-table-row {
    display: table-row !important;
  }
  .d-xl-table-cell {
    display: table-cell !important;
  }
  .d-xl-flex {
    display: flex !important;
  }
  .d-xl-inline-flex {
    display: inline-flex !important;
  }
  .d-xl-none {
    display: none !important;
  }
  .flex-xl-fill {
    flex: 1 1 auto !important;
  }
  .flex-xl-row {
    flex-direction: row !important;
  }
  .flex-xl-column {
    flex-direction: column !important;
  }
  .flex-xl-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-xl-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-xl-grow-0 {
    flex-grow: 0 !important;
  }
  .flex-xl-grow-1 {
    flex-grow: 1 !important;
  }
  .flex-xl-shrink-0 {
    flex-shrink: 0 !important;
  }
  .flex-xl-shrink-1 {
    flex-shrink: 1 !important;
  }
  .flex-xl-wrap {
    flex-wrap: wrap !important;
  }
  .flex-xl-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-xl-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .gap-xl-0 {
    gap: 0 !important;
  }
  .gap-xl-1 {
    gap: 0.25rem !important;
  }
  .gap-xl-2 {
    gap: 0.5rem !important;
  }
  .gap-xl-3 {
    gap: 1rem !important;
  }
  .gap-xl-4 {
    gap: 1.5rem !important;
  }
  .gap-xl-5 {
    gap: 3rem !important;
  }
  .justify-content-xl-start {
    justify-content: flex-start !important;
  }
  .justify-content-xl-end {
    justify-content: flex-end !important;
  }
  .justify-content-xl-center {
    justify-content: center !important;
  }
  .justify-content-xl-between {
    justify-content: space-between !important;
  }
  .justify-content-xl-around {
    justify-content: space-around !important;
  }
  .justify-content-xl-evenly {
    justify-content: space-evenly !important;
  }
  .align-items-xl-start {
    align-items: flex-start !important;
  }
  .align-items-xl-end {
    align-items: flex-end !important;
  }
  .align-items-xl-center {
    align-items: center !important;
  }
  .align-items-xl-baseline {
    align-items: baseline !important;
  }
  .align-items-xl-stretch {
    align-items: stretch !important;
  }
  .align-content-xl-start {
    align-content: flex-start !important;
  }
  .align-content-xl-end {
    align-content: flex-end !important;
  }
  .align-content-xl-center {
    align-content: center !important;
  }
  .align-content-xl-between {
    align-content: space-between !important;
  }
  .align-content-xl-around {
    align-content: space-around !important;
  }
  .align-content-xl-stretch {
    align-content: stretch !important;
  }
  .align-self-xl-auto {
    align-self: auto !important;
  }
  .align-self-xl-start {
    align-self: flex-start !important;
  }
  .align-self-xl-end {
    align-self: flex-end !important;
  }
  .align-self-xl-center {
    align-self: center !important;
  }
  .align-self-xl-baseline {
    align-self: baseline !important;
  }
  .align-self-xl-stretch {
    align-self: stretch !important;
  }
  .order-xl-first {
    order: -1 !important;
  }
  .order-xl-0 {
    order: 0 !important;
  }
  .order-xl-1 {
    order: 1 !important;
  }
  .order-xl-2 {
    order: 2 !important;
  }
  .order-xl-3 {
    order: 3 !important;
  }
  .order-xl-4 {
    order: 4 !important;
  }
  .order-xl-5 {
    order: 5 !important;
  }
  .order-xl-last {
    order: 6 !important;
  }
  .m-xl-0 {
    margin: 0 !important;
  }
  .m-xl-1 {
    margin: 0.25rem !important;
  }
  .m-xl-2 {
    margin: 0.5rem !important;
  }
  .m-xl-3 {
    margin: 1rem !important;
  }
  .m-xl-4 {
    margin: 1.5rem !important;
  }
  .m-xl-5 {
    margin: 3rem !important;
  }
  .m-xl-auto {
    margin: auto !important;
  }
  .mx-xl-0 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }
  .mx-xl-1 {
    margin-right: 0.25rem !important;
    margin-left: 0.25rem !important;
  }
  .mx-xl-2 {
    margin-right: 0.5rem !important;
    margin-left: 0.5rem !important;
  }
  .mx-xl-3 {
    margin-right: 1rem !important;
    margin-left: 1rem !important;
  }
  .mx-xl-4 {
    margin-right: 1.5rem !important;
    margin-left: 1.5rem !important;
  }
  .mx-xl-5 {
    margin-right: 3rem !important;
    margin-left: 3rem !important;
  }
  .mx-xl-auto {
    margin-right: auto !important;
    margin-left: auto !important;
  }
  .my-xl-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }
  .my-xl-1 {
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important;
  }
  .my-xl-2 {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }
  .my-xl-3 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important;
  }
  .my-xl-4 {
    margin-top: 1.5rem !important;
    margin-bottom: 1.5rem !important;
  }
  .my-xl-5 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important;
  }
  .my-xl-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
  .mt-xl-0 {
    margin-top: 0 !important;
  }
  .mt-xl-1 {
    margin-top: 0.25rem !important;
  }
  .mt-xl-2 {
    margin-top: 0.5rem !important;
  }
  .mt-xl-3 {
    margin-top: 1rem !important;
  }
  .mt-xl-4 {
    margin-top: 1.5rem !important;
  }
  .mt-xl-5 {
    margin-top: 3rem !important;
  }
  .mt-xl-auto {
    margin-top: auto !important;
  }
  .me-xl-0 {
    margin-right: 0 !important;
  }
  .me-xl-1 {
    margin-right: 0.25rem !important;
  }
  .me-xl-2 {
    margin-right: 0.5rem !important;
  }
  .me-xl-3 {
    margin-right: 1rem !important;
  }
  .me-xl-4 {
    margin-right: 1.5rem !important;
  }
  .me-xl-5 {
    margin-right: 3rem !important;
  }
  .me-xl-auto {
    margin-right: auto !important;
  }
  .mb-xl-0 {
    margin-bottom: 0 !important;
  }
  .mb-xl-1 {
    margin-bottom: 0.25rem !important;
  }
  .mb-xl-2 {
    margin-bottom: 0.5rem !important;
  }
  .mb-xl-3 {
    margin-bottom: 1rem !important;
  }
  .mb-xl-4 {
    margin-bottom: 1.5rem !important;
  }
  .mb-xl-5 {
    margin-bottom: 3rem !important;
  }
  .mb-xl-auto {
    margin-bottom: auto !important;
  }
  .ms-xl-0 {
    margin-left: 0 !important;
  }
  .ms-xl-1 {
    margin-left: 0.25rem !important;
  }
  .ms-xl-2 {
    margin-left: 0.5rem !important;
  }
  .ms-xl-3 {
    margin-left: 1rem !important;
  }
  .ms-xl-4 {
    margin-left: 1.5rem !important;
  }
  .ms-xl-5 {
    margin-left: 3rem !important;
  }
  .ms-xl-auto {
    margin-left: auto !important;
  }
  .p-xl-0 {
    padding: 0 !important;
  }
  .p-xl-1 {
    padding: 0.25rem !important;
  }
  .p-xl-2 {
    padding: 0.5rem !important;
  }
  .p-xl-3 {
    padding: 1rem !important;
  }
  .p-xl-4 {
    padding: 1.5rem !important;
  }
  .p-xl-5 {
    padding: 3rem !important;
  }
  .px-xl-0 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }
  .px-xl-1 {
    padding-right: 0.25rem !important;
    padding-left: 0.25rem !important;
  }
  .px-xl-2 {
    padding-right: 0.5rem !important;
    padding-left: 0.5rem !important;
  }
  .px-xl-3 {
    padding-right: 1rem !important;
    padding-left: 1rem !important;
  }
  .px-xl-4 {
    padding-right: 1.5rem !important;
    padding-left: 1.5rem !important;
  }
  .px-xl-5 {
    padding-right: 3rem !important;
    padding-left: 3rem !important;
  }
  .py-xl-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }
  .py-xl-1 {
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important;
  }
  .py-xl-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }
  .py-xl-3 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }
  .py-xl-4 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important;
  }
  .py-xl-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }
  .pt-xl-0 {
    padding-top: 0 !important;
  }
  .pt-xl-1 {
    padding-top: 0.25rem !important;
  }
  .pt-xl-2 {
    padding-top: 0.5rem !important;
  }
  .pt-xl-3 {
    padding-top: 1rem !important;
  }
  .pt-xl-4 {
    padding-top: 1.5rem !important;
  }
  .pt-xl-5 {
    padding-top: 3rem !important;
  }
  .pe-xl-0 {
    padding-right: 0 !important;
  }
  .pe-xl-1 {
    padding-right: 0.25rem !important;
  }
  .pe-xl-2 {
    padding-right: 0.5rem !important;
  }
  .pe-xl-3 {
    padding-right: 1rem !important;
  }
  .pe-xl-4 {
    padding-right: 1.5rem !important;
  }
  .pe-xl-5 {
    padding-right: 3rem !important;
  }
  .pb-xl-0 {
    padding-bottom: 0 !important;
  }
  .pb-xl-1 {
    padding-bottom: 0.25rem !important;
  }
  .pb-xl-2 {
    padding-bottom: 0.5rem !important;
  }
  .pb-xl-3 {
    padding-bottom: 1rem !important;
  }
  .pb-xl-4 {
    padding-bottom: 1.5rem !important;
  }
  .pb-xl-5 {
    padding-bottom: 3rem !important;
  }
  .ps-xl-0 {
    padding-left: 0 !important;
  }
  .ps-xl-1 {
    padding-left: 0.25rem !important;
  }
  .ps-xl-2 {
    padding-left: 0.5rem !important;
  }
  .ps-xl-3 {
    padding-left: 1rem !important;
  }
  .ps-xl-4 {
    padding-left: 1.5rem !important;
  }
  .ps-xl-5 {
    padding-left: 3rem !important;
  }
  .text-xl-start {
    text-align: left !important;
  }
  .text-xl-end {
    text-align: right !important;
  }
  .text-xl-center {
    text-align: center !important;
  }
}
@media (min-width: 1400px) {
  .float-xxl-start {
    float: left !important;
  }
  .float-xxl-end {
    float: right !important;
  }
  .float-xxl-none {
    float: none !important;
  }
  .d-xxl-inline {
    display: inline !important;
  }
  .d-xxl-inline-block {
    display: inline-block !important;
  }
  .d-xxl-block {
    display: block !important;
  }
  .d-xxl-grid {
    display: grid !important;
  }
  .d-xxl-table {
    display: table !important;
  }
  .d-xxl-table-row {
    display: table-row !important;
  }
  .d-xxl-table-cell {
    display: table-cell !important;
  }
  .d-xxl-flex {
    display: flex !important;
  }
  .d-xxl-inline-flex {
    display: inline-flex !important;
  }
  .d-xxl-none {
    display: none !important;
  }
  .flex-xxl-fill {
    flex: 1 1 auto !important;
  }
  .flex-xxl-row {
    flex-direction: row !important;
  }
  .flex-xxl-column {
    flex-direction: column !important;
  }
  .flex-xxl-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-xxl-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-xxl-grow-0 {
    flex-grow: 0 !important;
  }
  .flex-xxl-grow-1 {
    flex-grow: 1 !important;
  }
  .flex-xxl-shrink-0 {
    flex-shrink: 0 !important;
  }
  .flex-xxl-shrink-1 {
    flex-shrink: 1 !important;
  }
  .flex-xxl-wrap {
    flex-wrap: wrap !important;
  }
  .flex-xxl-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-xxl-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .gap-xxl-0 {
    gap: 0 !important;
  }
  .gap-xxl-1 {
    gap: 0.25rem !important;
  }
  .gap-xxl-2 {
    gap: 0.5rem !important;
  }
  .gap-xxl-3 {
    gap: 1rem !important;
  }
  .gap-xxl-4 {
    gap: 1.5rem !important;
  }
  .gap-xxl-5 {
    gap: 3rem !important;
  }
  .justify-content-xxl-start {
    justify-content: flex-start !important;
  }
  .justify-content-xxl-end {
    justify-content: flex-end !important;
  }
  .justify-content-xxl-center {
    justify-content: center !important;
  }
  .justify-content-xxl-between {
    justify-content: space-between !important;
  }
  .justify-content-xxl-around {
    justify-content: space-around !important;
  }
  .justify-content-xxl-evenly {
    justify-content: space-evenly !important;
  }
  .align-items-xxl-start {
    align-items: flex-start !important;
  }
  .align-items-xxl-end {
    align-items: flex-end !important;
  }
  .align-items-xxl-center {
    align-items: center !important;
  }
  .align-items-xxl-baseline {
    align-items: baseline !important;
  }
  .align-items-xxl-stretch {
    align-items: stretch !important;
  }
  .align-content-xxl-start {
    align-content: flex-start !important;
  }
  .align-content-xxl-end {
    align-content: flex-end !important;
  }
  .align-content-xxl-center {
    align-content: center !important;
  }
  .align-content-xxl-between {
    align-content: space-between !important;
  }
  .align-content-xxl-around {
    align-content: space-around !important;
  }
  .align-content-xxl-stretch {
    align-content: stretch !important;
  }
  .align-self-xxl-auto {
    align-self: auto !important;
  }
  .align-self-xxl-start {
    align-self: flex-start !important;
  }
  .align-self-xxl-end {
    align-self: flex-end !important;
  }
  .align-self-xxl-center {
    align-self: center !important;
  }
  .align-self-xxl-baseline {
    align-self: baseline !important;
  }
  .align-self-xxl-stretch {
    align-self: stretch !important;
  }
  .order-xxl-first {
    order: -1 !important;
  }
  .order-xxl-0 {
    order: 0 !important;
  }
  .order-xxl-1 {
    order: 1 !important;
  }
  .order-xxl-2 {
    order: 2 !important;
  }
  .order-xxl-3 {
    order: 3 !important;
  }
  .order-xxl-4 {
    order: 4 !important;
  }
  .order-xxl-5 {
    order: 5 !important;
  }
  .order-xxl-last {
    order: 6 !important;
  }
  .m-xxl-0 {
    margin: 0 !important;
  }
  .m-xxl-1 {
    margin: 0.25rem !important;
  }
  .m-xxl-2 {
    margin: 0.5rem !important;
  }
  .m-xxl-3 {
    margin: 1rem !important;
  }
  .m-xxl-4 {
    margin: 1.5rem !important;
  }
  .m-xxl-5 {
    margin: 3rem !important;
  }
  .m-xxl-auto {
    margin: auto !important;
  }
  .mx-xxl-0 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }
  .mx-xxl-1 {
    margin-right: 0.25rem !important;
    margin-left: 0.25rem !important;
  }
  .mx-xxl-2 {
    margin-right: 0.5rem !important;
    margin-left: 0.5rem !important;
  }
  .mx-xxl-3 {
    margin-right: 1rem !important;
    margin-left: 1rem !important;
  }
  .mx-xxl-4 {
    margin-right: 1.5rem !important;
    margin-left: 1.5rem !important;
  }
  .mx-xxl-5 {
    margin-right: 3rem !important;
    margin-left: 3rem !important;
  }
  .mx-xxl-auto {
    margin-right: auto !important;
    margin-left: auto !important;
  }
  .my-xxl-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }
  .my-xxl-1 {
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important;
  }
  .my-xxl-2 {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }
  .my-xxl-3 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important;
  }
  .my-xxl-4 {
    margin-top: 1.5rem !important;
    margin-bottom: 1.5rem !important;
  }
  .my-xxl-5 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important;
  }
  .my-xxl-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
  .mt-xxl-0 {
    margin-top: 0 !important;
  }
  .mt-xxl-1 {
    margin-top: 0.25rem !important;
  }
  .mt-xxl-2 {
    margin-top: 0.5rem !important;
  }
  .mt-xxl-3 {
    margin-top: 1rem !important;
  }
  .mt-xxl-4 {
    margin-top: 1.5rem !important;
  }
  .mt-xxl-5 {
    margin-top: 3rem !important;
  }
  .mt-xxl-auto {
    margin-top: auto !important;
  }
  .me-xxl-0 {
    margin-right: 0 !important;
  }
  .me-xxl-1 {
    margin-right: 0.25rem !important;
  }
  .me-xxl-2 {
    margin-right: 0.5rem !important;
  }
  .me-xxl-3 {
    margin-right: 1rem !important;
  }
  .me-xxl-4 {
    margin-right: 1.5rem !important;
  }
  .me-xxl-5 {
    margin-right: 3rem !important;
  }
  .me-xxl-auto {
    margin-right: auto !important;
  }
  .mb-xxl-0 {
    margin-bottom: 0 !important;
  }
  .mb-xxl-1 {
    margin-bottom: 0.25rem !important;
  }
  .mb-xxl-2 {
    margin-bottom: 0.5rem !important;
  }
  .mb-xxl-3 {
    margin-bottom: 1rem !important;
  }
  .mb-xxl-4 {
    margin-bottom: 1.5rem !important;
  }
  .mb-xxl-5 {
    margin-bottom: 3rem !important;
  }
  .mb-xxl-auto {
    margin-bottom: auto !important;
  }
  .ms-xxl-0 {
    margin-left: 0 !important;
  }
  .ms-xxl-1 {
    margin-left: 0.25rem !important;
  }
  .ms-xxl-2 {
    margin-left: 0.5rem !important;
  }
  .ms-xxl-3 {
    margin-left: 1rem !important;
  }
  .ms-xxl-4 {
    margin-left: 1.5rem !important;
  }
  .ms-xxl-5 {
    margin-left: 3rem !important;
  }
  .ms-xxl-auto {
    margin-left: auto !important;
  }
  .p-xxl-0 {
    padding: 0 !important;
  }
  .p-xxl-1 {
    padding: 0.25rem !important;
  }
  .p-xxl-2 {
    padding: 0.5rem !important;
  }
  .p-xxl-3 {
    padding: 1rem !important;
  }
  .p-xxl-4 {
    padding: 1.5rem !important;
  }
  .p-xxl-5 {
    padding: 3rem !important;
  }
  .px-xxl-0 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }
  .px-xxl-1 {
    padding-right: 0.25rem !important;
    padding-left: 0.25rem !important;
  }
  .px-xxl-2 {
    padding-right: 0.5rem !important;
    padding-left: 0.5rem !important;
  }
  .px-xxl-3 {
    padding-right: 1rem !important;
    padding-left: 1rem !important;
  }
  .px-xxl-4 {
    padding-right: 1.5rem !important;
    padding-left: 1.5rem !important;
  }
  .px-xxl-5 {
    padding-right: 3rem !important;
    padding-left: 3rem !important;
  }
  .py-xxl-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }
  .py-xxl-1 {
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important;
  }
  .py-xxl-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }
  .py-xxl-3 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }
  .py-xxl-4 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important;
  }
  .py-xxl-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }
  .pt-xxl-0 {
    padding-top: 0 !important;
  }
  .pt-xxl-1 {
    padding-top: 0.25rem !important;
  }
  .pt-xxl-2 {
    padding-top: 0.5rem !important;
  }
  .pt-xxl-3 {
    padding-top: 1rem !important;
  }
  .pt-xxl-4 {
    padding-top: 1.5rem !important;
  }
  .pt-xxl-5 {
    padding-top: 3rem !important;
  }
  .pe-xxl-0 {
    padding-right: 0 !important;
  }
  .pe-xxl-1 {
    padding-right: 0.25rem !important;
  }
  .pe-xxl-2 {
    padding-right: 0.5rem !important;
  }
  .pe-xxl-3 {
    padding-right: 1rem !important;
  }
  .pe-xxl-4 {
    padding-right: 1.5rem !important;
  }
  .pe-xxl-5 {
    padding-right: 3rem !important;
  }
  .pb-xxl-0 {
    padding-bottom: 0 !important;
  }
  .pb-xxl-1 {
    padding-bottom: 0.25rem !important;
  }
  .pb-xxl-2 {
    padding-bottom: 0.5rem !important;
  }
  .pb-xxl-3 {
    padding-bottom: 1rem !important;
  }
  .pb-xxl-4 {
    padding-bottom: 1.5rem !important;
  }
  .pb-xxl-5 {
    padding-bottom: 3rem !important;
  }
  .ps-xxl-0 {
    padding-left: 0 !important;
  }
  .ps-xxl-1 {
    padding-left: 0.25rem !important;
  }
  .ps-xxl-2 {
    padding-left: 0.5rem !important;
  }
  .ps-xxl-3 {
    padding-left: 1rem !important;
  }
  .ps-xxl-4 {
    padding-left: 1.5rem !important;
  }
  .ps-xxl-5 {
    padding-left: 3rem !important;
  }
  .text-xxl-start {
    text-align: left !important;
  }
  .text-xxl-end {
    text-align: right !important;
  }
  .text-xxl-center {
    text-align: center !important;
  }
}
@media (min-width: 1200px) {
  .fs-1 {
    font-size: 2.5rem !important;
  }
  .fs-2 {
    font-size: 2rem !important;
  }
  .fs-3 {
    font-size: 1.75rem !important;
  }
  .fs-4 {
    font-size: 1.5rem !important;
  }
}
@media print {
  .d-print-inline {
    display: inline !important;
  }
  .d-print-inline-block {
    display: inline-block !important;
  }
  .d-print-block {
    display: block !important;
  }
  .d-print-grid {
    display: grid !important;
  }
  .d-print-table {
    display: table !important;
  }
  .d-print-table-row {
    display: table-row !important;
  }
  .d-print-table-cell {
    display: table-cell !important;
  }
  .d-print-flex {
    display: flex !important;
  }
  .d-print-inline-flex {
    display: inline-flex !important;
  }
  .d-print-none {
    display: none !important;
  }
}
/*! normalize.css v8.0.1 | MIT License | github.com/necolas/normalize.css */
/* Document
   ========================================================================== */
/**
 * 1. Correct the line height in all browsers.
 * 2. Prevent adjustments of font size after orientation changes in iOS.
 */
html {
  line-height: 1.15; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
}

/* Sections
   ========================================================================== */
/**
 * Remove the margin in all browsers.
 */
body {
  margin: 0;
}

/**
 * Render the `main` element consistently in IE.
 */
main {
  display: block;
}

/**
 * Correct the font size and margin on `h1` elements within `section` and
 * `article` contexts in Chrome, Firefox, and Safari.
 */
h1, .h1 {
  font-size: 2em;
  margin: 0.67em 0;
}

/* Grouping content
   ========================================================================== */
/**
 * 1. Add the correct box sizing in Firefox.
 * 2. Show the overflow in Edge and IE.
 */
hr {
  box-sizing: content-box; /* 1 */
  height: 0; /* 1 */
  overflow: visible; /* 2 */
}

/**
 * 1. Correct the inheritance and scaling of font size in all browsers.
 * 2. Correct the odd `em` font sizing in all browsers.
 */
pre {
  font-family: monospace, monospace; /* 1 */
  font-size: 1em; /* 2 */
}

/* Text-level semantics
   ========================================================================== */
/**
 * Remove the gray background on active links in IE 10.
 */
a {
  background-color: transparent;
}

/**
 * 1. Remove the bottom border in Chrome 57-
 * 2. Add the correct text decoration in Chrome, Edge, IE, Opera, and Safari.
 */
abbr[title] {
  border-bottom: none; /* 1 */
  text-decoration: underline; /* 2 */
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted; /* 2 */
}

/**
 * Add the correct font weight in Chrome, Edge, and Safari.
 */
b,
strong {
  font-weight: bolder;
}

/**
 * 1. Correct the inheritance and scaling of font size in all browsers.
 * 2. Correct the odd `em` font sizing in all browsers.
 */
code,
kbd,
samp {
  font-family: monospace, monospace; /* 1 */
  font-size: 1em; /* 2 */
}

/**
 * Add the correct font size in all browsers.
 */
small, .small {
  font-size: 80%;
}

/**
 * Prevent `sub` and `sup` elements from affecting the line height in
 * all browsers.
 */
sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/* Embedded content
   ========================================================================== */
/**
 * Remove the border on images inside links in IE 10.
 */
img {
  border-style: none;
}

/* Forms
   ========================================================================== */
/**
 * 1. Change the font styles in all browsers.
 * 2. Remove the margin in Firefox and Safari.
 */
button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-size: 100%; /* 1 */
  line-height: 1.15; /* 1 */
  margin: 0; /* 2 */
}

/**
 * Show the overflow in IE.
 * 1. Show the overflow in Edge.
 */
button,
input {
  /* 1 */
  overflow: visible;
}

/**
 * Remove the inheritance of text transform in Edge, Firefox, and IE.
 * 1. Remove the inheritance of text transform in Firefox.
 */
button,
select {
  /* 1 */
  text-transform: none;
}

/**
 * Correct the inability to style clickable types in iOS and Safari.
 */
button,
[type=button],
[type=reset],
[type=submit] {
  -webkit-appearance: button;
}

/**
 * Remove the inner border and padding in Firefox.
 */
button::-moz-focus-inner,
[type=button]::-moz-focus-inner,
[type=reset]::-moz-focus-inner,
[type=submit]::-moz-focus-inner {
  border-style: none;
  padding: 0;
}

/**
 * Restore the focus styles unset by the previous rule.
 */
button:-moz-focusring,
[type=button]:-moz-focusring,
[type=reset]:-moz-focusring,
[type=submit]:-moz-focusring {
  outline: 1px dotted ButtonText;
}

/**
 * Correct the padding in Firefox.
 */
fieldset {
  padding: 0.35em 0.75em 0.625em;
}

/**
 * 1. Correct the text wrapping in Edge and IE.
 * 2. Correct the color inheritance from `fieldset` elements in IE.
 * 3. Remove the padding so developers are not caught out when they zero out
 *    `fieldset` elements in all browsers.
 */
legend {
  box-sizing: border-box; /* 1 */
  color: inherit; /* 2 */
  display: table; /* 1 */
  max-width: 100%; /* 1 */
  padding: 0; /* 3 */
  white-space: normal; /* 1 */
}

/**
 * Add the correct vertical alignment in Chrome, Firefox, and Opera.
 */
progress {
  vertical-align: baseline;
}

/**
 * Remove the default vertical scrollbar in IE 10+.
 */
textarea {
  overflow: auto;
}

/**
 * 1. Add the correct box sizing in IE 10.
 * 2. Remove the padding in IE 10.
 */
[type=checkbox],
[type=radio] {
  box-sizing: border-box; /* 1 */
  padding: 0; /* 2 */
}

/**
 * Correct the cursor style of increment and decrement buttons in Chrome.
 */
[type=number]::-webkit-inner-spin-button,
[type=number]::-webkit-outer-spin-button {
  height: auto;
}

/**
 * 1. Correct the odd appearance in Chrome and Safari.
 * 2. Correct the outline style in Safari.
 */
[type=search] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/**
 * Remove the inner padding in Chrome and Safari on macOS.
 */
[type=search]::-webkit-search-decoration {
  -webkit-appearance: none;
}

/**
 * 1. Correct the inability to style clickable types in iOS and Safari.
 * 2. Change font properties to `inherit` in Safari.
 */
::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/* Interactive
   ========================================================================== */
/*
 * Add the correct display in Edge, IE 10+, and Firefox.
 */
details {
  display: block;
}

/*
 * Add the correct display in all browsers.
 */
summary {
  display: list-item;
}

/* Misc
   ========================================================================== */
/**
 * Add the correct display in IE 10+.
 */
template {
  display: none;
}

/**
 * Add the correct display in IE 10.
 */
[hidden] {
  display: none;
}

body {
  background: #fff;
  font-weight: 400;
  font-size: 16px;
  line-height: 1.42;
  color: #000;
  overflow-x: hidden;
  word-break: break-word;
}

img {
  max-width: 100%;
  vertical-align: middle;
}

.error-massage-input {
  display: block;
  color: red;
  font-size: 14px;
  margin: 10px 0;
}

.is-invalid {
  display: block;
}

.line-though {
  text-decoration: line-through;
}

.line-through {
  border-top: 1px solid #dadce0;
  display: inline-block;
  width: 100%;
}

a {
  text-decoration: none;
  transition: 0.3s all;
}
a:focus, a:hover {
  text-decoration: none;
  transition: 0.3s all;
}

.form-control:focus,
.lending-sidebar-right .sidebar input:focus,
.lending-sidebar-right .sidebar select:focus {
  border-color: #EC1C24;
  box-shadow: 0 0 0 0.2rem rgba(236, 28, 36, 0.25);
}

.hover-scale {
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
}
.hover-scale img {
  transition: all 0.3s;
}
.hover-scale:hover img {
  transform: scale(1.1);
}

.back-to-top {
  position: fixed;
  bottom: 50px;
  right: 30px;
  background-color: #f36f21;
  width: 50px;
  height: 50px;
  line-height: 50px;
  border-radius: 100%;
  text-align: center;
  font-size: 30px;
  color: #fff;
  z-index: 111;
}

.title-theme {
  color: #EC1C24;
}

.page_static .wrap_content {
  padding: 30px 0 60px;
}

.scrollbar-style-asset {
  overflow-y: auto;
}
.scrollbar-style-asset::-webkit-scrollbar {
  width: 8px;
  height: 3px;
  border-radius: 20px;
}
.scrollbar-style-asset::-webkit-scrollbar-track {
  background: #D9D9D9;
  border-radius: 20px;
}
.scrollbar-style-asset::-webkit-scrollbar-thumb {
  background: var(--xanh, #1A68B3);
  border-radius: 20px;
}

/*============================
==============================
==============================
error 404
==============================
==============================
============================*/
.page_404 {
  min-height: 100vh;
  padding-top: 20px;
  padding-bottom: 20px;
}
.page_404 img {
  max-width: 100%;
}
.page_404 h4, .page_404 .h4 {
  font-size: 18px;
  color: rgb(65, 65, 65);
  margin-top: 20px;
  margin-bottom: 15px;
}
.page_404 a {
  background: #388cf5;
  color: #fff;
  font-weight: normal;
}

@media (max-width: 768px) {
  .back-to-top {
    display: none;
  }
  .page_static .wrap_content {
    padding: 0px 0 30px;
  }
}
@media (max-width: 439px) {
  .type-title-1 p {
    font-size: 22px;
  }
}
@keyframes loader-rotate {
  100% {
    transform: rotate(360deg);
  }
}
@keyframes loader-dash {
  0% {
    stroke-dasharray: 1, 200;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 89, 200;
    stroke-dashoffset: -35px;
  }
  100% {
    stroke-dasharray: 89, 200;
    stroke-dashoffset: -136px;
  }
}
button {
  cursor: pointer;
}

button,
input,
optgroup,
select,
textarea {
  outline: none;
  box-shadow: 0;
}
button:focus, button:active, button:hover,
input:focus,
input:active,
input:hover,
optgroup:focus,
optgroup:active,
optgroup:hover,
select:focus,
select:active,
select:hover,
textarea:focus,
textarea:active,
textarea:hover {
  outline: none;
  box-shadow: 0;
}

.modal-open .mm-slideout {
  z-index: unset;
}

.lazy[src="images/loading-icon.jpeg"] {
  -o-object-fit: scale-down !important;
     object-fit: scale-down !important;
  max-width: 20px;
}

.fs-10 {
  font-size: 10px;
}

.fs-11 {
  font-size: 11px;
}

.fs-12 {
  font-size: 12px;
}

.fs-13 {
  font-size: 13px;
}

.fs-14 {
  font-size: 14px;
}

.fs-15 {
  font-size: 15px;
}

.fs-16 {
  font-size: 16px;
}

.fs-17 {
  font-size: 17px;
}

.fs-18 {
  font-size: 18px;
}

.fs-19 {
  font-size: 19px;
}

.fs-20 {
  font-size: 20px;
}

.fs-21 {
  font-size: 21px;
}

.fs-22 {
  font-size: 22px;
}

.fs-23 {
  font-size: 23px;
}

.fs-24 {
  font-size: 24px;
}

.fs-25 {
  font-size: 25px;
}

.fs-26 {
  font-size: 26px;
}

.fs-27 {
  font-size: 27px;
}

.fs-28 {
  font-size: 28px;
}

.fs-29 {
  font-size: 29px;
}

.fs-30 {
  font-size: 30px;
}

.fs-31 {
  font-size: 31px;
}

.fs-32 {
  font-size: 32px;
}

.fs-33 {
  font-size: 33px;
}

.fs-34 {
  font-size: 34px;
}

.fs-35 {
  font-size: 35px;
}

.fs-36 {
  font-size: 36px;
}

.fs-37 {
  font-size: 37px;
}

.fs-38 {
  font-size: 38px;
}

.fs-39 {
  font-size: 39px;
}

.fs-40 {
  font-size: 40px;
}

.fs-41 {
  font-size: 41px;
}

.fs-42 {
  font-size: 42px;
}

.fs-43 {
  font-size: 43px;
}

.fs-44 {
  font-size: 44px;
}

.fs-45 {
  font-size: 45px;
}

.fs-46 {
  font-size: 46px;
}

.fs-47 {
  font-size: 47px;
}

.fs-48 {
  font-size: 48px;
}

.fs-49 {
  font-size: 49px;
}

.fs-50 {
  font-size: 50px;
}

@media (min-width: 576px) {
  .fs-sm-10 {
    font-size: 10px;
  }
  .fs-sm-11 {
    font-size: 11px;
  }
  .fs-sm-12 {
    font-size: 12px;
  }
  .fs-sm-13 {
    font-size: 13px;
  }
  .fs-sm-14 {
    font-size: 14px;
  }
  .fs-sm-15 {
    font-size: 15px;
  }
  .fs-sm-16 {
    font-size: 16px;
  }
  .fs-sm-17 {
    font-size: 17px;
  }
  .fs-sm-18 {
    font-size: 18px;
  }
  .fs-sm-19 {
    font-size: 19px;
  }
  .fs-sm-20 {
    font-size: 20px;
  }
  .fs-sm-21 {
    font-size: 21px;
  }
  .fs-sm-22 {
    font-size: 22px;
  }
  .fs-sm-23 {
    font-size: 23px;
  }
  .fs-sm-24 {
    font-size: 24px;
  }
  .fs-sm-25 {
    font-size: 25px;
  }
  .fs-sm-26 {
    font-size: 26px;
  }
  .fs-sm-27 {
    font-size: 27px;
  }
  .fs-sm-28 {
    font-size: 28px;
  }
  .fs-sm-29 {
    font-size: 29px;
  }
  .fs-sm-30 {
    font-size: 30px;
  }
  .fs-sm-31 {
    font-size: 31px;
  }
  .fs-sm-32 {
    font-size: 32px;
  }
  .fs-sm-33 {
    font-size: 33px;
  }
  .fs-sm-34 {
    font-size: 34px;
  }
  .fs-sm-35 {
    font-size: 35px;
  }
  .fs-sm-36 {
    font-size: 36px;
  }
  .fs-sm-37 {
    font-size: 37px;
  }
  .fs-sm-38 {
    font-size: 38px;
  }
  .fs-sm-39 {
    font-size: 39px;
  }
  .fs-sm-40 {
    font-size: 40px;
  }
  .fs-sm-41 {
    font-size: 41px;
  }
  .fs-sm-42 {
    font-size: 42px;
  }
  .fs-sm-43 {
    font-size: 43px;
  }
  .fs-sm-44 {
    font-size: 44px;
  }
  .fs-sm-45 {
    font-size: 45px;
  }
  .fs-sm-46 {
    font-size: 46px;
  }
  .fs-sm-47 {
    font-size: 47px;
  }
  .fs-sm-48 {
    font-size: 48px;
  }
  .fs-sm-49 {
    font-size: 49px;
  }
  .fs-sm-50 {
    font-size: 50px;
  }
}
@media (min-width: 768px) {
  .fs-md-10 {
    font-size: 10px;
  }
  .fs-md-11 {
    font-size: 11px;
  }
  .fs-md-12 {
    font-size: 12px;
  }
  .fs-md-13 {
    font-size: 13px;
  }
  .fs-md-14 {
    font-size: 14px;
  }
  .fs-md-15 {
    font-size: 15px;
  }
  .fs-md-16 {
    font-size: 16px;
  }
  .fs-md-17 {
    font-size: 17px;
  }
  .fs-md-18 {
    font-size: 18px;
  }
  .fs-md-19 {
    font-size: 19px;
  }
  .fs-md-20 {
    font-size: 20px;
  }
  .fs-md-21 {
    font-size: 21px;
  }
  .fs-md-22 {
    font-size: 22px;
  }
  .fs-md-23 {
    font-size: 23px;
  }
  .fs-md-24 {
    font-size: 24px;
  }
  .fs-md-25 {
    font-size: 25px;
  }
  .fs-md-26 {
    font-size: 26px;
  }
  .fs-md-27 {
    font-size: 27px;
  }
  .fs-md-28 {
    font-size: 28px;
  }
  .fs-md-29 {
    font-size: 29px;
  }
  .fs-md-30 {
    font-size: 30px;
  }
  .fs-md-31 {
    font-size: 31px;
  }
  .fs-md-32 {
    font-size: 32px;
  }
  .fs-md-33 {
    font-size: 33px;
  }
  .fs-md-34 {
    font-size: 34px;
  }
  .fs-md-35 {
    font-size: 35px;
  }
  .fs-md-36 {
    font-size: 36px;
  }
  .fs-md-37 {
    font-size: 37px;
  }
  .fs-md-38 {
    font-size: 38px;
  }
  .fs-md-39 {
    font-size: 39px;
  }
  .fs-md-40 {
    font-size: 40px;
  }
  .fs-md-41 {
    font-size: 41px;
  }
  .fs-md-42 {
    font-size: 42px;
  }
  .fs-md-43 {
    font-size: 43px;
  }
  .fs-md-44 {
    font-size: 44px;
  }
  .fs-md-45 {
    font-size: 45px;
  }
  .fs-md-46 {
    font-size: 46px;
  }
  .fs-md-47 {
    font-size: 47px;
  }
  .fs-md-48 {
    font-size: 48px;
  }
  .fs-md-49 {
    font-size: 49px;
  }
  .fs-md-50 {
    font-size: 50px;
  }
}
@media (min-width: 992px) {
  .fs-lg-10 {
    font-size: 10px;
  }
  .fs-lg-11 {
    font-size: 11px;
  }
  .fs-lg-12 {
    font-size: 12px;
  }
  .fs-lg-13 {
    font-size: 13px;
  }
  .fs-lg-14 {
    font-size: 14px;
  }
  .fs-lg-15 {
    font-size: 15px;
  }
  .fs-lg-16 {
    font-size: 16px;
  }
  .fs-lg-17 {
    font-size: 17px;
  }
  .fs-lg-18 {
    font-size: 18px;
  }
  .fs-lg-19 {
    font-size: 19px;
  }
  .fs-lg-20 {
    font-size: 20px;
  }
  .fs-lg-21 {
    font-size: 21px;
  }
  .fs-lg-22 {
    font-size: 22px;
  }
  .fs-lg-23 {
    font-size: 23px;
  }
  .fs-lg-24 {
    font-size: 24px;
  }
  .fs-lg-25 {
    font-size: 25px;
  }
  .fs-lg-26 {
    font-size: 26px;
  }
  .fs-lg-27 {
    font-size: 27px;
  }
  .fs-lg-28 {
    font-size: 28px;
  }
  .fs-lg-29 {
    font-size: 29px;
  }
  .fs-lg-30 {
    font-size: 30px;
  }
  .fs-lg-31 {
    font-size: 31px;
  }
  .fs-lg-32 {
    font-size: 32px;
  }
  .fs-lg-33 {
    font-size: 33px;
  }
  .fs-lg-34 {
    font-size: 34px;
  }
  .fs-lg-35 {
    font-size: 35px;
  }
  .fs-lg-36 {
    font-size: 36px;
  }
  .fs-lg-37 {
    font-size: 37px;
  }
  .fs-lg-38 {
    font-size: 38px;
  }
  .fs-lg-39 {
    font-size: 39px;
  }
  .fs-lg-40 {
    font-size: 40px;
  }
  .fs-lg-41 {
    font-size: 41px;
  }
  .fs-lg-42 {
    font-size: 42px;
  }
  .fs-lg-43 {
    font-size: 43px;
  }
  .fs-lg-44 {
    font-size: 44px;
  }
  .fs-lg-45 {
    font-size: 45px;
  }
  .fs-lg-46 {
    font-size: 46px;
  }
  .fs-lg-47 {
    font-size: 47px;
  }
  .fs-lg-48 {
    font-size: 48px;
  }
  .fs-lg-49 {
    font-size: 49px;
  }
  .fs-lg-50 {
    font-size: 50px;
  }
}
@media (min-width: 1200px) {
  .fs-xl-10 {
    font-size: 10px;
  }
  .fs-xl-11 {
    font-size: 11px;
  }
  .fs-xl-12 {
    font-size: 12px;
  }
  .fs-xl-13 {
    font-size: 13px;
  }
  .fs-xl-14 {
    font-size: 14px;
  }
  .fs-xl-15 {
    font-size: 15px;
  }
  .fs-xl-16 {
    font-size: 16px;
  }
  .fs-xl-17 {
    font-size: 17px;
  }
  .fs-xl-18 {
    font-size: 18px;
  }
  .fs-xl-19 {
    font-size: 19px;
  }
  .fs-xl-20 {
    font-size: 20px;
  }
  .fs-xl-21 {
    font-size: 21px;
  }
  .fs-xl-22 {
    font-size: 22px;
  }
  .fs-xl-23 {
    font-size: 23px;
  }
  .fs-xl-24 {
    font-size: 24px;
  }
  .fs-xl-25 {
    font-size: 25px;
  }
  .fs-xl-26 {
    font-size: 26px;
  }
  .fs-xl-27 {
    font-size: 27px;
  }
  .fs-xl-28 {
    font-size: 28px;
  }
  .fs-xl-29 {
    font-size: 29px;
  }
  .fs-xl-30 {
    font-size: 30px;
  }
  .fs-xl-31 {
    font-size: 31px;
  }
  .fs-xl-32 {
    font-size: 32px;
  }
  .fs-xl-33 {
    font-size: 33px;
  }
  .fs-xl-34 {
    font-size: 34px;
  }
  .fs-xl-35 {
    font-size: 35px;
  }
  .fs-xl-36 {
    font-size: 36px;
  }
  .fs-xl-37 {
    font-size: 37px;
  }
  .fs-xl-38 {
    font-size: 38px;
  }
  .fs-xl-39 {
    font-size: 39px;
  }
  .fs-xl-40 {
    font-size: 40px;
  }
  .fs-xl-41 {
    font-size: 41px;
  }
  .fs-xl-42 {
    font-size: 42px;
  }
  .fs-xl-43 {
    font-size: 43px;
  }
  .fs-xl-44 {
    font-size: 44px;
  }
  .fs-xl-45 {
    font-size: 45px;
  }
  .fs-xl-46 {
    font-size: 46px;
  }
  .fs-xl-47 {
    font-size: 47px;
  }
  .fs-xl-48 {
    font-size: 48px;
  }
  .fs-xl-49 {
    font-size: 49px;
  }
  .fs-xl-50 {
    font-size: 50px;
  }
}
@media (min-width: 1400px) {
  .fs-xxl-10 {
    font-size: 10px;
  }
  .fs-xxl-11 {
    font-size: 11px;
  }
  .fs-xxl-12 {
    font-size: 12px;
  }
  .fs-xxl-13 {
    font-size: 13px;
  }
  .fs-xxl-14 {
    font-size: 14px;
  }
  .fs-xxl-15 {
    font-size: 15px;
  }
  .fs-xxl-16 {
    font-size: 16px;
  }
  .fs-xxl-17 {
    font-size: 17px;
  }
  .fs-xxl-18 {
    font-size: 18px;
  }
  .fs-xxl-19 {
    font-size: 19px;
  }
  .fs-xxl-20 {
    font-size: 20px;
  }
  .fs-xxl-21 {
    font-size: 21px;
  }
  .fs-xxl-22 {
    font-size: 22px;
  }
  .fs-xxl-23 {
    font-size: 23px;
  }
  .fs-xxl-24 {
    font-size: 24px;
  }
  .fs-xxl-25 {
    font-size: 25px;
  }
  .fs-xxl-26 {
    font-size: 26px;
  }
  .fs-xxl-27 {
    font-size: 27px;
  }
  .fs-xxl-28 {
    font-size: 28px;
  }
  .fs-xxl-29 {
    font-size: 29px;
  }
  .fs-xxl-30 {
    font-size: 30px;
  }
  .fs-xxl-31 {
    font-size: 31px;
  }
  .fs-xxl-32 {
    font-size: 32px;
  }
  .fs-xxl-33 {
    font-size: 33px;
  }
  .fs-xxl-34 {
    font-size: 34px;
  }
  .fs-xxl-35 {
    font-size: 35px;
  }
  .fs-xxl-36 {
    font-size: 36px;
  }
  .fs-xxl-37 {
    font-size: 37px;
  }
  .fs-xxl-38 {
    font-size: 38px;
  }
  .fs-xxl-39 {
    font-size: 39px;
  }
  .fs-xxl-40 {
    font-size: 40px;
  }
  .fs-xxl-41 {
    font-size: 41px;
  }
  .fs-xxl-42 {
    font-size: 42px;
  }
  .fs-xxl-43 {
    font-size: 43px;
  }
  .fs-xxl-44 {
    font-size: 44px;
  }
  .fs-xxl-45 {
    font-size: 45px;
  }
  .fs-xxl-46 {
    font-size: 46px;
  }
  .fs-xxl-47 {
    font-size: 47px;
  }
  .fs-xxl-48 {
    font-size: 48px;
  }
  .fs-xxl-49 {
    font-size: 49px;
  }
  .fs-xxl-50 {
    font-size: 50px;
  }
}
.nav-breadcrumb {
  margin: 20px 0;
}
.nav-breadcrumb .breadcrumb .breadcrumb-item {
  font-size: 12px;
}
.nav-breadcrumb .breadcrumb .breadcrumb-item a {
  color: #606368;
}
.nav-breadcrumb .breadcrumb .breadcrumb-item a:hover {
  color: #EC1C24;
}

.line-clamp-1 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
}

.line-clamp-4 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 4;
  overflow: hidden;
}

.line-clamp-5 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 5;
  overflow: hidden;
}

.line-clamp-6 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 6;
  overflow: hidden;
}

.line-clamp-7 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 7;
  overflow: hidden;
}

.line-clamp-8 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 8;
  overflow: hidden;
}

.line-clamp-9 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 9;
  overflow: hidden;
}

.line-clamp-10 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 10;
  overflow: hidden;
}

.box-title {
  width: 100%;
  margin-bottom: 34px;
}
.box-title .title {
  color: #7C0410;
  font-size: 40px;
  font-style: normal;
  line-height: 150%;
  text-transform: capitalize;
  font-weight: 600;
  margin: 0;
}
.box-title .sub_title {
  color: #7C0410;
  font-size: 30px;
  font-style: normal;
  line-height: 150%;
  text-transform: capitalize;
  font-weight: 600;
}
.box-title .desc {
  color: #4F4F4F;
  font-size: 20px;
  font-style: normal;
  line-height: 150%;
  font-weight: 600;
}
@media screen and (max-width: 1600px) {
  .box-title .sub_title {
    font-size: 26px;
  }
  .box-title .title {
    font-size: 36px;
  }
}
@media screen and (max-width: 1400px) {
  .box-title .sub_title {
    font-size: 24px;
  }
  .box-title .title {
    font-size: 32px;
  }
  .box-title .desc {
    font-size: 18px;
  }
}
@media screen and (max-width: 1200px) {
  .box-title .title {
    font-size: 28px;
  }
  .box-title .sub_title {
    font-size: 20px;
  }
}
@media screen and (max-width: 991px) {
  .box-title .title {
    font-size: 26px;
  }
  .box-title .sub_title {
    font-size: 18px;
  }
  .box-title .desc {
    font-size: 16px;
  }
}
@media screen and (max-width: 767px) {
  .box-title .title {
    font-size: 22px;
  }
}

.btn-links {
  color: #7C0410;
  font-size: 24px;
  font-style: normal;
  line-height: 140%;
  padding-bottom: 7px;
  display: inline-block;
  border-bottom: 1px solid #7C0410;
  font-weight: 500;
}
.btn-links:hover {
  color: #7C0410;
}
@media screen and (max-width: 1400px) {
  .btn-links {
    font-size: 22px;
  }
}
@media screen and (max-width: 991px) {
  .btn-links {
    font-size: 18px;
  }
}
@media screen and (max-width: 768px) {
  .btn-links {
    font-size: 16px;
  }
}

.producbox--btn .swiper-button-next,
.producbox--btn .swiper-button-prev {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border: 1px solid #D5D5D5;
  background: #FFF;
  opacity: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}
.producbox--btn .swiper-button-next::after,
.producbox--btn .swiper-button-prev::after {
  display: none;
}
.producbox--btn .swiper-button-next.swiper-button-lock,
.producbox--btn .swiper-button-prev.swiper-button-lock {
  display: none;
}
.producbox--btn .swiper-button-next {
  right: -18px;
}
.producbox--btn .swiper-button-prev {
  left: -18px;
}

.producbox--panigation .swiper-pagination {
  position: unset;
  width: 100%;
}
.producbox--panigation .swiper-pagination .swiper-pagination-bullet {
  background-color: #9E9E9E;
  opacity: 0.5;
}
.producbox--panigation .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
  background-color: #CCB26A;
  opacity: 1;
}

.productcate--items {
  width: 100%;
}
.productcate--items .img {
  display: block;
  overflow: hidden;
  border: 0.5px solid #D9D9D9;
  background: linear-gradient(180deg, #620004 0%, #840207 100%);
  padding: 5px;
  margin-bottom: 16px;
}
.productcate--items .img span {
  display: block;
  width: 100%;
  position: relative;
  padding-top: 100%;
  overflow: hidden;
}
.productcate--items .img span img {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  -o-object-fit: scale-down;
     object-fit: scale-down;
  -o-object-position: center;
     object-position: center;
  transition: all 0.3s;
}
.productcate--items .cate--title {
  color: #4F4F4F;
  font-size: 20px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  margin: 0;
}
.productcate--items .cate--title a {
  color: inherit;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}
.productcate--items:hover .img span img {
  transform: scale(1.1);
}
.productcate--items:hover .cate--title {
  color: #7C0410;
}
@media screen and (max-width: 1400px) {
  .productcate--items .cate--title {
    font-size: 18px;
  }
}

.product--items {
  width: 100%;
  display: block;
  height: 100%;
  border: 1px solid #D5D5D5;
  padding: 33px 28px 27px 33px;
}
.product--items .img {
  display: block;
  width: 100%;
  position: relative;
  overflow: hidden;
  padding-top: 100%;
  margin-bottom: 55px;
}
.product--items .img img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
  position: absolute;
  top: 0;
  left: 0;
  transition: all 0.3s;
}
.product--items .product--body {
  position: relative;
  width: 100%;
}
.product--items .product--body .title {
  color: #4F4F4F;
  font-size: 20.074px;
  font-style: normal;
  font-weight: 700;
  line-height: 150%;
  margin: 0 0 13px;
}
.product--items .product--body .title a {
  color: inherit;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}
.product--items .product--body .gold--infor {
  display: flex;
  align-items: center;
  color: #AE8751;
  font-size: 16.059px;
  line-height: 150%;
  margin: 0 0 12px;
}
.product--items .product--body .price {
  color: #4F4F4F;
  font-size: 20.074px;
  font-style: normal;
  font-weight: 700;
  line-height: 122.304%;
  padding-right: 43px;
}
.product--items .product--body .add--cart {
  border: 0;
  border-radius: 0;
  box-shadow: none;
  background: transparent;
  margin: 0;
  padding: 0;
  position: absolute;
  bottom: 0;
  right: 0;
  width: -moz-max-content;
  width: max-content;
  height: -moz-max-content;
  height: max-content;
}
.product--items:hover .img img {
  transform: scale(1.2);
}
.product--items:hover .product--body .title {
  color: #7C0410;
}
.product--items:hover .product--body .price {
  color: #7C0410;
}
@media screen and (max-width: 1600px) {
  .product--items {
    padding: 25px 20px;
  }
  .product--items .product--body .title {
    font-size: 18px;
  }
  .product--items .product--body .gold--infor {
    font-size: 16px;
  }
  .product--items .product--body .price {
    font-size: 18px;
  }
  .product--items .product--body .add--cart svg {
    max-width: 35px;
    height: auto;
  }
}
@media screen and (max-width: 991px) {
  .product--items {
    padding: 25px 20px;
  }
  .product--items .img {
    margin-bottom: 30px;
  }
  .product--items .product--body .title {
    font-size: 18px;
  }
  .product--items .product--body .gold--infor {
    font-size: 16px;
  }
  .product--items .product--body .price {
    font-size: 16px;
  }
  .product--items .product--body .add--cart svg {
    max-width: 35px;
    height: auto;
  }
}
@media screen and (max-width: 767px) {
  .product--items {
    padding: 17px 15px;
  }
  .product--items .img {
    margin-bottom: 34px;
  }
  .product--items .product--body .title {
    font-size: 9.317px;
    margin-bottom: 6px;
  }
  .product--items .product--body .gold--infor {
    font-size: 7.454px;
    margin-bottom: 5px;
  }
  .product--items .product--body .price {
    font-size: 9.317px;
  }
  .product--items .product--body .add--cart svg {
    max-width: 18px;
    height: auto;
  }
}

.news--items {
  width: 100%;
  height: 100%;
  display: block;
}
.news--items .img {
  display: block;
  width: 100%;
  overflow: hidden;
  padding-top: 62.4%;
  position: relative;
  margin-bottom: 40px;
}
.news--items .img img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
  position: absolute;
  top: 0;
  left: 0;
  transition: all 0.3s;
}
.news--items .news--body {
  width: 100%;
}
.news--items .news--body .sub_cate {
  display: block;
  width: 100%;
  margin-bottom: 8px;
  color: #AE8751;
  font-size: 16px;
  font-style: normal;
  line-height: 129%;
  text-transform: capitalize;
}
.news--items .news--body .title {
  width: 100%;
  color: #4F4F4F;
  font-size: 20px;
  font-weight: 700;
  line-height: 129%;
  text-transform: capitalize;
}
.news--items .news--body .title a {
  color: inherit;
}
.news--items:hover .img img {
  transform: scale(1.2);
}
.news--items:hover .news--body .title {
  color: #7C0410;
}
@media screen and (max-width: 991px) {
  .news--items .img {
    margin-bottom: 20px;
  }
  .news--items .news--body .title {
    width: 100%;
    font-size: 18px;
  }
}
@media screen and (max-width: 767px) {
  .news--items .news--body .title {
    font-size: 16px;
  }
}

.icon-phone {
  width: 16px;
  height: 16px;
  background-image: url("../images/icon/icon-phone-call-1.svg");
  background-repeat: no-repeat;
  display: inline-block;
  vertical-align: -3px;
}

.icon-mail {
  width: 16px;
  height: 16px;
  background-image: url("../images/icon/icon-email.svg");
  background-repeat: no-repeat;
  display: inline-block;
  vertical-align: -5px;
}

.icon-search {
  width: 16px;
  height: 16px;
  background-image: url("../images/icon/icon-search-white.svg");
  background-repeat: no-repeat;
  display: inline-block;
  vertical-align: -5px;
}

.icon-user {
  width: 16px;
  height: 16px;
  background-image: url("../images/icon/icon-user-white.svg");
  background-repeat: no-repeat;
  display: inline-block;
  vertical-align: -4px;
}

.icon-language {
  width: 20px;
  height: 20px;
  background-repeat: no-repeat;
  display: inline-block;
  vertical-align: -4px;
}

.icon-language.icon-en {
  background-image: url("../images/icon/lang-en.png");
}

.icon-language.icon-vi {
  background-image: url("../images/icon/lang-vi.png");
}

.icon-cart-item {
  width: 24px;
  height: 24px;
  background-repeat: no-repeat;
  display: inline-block;
  vertical-align: -4px;
  background-image: url("../images/icon/icon-shopping-cart-1.svg");
}

body {
  word-break: break-word;
  font-family: "SVN-Artifex CF", sans-serif;
  font-size: 16px;
  font-weight: 400;
}

.box--language {
  display: flex;
  align-items: center;
  color: #9E9E9E;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.box--language button {
  border: 0;
  border-radius: 0;
  box-shadow: none;
  background: transparent;
  color: #9E9E9E;
  font-size: 16px;
  font-style: normal;
  line-height: normal;
}
.box--language button.active {
  text-decoration-line: underline;
  text-decoration-style: solid;
  -webkit-text-decoration-skip: ink;
          text-decoration-skip-ink: auto;
  text-decoration-thickness: auto;
  text-underline-offset: auto;
  text-underline-position: from-font;
}
.box--language button:focus, .box--language button:focus-visible {
  outline: none;
  box-shadow: none;
}
@media screen and (max-width: 991px) {
  .box--language button {
    font-size: 14px;
    padding: 0;
  }
}

header {
  z-index: 1000;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background: #fff;
}
header .header-top-main {
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;
  padding: 32px 0 65px;
  position: relative;
}
header .header-top-main .header--action {
  display: flex;
  align-items: center;
  gap: 8px;
}
header .header-top-main .header--action ._icon {
  flex-shrink: 0;
}
header .header-top-main .header--action .txt {
  color: #9E9E9E;
  font-size: 16px;
  font-style: normal;
  line-height: normal;
  font-weight: 600;
}
header .header-top-main .header-sp {
  display: flex;
  gap: 32px;
  align-items: center;
}
header .header-top-main .header-sp-r {
  display: flex;
  gap: 32px;
  align-items: center;
}
header .header-top-main .header-sp-r .box--language {
  display: none;
}
header .header-top-main .header-sp-r > .search-head {
  max-width: 146px;
  width: 100%;
  height: auto;
}
header .header-top-main .header-sp-r > .search-head .box-search {
  max-width: 100%;
  display: block;
  position: unset;
}
header .header-top-main .header-sp-r > .search-head .box-search .search-form {
  position: relative;
}
header .header-top-main .header-sp-r > .search-head .box-search .search-form input {
  background: transparent;
}
header .header-top-wrap {
  width: 100%;
}
header .header-top-wrap .header-logo {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  margin: auto;
  height: -moz-max-content;
  height: max-content;
  width: -moz-max-content;
  width: max-content;
  max-width: 208px;
}
header .header-top-wrap .header-logo img.img-df {
  display: block;
}
header .header-top-wrap .header-logo img.img-scr {
  display: none;
}
header.header-static {
  position: static;
  box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.1);
}
header.header-static .logo-scroll {
  display: block;
}
header.header-static .logo-df {
  display: none;
}
header.header-static .header-top {
  padding: 14px 0 20px;
}
header.header-static .header-menu .header-menu-wrap .header-menu-root .menu-root {
  color: #333;
}
header.header-static .header-menu .header-menu-wrap .header-menu-root .menu-root svg path {
  stroke: #333;
}
header.header-static .header-menu .header-menu-wrap .header-menu-root .menu-root:hover, header.header-static .header-menu .header-menu-wrap .header-menu-root .menu-root.active {
  color: #1A68B3;
}
header.header-static .header-menu .header-menu-wrap .header-menu-root .menu-root:hover svg path, header.header-static .header-menu .header-menu-wrap .header-menu-root .menu-root.active svg path {
  stroke: #1A68B3;
}
@media (min-width: 320px) {
  header.header-static .search-head {
    background: transparent url(../images/ic-5.png) no-repeat center center;
  }
  header.header-static .box_head_right .gamuda_language .gamuda_language_child a {
    color: #333;
  }
  header.header-static .box_head_right .gamuda_language .gamuda_language_child a:first-child::after {
    background: #333;
  }
  header.header-static .bar {
    background: #333;
  }
  header.header-static .bar:before {
    background: #333;
  }
  header.header-static .bar:after {
    background: #333;
  }
}
header .banner-header-top {
  height: 16px;
}
@media (max-width: 1366px) {
  header .banner-header-top {
    height: 12px;
  }
}
header .banner-header-top img {
  height: 16px;
  width: 100%;
  vertical-align: top;
}
@media (max-width: 1366px) {
  header .banner-header-top img {
    height: 12px;
  }
}
@media (max-width: 991px) {
  header .header-top {
    display: none;
  }
}
header .header-top .header-top-wrapbot {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}
header .menu-line {
  position: relative;
  margin: 0 auto;
  height: -moz-max-content;
  height: max-content;
  width: 32px;
}
@media (max-width: 400px) {
  header .menu-line {
    width: 25px;
  }
}
header .menu-line .lines {
  cursor: pointer;
  transition: all 0.2s;
  margin-bottom: 0;
}
header .menu-line .line {
  position: absolute;
  height: 2px;
  width: 100%;
  background-color: #2c2c35;
  transition: all 0.3s;
  margin-left: 0 !important;
}
header .menu-line .diagonal.part-1 {
  position: relative;
  float: left;
}
header .menu-line .diagonal.part-2 {
  position: relative;
  float: left;
  margin-top: 6px;
}
header .menu-line .horizontal {
  position: relative;
  float: left;
  margin-top: 6px;
}
header .mobile-ham {
  display: none;
}
@media (max-width: 991px) {
  header .mobile-ham {
    display: block;
    height: -moz-max-content;
    height: max-content;
  }
}
header .mobile-ham a.icon-menu-mb {
  display: inline-block;
}
header .mobile-ham a.icon-menu-mb svg {
  transform: rotate(180deg);
}
header.header--home {
  background: transparent;
}
header.header--home .header-menu .header-menu-wrap .header-menu-root .menu-root {
  color: #fff;
}
header.header--home .box--language {
  color: #fff;
}
header.header--home .box--language button {
  color: #fff;
}
header.header--home .mobile-ham a.icon-menu-mb svg path {
  fill: #fff;
}
header.header--home .header-top-main .header--action ._icon svg path {
  stroke: #fff;
}
header.header--home .header-top-main .header--action .txt {
  color: #fff;
}
header.header--home .header-top-main .header-sp-r > .search-head .box-search .search-form button svg path {
  stroke: #fff;
}
header.header--home .header-top-main .header-sp-r > .search-head .box-search .search-form input {
  border-bottom: 1px solid #fff;
  color: #fff;
}
header.header--home .header-top-main .header-sp-r > .search-head .box-search .search-form input::-moz-placeholder {
  color: #fff;
}
header.header--home .header-top-main .header-sp-r > .search-head .box-search .search-form input::placeholder {
  color: #fff;
}
@media screen and (max-width: 991px) {
  header.header--home .header-top-main .search-head {
    background: transparent url(../images/ic-5-1.png) no-repeat center center;
  }
}
header.header--home .header-top-wrap .header-logo img.img-df {
  display: none;
}
header.header--home .header-top-wrap .header-logo img.img-scr {
  display: block;
}
header.header--home.fixed .header-top-wrap .header-logo img.img-df {
  display: block;
}
header.header--home.fixed .header-top-wrap .header-logo img.img-scr {
  display: none;
}
header.header--home.fixed .box--language {
  color: #9E9E9E;
}
header.header--home.fixed .box--language button {
  color: #9E9E9E;
}
header.header--home.fixed .mobile-ham a.icon-menu-mb svg path {
  fill: #7E7E7E;
}
header.header--home.fixed .header-top-main .header--action ._icon svg path {
  stroke: #9E9E9E;
}
header.header--home.fixed .header-top-main .header--action .txt {
  color: #9E9E9E;
}
header.header--home.fixed .header-top-main .header-sp-r > .search-head .box-search .search-form button svg path {
  stroke: #9E9E9E;
}
header.header--home.fixed .header-top-main .header-sp-r > .search-head .box-search .search-form input {
  border-bottom: 1px solid #9E9E9E;
  color: #4f4f4f;
}
header.header--home.fixed .header-top-main .header-sp-r > .search-head .box-search .search-form input::-moz-placeholder {
  color: #4f4f4f;
}
header.header--home.fixed .header-top-main .header-sp-r > .search-head .box-search .search-form input::placeholder {
  color: #4f4f4f;
}
@media screen and (max-width: 991px) {
  header.header--home.fixed .header-top-main .search-head {
    background: transparent url(../images/ic-5.png) no-repeat center center;
  }
}
header.fixed {
  position: fixed;
  width: 100%;
  top: 0;
  left: 0;
  filter: drop-shadow(0px 4px 20px rgba(0, 0, 0, 0.08));
  z-index: 1000;
  background: #fff;
  animation: MoveDown 0.6s;
}
header.fixed .banner-header-top {
  display: none;
}
header.fixed .header-menu .header-menu-wrap .header-menu-root .menu-root {
  color: #4F4F4F;
}
header.fixed .header-menu .header-menu-wrap .header-menu-root .menu-root span {
  bottom: calc(100% - 15px);
}
@media screen and (min-width: 992px) {
  header.fixed .header-top-main {
    display: none;
  }
}
@media screen and (max-width: 991px) {
  header {
    background: #fff;
  }
  header .header-top-main {
    padding: 32px 0;
  }
  header .header-top-main > .search-head {
    display: none;
  }
  header .header-top-main .header--action .txt {
    display: none;
  }
  header .header-top-main .header-sp {
    gap: 10px;
  }
  header .header-top-main .header-sp .box--language,
  header .header-top-main .header-sp .header--action {
    display: none;
  }
  header .header-top-main .header-sp-r {
    gap: 10px;
  }
  header .header-top-main .header-sp-r .box--language {
    display: block;
  }
  header .header-top-main .header-sp-r > .search-head {
    display: none !important;
  }
  header .header-top-main .header-sp-r .box_head_right {
    display: none;
  }
  header .header-logo {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    margin: auto;
    width: -moz-max-content;
    width: max-content;
    height: -moz-max-content;
    height: max-content;
  }
}
@media screen and (max-width: 767px) {
  header .header-top-main {
    padding: 20px 0;
  }
  header .header-logo img {
    max-width: 120px;
  }
}
@media screen and (max-width: 375px) {
  header .header-logo img {
    max-width: 104px;
  }
}

.boxmenu-style .boxmenu--other {
  width: 100%;
  text-align: left;
  max-width: 314px;
}
.boxmenu-style .boxmenu--other .title--menu {
  color: #7C0410;
  text-align: center;
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  margin: 0 0 30px;
  display: block;
  text-align: left;
}
.boxmenu-style .boxmenu--other .desc--menu {
  color: #4F4F4F;
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: 150%;
  margin-bottom: 70px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
  min-height: 81px;
}
.boxmenu-style .boxmenu--other .boxmenu-style--list {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 54px;
}
.boxmenu-style .boxmenu--other .boxmenu-style--list > * {
  width: 100%;
  color: #4F4F4F;
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  text-transform: capitalize;
}
.boxmenu-style .boxmenu--other .boxmenu-style--list > *:hover, .boxmenu-style .boxmenu--other .boxmenu-style--list > *.active {
  color: #7C0410;
}
.boxmenu-style .boxmenu--other .box--btn .btn-links {
  font-size: 20px;
}
.boxmenu-style.boxmenu-style2 {
  display: flex;
  gap: 100px;
}
.boxmenu-style.boxmenu-style2 .boxmenu-style--items {
  width: -moz-max-content;
  width: max-content;
  max-width: calc(33.3333% - 66.6666px);
}
@media screen and (max-width: 1400px) {
  .boxmenu-style .boxmenu--other .title--menu {
    font-size: 18px;
  }
  .boxmenu-style .boxmenu--other .desc--menu {
    font-size: 16px;
    min-height: 81px;
  }
  .boxmenu-style .boxmenu--other .boxmenu-style--list > * {
    font-size: 16px;
  }
  .boxmenu-style .boxmenu--other .box--btn .btn-links {
    font-size: 18px;
  }
}
@media screen and (max-width: 1200px) {
  .boxmenu-style .boxmenu--other .box--btn .btn-links {
    font-size: 16px;
  }
  .boxmenu-style.boxmenu-style2 {
    gap: 50px;
  }
  .boxmenu-style.boxmenu-style2 .boxmenu-style--items {
    max-width: calc(33.3333% - 33.3333px);
  }
}

main.page-home {
  padding-top: 0 !important;
}

.logo-scroll {
  display: none;
}

#mmenu:not(.mm-menu) {
  display: none;
}

.img-menu {
  display: block;
  width: 100%;
  padding-top: 72.2%;
  overflow: hidden;
  position: relative;
}
.img-menu img {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
}

.header-menu {
  width: 100%;
}
.header-menu .header-menu-wrap {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.header-menu .header-menu-wrap .header-menu-root {
  text-align: center;
}
.header-menu .header-menu-wrap .header-menu-root:last-child {
  margin-right: 0px;
}
.header-menu .header-menu-wrap .header-menu-root .menu-root {
  color: #4F4F4F;
  text-align: center;
  font-size: 20px;
  font-style: normal;
  line-height: normal;
  position: relative;
  font-weight: 600;
  padding: 15px 0;
  display: block;
}
.header-menu .header-menu-wrap .header-menu-root .menu-root::after {
  content: "";
  display: block;
  width: 100%;
  height: 3px;
  background: #7C0410;
  position: absolute;
  bottom: 0;
  left: 0;
  opacity: 0;
  visibility: hidden;
}
.header-menu .header-menu-wrap .header-menu-root .menu-root span {
  display: block;
  width: 29px;
  height: 29px;
  position: absolute;
  bottom: calc(100% - 3px);
  left: 100%;
}
.header-menu .header-menu-wrap .header-menu-root .menu-root span img {
  max-width: 100%;
}
@media (max-width: 1500px) {
  .header-menu .header-menu-wrap .header-menu-root .menu-root {
    font-size: 15px;
  }
}
.header-menu .header-menu-wrap .header-menu-root .menu-root:hover, .header-menu .header-menu-wrap .header-menu-root .menu-root.active {
  color: #7C0410;
}
.header-menu .header-menu-wrap .header-menu-root .menu-root:hover svg path, .header-menu .header-menu-wrap .header-menu-root .menu-root.active svg path {
  stroke: #000;
}
.header-menu .header-menu-wrap .header-menu-root .menu-root:hover::after, .header-menu .header-menu-wrap .header-menu-root .menu-root.active::after {
  opacity: 1;
  visibility: visible;
}
.header-menu .header-menu-wrap .header-menu-root .menu-root svg {
  margin-left: 4px;
}
.header-menu .header-menu-wrap .header-menu-root .menu-root.menu-home .ic-home {
  display: block;
}
.header-menu .header-menu-wrap .header-menu-root .menu-root.menu-home .ic-home-ac {
  display: none;
}
.header-menu .header-menu-wrap .header-menu-root .menu-root.menu-home::before {
  display: none !important;
}
.header-menu .header-menu-wrap .header-menu-root .menu-root.menu-home.active .ic-home, .header-menu .header-menu-wrap .header-menu-root .menu-root.menu-home:hover .ic-home {
  display: none;
}
.header-menu .header-menu-wrap .header-menu-root .menu-root.menu-home.active .ic-home-ac, .header-menu .header-menu-wrap .header-menu-root .menu-root.menu-home:hover .ic-home-ac {
  display: block;
}
.header-menu .header-menu-wrap .header-menu-root .menu-root:hover .icon-dropdown svg path {
  fill: #FFF;
}
.header-menu .header-menu-wrap .header-menu-root .menu-root.active::before {
  width: 100%;
  transition: all 0.5s;
}
.header-menu .header-menu-wrap .header-menu-root .menu-root.active .icon-dropdown svg path {
  fill: #FFF;
}
.header-menu .header-menu-wrap .header-menu-root > .menu-root {
  position: relative;
}
.header-menu .header-menu-wrap .header-menu-root.has-sub:hover > .menu-root {
  color: #7C0410;
}
.header-menu .header-menu-wrap .header-menu-root.has-sub:hover > .menu-root::after {
  opacity: 1;
  visibility: visible;
}
.header-menu .header-menu-wrap .header-menu-root .sub-menu {
  opacity: 0;
  visibility: hidden;
  position: absolute;
  top: calc(100% - 1px);
  left: 0;
  transform: translateX(0);
  transition: all 0.4s;
  z-index: 200;
  width: 100%;
  background: #fff;
  border-top: 1px solid #D5D5D5;
  padding: 58px 0 73px;
}
.header-menu .header-menu-wrap .header-menu-root .sub-menu .container {
  padding: 0 15px;
}
.header-menu .header-menu-wrap .header-menu-root .sub-menu .col-lg-8 {
  width: 70.4%;
}
.header-menu .header-menu-wrap .header-menu-root .sub-menu .col-lg-8 {
  width: 29.6%;
}
.header-menu .header-menu-wrap .has-sub:hover > .sub-menu {
  opacity: 1;
  visibility: visible;
}
.header-menu .header-menu-wrap .has-sub:hover > .menu-root {
  color: #000;
}
.header-menu .header-menu-wrap .has-sub:hover > .menu-root::before {
  width: 100%;
}
@media (max-width: 991px) {
  .header-menu {
    display: none;
  }
}

.language.language-mb {
  display: none;
}
@media (max-width: 992px) {
  .language.language-mb {
    display: block;
    position: absolute;
    right: 42px;
    top: 8px;
  }
  .language.language-mb .dropdown-menu {
    padding: 10px;
  }
  .language.language-mb .langitem {
    font-weight: 400 !important;
    font-size: 14px;
  }
  .language.language-mb .langitem.current-lang {
    text-transform: uppercase;
    font-weight: 400 !important;
  }
}
.language .dropdown-toggle {
  display: flex;
  align-items: center;
  padding: 0px;
  line-height: 20px;
  height: 20px;
}
@media (max-width: 1199px) {
  .language .dropdown-toggle {
    padding: 0px 25px;
    margin-right: 42px;
  }
}
@media (max-width: 400px) {
  .language .dropdown-toggle {
    margin-right: 20px;
  }
}
.language .dropdown-toggle .icon-dropdown {
  margin-left: 0px;
}
.language .dropdown-toggle::after {
  display: none;
}
.language .dropdown-menu {
  min-width: 80px;
  padding: 0;
  border-radius: 0;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.15);
  border: none;
  margin-top: 7px !important;
  border-top: 4px solid #DCEAFE;
}
.language .dropdown-menu li {
  border-bottom: 0.5px solid #C9CBCD;
}
.language .dropdown-menu li:last-child {
  border-bottom: none;
}
.language .dropdown-menu li .langitem {
  border-right: none;
  padding: 11px 0;
  padding-right: 0px;
  white-space: nowrap;
  justify-content: center;
  text-transform: uppercase;
}
.language .dropdown-menu li:hover .langitem, .language .dropdown-menu li.active .langitem {
  color: #FFA01C;
}
.language .langitem {
  display: flex;
  align-items: center;
  font-weight: 400;
  font-size: 18px;
  line-height: 21px;
  color: #000;
  padding-right: 5px;
}
.language .langitem.current-lang {
  color: #5B5B65;
}
.language .langitem span.icon {
  display: flex;
  margin-right: 5px;
  width: 26px;
  height: 26px;
  border-radius: 0px;
  overflow: hidden;
}
@media (max-width: 768px) {
  .language .dropdown-toggle {
    padding: 7px 12px;
  }
}

.hamburger-menu {
  position: absolute;
  top: 0%;
  right: 0;
  bottom: 0;
  margin: auto;
  height: 25px;
  cursor: pointer;
  -webkit-tap-highlight-color: rgba(255, 255, 255, 0);
  display: none;
}
@media (max-width: 1199px) {
  .hamburger-menu {
    display: block;
  }
}

.bar,
.bar:after,
.bar:before {
  width: 14px;
  height: 2px;
}

.hamburger-menu .bar,
.hamburger-menu .bar:after,
.hamburger-menu .bar:before {
  width: 32px;
  height: 2px;
}

.bar {
  position: relative;
  transform: translateY(10px);
  background: #000;
  transition: all 0ms 300ms;
}
.bar:before {
  content: "";
  position: absolute;
  left: 0;
  bottom: 10px;
  background: #fff;
  transition: bottom 300ms 300ms cubic-bezier(0.23, 1, 0.32, 1), transform 300ms cubic-bezier(0.23, 1, 0.32, 1);
}
.bar:after {
  content: "";
  position: absolute;
  left: 0;
  top: 10px;
  background: #fff;
  transition: top 300ms 300ms cubic-bezier(0.23, 1, 0.32, 1), transform 300ms cubic-bezier(0.23, 1, 0.32, 1);
}
.bar.animate {
  background: rgba(255, 255, 255, 0);
}
.bar.animate:after {
  top: 0;
  transform: rotate(45deg);
  transition: top 300ms cubic-bezier(0.23, 1, 0.32, 1), transform 300ms 300ms cubic-bezier(0.23, 1, 0.32, 1);
}
.bar.animate:before {
  bottom: 0;
  transform: rotate(-45deg);
  transition: bottom 300ms cubic-bezier(0.23, 1, 0.32, 1), transform 300ms 300ms cubic-bezier(0.23, 1, 0.32, 1);
}

/*---------------------
  Mobiles Menu 
  ----------------------*/
/*---------------------
      Mobiles Menu - Design 
      ----------------------*/
.mobile-menu ul {
  margin: 0;
  padding: 0;
}
.mobile-menu ul.mobile-menu-list {
  width: 100%;
  height: 100%;
  overflow-y: scroll;
  overflow-x: hidden;
}

.mobile-menu .logo-mobile {
  text-align: center;
}
.mobile-menu .logo-mobile a.header-logo {
  display: none !important;
}
.mobile-menu .logo-mobile #closeButton {
  background: rgba(0, 0, 0, 0);
  border: none;
  position: absolute;
  top: 24px;
  right: 13px;
}
.mobile-menu li {
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 22px;
  list-style: none;
  border-bottom: 1px solid #F2F2F2;
  padding: 12px 0;
  position: relative;
  color: #333;
}
.mobile-menu li:hover {
  color: #262262;
}
.mobile-menu .gamuda_language {
  display: block;
  position: absolute;
  left: 20px;
  bottom: 40px;
  padding-left: 0;
}
.mobile-menu .gamuda_language .gamuda_language_child a {
  color: rgba(51, 51, 51, 0.5);
  font-size: 21px !important;
}
.mobile-menu .gamuda_language .gamuda_language_child a.active {
  color: rgb(51, 51, 51);
}
.mobile-menu .gamuda_language .gamuda_language_child a:first-child::after {
  background-color: rgb(51, 51, 51);
}

.mobile-menu li:first-child {
  margin-top: 20px;
}

.mobile-menu li a {
  display: block;
  width: 100%;
  text-decoration: none;
  color: #333;
}
.mobile-menu li a:hover, .mobile-menu li a.active {
  color: #1A68B3;
}
.mobile-menu li a:hover ~ .icon-arrow svg path, .mobile-menu li a.active ~ .icon-arrow svg path {
  fill: #1A68B3;
}
.mobile-menu li a.icon-arrow {
  transition: 0.6s;
  -webkit-transition: 0.6s;
  -moz-transition: 0.6s;
  width: 30px;
  height: 50px;
  position: absolute;
  display: flex;
  top: 0;
  right: 0;
  align-items: center;
  justify-content: center;
}
.mobile-menu li a.icon-arrow svg path {
  fill: #333;
}
.mobile-menu li a.icon-arrow.open {
  transform: rotate(-180deg);
  -webkit-transform: rotate(-180deg);
  -moz-transform: rotate(-180deg);
}
.mobile-menu li a.icon-arrow.open svg path {
  fill: #1A68B3;
}

/*---------------------
      Mobiles Menu - Slide IN 
      ----------------------*/
.mobile-menu {
  top: 0;
  max-width: 336px;
  right: -100%;
  width: 100%;
  background: #fff;
  color: black;
  padding: 24px 20px;
  height: 100vh;
  position: fixed;
  z-index: 9997;
  overflow-y: auto;
  transform: translate3d(0, 0, 205px);
  transition: all 500ms ease-in-out;
}
.mobile-menu .children li {
  padding-left: 10px;
  border-top: 1px solid #F2F2F2;
  border-bottom: none;
}
.mobile-menu .children li:last-child {
  padding-bottom: 0;
}
.mobile-menu .children li:first-child {
  margin-top: 14px;
}

.mobile-menu.active {
  right: 0;
  transform: translate3d(0, 0, 0);
  transition: all 500ms ease-in-out;
  padding-bottom: 90px;
}

/*---------------------
      Mobiles Menu - Dropdown Submenu
      ----------------------*/
.has-children:hover {
  cursor: hand;
}

.children {
  display: none;
}

.mm-navbar__title {
  text-indent: -99999px;
  background-image: url("../images/image-home/logo-alina.png");
  background-repeat: no-repeat;
  background-position: center;
  background-size: 90px auto;
}

.box_head_right {
  display: flex;
  align-items: center;
  display: none;
}
@media (max-width: 991px) {
  .box_head_right {
    display: block;
  }
}

.gamuda_language {
  padding-left: 20px;
  display: flex;
  align-items: center;
}
@media (max-width: 1500px) {
  .gamuda_language {
    padding-left: 10px;
  }
}
@media (max-width: 1200px) {
  .gamuda_language {
    display: none;
  }
}
.gamuda_language .gamuda_language_child a {
  display: inline-block;
  text-transform: uppercase;
  color: rgba(255, 255, 255, 0.5);
  margin: 0 10px;
  position: relative;
  font-size: 21px;
}
@media (max-width: 1500px) {
  .gamuda_language .gamuda_language_child a {
    font-size: 15px;
  }
}
@media (max-width: 1300px) {
  .gamuda_language .gamuda_language_child a {
    font-size: 13px;
  }
}
.gamuda_language .gamuda_language_child a.active {
  color: rgb(255, 255, 255);
  font-weight: 700;
}
.gamuda_language .gamuda_language_child a:first-child {
  margin-left: 0;
}
.gamuda_language .gamuda_language_child a:last-child {
  margin-right: 0;
}
.gamuda_language .gamuda_language_child a:first-child::after {
  display: block;
  content: "";
  width: 3px;
  height: 65%;
  position: absolute;
  top: 0;
  right: -13px;
  bottom: 0;
  margin: auto;
  background-color: rgb(255, 255, 255);
}

.search-head {
  position: relative;
  z-index: 50;
  width: 15px;
  height: 23px;
}
.search-head .search-head-ic {
  display: block;
  width: 100%;
  height: 100%;
}
.search-head .link-search img {
  margin-top: -3px;
}
.search-head .box-search {
  display: none;
  position: fixed;
  z-index: 100;
  width: 270px;
  background: transparent;
  right: 0;
  left: 0;
  margin: auto;
  top: 100px;
  transition: 0.85s;
  border-radius: 29px;
}
@media (max-width: 480px) {
  .search-head .box-search {
    position: fixed;
    top: 90px;
    width: calc(100% - 30px);
    left: 0;
    right: 0;
    margin: auto;
  }
}
.search-head .box-search .search-form {
  position: relative;
  z-index: 5;
}
.search-head .box-search .search-form input {
  color: #4f4f4f;
  height: 28px;
  width: 100%;
  border: 0;
  padding: 0 4px 0 30px;
  border-radius: 0;
  border-bottom: 1px solid #9E9E9E;
  background: #FFF;
  font-size: 16px;
}
.search-head .box-search .search-form input::-moz-placeholder {
  color: #9E9E9E;
  opacity: 1;
  line-height: 20px;
}
.search-head .box-search .search-form input::placeholder {
  color: #9E9E9E;
  opacity: 1;
  line-height: 20px;
}
.search-head .box-search .search-form input:-internal-autofill-selected {
  background: #fff !important;
}
.search-head .box-search .search-form button {
  border: none;
  background-color: transparent;
  outline: none;
  padding: 0;
  left: 3px;
  bottom: 4px;
  margin: auto;
  position: absolute;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
}
@media (max-width: 820px) {
  .search-head .box-search .search-form button {
    display: flex;
  }
}
.search-head .box-search .search-form button span.icon img {
  max-width: 100%;
}
.search-head .box-search .search-form button:active, .search-head .box-search .search-form button:focus-visible, .search-head .box-search .search-form button:focus {
  outline: none;
  box-shadow: none;
}
.search-head .box-search.active {
  display: block;
}
.search-head .box-search.active::after {
  content: "";
  display: block;
  width: 100%;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.3);
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1;
}
.search-head .box-search.active input {
  padding: 0 5px 0 30px;
  background-color: #f7f3f3;
}
@media (max-width: 991px) {
  .search-head {
    background: transparent url("../images/ic-5.png") no-repeat center center;
    background-size: auto;
  }
}
@keyframes MoveDown {
  0% {
    transform: translateY(-100%);
  }
  100% {
    transform: translateY(0);
  }
}
@keyframes MoveUp {
  0% {
    transform: translateY(100%);
  }
  100% {
    transform: translateY(0);
  }
}
.mm-listitem__text {
  text-transform: capitalize;
}

.mm-navbar {
  background: #f3f3f3;
}
.mm-navbar a {
  color: #333 !important;
}

.mm-listview {
  background: #f3f3f3;
}
.mm-listview a {
  color: #333;
}

.mm-menu .item-menu-mb {
  color: #333 !important;
}

.mm-menu .item-menu-mb.active {
  color: #1A68B3 !important;
}

.mm-btn_next:after {
  right: 33px;
  border-color: #003828;
}

.mm-btn_next .mm-counter {
  min-width: auto;
}

.mm-listitem {
  border-color: rgba(0, 0, 0, 0.1) !important;
}
.mm-listitem::after {
  right: 20px !important;
  border-color: rgba(0, 0, 0, 0.1);
}

.mm-btn_prev:before {
  border-color: #003828;
}

.mm-panels .sub-menu .menu-child {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}
.mm-panels .sub-menu .menu-child .menu-root {
  font-weight: 400;
  font-size: 16px;
  line-height: 138%;
  color: #333333;
  display: block;
  padding: 10px 0;
}

.mm-panels > .mm-panel > .mm-listview:first-child,
.mm-panels > .mm-panel > .mm-navbar + .mm-listview {
  margin-top: 0;
}

.mm-panel {
  padding: 0 15px;
}
.mm-panel:after, .mm-panel:before {
  display: none;
}

.mm-btn_close:after,
.mm-btn_close:before {
  border-color: #003828;
}

.mm-panel:not(.mm-hidden) {
  background: #f3f3f3;
}

.bgover-white {
  position: absolute;
  top: 100%;
  height: 50px;
  background-color: #fff;
  width: 100%;
  left: 0;
  display: none;
}

.mm-btn_next .mm-counter {
  display: none;
}

.mm-navbars_bottom {
  border: 0;
}

.footer {
  width: 100%;
  background-color: #fff;
  padding: 57px 0 50px;
}
.footer .footer--top {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: 20px 80px;
  margin-bottom: 75px;
}
.footer .footer--top .footer--col {
  flex: 1;
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  flex-direction: column;
  justify-content: space-between;
}
.footer .footer--top .footer--col .footer--title {
  width: 100%;
  color: #4F4F4F;
  font-size: 20px;
  font-style: normal;
  font-weight: 900;
  line-height: normal;
  padding-bottom: 17px;
  border-bottom: 1px solid #D5D5D5;
  margin-bottom: 52px;
}
.footer .footer--top .footer--col .footer--body {
  width: 100%;
}
.footer .footer--top .footer--col .footer--body .name--company {
  color: #4F4F4F;
  font-size: 18px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  text-transform: capitalize;
  max-width: 330px;
  margin: 0 0 29px;
}
.footer .footer--top .footer--col .footer--body .footer--address {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: 25px 44px;
}
.footer .footer--top .footer--col .footer--body .footer--address ._left {
  width: calc(55% - 22px);
}
.footer .footer--top .footer--col .footer--body .footer--address ._left .item, .footer .footer--top .footer--col .footer--body .footer--address ._left ._right .item a, .footer .footer--top .footer--col .footer--body .footer--address ._right .item ._left a, .footer .footer--top .footer--col .footer--body .footer--address ._left ._right .item .txt, .footer .footer--top .footer--col .footer--body .footer--address ._right .item ._left .txt {
  width: 100%;
  margin-bottom: 25px;
  color: #4F4F4F;
  font-size: 16px;
  font-style: normal;
  line-height: normal;
  font-weight: 600;
}
.footer .footer--top .footer--col .footer--body .footer--address ._left .item:last-child, .footer .footer--top .footer--col .footer--body .footer--address ._left ._right .item a:last-child, .footer .footer--top .footer--col .footer--body .footer--address ._right .item ._left a:last-child, .footer .footer--top .footer--col .footer--body .footer--address ._left ._right .item .txt:last-child, .footer .footer--top .footer--col .footer--body .footer--address ._right .item ._left .txt:last-child {
  margin-bottom: 0;
}
.footer .footer--top .footer--col .footer--body .footer--address ._right {
  width: calc(45% - 22px);
}
.footer .footer--top .footer--col .footer--body .footer--address ._right .item, .footer .footer--top .footer--col .footer--body .footer--address ._right .item a, .footer .footer--top .footer--col .footer--body .footer--address ._right .item .txt a, .footer .footer--top .footer--col .footer--body .footer--address ._right .item a a, .footer .footer--top .footer--col .footer--body .footer--address ._right .item .txt {
  color: #4F4F4F;
  font-size: 16px;
  font-style: normal;
  line-height: normal;
  margin-bottom: 16px;
  font-weight: 600;
}
.footer .footer--top .footer--col .footer--body .footer--address ._right .item:last-child, .footer .footer--top .footer--col .footer--body .footer--address ._right .item a:last-child, .footer .footer--top .footer--col .footer--body .footer--address ._right .item .txt:last-child {
  margin-bottom: 0;
}
.footer .footer--top .footer--col .footer--body .footer--address ._right .item .txt {
  margin: 0;
}
.footer .footer--top .footer--col .footer--body .footer--address ._right .item a, .footer .footer--top .footer--col .footer--body .footer--address ._right .item a a, .footer .footer--top .footer--col .footer--body .footer--address ._right .item a a a, .footer .footer--top .footer--col .footer--body .footer--address ._right .item .txt a {
  font-size: 20px;
  line-height: 135%;
}
.footer .footer--top .footer--col .footer--body ul {
  padding: 0;
  margin: 0;
  display: flex;
  flex-wrap: wrap;
  gap: 0 30px;
  width: 100%;
  max-width: 550px;
}
.footer .footer--top .footer--col .footer--body ul li {
  width: calc(50% - 15px);
  list-style: none;
}
.footer .footer--top .footer--col .footer--body ul li a {
  color: #4F4F4F;
  font-size: 20px;
  font-style: normal;
  line-height: 195%;
  font-weight: 600;
}
.footer .footer--top .footer--col .footer--body ul li a.active, .footer .footer--top .footer--col .footer--body ul li a:hover {
  color: #7C0410;
}
.footer .footer--top .footer--logo {
  flex-shrink: 0;
}
.footer .footer--bot {
  width: 100%;
  position: relative;
}
.footer .footer--bot .ic-dmca {
  display: block;
  max-width: 185px;
  position: absolute;
  top: 27px;
  left: 0;
}
.footer .footer--bot .ic-dmca img {
  max-width: 100%;
}
.footer .footer--bot .title {
  text-align: center;
  position: relative;
}
.footer .footer--bot .title span {
  display: inline-block;
  width: -moz-max-content;
  width: max-content;
  background-color: #fff;
  position: relative;
  z-index: 3;
  color: #4F4F4F;
  text-align: center;
  font-size: 20px;
  font-style: normal;
  font-weight: 900;
  line-height: normal;
  padding: 0 30px;
}
.footer .footer--bot .title::after {
  content: "";
  display: block;
  width: 100%;
  position: absolute;
  height: 1px;
  background-color: #D5D5D5;
  top: 0;
  left: 0;
  bottom: 0;
  margin: auto;
}
.footer .footer--bot .footer--bot---main {
  width: 100%;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 10px 131px;
  position: relative;
  padding-top: 47px;
}
.footer .footer--bot .footer--bot---main a {
  color: #4F4F4F;
  text-align: center;
  font-size: 14px;
  font-style: normal;
  line-height: 142.857%;
  letter-spacing: 1.5px;
  text-transform: uppercase;
}
@media screen and (max-width: 1600px) {
  .footer .footer--top .footer--col .footer--title {
    font-size: 18px;
  }
  .footer .footer--top .footer--col .footer--body .name--company {
    font-size: 16px;
  }
  .footer .footer--top .footer--col .footer--body .footer--address ._right .item a, .footer .footer--top .footer--col .footer--body .footer--address ._right .item .txt a, .footer .footer--top .footer--col .footer--body .footer--address ._right .item a a, .footer .footer--top .footer--col .footer--body .footer--address ._right .item a a a {
    font-size: 18px;
  }
  .footer .footer--top .footer--col .footer--body ul li a {
    font-size: 18px;
  }
  .footer .footer--bot .title span {
    font-size: 18px;
  }
}
@media screen and (max-width: 1200px) {
  .footer .footer--top .footer--col .footer--body .footer--address ._left {
    width: 100%;
  }
  .footer .footer--top .footer--col .footer--body .footer--address ._right {
    width: 100%;
  }
  .footer .footer--bot .footer--bot---main {
    gap: 10px 60px;
  }
}
@media screen and (max-width: 991px) {
  .footer {
    padding: 40px 0;
  }
  .footer .footer--top .footer--col .footer--title {
    font-size: 16px;
  }
  .footer .footer--top .footer--col .footer--body .name--company {
    font-size: 14px;
  }
  .footer .footer--top .footer--col .footer--body .footer--address ._right .item a, .footer .footer--top .footer--col .footer--body .footer--address ._right .item .txt a, .footer .footer--top .footer--col .footer--body .footer--address ._right .item a a, .footer .footer--top .footer--col .footer--body .footer--address ._right .item a a a {
    font-size: 16px;
  }
  .footer .footer--top .footer--col .footer--body ul li a {
    font-size: 16px;
  }
  .footer .footer--bot .footer--bot---main {
    gap: 10px 40px;
    flex-wrap: wrap;
  }
  .footer .footer--bot .title span {
    font-size: 16px;
  }
  .footer .footer--bot .ic-dmca {
    position: unset;
    margin: 20px auto 0;
    width: 100%;
    max-width: 100%;
    text-align: center;
  }
}
@media screen and (max-width: 767px) {
  .footer .footer--top {
    margin-bottom: 40px;
  }
  .footer .footer--top .footer--logo {
    display: flex;
    order: -1;
    margin: 0 auto;
  }
  .footer .footer--top .footer--col {
    width: 100%;
    flex: auto;
  }
  .footer .footer--top .footer--col .footer--title {
    margin-bottom: 30px;
  }
  .footer .footer--bot .footer--bot---main {
    padding-top: 35px;
  }
}
@media screen and (max-width: 480px) {
  .footer .footer--top .footer--col .footer--body ul li {
    width: 100%;
  }
}

:root {
  --scale-px: 1px;
}

.mg-auto {
  margin: 0 auto;
}

.tx-center {
  text-align: center;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px 16px;
  margin: 82px 0 0;
}
@media screen and (max-width: 991px) {
  .pagination {
    margin: 40px 0 0;
  }
}
.pagination .page-item {
  border: none;
  border-radius: 100%;
}
.pagination .page-item:last-child .page-link, .pagination .page-item:first-child .page-link {
  line-height: 21px !important;
  background-color: transparent !important;
  color: #4F4F4F !important;
  border: 0 !important;
}
.pagination .page-item:last-child .page-link::after, .pagination .page-item:first-child .page-link::after {
  display: none;
}
.pagination .page-item.disabled {
  cursor: not-allowed;
}
.pagination .page-item.active .page-link, .pagination .page-item:hover .page-link {
  border-bottom: 1px solid #C50;
  color: #C50;
}
.pagination .page-item.active .page-link svg path, .pagination .page-item:hover .page-link svg path {
  fill: #fff;
}
.pagination .page-item .page-link {
  border: 1px solid transparent;
  background: transparent;
  color: #4F4F4F;
  font-family: "Nunito Sans";
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: none;
  width: 40px;
  height: 40px;
}
.pagination .page-item .page-link svg {
  vertical-align: inherit;
}
@keyframes shake-bottom {
  0%, 100% {
    transform: rotate(0deg);
    transform-origin: 50% 100%;
  }
  10% {
    transform: rotate(2deg);
  }
  20%, 40%, 60% {
    transform: rotate(-4deg);
  }
  30%, 50%, 70% {
    transform: rotate(4deg);
  }
  80% {
    transform: rotate(-2deg);
  }
  90% {
    transform: rotate(2deg);
  }
}
@keyframes heartbeat {
  from {
    transform: scale(1);
    transform-origin: center center;
    animation-timing-function: ease-out;
  }
  10% {
    transform: scale(0.91);
    animation-timing-function: ease-in;
  }
  17% {
    transform: scale(0.98);
    animation-timing-function: ease-out;
  }
  33% {
    transform: scale(0.87);
    animation-timing-function: ease-in;
  }
  45% {
    transform: scale(1);
    animation-timing-function: ease-out;
  }
}
@keyframes vibrate-1 {
  0% {
    transform: translate(0);
  }
  20% {
    transform: translate(-2px, 2px);
  }
  40% {
    transform: translate(-2px, -2px);
  }
  60% {
    transform: translate(2px, 2px);
  }
  80% {
    transform: translate(2px, -2px);
  }
  100% {
    transform: translate(0);
  }
}
@keyframes width-border {
  0% {
    width: 0;
  }
  100% {
    width: 100%;
  }
}
@keyframes width-bg {
  0% {
    right: -100%;
  }
  100% {
    left: 0;
  }
}
@keyframes trackBallSlide {
  0% {
    opacity: 1;
    transform: translateY(12px);
  }
  15% {
    opacity: 0;
    transform: translateY(-8px);
  }
  30% {
    opacity: 1;
    transform: translateY(12px);
  }
  50% {
    opacity: 0;
    transform: translateY(-8px);
  }
  60% {
    opacity: 1;
    transform: translateY(12px);
  }
  100% {
    opacity: 1;
    transform: translateY(12px);
  }
}
@media (min-width: 1600px) {
  .container {
    max-width: 1670px;
  }
}
@keyframes rotateMouse {
  0%, 100%, 30% {
    transform: rotateZ(0);
  }
  10% {
    transform: rotateZ(10deg);
  }
  20% {
    transform: rotateZ(-10deg);
  }
}
@keyframes shake {
  10%, 90% {
    transform: translate3d(-1px, 0, -1px);
  }
  20%, 80% {
    transform: translate3d(2px, 0, 2px);
  }
  30%, 50%, 70% {
    transform: translate3d(-4px, 0, -4px);
  }
  40%, 60% {
    transform: translate3d(4px, 0, 4px);
  }
}
.bg_overlay {
  position: fixed;
  width: 100%;
  height: 100vh;
  top: 0;
  right: -100%;
  z-index: 9996;
  background: rgba(0, 0, 0, 0.8);
  display: none;
}
.bg_overlay.active {
  display: block;
  right: 0;
  transform: translate3d(0, 0, 0);
  transition: all 500ms ease-in-out;
}

.scrollbar-macosx > .scroll-element.scroll-x {
  height: 7px;
}

.scroll-wrapper > .scroll-content::-webkit-scrollbar {
  height: 0;
  width: 0;
}

.scrollbar-macosx > .scroll-element.scroll-x .scroll-bar {
  top: 0;
}

.scrollbar-macosx:hover > .scroll-element .scroll-bar,
.scrollbar-macosx > .scroll-element.scroll-draggable .scroll-bar {
  opacity: 0.7;
}

._boxfixed {
  width: 65px;
  position: fixed;
  display: flex;
  gap: 6px;
  right: 30px;
  bottom: 150px;
  flex-direction: column;
  align-items: center;
  z-index: 55;
}
._boxfixed .icon-mess {
  width: 65px;
  height: 65px;
  display: block;
}
._boxfixed .icon-mess img {
  max-width: 100%;
}
._boxfixed .img-banner--fix {
  width: -moz-max-content;
  width: max-content;
  height: -moz-max-content;
  height: max-content;
  position: absolute;
  bottom: calc(100% + 12px);
  right: 0;
}
@media screen and (max-width: 767px) {
  ._boxfixed {
    width: 40px;
    right: 15px;
  }
  ._boxfixed .icon-mess {
    width: 40px;
    height: 40px;
  }
  ._boxfixed .img-banner--fix {
    width: 80px;
  }
}

.back-top {
  width: 40px;
  height: 40px;
  z-index: 888;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: 0.2s;
  border-radius: 50%;
  background: #fff;
  box-shadow: 0 0 7px 0px rgba(0, 0, 0, 0.1);
}

.icon-mess {
  position: relative;
}
.icon-mess .icon-mess-other {
  position: absolute;
  bottom: -35px;
  right: calc(100% + 17px);
  border-radius: 8px;
  background: #FFF;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.06);
  padding: 0 20px;
  width: 220px;
  display: flex;
  flex-wrap: wrap;
}
.icon-mess .icon-mess-other a {
  width: 100%;
  padding: 20px 0 17px;
  border-bottom: 1px solid #E9E9E9;
  display: flex;
  gap: 15px;
  align-items: center;
}
.icon-mess .icon-mess-other a:last-child {
  padding: 17px 0 20px;
  border-bottom: 0;
}
.icon-mess .icon-mess-other a .icon {
  width: 40px;
  flex-shrink: 0;
}
.icon-mess .icon-mess-other a .icon .img {
  max-width: 100%;
}
.icon-mess .icon-mess-other a .txt {
  color: rgba(0, 0, 0, 0.8);
  font-size: 18px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  letter-spacing: 0.36px;
  flex: 1;
  width: 100%;
}
.icon-mess .icon-mess-other a .txt span {
  display: block;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 0.28px;
  width: 100%;
}
@media screen and (max-width: 767px) {
  .icon-mess .icon-mess-other {
    padding: 0 15px;
    width: 180px;
  }
  .icon-mess .icon-mess-other a {
    padding: 15px 0;
    gap: 10px;
  }
  .icon-mess .icon-mess-other a:last-child {
    padding: 15px 0;
    border-bottom: 0;
  }
  .icon-mess .icon-mess-other a .icon {
    width: 30px;
  }
  .icon-mess .icon-mess-other a .txt {
    font-size: 15px;
  }
  .icon-mess .icon-mess-other a .txt span {
    font-size: 12px;
  }
}

#fp-nav ul li,
.fp-slidesNav ul li {
  margin: 10px 7px;
  height: auto;
}
#fp-nav ul li a,
.fp-slidesNav ul li a {
  background: #2da0d0;
  width: 10px;
  height: 10px;
  border-radius: 50%;
}
#fp-nav ul li a span,
.fp-slidesNav ul li a span {
  width: 11px;
  height: 11px;
  display: none;
}
#fp-nav ul li a.active,
.fp-slidesNav ul li a.active {
  border-radius: 50px;
  height: 30px;
  background: #0ED3FE;
}
#fp-nav ul li:hover a,
.fp-slidesNav ul li:hover a {
  background: #0ED3FE;
  height: 30px;
  border-radius: 50px;
}
#fp-nav ul li:hover a.active,
.fp-slidesNav ul li:hover a.active {
  height: 30px;
  border-radius: 50px;
}

#fp-nav ul li .fp-tooltip {
  max-width: -moz-fit-content;
  max-width: fit-content;
  color: #262262;
  text-align: center;
  font-size: 16px;
  font-weight: 700;
  line-height: 137.5%;
  padding: 8px 20px;
  background: #fff;
}

.btn_all {
  display: inline-flex;
  gap: 10px;
  position: relative;
  align-items: center;
  color: #000;
  font-size: 14px;
  line-height: 171.429%;
  background-color: transparent;
  transition: all 0.3s;
  padding-left: 0;
  overflow: hidden;
  border-radius: 40px;
}
.btn_all span {
  position: relative;
  z-index: 2;
}
.btn_all span.icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 42px;
  height: 42px;
  border-radius: 50%;
  background-color: #1A68B3;
}
.btn_all span.icon img {
  max-width: 100%;
}
.btn_all span.icon svg path {
  fill: #fff;
}
.btn_all:hover {
  color: #fff;
  background-color: #1A68B3;
  padding-left: 17px;
}
.btn_all:hover span.icon {
  background-color: transparent;
}
@keyframes BeProud {
  100% {
    background-position: 100vw 0px;
  }
}
@keyframes bg-banner {
  100% {
    background-position: 100% 0%;
  }
}
.news_item {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.news_item .news_item_top {
  width: 100%;
  margin-bottom: 36px;
  display: block;
}
.news_item .news_item_top .img {
  display: block;
  width: 100%;
  position: relative;
  padding-top: 60.5%;
  overflow: hidden;
}
.news_item .news_item_top .img img {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  -o-object-position: center;
     object-position: center;
  -o-object-fit: cover;
     object-fit: cover;
  transition: 0.85s;
}
@media screen and (max-width: 1550px) and (min-width: 1200px) {
  .news_item .news_item_top {
    margin-bottom: 20px;
  }
}
.news_item .news_item_body {
  width: 100%;
  padding-bottom: 30px;
}
.news_item .news_item_body .news_item_tagdate {
  width: 100%;
  display: flex;
  color: #828282;
  font-size: 16px;
  text-transform: uppercase;
  flex-wrap: wrap;
  gap: 5px 10px;
  margin-bottom: 10px;
}
@media screen and (max-width: 767px) {
  .news_item .news_item_body .news_item_tagdate {
    font-size: 14px;
  }
}
.news_item .news_item_body .news_item_tagdate a {
  color: #828282;
}
.news_item .news_item_body .title {
  color: #333;
  font-size: 24px;
  font-weight: 700;
  line-height: 125%;
  letter-spacing: -0.48px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  min-height: auto;
  margin-bottom: 30px;
}
.news_item .news_item_body .title:hover {
  color: #262262;
}
@media screen and (max-width: 1550px) and (min-width: 1200px) {
  .news_item .news_item_body .title {
    font-size: 20px !important;
  }
}
.news_item .news_item_body .txt {
  color: #828282;
  font-size: 18px;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  min-height: auto;
}

.modalall .modal-dialog {
  max-width: 600px;
}
.modalall .modal-dialog .modal-content {
  border: 0;
  border-radius: 0;
  box-shadow: none;
  padding: 68px 30px 90px;
  background: #fff;
}
.modalall .modal-dialog .modal-content .btn-close {
  position: absolute;
  width: 27px;
  height: 27px;
  border: 0;
  padding: 0;
  top: 10px;
  opacity: 1;
  right: 10px;
  background: transparent;
}
.modalall .modal-dialog .modal-content .btn-close img {
  max-width: 100%;
}
.modalall .modal-dialog .modal-content .modal_main {
  width: 100%;
  max-width: 470px;
  margin: 0 auto;
}
.modalall .modal-dialog .modal-content .modal_main .title {
  color: #262262;
  text-align: center;
  font-size: 26px;
  font-weight: 700;
  text-transform: uppercase;
  margin-bottom: 10px;
}
.modalall .modal-dialog .modal-content .modal_main .txt {
  color: #828282;
  text-align: center;
  font-size: 18px;
  letter-spacing: -0.36px;
}
.modalall .modal-dialog .modal-content .modal_main .img {
  text-align: center;
  margin-bottom: 40px;
}
.modalall .modal-dialog .modal-content .modal_main .img img {
  max-width: 100%;
}

.loading-atg-other {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 105vh;
  z-index: 99999;
}
.loading-atg-other .loading-atg-child {
  position: relative;
  width: 100%;
  height: 100%;
}

.loading-atg {
  position: sticky;
  top: 0;
  left: 0;
  width: 100% !important;
  height: 100vh !important;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent url("../images/img-ldhome.jpg?v=1.1") no-repeat center center;
  background-size: cover;
}
@media screen and (max-width: 767px) {
  .loading-atg canvas {
    width: 85% !important;
    height: auto !important;
  }
}
.loading-atg img {
  max-width: 100%;
}
@media screen and (max-width: 574px) {
  .loading-atg img {
    max-width: 70%;
  }
}

.row-breadcrumb {
  width: 100%;
  background-color: transparent;
  padding-top: 20px;
  padding-bottom: 0;
}
.row-breadcrumb nav {
  width: 100%;
  float: left;
}
@media (max-width: 767px) {
  .row-breadcrumb {
    padding: 10px 5px;
  }
}
.row-breadcrumb .breadcrumb {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 0px 0 29px;
  border-bottom: 1px solid #D5D5D5;
  margin-bottom: 0px;
  list-style: none;
  background-color: transparent;
  border-radius: 0;
  width: 100%;
}
.row-breadcrumb .breadcrumb .breadcrumb-item {
  margin-bottom: 0px;
  color: #757575;
  font-size: 12px;
  font-style: normal;
  line-height: 140%;
  text-align: left;
  margin-right: 20px;
  padding-left: 0;
}
.row-breadcrumb .breadcrumb .breadcrumb-item:last-child {
  margin-right: 0;
}
.row-breadcrumb .breadcrumb .breadcrumb-item:last-child a {
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  white-space: normal;
}
.row-breadcrumb .breadcrumb .breadcrumb-item a {
  color: inherit;
  text-transform: none;
}
.row-breadcrumb .breadcrumb .breadcrumb-item a:hover {
  color: #7C0410;
}
.row-breadcrumb .breadcrumb .breadcrumb-item a:hover svg path {
  fill: #7C0410;
}
.row-breadcrumb .breadcrumb .breadcrumb-item + .breadcrumb-item::before {
  content: "|";
  margin-right: 20px;
  color: #BDBDBD;
  padding-right: 0;
}
.row-breadcrumb.row-breadcrumb-detail {
  padding-top: 15px;
}
.row-breadcrumb.row-breadcrumb-detail .breadcrumb {
  border: 0;
}
.intro-top {
  width: 100%;
  padding: 110px 10px 80px;
}
.intro-top .box-title {
  margin-bottom: 85px;
}
.intro-top .box-title .title {
  text-align: center;
}
.intro-top .desc-txt {
  color: #4F4F4F;
  text-align: center;
  font-size: 20px;
  font-style: normal;
  line-height: 150%;
  width: 100%;
  max-width: 808px;
  margin: 0 auto 85px;
}
.intro-top .desc-txt p:last-child {
  margin-bottom: 0;
}
.intro-top .img {
  text-align: center;
}
.intro-top .img img {
  max-width: 100%;
}
@media screen and (max-width: 1600px) {
  .intro-top {
    padding: 80px 10px;
  }
  .intro-top .box-title {
    margin-bottom: 60px;
  }
}
@media screen and (max-width: 991px) {
  .intro-top {
    padding: 60px 10px;
  }
  .intro-top .box-title {
    margin-bottom: 30px;
  }
  .intro-top .box-title .title img {
    width: 80%;
    max-width: 339px;
  }
  .intro-top .desc-txt {
    margin: 0 auto 40px;
    font-size: 18px;
  }
}

.intro-corevalues {
  width: 100%;
  padding: 60px 10px 100px;
}
.intro-corevalues .corevalues--list {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  max-width: 1355px;
  margin: 0 auto;
  gap: 30px;
}
.intro-corevalues .corevalues--list ._left {
  width: calc(35% - 15px);
}
.intro-corevalues .corevalues--list ._left .box-title {
  margin: 0;
}
@media screen and (min-width: 1600px) {
  .intro-corevalues .corevalues--list ._left .box-title .title {
    font-size: 36px;
    line-height: 129%;
  }
}
.intro-corevalues .corevalues--list ._right {
  width: calc(65% - 15px);
  max-width: 800px;
}
@media screen and (max-width: 991px) {
  .intro-corevalues {
    padding: 60px 10px;
  }
  .intro-corevalues .corevalues--list ._left {
    width: 100%;
  }
  .intro-corevalues .corevalues--list ._left .box-title {
    margin: 0;
  }
  .intro-corevalues .corevalues--list ._left .box-title .title {
    text-align: center;
  }
  .intro-corevalues .corevalues--list ._right {
    width: 100%;
  }
}

.corevalues--box {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}
.corevalues--box .corevalues--items {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  padding: 36px 0;
  border-top: 1px solid #AE8751;
}
.corevalues--box .corevalues--items:last-child {
  padding-bottom: 0;
}
.corevalues--box .corevalues--items:first-child {
  padding-top: 0;
  border-top: 0;
}
.corevalues--box .corevalues--items .corevalues--col {
  flex: 1;
  width: 100%;
}
.corevalues--box .corevalues--items .corevalues--col .title {
  color: #7C0410;
  font-size: 30px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  text-transform: capitalize;
}
.corevalues--box .corevalues--items .corevalues--col .txt {
  color: #4F4F4F;
  font-size: 20px;
  font-style: normal;
  line-height: 150%;
}
.corevalues--box .corevalues--items .corevalues--col .txt p:last-child {
  margin-bottom: 0;
}
@media screen and (max-width: 1600px) {
  .corevalues--box .corevalues--items .corevalues--col .title {
    font-size: 26px;
  }
  .corevalues--box .corevalues--items .corevalues--col .txt {
    font-size: 18px;
  }
}
@media screen and (max-width: 991px) {
  .corevalues--box .corevalues--items .corevalues--col .title {
    font-size: 22px;
  }
  .corevalues--box .corevalues--items .corevalues--col .txt {
    font-size: 16px;
  }
}
@media screen and (max-width: 767px) {
  .corevalues--box .corevalues--items {
    padding: 20px 0;
  }
  .corevalues--box .corevalues--items .corevalues--col .title {
    font-size: 18px;
  }
}
@media screen and (max-width: 574px) {
  .corevalues--box .corevalues--items .corevalues--col {
    flex: none;
  }
}

.home-brandvision {
  width: 100%;
  padding: 78px 10px 118px;
  background: transparent url("../images/bg-brand vision.jpg") no-repeat center center;
  background-size: cover;
}
.home-brandvision .brandvision--list {
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
  align-items: center;
  width: 100%;
  max-width: 1355px;
  margin: 0 auto;
}
.home-brandvision .brandvision--list ._left {
  width: 35%;
  max-width: 531px;
}
.home-brandvision .brandvision--list ._left img {
  text-align: center;
  max-width: 100% !important;
  height: auto !important;
}
.home-brandvision .brandvision--list ._right {
  flex: 1;
  width: 100%;
}
.home-brandvision .brandvision--list ._right .brandvision--body {
  width: 100%;
  max-width: 546px;
  margin: 0 auto;
}
.home-brandvision .brandvision--list ._right .brandvision--body .box-title {
  margin-bottom: 43px;
}
.home-brandvision .brandvision--list ._right .brandvision--body .box-title .title {
  color: #EDD0A7;
  text-align: center;
}
@media screen and (min-width: 1600px) {
  .home-brandvision .brandvision--list ._right .brandvision--body .box-title .title {
    font-size: 36px;
    line-height: 129%;
  }
}
.home-brandvision .brandvision--list ._right .brandvision--body .desc {
  color: #FFF;
  text-align: center;
  font-size: 20px;
  font-style: normal;
  line-height: 150%;
  margin-bottom: 55px;
}
.home-brandvision .brandvision--list ._right .brandvision--body .desc p:last-child {
  margin-bottom: 0;
}
.home-brandvision .brandvision--list ._right .brandvision--body .infor--author {
  color: #EDD0A7;
  text-align: center;
  font-size: 16px;
  font-style: normal;
  line-height: 150%;
}
@media screen and (max-width: 991px) {
  .home-brandvision {
    padding: 60px 10px;
  }
  .home-brandvision .brandvision--list ._left {
    width: 100%;
    text-align: center;
    max-width: 100%;
  }
  .home-brandvision .brandvision--list ._right {
    width: 100%;
    flex: none;
    order: -1;
  }
  .home-brandvision .brandvision--list ._right .brandvision--body .box-title {
    margin-bottom: 30px;
  }
  .home-brandvision .brandvision--list ._right .brandvision--body .desc {
    font-size: 18px;
    margin-bottom: 30px;
  }
}

.intro-historyestablishment {
  padding: 121px 10px 80px;
  background: transparent url("../images/bg-lsht.jpg") no-repeat center center;
  background-size: cover;
}
.intro-historyestablishment .box-title {
  margin-bottom: 82px;
}
.intro-historyestablishment .box-title .title {
  color: #FFF;
}
@media screen and (max-width: 1400px) {
  .intro-historyestablishment {
    padding: 80px 10px;
  }
  .intro-historyestablishment .box-title {
    margin-bottom: 60px;
  }
}
@media screen and (max-width: 991px) {
  .intro-historyestablishment {
    padding: 60px 10px;
  }
  .intro-historyestablishment .box-title {
    margin-bottom: 30px;
  }
}

.historyestablishment--list {
  width: calc(100% - (100% - 1670px) / 2);
  margin-left: auto;
  padding-left: 15px;
}
.historyestablishment--list .historyestablishment--top {
  position: relative;
  margin-bottom: 57px;
}
.historyestablishment--list .historyestablishment--top .producbox--btn .swiper-button-next {
  right: 10px;
}
.historyestablishment--list .historyestablishment--top .producbox--btn .swiper-button-next.swiper-button-disabled {
  display: none !important;
}
.historyestablishment--list .historyestablishment--top .producbox--btn .swiper-button-prev {
  left: -15px;
}
.historyestablishment--list .historyestablishment--top .producbox--btn .swiper-button-prev.swiper-button-disabled {
  display: none !important;
}
.historyestablishment--list .historyestablishment--top .historyestablishment--other {
  width: 100%;
  overflow: hidden;
  padding-left: 381px;
}
.historyestablishment--list .historyestablishment--top .historyestablishment--other .swiper {
  overflow: visible;
  height: auto;
  width: 100%;
  max-width: 459px;
  margin-left: 0;
}
.historyestablishment--list .historyestablishment--top .historyestablishment--other .swiper .swiper-slide {
  height: auto;
  padding: 0 96px 65px 78px;
}
.historyestablishment--list .historyestablishment--top .historyestablishment--other .swiper .swiper-slide.swiper-slide-prev, .historyestablishment--list .historyestablishment--top .historyestablishment--other .swiper .swiper-slide.swiper-slide-next, .historyestablishment--list .historyestablishment--top .historyestablishment--other .swiper .swiper-slide.swiper-slide-active {
  border-right: 1px solid #FFE9CA;
}
.historyestablishment--list .historyestablishment--top .historyestablishment--other .swiper .swiper-slide:last-child {
  border-right: 0;
}
.historyestablishment--list .historyestablishment--top .historyestablishment--other .swiper .swiper-slide.swiper-slide-active {
  padding: 0 31px 65px 36px;
}
.historyestablishment--list .historyestablishment--top .historyestablishment--other .swiper .swiper-slide.swiper-slide-active .historyestablishment--items .img {
  display: none;
}
.historyestablishment--list .historyestablishment--top .historyestablishment--other .swiper .swiper-slide.swiper-slide-active .historyestablishment--items .years {
  margin-bottom: 40px;
  font-size: 80px;
  font-style: normal;
  font-weight: 200;
  line-height: 130.839%;
}
.historyestablishment--list .historyestablishment--top .historyestablishment--other .swiper .swiper-slide.swiper-slide-active .historyestablishment--items .historyestablishment--body .desc {
  -webkit-line-clamp: unset;
}
.historyestablishment--list .historyestablishment--bot .swiper .swiper-slide {
  width: -moz-max-content;
  width: max-content;
  color: rgba(255, 255, 255, 0.4);
  font-size: 15.047px;
  font-style: normal;
  line-height: 179.506%;
  cursor: pointer;
}
.historyestablishment--list .historyestablishment--bot .swiper .swiper-slide.swiper-slide-thumb-active {
  color: #FFF;
}
@media screen and (max-width: 1670px) {
  .historyestablishment--list {
    width: calc(100% - 20px);
    margin-right: auto;
    padding-left: 0;
  }
  .historyestablishment--list .historyestablishment--top .producbox--btn .swiper-button-next {
    right: -15px;
  }
}
@media screen and (max-width: 1600px) {
  .historyestablishment--list .historyestablishment--top .historyestablishment--other .swiper .swiper-slide.swiper-slide-active .historyestablishment--items .years {
    margin-bottom: 30px;
    font-size: 60px;
  }
  .historyestablishment--list .historyestablishment--bot .swiper .swiper-slide {
    font-size: 14px;
  }
}
@media screen and (max-width: 991px) {
  .historyestablishment--list .historyestablishment--top .historyestablishment--other {
    padding: 0;
  }
  .historyestablishment--list .historyestablishment--top .historyestablishment--other .swiper .swiper-slide {
    padding-bottom: 30px;
  }
  .historyestablishment--list .historyestablishment--top .historyestablishment--other .swiper .swiper-slide.swiper-slide-active {
    padding-bottom: 30px;
  }
  .historyestablishment--list .historyestablishment--top .historyestablishment--other .swiper .swiper-slide.swiper-slide-active .historyestablishment--items .years {
    font-size: 50px;
  }
}
@media screen and (max-width: 574px) {
  .historyestablishment--list .historyestablishment--top .historyestablishment--other {
    padding: 0;
  }
  .historyestablishment--list .historyestablishment--top .historyestablishment--other .swiper .swiper-slide {
    padding-bottom: 30px;
  }
  .historyestablishment--list .historyestablishment--top .historyestablishment--other .swiper .swiper-slide.swiper-slide-prev, .historyestablishment--list .historyestablishment--top .historyestablishment--other .swiper .swiper-slide.swiper-slide-next, .historyestablishment--list .historyestablishment--top .historyestablishment--other .swiper .swiper-slide.swiper-slide-active {
    border-right: 0;
  }
  .historyestablishment--list .historyestablishment--top .historyestablishment--other .swiper .swiper-slide.swiper-slide-active {
    padding-bottom: 30px;
  }
  .historyestablishment--list .historyestablishment--top .historyestablishment--other .swiper .swiper-slide.swiper-slide-active .historyestablishment--items .years {
    font-size: 45px;
  }
}

.historyestablishment--items {
  width: 100%;
}
.historyestablishment--items .img {
  padding-top: 100%;
  width: 100%;
  position: relative;
  margin-bottom: 55px;
}
.historyestablishment--items .img img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
  position: absolute;
  top: 0;
  left: 0;
}
.historyestablishment--items .years {
  color: #FFE9CA;
  font-size: 30px;
  font-style: normal;
  line-height: 166.667%;
  margin-bottom: 25px;
}
.historyestablishment--items .historyestablishment--body .title {
  color: #FFF;
  font-size: 20px;
  font-style: normal;
  font-weight: 700;
  line-height: 139.95%;
  margin-bottom: 20px;
}
.historyestablishment--items .historyestablishment--body .desc {
  color: #FFF;
  font-size: 16px;
  font-style: normal;
  line-height: 150%;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 4;
  overflow: hidden;
}
.historyestablishment--items .historyestablishment--body .desc p:last-child {
  margin-bottom: 0;
}
@media screen and (max-width: 991px) {
  .historyestablishment--items .years {
    font-size: 22px;
  }
  .historyestablishment--items .historyestablishment--body .title {
    font-size: 18px;
  }
  .historyestablishment--items .historyestablishment--body .desc {
    font-size: 16px;
  }
}
@media screen and (max-width: 767px) {
  .historyestablishment--items .img {
    margin-bottom: 30px;
  }
}
@media screen and (max-width: 574px) {
  .historyestablishment--items .img {
    display: none;
  }
}

.intro-philosophy {
  padding: 123px 10px 96px;
  width: 100%;
}
.intro-philosophy .box-title {
  margin-bottom: 80px;
}
.intro-philosophy .box-title .title {
  text-align: center;
  margin-bottom: 15px;
}
.intro-philosophy .box-title .desc {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}
.intro-philosophy .philosophy--list {
  position: relative;
  margin-bottom: 100px;
}
.intro-philosophy .box--btn {
  text-align: center;
}
@media screen and (max-width: 1600px) {
  .intro-philosophy {
    padding: 96px 10px;
  }
  .intro-philosophy .box-title {
    margin-bottom: 60px;
  }
  .intro-philosophy .philosophy--list {
    position: relative;
    margin-bottom: 60px;
  }
}
@media screen and (max-width: 991px) {
  .intro-philosophy {
    padding: 60px 10px;
  }
  .intro-philosophy .box-title {
    margin-bottom: 30px;
  }
  .intro-philosophy .philosophy--list {
    position: relative;
    margin-bottom: 40px;
  }
}

.philosophy--items {
  width: 100%;
}
.philosophy--items .img {
  width: 100%;
  padding-top: 100%;
  overflow: hidden;
  position: relative;
  display: block;
  margin-bottom: 65px;
}
.philosophy--items .img img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
  position: absolute;
  top: 0;
  left: 0;
}
.philosophy--items .desc {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
  color: #4F4F4F;
  font-size: 20px;
  line-height: 140%;
}
@media screen and (max-width: 991px) {
  .philosophy--items {
    padding-bottom: 20px;
  }
  .philosophy--items .img {
    margin-bottom: 30px;
  }
  .philosophy--items .desc {
    font-size: 18px;
  }
}

.bannerslide-main {
  width: 100%;
}
.bannerslide-main .swiper-slide {
  height: auto;
}
.bannerslide-main .bannerslide--items {
  width: 100%;
  height: 100%;
  padding: 80px 10px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  display: flex;
  align-items: center;
  height: calc(100vh - 183px);
}
.bannerslide-main .bannerslide--items .bannerslide--other {
  width: 100%;
  max-width: 401px;
}
.bannerslide-main .bannerslide--items .bannerslide--other .box-title {
  margin-bottom: 44px;
}
.bannerslide-main .bannerslide--items .bannerslide--other .box-title .title {
  color: #EDD0A7;
}
.bannerslide-main .bannerslide--items .bannerslide--other .box-title .sub_title {
  color: #EDD0A7;
  margin-bottom: 34px;
}
.bannerslide-main .bannerslide--items .bannerslide--other .box-title .desc {
  color: #fff;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 6;
  overflow: hidden;
}
.bannerslide-main .bannerslide--items .bannerslide--other .box--btn .btn-links {
  color: #EDD0A7;
  border-bottom: 1px solid #EDD0A7;
}
.bannerslide-main .bannerslide--items .goldmain-title {
  color: #4F4F4F;
  text-align: center;
  font-family: "Pinyon Script";
  font-size: 131.842px;
  font-style: normal;
  font-weight: 400;
  line-height: 110%;
  text-transform: capitalize;
  margin: 0;
}
.bannerslide-main .bannerslide--items.style--2 .bannerslide--other .box-title .title {
  color: #4F4F4F;
}
.bannerslide-main .bannerslide--items.style--2 .bannerslide--other .box-title .sub_title {
  color: #4F4F4F;
}
.bannerslide-main .bannerslide--items.style--2 .bannerslide--other .box-title .desc {
  color: #4F4F4F;
  font-size: 18px;
}
.bannerslide-main .bannerslide--items.style--2 .bannerslide--other .box--btn .btn-links {
  color: #4F4F4F;
  border-bottom: 1px solid #4F4F4F;
}
.bannerslide-main .producbox--panigation .swiper-pagination {
  position: absolute;
}
.bannerslide-main.bannerslide-boxstyle2 .bannerslide--items .bannerslide--other {
  margin-right: 126px;
}
.bannerslide-main.bannerslide-child .bannerslide--items .bannerslide--other .box-title {
  color: #EDD0A7;
  font-size: 18px;
}
.bannerslide-main.bannerslide-main-club .bannerslide--items .bannerslide--other {
  max-width: 684px;
}
.bannerslide-main.bannerslide-main-club .bannerslide--items .bannerslide--other .box-title {
  margin-bottom: 24px;
}
.bannerslide-main.bannerslide-main-club .bannerslide--items .bannerslide--other .box-title .title {
  color: #fff;
  margin-bottom: 24px;
}
.bannerslide-main.bannerslide-main-club .bannerslide--items .bannerslide--other .box-title .desc {
  color: #fff;
  font-size: 18px;
}
.bannerslide-main.bannerslide-main-club .bannerslide--items .bannerslide--other .box--btn .btn-links {
  color: #FFFFFF;
  border-bottom: 1px solid #FFFFFF;
}
@media screen and (max-width: 1600px) {
  .bannerslide-main .bannerslide--items {
    padding: 60px 10px;
  }
  .bannerslide-main .bannerslide--items .bannerslide--other .box-title {
    margin-bottom: 40px;
  }
  .bannerslide-main .bannerslide--items .bannerslide--other .box-title .sub_title {
    color: #EDD0A7;
    margin-bottom: 30px;
  }
  .bannerslide-main .bannerslide--items .goldmain-title {
    font-size: 120px;
  }
}
@media screen and (max-width: 1400px) {
  .bannerslide-main .bannerslide--items .bannerslide--other .box-title {
    margin-bottom: 30px;
  }
  .bannerslide-main .bannerslide--items .bannerslide--other .box-title .sub_title {
    margin-bottom: 25px;
  }
  .bannerslide-main .bannerslide--items .goldmain-title {
    font-size: 110px;
  }
}
@media screen and (max-width: 1200px) {
  .bannerslide-main .bannerslide--items .goldmain-title {
    font-size: 100px;
  }
}
@media screen and (max-width: 991px) {
  .bannerslide-main .bannerslide--items .goldmain-title {
    font-size: 80px;
  }
}
@media screen and (max-width: 767px) {
  .bannerslide-main .bannerslide--items .goldmain-title {
    font-size: 60px;
    margin-bottom: 30px;
  }
  .bannerslide-main .bannerslide--items .col-md-6:last-child {
    order: -1;
  }
}
@media screen and (max-width: 574px) {
  .bannerslide-main .bannerslide--items .goldmain-title {
    font-size: 40px;
  }
}

.introchild-collections {
  width: 100%;
  padding: 50px 10px 137px;
  overflow: hidden;
}
.introchild-collections .box-title {
  margin-bottom: 38px;
}
.introchild-collections .box-title .title {
  text-align: center;
}
@media screen and (min-width: 1600px) {
  .introchild-collections .box-title .title {
    font-size: 36px;
  }
}
.introchild-collections .box-title .desc {
  font-size: 16.855px;
  text-align: center;
}
.introchild-collections .collections--list {
  position: relative;
  width: 100%;
  max-width: 803px;
  margin: 0 auto;
}
.introchild-collections .collections--list .producbox--panigation {
  position: absolute;
  width: 100%;
  bottom: -55px;
}
.introchild-collections .collections--list .producbox--panigation .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
  background-color: #4F4F4F;
}
.introchild-collections .collections--list::after {
  width: 1px;
  height: calc(50% - 18px);
  background: #AE8751;
  content: "";
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 3;
}
.introchild-collections .collections--list::before {
  width: 1px;
  height: calc(50% - 18px);
  background: #AE8751;
  content: "";
  display: block;
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 3;
}
.introchild-collections .collections--list .producbox--btn .swiper-button-next,
.introchild-collections .collections--list .producbox--btn .swiper-button-prev {
  margin-top: 0;
  top: calc(50% - 18px);
  border: 1px solid #AE8751;
  background-color: transparent;
}
.introchild-collections .collections--list .producbox--btn .swiper-button-next svg path,
.introchild-collections .collections--list .producbox--btn .swiper-button-prev svg path {
  fill: #AE8751;
}
.introchild-collections .collections--list .swiper {
  overflow: visible;
}
.introchild-collections .collections--list .swiper::after {
  width: 1px;
  height: calc(50% - 18px);
  background: #AE8751;
  content: "";
  display: block;
  position: absolute;
  top: 0;
  right: 0;
  z-index: 3;
}
.introchild-collections .collections--list .swiper::before {
  width: 1px;
  height: calc(50% - 18px);
  background: #AE8751;
  content: "";
  display: block;
  position: absolute;
  bottom: 0;
  right: 0;
  z-index: 3;
}
.introchild-collections .collections--list .swiper .swiper-slide.swiper-slide-active .collections--items::after {
  top: auto;
  left: 0;
  bottom: 0;
  background: linear-gradient(180deg, rgba(127, 18, 21, 0) 0%, #7F1215 100%);
  height: 200px;
}
.introchild-collections .collections--list .swiper .swiper-slide.swiper-slide-active .collections--items .collections--body {
  display: block;
}
@media screen and (max-width: 991px) {
  .introchild-collections {
    padding: 40px 10px;
  }
}

.collections--items {
  width: 100%;
  display: block;
  overflow: hidden;
  position: relative;
  padding-top: 62.765%;
}
.collections--items > img {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
  transition: all 0.3s;
}
.collections--items::after {
  display: block;
  content: "";
  width: 100%;
  height: 100%;
  z-index: 2;
  position: absolute;
  top: 0;
  left: 0;
  background: linear-gradient(0deg, rgba(119, 6, 17, 0.7) 0%, rgba(119, 6, 17, 0.7) 100%);
}
.collections--items .collections--body {
  width: 100%;
  height: -moz-max-content;
  height: max-content;
  padding: 34px 20px;
  position: absolute;
  z-index: 5;
  bottom: 0;
  left: 0;
  display: block;
  text-align: center;
  display: none;
  transition: all 0.3s;
}
.collections--items .collections--body .title {
  color: #EDD0A7;
  text-align: center;
  font-size: 30px;
  font-style: normal;
  font-weight: 600;
  line-height: 129%;
  text-transform: capitalize;
  display: block;
  width: 100%;
  margin-bottom: 7px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}
.collections--items .collections--body .btn-links {
  margin: 0 auto;
  color: #EDD0A7;
  border-bottom: 1px solid #EDD0A7;
  font-size: 15px;
  font-style: normal;
  line-height: 140%;
}
@media screen and (max-width: 1600px) {
  .collections--items .collections--body .title {
    font-size: 26px;
  }
}
@media screen and (max-width: 1400px) {
  .collections--items .collections--body .title {
    font-size: 22px;
  }
  .collections--items .collections--body .btn-links {
    font-size: 14px;
  }
}
@media screen and (max-width: 991px) {
  .collections--items .collections--body .title {
    font-size: 20px;
  }
}
@media screen and (max-width: 767px) {
  .collections--items .collections--body .title {
    font-size: 18px;
  }
}

.block-virtue {
  width: 100%;
  padding: 40px 10px;
  position: relative;
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
}
.block-virtue::after {
  content: "";
  display: block;
  width: 50%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  background: transparent url("../images/bg-vtt1.jpg") no-repeat top center;
  background-size: cover;
}
.block-virtue::before {
  content: "";
  display: block;
  width: 50%;
  height: 100%;
  position: absolute;
  top: 0;
  right: 0;
  background: transparent url("../images/bg-vtt2.jpg") no-repeat top center;
  background-size: cover;
}
.block-virtue .container {
  position: relative;
  z-index: 5;
  width: 100%;
  max-width: 100%;
}
.block-virtue .virtue--content {
  text-align: center;
}
.block-virtue .virtue--content img {
  max-width: 100%;
}
.block-virtue .virtue--content .line {
  width: 100%;
  max-width: 374px;
  height: 1px;
  margin: 56px auto 46px;
  background-color: #AE8751;
}
.block-virtue .virtue--content .desc {
  width: 100%;
  max-width: 360px;
  margin: 0 auto;
  color: #EDD0A7;
  text-align: center;
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: 150%;
  text-transform: capitalize;
}
.block-virtue .virtue--content .desc p:last-child {
  margin-bottom: 0;
}
@media screen and (max-width: 1600px) {
  .block-virtue .virtue--content .line {
    margin: 40px auto;
  }
  .block-virtue .virtue--content .desc {
    font-size: 18px;
  }
}
@media screen and (max-width: 1200px) {
  .block-virtue .virtue--content .desc {
    font-size: 18px;
  }
  .block-virtue .virtue--content img {
    width: 70%;
    max-width: 344px;
  }
}
@media screen and (max-width: 991px) {
  .block-virtue {
    height: auto;
    min-height: auto;
    padding: 40px 10px;
  }
}
@media screen and (max-width: 767px) {
  .block-virtue .virtue--content {
    margin-bottom: 30px;
  }
  .block-virtue .virtue--content .desc {
    font-size: 16px;
  }
}

.virtue--video {
  width: 100%;
  max-width: 678px;
  margin: 0 auto;
}
.virtue--video .virtue--other {
  width: 100%;
  padding-top: 56.34%;
  overflow: hidden;
  position: relative;
}
.virtue--video .virtue--other video,
.virtue--video .virtue--other iframe {
  width: 100% !important;
  height: 100% !important;
  position: absolute;
  top: 0;
  left: 0;
}
.virtue--video .virtue--other .buttonclick-autoplay {
  width: 100%;
  display: flex;
  height: 100%;
  align-items: center;
  justify-content: center;
  background-position: center;
  background-repeat: no-repeat;
  position: absolute;
  top: 0;
  left: 0;
  cursor: pointer;
}
.virtue--video .virtue--other .buttonclick-autoplay span {
  color: #FFE9CA;
  font-size: 24px;
  line-height: 140%;
  border-bottom: 1px solid #FFE9CA;
}
@media screen and (max-width: 1600px) {
  .virtue--video .virtue--other .buttonclick-autoplay span {
    font-size: 20px;
  }
}
@media screen and (max-width: 991px) {
  .virtue--video .virtue--other .buttonclick-autoplay span {
    font-size: 18px;
  }
}

.block-pricegold {
  width: 100%;
  padding: 40px 10px;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}
.block-pricegold::after {
  content: "";
  display: block;
  width: 50%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  background: linear-gradient(180deg, #BA2025 0%, #460D0F 100%);
}
.block-pricegold::before {
  content: "";
  display: block;
  width: 50%;
  height: 100%;
  position: absolute;
  top: 0;
  right: 0;
  background: linear-gradient(180deg, #FFF 0%, #F9EDCD 100%);
}
.block-pricegold .container {
  position: relative;
  z-index: 5;
  width: 100%;
  max-width: 100%;
}
.block-pricegold .pricegold--list {
  width: 100%;
  max-width: 642px;
  margin: 0 auto;
}
.block-pricegold .pricegold--list .swiper {
  margin-bottom: 50px;
}
.block-pricegold .pricegold--list .swiper .swiper-slide {
  height: auto;
}
.block-pricegold .pricegold--list .box-title {
  margin-bottom: 50px;
}
.block-pricegold .pricegold--list .box-title .title {
  text-align: center;
  color: #EDD0A7;
}
.block-pricegold .pricegold--list .box-title .sub_title {
  text-align: center;
  color: #EDD0A7;
}
@media screen and (min-width: 1600px) {
  .block-pricegold .pricegold--list .box-title .sub_title {
    font-size: 33px;
  }
}
.block-pricegold .pricegold--list .img {
  width: 100%;
  padding-top: 87.85%;
  overflow: hidden;
  position: relative;
  margin-bottom: 30px;
}
.block-pricegold .pricegold--list .img img {
  width: 100%;
  height: 100%;
  -o-object-fit: scale-down;
     object-fit: scale-down;
  -o-object-position: center;
     object-position: center;
  position: absolute;
  top: 0;
  left: 0;
}
.block-pricegold .pricegold--list .desc {
  color: #EDD0A7;
  text-align: center;
  font-size: 18px;
  font-style: normal;
  line-height: 150%;
}
@media screen and (max-width: 991px) {
  .block-pricegold::after {
    width: 100%;
  }
  .block-pricegold::before {
    display: none;
  }
  .block-pricegold .pricegold--list {
    margin-bottom: 30px;
  }
}

.pricegold--right {
  width: 100%;
  padding: 20px 20px 20px 79px;
}
.pricegold--right .pricegold--other {
  width: 100%;
  max-width: 726px;
}
.pricegold--right .pricegold--other .title {
  color: #7C0410;
  font-size: 33px;
  font-style: normal;
  font-weight: 600;
  line-height: 150%;
  margin-bottom: 42px;
  text-transform: capitalize;
}
.pricegold--right .pricegold--other .pricegold--form {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20px;
}
.pricegold--right .pricegold--other .pricegold--form .pricegold--row {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: 10px 30px;
  align-items: center;
  padding: 36px 0;
  border-bottom: 1px solid #D1B181;
}
.pricegold--right .pricegold--other .pricegold--form .pricegold--row:last-child {
  border-bottom: 0;
}
.pricegold--right .pricegold--other .pricegold--form .pricegold--row .pricegold--col1 {
  width: calc(15.5% - 22.5px);
}
.pricegold--right .pricegold--other .pricegold--form .pricegold--row .pricegold--col1 img {
  max-width: 100%;
}
.pricegold--right .pricegold--other .pricegold--form .pricegold--row .pricegold--col2 {
  width: calc(29.5% - 22.5px);
}
.pricegold--right .pricegold--other .pricegold--form .pricegold--row .pricegold--col3 {
  width: calc(27.5% - 22.5px);
}
.pricegold--right .pricegold--other .pricegold--form .pricegold--box {
  width: 100%;
}
.pricegold--right .pricegold--other .pricegold--form .pricegold--box .name {
  color: #4F4F4F;
  font-size: 16px;
  font-style: normal;
  line-height: 150%;
  margin-bottom: 10px;
}
.pricegold--right .pricegold--other .pricegold--form .pricegold--box .parameter {
  color: #D1B181;
  font-size: 16px;
  font-style: normal;
  line-height: 150%;
  margin-bottom: 10px;
}
.pricegold--right .pricegold--other .pricegold--form .pricegold--box .price {
  color: #4F4F4F;
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: 197.085%;
}
.pricegold--right .pricegold--other .pricegold--form .pricegold--quantity .quantity--title {
  color: #212121;
  font-size: 16px;
  font-style: normal;
  line-height: 150%;
  margin-bottom: 46px;
}
.pricegold--right .pricegold--other .pricegold--total {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  align-items: end;
  justify-content: space-between;
  gap: 20px 10px;
}
.pricegold--right .pricegold--other .pricegold--total .number .txt {
  color: #000;
  font-size: 24px;
  font-style: normal;
  line-height: 150%;
  text-transform: capitalize;
}
.pricegold--right .pricegold--other .pricegold--total .number .total--price {
  color: #7C0410;
  font-size: 36px;
  font-style: normal;
  font-weight: 800;
  line-height: 109.492%;
}
@media screen and (max-width: 1600px) {
  .pricegold--right .pricegold--other .title {
    font-size: 26px;
    margin-bottom: 35px;
  }
  .pricegold--right .pricegold--other .pricegold--form .pricegold--box .price {
    font-size: 18px;
  }
  .pricegold--right .pricegold--other .pricegold--total .number .txt {
    font-size: 22px;
  }
  .pricegold--right .pricegold--other .pricegold--total .number .total--price {
    font-size: 32px;
  }
}
@media screen and (max-width: 1400px) {
  .pricegold--right {
    padding: 20px 20px 20px 50px;
  }
  .pricegold--right .pricegold--other .title {
    font-size: 22px;
  }
  .pricegold--right .pricegold--other .pricegold--form .pricegold--row {
    gap: 10px 20px;
  }
  .pricegold--right .pricegold--other .pricegold--form .pricegold--row .pricegold--col1 {
    width: calc(15.5% - 15px);
  }
  .pricegold--right .pricegold--other .pricegold--form .pricegold--row .pricegold--col2 {
    width: calc(29.5% - 15px);
  }
  .pricegold--right .pricegold--other .pricegold--form .pricegold--row .pricegold--col3 {
    width: calc(27.5% - 15px);
  }
  .pricegold--right .pricegold--other .pricegold--form .pricegold--box .price {
    font-size: 18px;
  }
  .pricegold--right .pricegold--other .pricegold--total .number .txt {
    font-size: 20px;
  }
  .pricegold--right .pricegold--other .pricegold--total .number .total--price {
    font-size: 26px;
  }
}
@media screen and (max-width: 991px) {
  .pricegold--right {
    padding: 30px 20px;
    background: linear-gradient(180deg, #FFF 0%, #F9EDCD 100%);
  }
  .pricegold--right .pricegold--other {
    max-width: 100%;
  }
  .pricegold--right .pricegold--other .pricegold--total .number .txt {
    font-size: 18px;
  }
  .pricegold--right .pricegold--other .pricegold--total .number .total--price {
    font-size: 22px;
  }
}
@media screen and (max-width: 767px) {
  .pricegold--right .pricegold--other .title {
    margin-bottom: 10px;
  }
  .pricegold--right .pricegold--other .pricegold--form .pricegold--row {
    flex-direction: column;
    align-items: start;
  }
  .pricegold--right .pricegold--other .pricegold--form .pricegold--row .pricegold--col1 {
    width: 100%;
  }
  .pricegold--right .pricegold--other .pricegold--form .pricegold--row .pricegold--col2 {
    width: 100%;
  }
  .pricegold--right .pricegold--other .pricegold--form .pricegold--row .pricegold--col3 {
    width: 100%;
  }
  .pricegold--right .pricegold--other .pricegold--form .pricegold--quantity .quantity--title {
    margin-bottom: 10px;
  }
}

.pricegold--input {
  border: 0.68px solid #D1B181;
  max-width: 128px;
}
.pricegold--input select {
  width: 100%;
  height: 41px;
  border: 0;
  padding: 0 26px 0 18px;
  color: #4F4F4F;
  font-size: 13.593px;
  font-style: normal;
  font-weight: 600;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  background-image: url("../images/ic-7.png");
  background-repeat: no-repeat;
  background-position: right 18px center;
  background-color: transparent;
}
.pricegold--input .inp-sl {
  width: 100%;
  display: flex;
}
.pricegold--input .inp-sl .num {
  width: auto;
  flex: 1;
  width: 100%;
  height: 41px;
  border: 0;
  border-radius: 0;
  box-shadow: none;
  background: transparent;
  text-align: center;
}
.pricegold--input .inp-sl span {
  width: 35px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.block-product {
  width: 100%;
  padding: 116px 10px 96px;
}
.block-product .box-title .title {
  text-align: center;
}
@media screen and (min-width: 1600px) {
  .block-product .box-title .title {
    font-size: 36px;
  }
}
.block-product .product--list .gx-lg-4 {
  --bs-gutter-x: 0;
}
@media screen and (max-width: 1600px) {
  .block-product {
    padding: 80px 10px;
  }
}
@media screen and (max-width: 991px) {
  .block-product {
    padding: 40px 10px;
  }
}

.product--panigation {
  padding-top: 80px;
}
.product--panigation .txt {
  color: #4F4F4F;
  text-align: center;
  font-size: 16px;
  font-style: normal;
  line-height: 140%;
  margin-bottom: 14px;
}
.product--panigation .box--btn {
  text-align: center;
}
.product--panigation .box--btn .btn-links {
  color: #4F4F4F;
  font-size: 20px;
  line-height: 140%;
  border-bottom: 1px solid #4F4F4F;
}
@media screen and (max-width: 991px) {
  .product--panigation {
    padding-top: 40px;
  }
  .product--panigation .box--btn .btn-links {
    font-size: 16px;
  }
}

.retrojewelry-block {
  padding: 20px 10px;
  width: 100%;
}
.retrojewelry-block .retrojewelry--list {
  width: 100%;
  max-width: 1354px;
  margin: 0 auto;
}
.retrojewelry-block .retrojewelry--list .img {
  width: 100%;
  overflow: hidden;
  padding-top: 53.18%;
  position: relative;
  margin-bottom: 80px;
}
.retrojewelry-block .retrojewelry--list .img img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
  position: absolute;
  top: 0;
  left: 0;
}
.retrojewelry-block .retrojewelry--list .box-title {
  margin: 0 auto;
  max-width: 910px;
}
.retrojewelry-block .retrojewelry--list .box-title .title {
  text-align: center;
  line-height: 129%;
}
@media screen and (min-width: 1600px) {
  .retrojewelry-block .retrojewelry--list .box-title .title {
    font-size: 36px;
  }
}

.collection-top {
  width: 100%;
  padding: 90px 10px;
}
.collection-top .box-title {
  width: 100%;
  max-width: 673px;
  margin: 0 auto;
}
.collection-top .box-title .title {
  line-height: 129%;
  margin-bottom: 20px;
  text-align: center;
}
@media screen and (min-width: 1600px) {
  .collection-top .box-title .title {
    font-size: 36px;
  }
}
.collection-top .box-title .desc {
  text-align: center;
}
@media screen and (max-width: 1600px) {
  .collection-top {
    padding: 80px 10px;
  }
}
@media screen and (max-width: 991px) {
  .collection-top {
    padding: 40px 10px;
  }
}

.collection-perfectvalue {
  width: 100%;
  padding: 90px 10px 60px;
}
.collection-perfectvalue .perfectvalue--list {
  display: flex;
  flex-wrap: wrap;
  gap: 30px 70px;
}
.collection-perfectvalue .perfectvalue--list .perfectvalue--items {
  width: calc(33.3333% - 46.6666px);
}
.collection-perfectvalue .perfectvalue--list .perfectvalue--items .box-title {
  margin: 0;
  display: flex;
  align-items: center;
  height: 100%;
}
.collection-perfectvalue .perfectvalue--list .perfectvalue--items .box-title .title {
  line-height: 129%;
  margin-bottom: 0;
  width: 100%;
  text-align: center;
}
@media screen and (min-width: 1600px) {
  .collection-perfectvalue .perfectvalue--list .perfectvalue--items .box-title .title {
    font-size: 36px;
  }
}
.collection-perfectvalue .perfectvalue--list .perfectvalue--items .img {
  width: 100%;
  overflow: hidden;
  position: relative;
  padding-top: 125%;
  margin-bottom: 35px;
}
.collection-perfectvalue .perfectvalue--list .perfectvalue--items .img img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
  position: absolute;
  top: 0;
  left: 0;
}
.collection-perfectvalue .perfectvalue--list .perfectvalue--items .desc {
  color: #4F4F4F;
  font-size: 18px;
  font-style: normal;
  line-height: 150%;
  max-width: 401px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
}
.collection-perfectvalue .perfectvalue--list .perfectvalue--items .desc p:last-child {
  margin-bottom: 0;
}
@media screen and (max-width: 1600px) {
  .collection-perfectvalue {
    padding: 80px 10px 60px;
  }
  .collection-perfectvalue .perfectvalue--list .perfectvalue--items .img {
    margin-bottom: 30px;
  }
  .collection-perfectvalue .perfectvalue--list .perfectvalue--items .desc {
    font-size: 18px;
  }
}
@media screen and (max-width: 1400px) {
  .collection-perfectvalue .perfectvalue--list {
    gap: 30px 40px;
  }
  .collection-perfectvalue .perfectvalue--list .perfectvalue--items {
    width: calc(33.3333% - 26.6666px);
  }
  .collection-perfectvalue .perfectvalue--list .perfectvalue--items .desc {
    font-size: 18px;
  }
}
@media screen and (max-width: 991px) {
  .collection-perfectvalue {
    padding: 40px 10px;
  }
  .collection-perfectvalue .perfectvalue--list {
    gap: 30px;
  }
  .collection-perfectvalue .perfectvalue--list .perfectvalue--items {
    width: calc(50% - 15px);
  }
  .collection-perfectvalue .perfectvalue--list .perfectvalue--items:nth-child(2) {
    width: 100%;
    order: -1;
  }
  .collection-perfectvalue .perfectvalue--list .perfectvalue--items .desc {
    font-size: 16px;
  }
}
@media screen and (max-width: 574px) {
  .collection-perfectvalue .perfectvalue--list .perfectvalue--items {
    width: 100%;
  }
}

.collection-productintroduction {
  width: 100%;
  padding: 64px 10px 138px;
}
.collection-productintroduction .productintroduction--list {
  position: relative;
}
.collection-productintroduction .productintroduction--list .producbox--panigation {
  width: 40%;
  max-width: 401px;
  position: absolute;
  bottom: 20px;
  left: 0;
  z-index: 5;
}
.collection-productintroduction .productintroduction--list .productintroduction--other {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 30px;
  justify-content: space-between;
}
.collection-productintroduction .productintroduction--list .productintroduction--other ._left {
  width: 40%;
  max-width: 401px;
  flex-shrink: 0;
  padding: 60px 0;
}
.collection-productintroduction .productintroduction--list .productintroduction--other ._left .box-title .title {
  line-height: 129%;
  margin-bottom: 52px;
}
@media screen and (min-width: 1600px) {
  .collection-productintroduction .productintroduction--list .productintroduction--other ._left .box-title .title {
    font-size: 36px;
  }
}
.collection-productintroduction .productintroduction--list .productintroduction--other ._left .box-title .desc {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 5;
  overflow: hidden;
}
.collection-productintroduction .productintroduction--list .productintroduction--other ._left .box-title .desc p:last-child {
  margin-bottom: 0;
}
@media screen and (min-width: 1600px) {
  .collection-productintroduction .productintroduction--list .productintroduction--other ._left .box-title .desc {
    font-size: 18px;
  }
}
.collection-productintroduction .productintroduction--list .productintroduction--other ._right {
  width: 100%;
  flex: 1;
  max-width: 1130px;
}
.collection-productintroduction .productintroduction--list .productintroduction--other ._right .img {
  width: 100%;
  overflow: hidden;
  padding-top: 59.2%;
  position: relative;
}
.collection-productintroduction .productintroduction--list .productintroduction--other ._right .img img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
  position: absolute;
  top: 0;
  left: 0;
}
.collection-productintroduction .productintroduction--list.--style--news .producbox--panigation {
  width: 40%;
  max-width: 593px;
  left: auto;
  right: 0;
}
.collection-productintroduction .productintroduction--list.--style--news .productintroduction--other {
  align-items: flex-start;
}
.collection-productintroduction .productintroduction--list.--style--news .productintroduction--other ._left {
  max-width: 593px;
  padding-top: 0;
}
.collection-productintroduction .productintroduction--list.--style--news .productintroduction--other ._left .box-title .title {
  margin-bottom: 23px;
  color: #4F4F4F;
}
.collection-productintroduction .productintroduction--list.--style--news .productintroduction--other ._left .box-title .sub_title {
  color: #D1B181;
  font-size: 16px;
  margin-bottom: 34px;
}
.collection-productintroduction .productintroduction--list.--style--news .productintroduction--other ._left .box-title .desc {
  max-width: 542px;
}
.collection-productintroduction .productintroduction--list.--style--news .productintroduction--other ._right {
  max-width: 946px;
}
.collection-productintroduction .productintroduction--list.--style--news .productintroduction--other ._right .img {
  padding-top: 59.5%;
}
@media screen and (max-width: 1600px) {
  .collection-productintroduction .productintroduction--list .productintroduction--other ._left .box-title .title {
    margin-bottom: 40px;
  }
  .collection-productintroduction .productintroduction--list.--style--news .productintroduction--other ._left .box-title .title {
    margin-bottom: 20px;
  }
  .collection-productintroduction .productintroduction--list.--style--news .productintroduction--other ._left .box-title .sub_title {
    margin-bottom: 30px;
  }
}
@media screen and (max-width: 991px) {
  .collection-productintroduction {
    padding: 40px 10px;
  }
  .collection-productintroduction .productintroduction--list .productintroduction--other ._left .box-title .title {
    margin-bottom: 30px;
  }
}
@media screen and (max-width: 767px) {
  .collection-productintroduction {
    padding: 40px 10px;
  }
  .collection-productintroduction .productintroduction--list .producbox--panigation {
    position: unset;
    width: 100%;
    max-width: 100%;
  }
  .collection-productintroduction .productintroduction--list .productintroduction--other ._right {
    flex: auto;
    order: -1;
  }
  .collection-productintroduction .productintroduction--list .productintroduction--other ._left {
    padding: 0;
    max-width: 100%;
    width: 100%;
  }
  .collection-productintroduction .productintroduction--list .productintroduction--other ._left .box-title .title {
    margin-bottom: 20px;
  }
  .collection-productintroduction .productintroduction--list.--style--news .producbox--panigation {
    width: 100%;
    max-width: 100%;
    position: unset;
  }
}

.collection-prodsp {
  width: 100%;
  padding: 60px 10px;
}
.collection-prodsp.product-similar {
  padding: 90px 10px;
}
.collection-prodsp.product-similar .box-title {
  margin-bottom: 80px;
}
.collection-prodsp.product-similar .box-title .title {
  line-height: 129%;
  margin-bottom: 0;
  width: 100%;
  text-align: center;
}
@media screen and (min-width: 1600px) {
  .collection-prodsp.product-similar .box-title .title {
    font-size: 36px;
  }
}
@media screen and (max-width: 991px) {
  .collection-prodsp.product-similar {
    padding: 40px 10px;
  }
  .collection-prodsp.product-similar .box-title {
    margin-bottom: 40px;
  }
}

.prodsp--list {
  position: relative;
}
.prodsp--list .swiper {
  margin-bottom: 40px;
}
.prodsp--list .swiper .swiper-slide {
  height: auto;
}
.prodsp--list .box--btn {
  padding-top: 40px;
  text-align: center;
}

.collection-video {
  padding: 180px 10px 80px;
  width: 100%;
}
@media screen and (max-width: 1600px) {
  .collection-video {
    padding: 120px 10px 80px;
  }
}
@media screen and (max-width: 991px) {
  .collection-video {
    padding: 40px 10px;
  }
}

.video--other {
  width: 100%;
  position: relative;
  overflow: hidden;
  padding-top: 56.22%;
}
.video--other iframe,
.video--other video {
  width: 100% !important;
  height: 100% !important;
  -o-object-fit: cover;
     object-fit: cover;
  position: absolute;
  top: 0;
  left: 0;
}
.video--other .img {
  width: 100%;
  padding-top: 56.22%;
  position: absolute;
  overflow: hidden;
  top: 0;
  left: 0;
  z-index: 2;
}
.video--other .img img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
  position: absolute;
  top: 0;
  left: 0;
}
.video--other .desc {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  width: 100%;
  height: 100%;
  align-items: center;
  justify-content: center;
  z-index: 45;
}
.video--other .desc span {
  color: #EDD0A7;
  text-align: center;
  font-size: 36px;
  font-style: normal;
  font-weight: 600;
  line-height: 129%;
  text-transform: capitalize;
}
@media screen and (max-width: 1600px) {
  .video--other .desc span {
    font-size: 32px;
  }
}
@media screen and (max-width: 1400px) {
  .video--other .desc span {
    font-size: 26px;
  }
}
@media screen and (max-width: 991px) {
  .video--other .desc span {
    font-size: 22px;
  }
}

.product-tabslist {
  width: 100%;
  padding: 88px 10px 100px;
}
@media screen and (max-width: 991px) {
  .product-tabslist {
    padding: 40px 10px;
  }
}

.product-tabs {
  width: 100%;
  border-top: 0.5px solid #4F4F4F;
  border-bottom: 0.5px solid #4F4F4F;
  display: flex;
  gap: 10px 20px;
  justify-content: space-between;
  margin-bottom: 44px;
}
.product-tabs .product-tabs--left {
  width: calc(100% - 225px);
  display: flex;
  align-items: center;
  gap: 24px;
}
.product-tabs .product-tabs--left .totla--page {
  color: rgba(79, 79, 79, 0.7);
  font-size: 16px;
  flex-shrink: 0;
}
.product-tabs .product-tabs--left .--line {
  width: 1px;
  height: 24px;
  background: rgba(79, 79, 79, 0.7);
}
.product-tabs .product-tabs--left .product-tabs--menu {
  width: 100%;
  flex: 1;
  display: flex;
  gap: 40px;
}
.product-tabs .product-tabs--left .product-tabs--menu .items {
  position: relative;
  padding: 20px 0;
}
.product-tabs .product-tabs--left .product-tabs--menu .items a {
  white-space: nowrap;
  color: #4F4F4F;
  text-align: center;
  font-size: 16px;
  font-style: normal;
  text-transform: uppercase;
  flex: 1;
  width: 100%;
  display: flex;
  align-items: center;
  gap: 19px;
}
.product-tabs .product-tabs--left .product-tabs--menu .items a.active {
  color: #7C0410;
}
.product-tabs .product-tabs--left .product-tabs--menu .items .items--tabsbody {
  position: absolute;
  top: 100%;
  left: 0;
  width: -moz-max-content;
  width: max-content;
  min-width: 100%;
  flex-direction: column;
  padding: 23px 15px;
  display: none;
  flex-wrap: wrap;
  gap: 16px;
  background-color: #fff;
  visibility: hidden;
  opacity: 0;
  border: 1px solid #9E9E9E;
  z-index: 4;
}
.product-tabs .product-tabs--left .product-tabs--menu .items .items--tabsbody > * {
  font-size: 14px;
  text-align: left;
}
.product-tabs .product-tabs--left .product-tabs--menu .items:hover .items--tabsbody {
  opacity: 1;
  visibility: visible;
  display: flex;
}
.product-tabs .filter-select {
  width: 205px;
  flex-shrink: 0;
  gap: 10px;
  display: flex;
  align-items: center;
}
.product-tabs .filter-select .txt {
  color: #4F4F4F;
  font-size: 10px;
  font-style: normal;
  font-weight: 450;
  line-height: 130%;
  text-transform: uppercase;
  white-space: nowrap;
  flex-shrink: 0;
}
.product-tabs .filter-select select {
  height: 24px;
  flex: 1;
  padding-right: 14px;
  background-image: url("../images/ic-8.png");
  background-repeat: no-repeat;
  background-position: right center;
  border: 0;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  color: #4F4F4F;
  font-size: 12px;
  font-style: normal;
  text-transform: uppercase;
  line-height: normal;
  white-space: nowrap;
  width: 100%;
  text-overflow: ellipsis;
}
@media screen and (max-width: 1600px) {
  .product-tabs .product-tabs--left {
    gap: 10px;
  }
  .product-tabs .product-tabs--left .totla--page {
    font-size: 14px;
  }
  .product-tabs .product-tabs--left .--line {
    height: 12px;
  }
  .product-tabs .product-tabs--left .product-tabs--menu {
    gap: 10px;
  }
  .product-tabs .product-tabs--left .product-tabs--menu .items a {
    font-size: 14px;
    gap: 5px;
  }
  .product-tabs .product-tabs--left .product-tabs--menu .items .items--tabsbody > * {
    font-size: 14px;
  }
  .product-tabs .filter-select .txt {
    font-size: 10px;
  }
  .product-tabs .filter-select select {
    font-size: 12px;
  }
}
@media screen and (max-width: 1200px) {
  .product-tabs .product-tabs--left {
    gap: 10px;
  }
  .product-tabs .product-tabs--left .totla--page {
    font-size: 12px;
  }
  .product-tabs .product-tabs--left .--line {
    height: 12px;
  }
  .product-tabs .product-tabs--left .product-tabs--menu {
    gap: 10px;
  }
  .product-tabs .product-tabs--left .product-tabs--menu .items a {
    font-size: 12px;
  }
  .product-tabs .product-tabs--left .product-tabs--menu .items .items--tabsbody > * {
    font-size: 14px;
  }
  .product-tabs .filter-select {
    width: 125px;
  }
  .product-tabs .filter-select .txt {
    display: none;
  }
  .product-tabs .filter-select select {
    font-size: 12px;
  }
}
@media screen and (max-width: 991px) {
  .product-tabs .product-tabs--left .product-tabs--menu {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    overflow: scroll;
    background: #fff;
    padding: 20px;
    z-index: 1001;
  }
  .product-tabs .product-tabs--left .product-tabs--menu .items {
    padding: 0;
    margin-bottom: 10px;
  }
  .product-tabs .product-tabs--left .product-tabs--menu .items:last-child {
    margin-bottom: 0;
  }
  .product-tabs .product-tabs--left .product-tabs--menu .items .items--tabsbody {
    position: unset;
    box-shadow: none;
    display: flex;
    opacity: 1;
    visibility: visible;
    padding: 10px 0 0;
    border: 0;
    flex-direction: row;
  }
  .product-tabs .product-tabs--left .product-tabs--menu .items .items--tabsbody > * {
    width: calc(50% - 8px);
  }
  .product-tabs .product-tabs--left .product-tabs--menu.active {
    display: block;
  }
  .product-tabs .product-tabs--left .totla--page,
  .product-tabs .product-tabs--left .--line {
    display: none;
  }
}
@media screen and (max-width: 574px) {
  .product-tabs .product-tabs--left .product-tabs--menu .items .items--tabsbody > * {
    width: 100%;
  }
}

.product--all {
  display: flex;
  flex-wrap: wrap;
  gap: 0;
}
.product--all .product--col {
  width: 25%;
}
.product--all .product--col:nth-child(9n+7) {
  width: 50%;
}
.product--all .product--col:nth-child(9n+7) .product--items .img {
  padding-top: 45.84%;
}
@media screen and (max-width: 1200px) {
  .product--all .product--col:nth-child(9n+7) .product--items .img {
    padding-top: 44.84%;
  }
}
@media screen and (max-width: 991px) {
  .product--all .product--col {
    width: 33.3333%;
  }
  .product--all .product--col:nth-child(9n+7) {
    width: 66.6667%;
  }
  .product--all .product--col:nth-child(9n+7) .product--items .img {
    padding-top: 44.84%;
  }
}
@media screen and (max-width: 574px) {
  .product--all .product--col {
    width: 50%;
  }
  .product--all .product--col:nth-child(9n+7) {
    width: 100%;
  }
}

.btn-closefillter--mb {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 5;
}
.btn-closefillter--mb svg {
  width: 20px;
  height: auto;
}

.btn-opfillter--mb {
  display: none;
  align-items: center;
  gap: 12px;
  color: #4F4F4F;
  font-size: 12px;
  font-style: normal;
  text-transform: uppercase;
  white-space: nowrap;
}
.btn-opfillter--mb ._icon {
  flex-shrink: 0;
}
@media screen and (max-width: 991px) {
  .btn-opfillter--mb {
    display: flex;
  }
}

.items-checkbox {
  width: 100%;
}
.items-checkbox label {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 16px;
  color: #4F4F4F;
  text-align: center;
  font-size: 16px;
  font-style: normal;
  text-transform: uppercase;
}
.items-checkbox label span {
  width: 24px;
  height: 24px;
  border: 1px solid rgba(79, 79, 79, 0.5);
  position: relative;
}
.items-checkbox label span::after {
  content: "";
  display: block;
  width: 12px;
  height: 12px;
  position: absolute;
  background: rgba(79, 79, 79, 0.7);
  opacity: 0;
  visibility: hidden;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  margin: auto;
}
.items-checkbox input:checked ~ label span::after {
  opacity: 1;
  visibility: visible;
}
@media screen and (max-width: 1600px) {
  .items-checkbox label {
    font-size: 12px;
  }
}
@media screen and (max-width: 991px) {
  .items-checkbox label {
    font-size: 12px;
  }
}

.block-weddingjewelry {
  width: 100%;
  padding: 60px 0 0;
  position: relative;
  min-height: 983px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.block-weddingjewelry .weddingjewelry--other {
  width: 100%;
  max-width: 695px;
}
.block-weddingjewelry .weddingjewelry--other .weddingjewelry--logo {
  width: 100%;
  text-align: center;
  margin-bottom: 35px;
}
.block-weddingjewelry .weddingjewelry--other .weddingjewelry--logo img {
  max-width: 100%;
  max-width: 285px;
}
.block-weddingjewelry .weddingjewelry--other .desc {
  color: #6C6C6C;
  text-align: center;
  font-size: 20px;
  font-style: normal;
  line-height: 140%;
  max-width: 421px;
  margin: 0 auto 33px;
}
.block-weddingjewelry .weddingjewelry--other .desc p:last-child {
  margin-bottom: 0;
}
.block-weddingjewelry .weddingjewelry--other .box--btn {
  text-align: center;
}
.block-weddingjewelry .weddingjewelry--other .box--btn .btn-links {
  color: #CD8B30;
  border-bottom: 1px solid #CD8B30;
}
.block-weddingjewelry .weddingjewelry--img {
  width: 50%;
  height: 100%;
  overflow: hidden;
  position: absolute;
  top: 0;
  right: 0;
}
.block-weddingjewelry .weddingjewelry--img img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
}
.block-weddingjewelry.--style2 {
  background: var(--Main-Red, #7C0410);
}
.block-weddingjewelry.--style2 .weddingjewelry--logo {
  margin-bottom: 100px;
}
.block-weddingjewelry.--style2 .weddingjewelry--other .desc {
  color: #fff;
  font-size: 16px;
  max-width: 435px;
  margin: 0 auto 45px;
}
@media screen and (max-width: 1400px) {
  .block-weddingjewelry {
    min-height: auto;
    padding: 60px 0;
  }
}
@media screen and (max-width: 991px) {
  .block-weddingjewelry {
    padding: 40px 0 0;
  }
  .block-weddingjewelry .weddingjewelry--other {
    margin-bottom: 30px;
    padding: 0 10px;
  }
  .block-weddingjewelry .weddingjewelry--other .desc {
    font-size: 18px;
  }
  .block-weddingjewelry .weddingjewelry--img {
    width: 100%;
    height: auto;
    position: unset;
  }
}

.collection-all {
  width: 100%;
  padding: 0 10px 88px;
}
.collection-all .box--btn {
  padding-top: 105px;
  text-align: center;
}
.collection-all .box--btn .btn-links {
  color: #4F4F4F;
  border-bottom: 1px solid #4F4F4F;
}
@media screen and (max-width: 991px) {
  .collection-all {
    padding-bottom: 40px;
  }
  .collection-all .box--btn {
    padding-top: 40px;
  }
}

.collection--items {
  width: 100%;
  height: 100%;
  display: block;
}
.collection--items .img {
  background: #FFFCF3;
  padding: 56px 40px 60px;
  display: block;
  margin-bottom: 20px;
}
.collection--items .img span {
  display: block;
  width: 100%;
  padding-top: 133.3333%;
  overflow: hidden;
  position: relative;
}
.collection--items .img span img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
  position: absolute;
  top: 0;
  left: 0;
  transition: all 0.3s;
}
.collection--items .title {
  width: 100%;
  color: #4F4F4F;
  font-size: 24px;
  font-style: normal;
  line-height: 150%;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
}
.collection--items .title a {
  color: inherit;
}
.collection--items:hover .img span img {
  transform: scale(1.2);
}
.collection--items:hover .title {
  color: #7C0410;
}
@media screen and (max-width: 1600px) {
  .collection--items .img {
    padding: 56px 40px 60px;
  }
  .collection--items .title {
    font-size: 22px;
  }
}
@media screen and (max-width: 1200px) {
  .collection--items .img {
    padding: 50px 40px 60px;
  }
  .collection--items .title {
    font-size: 20px;
  }
}
@media screen and (max-width: 991px) {
  .collection--items .img {
    padding: 40px 30px 30px;
  }
  .collection--items .title {
    font-size: 18px;
  }
}

.collection-box {
  width: 100%;
}
.collection-box .gx-lg-4 {
  --bs-gutter-x: 30px;
  gap: 30px 0;
}

.fashion-latestcollection {
  width: 100%;
  padding: 96px 10px 39px;
}
.fashion-latestcollection .box-title {
  margin-bottom: 144px;
}
.fashion-latestcollection .box-title .title {
  color: #B5751D;
  margin: 0 0 40px;
  text-align: center;
}
.fashion-latestcollection .box-title .desc {
  max-width: 677px;
  margin: 0 auto;
  text-align: center;
}
.fashion-latestcollection .store--list ._left {
  max-width: 514px;
}
.fashion-latestcollection .store--list .store--box {
  padding-top: 10px;
}
.fashion-latestcollection .latestcollection--springday {
  width: 100%;
  background: linear-gradient(180deg, #7C0410 0%, #3B000C 100%);
}
.fashion-latestcollection .latestcollection--springday .title {
  text-align: center;
  font-size: 45px;
  font-style: normal;
  line-height: 150%;
  letter-spacing: 18px;
  text-transform: capitalize;
  color: #EDD0A7;
  background: var(--Gradient-Gold---Light, linear-gradient(180deg, #EDD0A7 0%, #FFF 100%));
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  padding: 20px 0;
  margin: 0 0 20px;
}
.fashion-latestcollection .latestcollection--springday .latestcollection--body {
  padding: 0 20px 40px;
}
.fashion-latestcollection .latestcollection--springday .latestcollection--body .img {
  display: block;
  margin: 0 auto 43px;
  width: 56%;
}
.fashion-latestcollection .latestcollection--springday .latestcollection--body .img img {
  width: 100% !important;
  height: auto !important;
}
.fashion-latestcollection .latestcollection--springday .latestcollection--body .box--btn {
  text-align: center;
}
.fashion-latestcollection .latestcollection--springday .latestcollection--body .box--btn .btn-links {
  color: #FFF;
  border-bottom: 1px solid #fff;
}
.fashion-latestcollection.fashion-latestcollection-mh .box-title .desc {
  max-width: 421px;
}
.fashion-latestcollection.fashion-latestcollection-mh .latestcollection--springday {
  background: linear-gradient(180deg, #CB892E 0%, #F4B85D 100%);
}
.fashion-latestcollection.fashion-latestcollection-mh .latestcollection--springday .title {
  color: #FBDEB1;
}
.fashion-latestcollection.fashion-latestcollection-mh .latestcollection--springday .latestcollection--body {
  padding: 0 20px 40px;
}
.fashion-latestcollection.fashion-latestcollection-mh .latestcollection--springday .latestcollection--body .img {
  display: block;
  margin: 0 auto 43px;
  width: 56%;
}
.fashion-latestcollection.fashion-latestcollection-mh .latestcollection--springday .latestcollection--body .img img {
  width: 100% !important;
  height: auto !important;
}
.fashion-latestcollection.fashion-latestcollection-mh .latestcollection--springday .latestcollection--body .box--btn {
  text-align: center;
}
.fashion-latestcollection.fashion-latestcollection-mh .latestcollection--springday .latestcollection--body .box--btn .btn-links {
  color: #4F4F4F;
  border-bottom: 1px solid #4F4F4F;
}
@media screen and (max-width: 1200px) {
  .fashion-latestcollection .box-title {
    margin-bottom: 40px;
  }
  .fashion-latestcollection .box-title .title {
    margin: 0 0 30px;
  }
  .fashion-latestcollection .store--list {
    padding: 0;
  }
  .fashion-latestcollection .store--list ._left {
    max-width: 100%;
  }
  .fashion-latestcollection .latestcollection--springday .title {
    font-size: 36px;
  }
  .fashion-latestcollection .latestcollection--springday .latestcollection--body {
    padding: 0 20px 40px;
  }
  .fashion-latestcollection .latestcollection--springday .latestcollection--body .img {
    display: block;
    margin: 0 auto 30px;
    width: 100%;
  }
}
@media screen and (max-width: 991px) {
  .fashion-latestcollection {
    padding: 40px 10px;
  }
}
@media screen and (max-width: 767px) {
  .fashion-latestcollection.fashion-latestcollection-mh .latestcollection--springday .latestcollection--body .img {
    width: 75%;
  }
}

.block-product-detail {
  padding: 50px 0px 60px;
  width: 100%;
  overflow: hidden;
  position: relative;
}
@media screen and (max-width: 991px) {
  .block-product-detail {
    padding: 40px 0;
  }
}

.detail-prdt {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  position: relative;
  max-width: 1360px;
  margin: 0 auto 108px;
  gap: 30px;
  justify-content: space-between;
}
.detail-prdt .box-img-detail {
  width: calc(57% - 15px);
  height: 480px;
  display: flex;
  position: relative;
  justify-content: space-between;
  margin-bottom: 50px;
  max-width: 651px;
}
.detail-prdt .box-img-detail .img-detail-prdt {
  width: 100%;
  height: 100%;
  background: #F6F6F6;
  border: 1px solid #f6f6f6;
}
.detail-prdt .box-img-detail .img-detail-prdt img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
}
.detail-prdt .box-img-detail .swiper {
  width: 100%;
  height: 100%;
}
.detail-prdt .box-img-detail .swiper-button-next::after,
.detail-prdt .box-img-detail .swiper-rtl .swiper-button-prev::after {
  display: none;
}
.detail-prdt .box-img-detail .swiper-button-prev::after,
.detail-prdt .box-img-detail .swiper-rtl .swiper-button-next::after {
  display: none;
}
.detail-prdt .box-img-detail .swiper-slide {
  text-align: center;
  font-size: 18px;
  background: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
}
.detail-prdt .box-img-detail .swiper-slide img {
  display: block;
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.detail-prdt .box-img-detail .swiper {
  width: 100%;
  height: 700px;
  margin-left: auto;
  margin-right: auto;
}
.detail-prdt .box-img-detail .swiper-slide {
  background-size: cover;
  background-position: center;
}
.detail-prdt .box-img-detail .mySwiper2 {
  height: 100%;
  width: calc(100% - 171px);
  margin-left: auto;
  margin-right: 0px;
}
.detail-prdt .box-img-detail .mySwiper {
  height: 100%;
  width: 113px;
  margin: 0px;
  box-sizing: border-box;
  padding: 0px;
  position: relative;
}
.detail-prdt .box-img-detail .mySwiper .swiper-slide {
  width: 100%;
  height: 100%;
  overflow: hidden;
  opacity: 0.9;
  border: 1px solid #f6f6f6;
  background: #F6F6F6;
  cursor: pointer;
}
.detail-prdt .box-img-detail .mySwiper .swiper-slide-thumb-active {
  opacity: 1;
  border: 2px solid #DEDEDE;
}
.detail-prdt .box-img-detail .swiper-slide img {
  display: block;
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.detail-prdt .box-img-detail .custom-nav-next {
  position: absolute;
  bottom: 0;
  width: 36px;
  height: 36px;
  margin: 0px;
  padding: 0px;
  left: 40px;
  bottom: -17px;
  margin-left: 0px;
  display: flex;
  align-items: center;
  justify-content: center;
  top: auto;
  right: auto;
  z-index: 10;
  border: 1px solid #DEDEDE;
  border-radius: 50%;
  transform: rotate(90deg);
  background-color: #fff;
}
.detail-prdt .box-img-detail .custom-nav-prev {
  position: absolute;
  bottom: 0;
  width: 36px;
  height: 36px;
  margin: 0px;
  padding: 0px;
  left: 40px;
  top: -17px;
  margin-left: 0px;
  display: flex;
  align-items: center;
  justify-content: center;
  bottom: auto;
  right: auto;
  z-index: 10;
  border: 1px solid #DEDEDE;
  border-radius: 50%;
  transform: rotate(90deg);
  background-color: #fff;
}
@media (max-width: 1279px) {
  .detail-prdt .box-img-detail {
    flex-direction: column;
    justify-content: space-between;
  }
  .detail-prdt .box-img-detail .mySwiper {
    width: calc(100% - 30px);
    margin-left: 15px;
    height: 90px;
    order: 2;
    margin-top: 0px;
  }
  .detail-prdt .box-img-detail .mySwiper2 {
    width: 100%;
    height: 430px;
    order: 1;
    margin-bottom: 30px;
  }
  .detail-prdt .box-img-detail .custom-nav-prev {
    top: auto;
    left: 0;
    right: auto;
    bottom: 26px;
    transform: rotate(0);
  }
  .detail-prdt .box-img-detail .custom-nav-next {
    left: auto;
    right: 0;
    transform: rotate(0);
    top: auto;
    bottom: 26px;
  }
}
@media (max-width: 992px) {
  .detail-prdt .box-img-detail {
    height: 500px;
  }
  .detail-prdt .box-img-detail .mySwiper {
    height: 90px;
  }
  .detail-prdt .box-img-detail .mySwiper2 {
    height: 390px;
    margin-bottom: 20px;
  }
}
.detail-prdt .info-detail {
  width: calc(43% - 15px);
  padding: 0 0 20px 102px;
  border-left: 1px solid #D5D5D5;
  display: flex;
  flex-direction: column;
  position: sticky;
  max-width: 527px;
}
.detail-prdt .info-detail .name {
  color: #4F4F4F;
  font-size: 23.867px;
  font-style: normal;
  line-height: 142.455%;
  text-transform: capitalize;
  margin: 0 0 10px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}
.detail-prdt .info-detail .sub_name {
  color: #AE8751;
  font-size: 16px;
  line-height: 130%;
  margin-bottom: 20px;
}
.detail-prdt .info-detail .prdt_price {
  color: #AE8751;
  font-size: 24px;
  line-height: 116.667%;
  margin-bottom: 17px;
}
.detail-prdt .info-detail .product--code {
  color: #757575;
  font-size: 16px;
  line-height: 140%;
  margin-bottom: 50px;
}
.detail-prdt .info-detail .row-choose-opt {
  width: 100%;
  margin-bottom: 17px;
}
.detail-prdt .info-detail .choose-color {
  width: 100%;
  display: flex;
  align-items: center;
  margin-bottom: 26px;
}
.detail-prdt .info-detail .choose-color .lbl {
  width: 120px;
  font-family: "SVN-Gilroy";
  font-size: 14px;
  font-weight: 400;
  line-height: 14.7px;
  text-align: left;
  color: #6A6769;
}
.detail-prdt .info-detail .choose-color .list {
  display: flex;
  flex-wrap: wrap;
  width: calc(100% - 120px);
  gap: 18px;
}
.detail-prdt .info-detail .choose-color .list .form-check {
  display: block;
  width: 36px;
  height: 36px;
  margin: 0px;
  cursor: pointer;
}
.detail-prdt .info-detail .choose-color .list .form-check-input {
  display: none;
}
.detail-prdt .info-detail .choose-color .list .form-check-input:checked ~ .form-check-checkmark {
  border: 1px solid transparent;
}
.detail-prdt .info-detail .choose-color .list .form-check-input:checked ~ .form-check-checkmark::before {
  border: none;
  display: block;
}
.detail-prdt .info-detail .choose-color .list .form-check-input:checked ~ .form-check-checkmark::after {
  content: "";
  position: absolute;
  top: -1px;
  left: -1px;
  height: 37px;
  width: 37px;
  border: 3px solid #DEDEDE;
  border-radius: 50%;
}
.detail-prdt .info-detail .choose-color .list .form-check-input:checked ~ .txt {
  color: var(--grey);
}
.detail-prdt .info-detail .choose-color .list .form-check-checkmark {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border: 1px solid #DEDEDE;
  top: 0px;
  display: block;
  position: absolute;
  left: 0px;
}
.detail-prdt .info-detail .choose-color .form-check-checkmark {
  border: 1px solid #ccc;
}
.detail-prdt .info-detail .choose-size {
  width: 100%;
  display: flex;
  align-items: center;
  margin-bottom: 26px;
}
.detail-prdt .info-detail .choose-size .lbl {
  width: 55px;
  color: #757575;
  font-size: 16px;
  font-style: normal;
  line-height: 140%;
}
.detail-prdt .info-detail .choose-size .list {
  display: flex;
  flex-wrap: wrap;
  max-width: calc(100% - 55px);
  margin-left: auto;
  gap: 10px 20px;
  justify-content: flex-start;
}
.detail-prdt .info-detail .choose-size .list .form-check {
  display: block;
  width: auto;
  height: 35px;
  margin: 0px;
  padding: 0px;
  cursor: pointer;
}
.detail-prdt .info-detail .choose-size .list .form-check-input {
  display: none;
}
.detail-prdt .info-detail .choose-size .list .form-check-input:checked ~ .form-check-checkmark {
  border: 1px solid #AE8751;
}
.detail-prdt .info-detail .choose-size .list .form-check-input:checked ~ .form-check-checkmark::before {
  border: none;
  display: block;
}
.detail-prdt .info-detail .choose-size .list .form-check-input:checked ~ .form-check-checkmark::after {
  display: none;
}
.detail-prdt .info-detail .choose-size .list .form-check-input:checked ~ .txt {
  color: var(--grey);
}
.detail-prdt .info-detail .choose-size .list .form-check-checkmark {
  width: auto;
  min-width: 75px;
  text-align: center;
  line-height: 35px;
  height: 35px;
  border-radius: 0px;
  border: 1px solid #EDD0A7;
  top: 0px;
  display: block;
  position: relative;
  left: 0px;
  white-space: nowrap;
  padding: 0px 10px;
  color: #4F4F4F;
  font-size: 12.624px;
  font-weight: 600;
}
.detail-prdt .info-detail .choose-size .form-check-checkmark {
  border: 1px solid #ccc;
}
.detail-prdt .info-detail .row-qty-btn-act {
  width: 100%;
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
  margin-bottom: 20px;
}
.detail-prdt .info-detail .row-qty-btn-act .pricegold--input {
  flex-shrink: 0;
}
.detail-prdt .info-detail .row-qty-btn-act .pricegold--input .inp-sl .num {
  height: 56px;
}
.detail-prdt .info-detail .prddetail-sp {
  width: 100%;
  padding-top: 30px;
  display: flex;
  flex-direction: column;
  gap: 25px;
  align-items: center;
}
.detail-prdt .info-detail .prddetail-sp .select--size {
  color: #4F4F4F;
  text-align: center;
  font-size: 12.797px;
  font-style: normal;
  line-height: 156.288%;
  border-bottom: 1px solid #4F4F4F;
  width: -moz-max-content;
  width: max-content;
}
.detail-prdt .info-detail .prddetail-sp .sp--phone {
  color: #4F4F4F;
  text-align: center;
  font-size: 12.188px;
  font-style: normal;
  line-height: 164.103%;
}
.detail-prdt .info-detail .prddetail-sp .sp--phone a {
  color: inherit;
  display: inline-block;
  border-bottom: 1px solid #4F4F4F;
}
.detail-prdt .info-detail .storesystem--btn {
  max-width: 100%;
  padding: 12px;
}
.detail-prdt .info-detail .btn-addtocart {
  color: #4F4F4F;
  text-align: center;
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: 56px;
  height: 56px;
  flex: 1;
  width: 100%;
  border: 1px solid #EDD0A7;
}
.detail-prdt .info-detail .btn-addtocart:hover {
  color: #7C0410;
  background: #EDD0A7;
}
@media (max-width: 1400px) {
  .detail-prdt .info-detail {
    padding-left: 30px;
  }
  .detail-prdt .info-detail .name {
    font-size: 24px;
    line-height: 34px;
  }
  .detail-prdt .info-detail .prdt_price,
  .detail-prdt .info-detail .desc-detail {
    padding-bottom: 20px;
    margin-bottom: 20px;
  }
  .detail-prdt .info-detail .row-choose-opt {
    margin-bottom: 20px;
    padding-bottom: 0px;
  }
  .detail-prdt .info-detail .row-choose-opt .choose-color,
  .detail-prdt .info-detail .row-choose-opt .choose-size {
    margin-bottom: 15px;
  }
  .detail-prdt .info-detail .row-choose-opt .choose-color .lbl,
  .detail-prdt .info-detail .row-choose-opt .choose-size .lbl {
    width: 100px;
  }
  .detail-prdt .info-detail .row-choose-opt .choose-color .list,
  .detail-prdt .info-detail .row-choose-opt .choose-size .list {
    gap: 10px;
  }
}
@media (max-width: 1279px) {
  .detail-prdt .box-img-detail {
    height: auto;
    margin-bottom: 0;
  }
  .detail-prdt .info-detail {
    margin-bottom: 40px;
  }
  .detail-prdt .info-detail .name {
    font-size: 20px;
    line-height: 30px;
  }
  .detail-prdt .info-detail .row-qty-btn-act {
    flex-wrap: wrap;
    justify-content: space-between;
  }
  .detail-prdt .info-detail .row-qty-btn-act .btn-addtocart {
    font-size: 18px;
  }
}
@media (max-width: 992px) {
  .detail-prdt {
    margin-bottom: 60px;
  }
  .detail-prdt .info-detail {
    width: 100%;
    max-width: 100%;
    border: 0;
    padding: 0;
  }
  .detail-prdt .info-detail .prdt_price {
    font-size: 20px;
  }
  .detail-prdt .info-detail .name {
    font-size: 18px;
  }
  .detail-prdt .box-img-detail {
    width: 100%;
    max-width: 100%;
  }
}
@media (max-width: 767px) {
  .detail-prdt {
    padding-top: 0px;
  }
  .detail-prdt .box-img-detail {
    width: 100%;
    margin-bottom: 30px;
    padding: 0px;
  }
  .detail-prdt .info-detail {
    width: 100%;
    padding: 0px;
    max-width: 100%;
    margin-bottom: 30px;
  }
}
@media (max-width: 570px) {
  .detail-prdt {
    padding-top: 0px;
  }
  .detail-prdt .box-img-detail {
    width: 100%;
    margin-bottom: 30px;
    padding: 0px;
    height: 400px;
  }
  .detail-prdt .box-img-detail .mySwiper {
    height: 80px;
  }
  .detail-prdt .box-img-detail .mySwiper {
    height: 80px;
  }
  .detail-prdt .box-img-detail .mySwiper2 {
    height: 300px;
  }
  .detail-prdt .info-detail {
    width: 100%;
    padding: 0px;
    max-width: 100%;
  }
  .detail-prdt .info-detail .row-qty-btn-act {
    flex-wrap: wrap;
    flex-direction: column;
  }
  .detail-prdt .info-detail .row-qty-btn-act .pricegold--input {
    max-width: 100%;
  }
}
.detail--content {
  max-width: 1360px;
  margin: 0 auto;
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: 50px;
}
.detail--content ._items {
  width: 100%;
}
.detail--content ._items .btn {
  width: 100%;
  color: #4F4F4F;
  font-size: 23.867px;
  font-style: normal;
  line-height: 142.455%;
  text-transform: capitalize;
  text-align: left;
  padding: 0 0 12px;
  border-radius: 0;
  border-bottom: 1px solid #D5D5D5;
  padding-right: 30px;
  position: relative;
}
.detail--content ._items .btn span {
  display: block;
  width: -moz-max-content;
  width: max-content;
  height: -moz-max-content;
  height: max-content;
  position: absolute;
  top: 0;
  right: 0;
  transition: all 0.3s;
}
.detail--content ._items .btn[aria-expanded=true] span {
  transform: rotate(-45deg);
}
.detail--content ._items .btn:focus, .detail--content ._items .btn:focus-visible {
  box-shadow: none;
}
.detail--content ._items .detail--child {
  padding: 62px 0 0;
}
.detail--content ._items .detail--child .detail--other {
  width: 100%;
  max-width: 1074px;
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
  align-items: flex-start;
  margin: 0 auto;
  justify-content: space-between;
}
.detail--content ._items .detail--child .detail--other .detail--style1 {
  width: -moz-max-content;
  width: max-content;
}
.detail--content ._items .detail--child .detail--other .detail--style1 > * {
  white-space: nowrap;
  margin: 0;
}
.detail--content ._items .detail--child .detail--other .detail--style2 {
  width: 100%;
  color: #4F4F4F;
  text-align: center;
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}
.detail--content ._items .detail--child .detail--style3 {
  background: #FBF6ED;
  padding: 45px 80px 3px;
}
.detail--content ._items .detail--child .detail--style3 .img-detail-logo {
  text-align: center;
  margin-bottom: 135px;
}
.detail--content ._items .detail--child .detail--style3 .img-detail-title {
  position: relative;
  text-align: center;
}
.detail--content ._items .detail--child .detail--style3 .img-detail-title span {
  display: inline-block;
  padding: 0 20px;
  background-color: #FBF6ED;
  color: #7C0410;
  text-align: center;
  font-size: 28px;
  line-height: normal;
  text-transform: uppercase;
  position: relative;
  z-index: 5;
}
.detail--content ._items .detail--child .detail--style3 .img-detail-title::after {
  width: 100%;
  height: 1px;
  background-color: #D5D5D5;
  content: "";
  display: block;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  margin: auto;
  position: absolute;
}
.detail--content ._items .detail--child .detail--style3 .detail--child---list {
  display: flex;
  flex-wrap: wrap;
  gap: 0 30px;
  justify-content: space-between;
}
.detail--content ._items .detail--child .detail--style3 .detail--child---list .detail--child---col {
  width: calc(33.3333% - 20px);
  max-width: 240px;
  padding: 73px 0 95px;
}
.detail--content ._items .detail--child .detail--style3 .detail--child---list .detail--child---col .img {
  text-align: center;
  margin-bottom: 19px;
}
.detail--content ._items .detail--child .detail--style3 .detail--child---list .detail--child---col .img img {
  max-width: 90px !important;
  height: auto !important;
}
.detail--content ._items .detail--child .detail--style3 .detail--child---list .detail--child---col .desc {
  color: #4F4F4F;
  text-align: center;
  font-size: 20px;
  line-height: normal;
}
.detail--content ._items .detail--child .detail--style3 .detail--child---list .__line {
  width: 100%;
  height: 1px;
  background: #D5D5D5;
}
@media screen and (max-width: 1600px) {
  .detail--content ._items .btn {
    font-size: 20px;
  }
  .detail--content ._items .detail--child {
    padding: 40px 0 0;
  }
  .detail--content ._items .detail--child .detail--other .detail--style2 {
    font-size: 18px;
  }
  .detail--content ._items .detail--child .detail--style3 .img-detail-logo {
    text-align: center;
    margin-bottom: 80px;
  }
  .detail--content ._items .detail--child .detail--style3 .img-detail-title span {
    font-size: 24px;
  }
  .detail--content ._items .detail--child .detail--style3 .detail--child---list .detail--child---col .desc {
    font-size: 18px;
  }
}
@media screen and (max-width: 1200px) {
  .detail--content ._items .btn {
    font-size: 18px;
  }
  .detail--content ._items .detail--child {
    padding: 40px 0 0;
  }
  .detail--content ._items .detail--child .detail--other .detail--style2 {
    font-size: 16px;
  }
  .detail--content ._items .detail--child .detail--style3 .img-detail-logo {
    text-align: center;
    margin-bottom: 80px;
  }
  .detail--content ._items .detail--child .detail--style3 .img-detail-title span {
    font-size: 20px;
  }
  .detail--content ._items .detail--child .detail--style3 .detail--child---list .detail--child---col .desc {
    font-size: 16px;
  }
}
@media screen and (max-width: 574px) {
  .detail--content ._items .detail--child .detail--style1 {
    width: 100%;
  }
  .detail--content ._items .detail--child .detail--style3 {
    padding: 20px;
  }
  .detail--content ._items .detail--child .detail--style3 .img-detail-logo {
    margin-bottom: 50px;
  }
  .detail--content ._items .detail--child .detail--style3 .detail--child---list {
    justify-content: center;
  }
  .detail--content ._items .detail--child .detail--style3 .detail--child---list .detail--child---col {
    width: 100%;
    padding: 20px 0;
  }
  .detail--content ._items .detail--child .detail--style3 .detail--child---list .__line {
    display: none;
  }
}

.page-news .collection-productintroduction {
  padding: 100px 10px 30px;
}
@media screen and (max-width: 991px) {
  .page-news .collection-productintroduction {
    padding: 40px 10px;
  }
}

.news--list---box {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  margin-bottom: 80px;
}
.news--list---box .gx-lg-4 {
  --bs-gutter-x: 70px;
  gap: 86px 0;
}
@media screen and (max-width: 1400px) {
  .news--list---box .gx-lg-4 {
    --bs-gutter-x: 20px;
    gap: 30px 0;
  }
}

.newsdettail-main {
  padding: 80px 10px 60px;
}
.newsdettail-main .newsdetail-title {
  margin-bottom: 52px;
}
.newsdettail-main .newsdetail-title h1, .newsdettail-main .newsdetail-title .h1 {
  color: #4F4F4F;
  text-align: center;
  font-size: 36px;
  font-style: normal;
  font-weight: 600;
  line-height: 129%;
  text-transform: capitalize;
  margin: 0 0 70px;
}
.newsdettail-main .newsdetail-title .date--time {
  display: flex;
  justify-content: center;
  color: #D1B181;
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: 129%;
  text-transform: capitalize;
  gap: 10px 37px;
}
.newsdettail-main .newsdettail--content {
  width: 100%;
  max-width: 1084px;
  margin: 0 auto;
  color: #4F4F4F;
  font-size: 20px;
  font-style: normal;
  line-height: 180%;
}
.newsdettail-main .newsdettail--content table {
  border: 1px solid #4F4F4F;
  width: 100%;
}
.newsdettail-main .newsdettail--content table tr td {
  padding: 5px;
}
.newsdettail-main .newsdettail--content .desc {
  width: 100%;
  max-width: 806px;
  margin: 0 auto;
}
.newsdettail-main .newsdettail--content .img {
  width: auto !important;
  max-width: 100% !important;
  height: auto !important;
  text-align: center;
  margin-bottom: 15px;
}
@media screen and (max-width: 1600px) {
  .newsdettail-main .newsdetail-title {
    margin-bottom: 40px;
  }
  .newsdettail-main .newsdetail-title h1, .newsdettail-main .newsdetail-title .h1 {
    font-size: 32px;
  }
  .newsdettail-main .newsdettail--content {
    font-size: 18px;
  }
}
@media screen and (max-width: 1400px) {
  .newsdettail-main .newsdetail-title h1, .newsdettail-main .newsdetail-title .h1 {
    font-size: 28px;
  }
}
@media screen and (max-width: 1200px) {
  .newsdettail-main .newsdetail-title h1, .newsdettail-main .newsdetail-title .h1 {
    font-size: 24px;
  }
}
@media screen and (max-width: 991px) {
  .newsdettail-main {
    padding: 40px 10px;
  }
  .newsdettail-main .newsdetail-title h1, .newsdettail-main .newsdetail-title .h1 {
    font-size: 20px;
  }
  .newsdettail-main .newsdettail--content {
    font-size: 16px;
  }
}

.present-box {
  width: 100%;
  padding: 68px 10px 130px;
}
.present-box .row {
  gap: 30px 0;
}
@media screen and (min-width: 1600px) {
  .present-box .row {
    margin: 0 -35px;
  }
  .present-box .row .col-lg-4 {
    padding: 0 35px;
  }
}
@media screen and (max-width: 991px) {
  .present-box {
    padding: 40px 10px;
  }
}

.present--items {
  display: block;
  width: 100%;
}
.present--items .img {
  display: block;
  padding-top: 100%;
  overflow: hidden;
  position: relative;
  margin-bottom: 40px;
}
.present--items .img img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
  position: absolute;
  top: 0;
  left: 0;
  transition: all 0.3s;
}
.present--items .present--body {
  width: 100%;
}
.present--items .present--body .title {
  color: #4F4F4F;
  font-size: 30px;
  font-style: normal;
  font-weight: 600;
  line-height: 129%;
  text-transform: capitalize;
  margin: 0 0 20px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.present--items .present--body .title a {
  color: inherit;
}
.present--items .present--body .desc {
  color: #4F4F4F;
  font-size: 20px;
  font-style: normal;
  line-height: 140%;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  max-width: 432px;
}
.present--items .present--body .desc p:last-child {
  margin-bottom: 0;
}
.present--items.--style2 .img {
  margin-bottom: 54px;
}
.present--items.--style2 .present--body .title {
  margin: 0 0 30px;
}
.present--items:hover .img img {
  transform: scale(1.2);
}
.present--items:hover .present--body .title {
  color: #7C0410;
}
@media screen and (max-width: 1600px) {
  .present--items .present--body .title {
    font-size: 26px;
  }
  .present--items .present--body .desc {
    font-size: 16px;
  }
}
@media screen and (max-width: 1400px) {
  .present--items .img {
    margin-bottom: 30px;
  }
  .present--items .present--body .title {
    font-size: 22px;
  }
}

.store-top {
  width: 100%;
  padding: 100px 10px 50px;
}
.store-top .box-title {
  margin-bottom: 50px;
}
.store-top .box-title .title {
  text-align: center;
  color: #4F4F4F;
}
@media screen and (min-width: 1600px) {
  .store-top .box-title .title {
    font-size: 36px;
  }
}
@media screen and (max-width: 991px) {
  .store-top {
    padding: 50px 10px 0;
  }
}

.store--fillter {
  width: 100%;
  max-width: 1247px;
  margin: 0 auto;
  display: flex;
  flex-wrap: wrap;
  gap: 10px 21px;
}
.store--fillter .store--fillter--col {
  flex: 1;
  width: 100%;
}
.store--fillter .store--fillter--col select {
  width: 100%;
  border: 1px solid #AE8751;
  height: 64px;
  padding: 0 35px 0 17px;
  color: #4F4F4F;
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  background-image: url("../images/ic-10.png");
  background-repeat: no-repeat;
  background-position: right 18px center;
}
@media screen and (max-width: 1400px) {
  .store--fillter .store--fillter--col select {
    height: 52px;
    font-size: 16px;
  }
}
@media screen and (max-width: 991px) {
  .store--fillter .store--fillter--col {
    flex: none;
  }
  .store--fillter .store--fillter--col select {
    height: 52px;
    font-size: 16px;
  }
}

.store-box {
  width: 100%;
  padding: 50px 10px 95px;
}
.store-box .store-list .row {
  gap: 96px 0;
}
@media screen and (min-width: 1600px) {
  .store-box .store-list .row {
    --bs-gutter-x: 26px;
  }
}
@media screen and (max-width: 1400px) {
  .store-box .store-list .row {
    gap: 30px 0;
  }
}
@media screen and (max-width: 991px) {
  .store-box {
    padding: 50px 10px;
  }
}

.store--items {
  display: block;
  width: 100%;
}
.store--items .img {
  display: block;
  width: 100%;
  padding-top: 61.1%;
  overflow: hidden;
  position: relative;
  margin-bottom: 24px;
}
.store--items .img img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
  position: absolute;
  top: 0;
  left: 0;
  transition: all 0.3s;
}
.store--items .img::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background: rgba(124, 4, 16, 0.3);
  top: 0;
  left: 0;
}
.store--items .store--items---body {
  width: 100%;
}
.store--items .store--items---body .store--items--base {
  color: #4F4F4F;
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 17px;
}
.store--items .store--items---body .store--items--address {
  color: #4F4F4F;
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: 129%;
  text-transform: capitalize;
  margin-bottom: 17px;
}
.store--items .store--items---body .box--btn .btn-links {
  font-size: 14px;
  line-height: 140%;
}
.store--items:hover .img img {
  transform: scale(1.2);
}
@media screen and (max-width: 991px) {
  .store--items .store--items---body .store--items--base {
    font-size: 18px;
  }
}

.contact-main {
  width: 100%;
  padding: 100px 10px 47px;
}
.contact-main .box-title {
  margin-bottom: 52px;
}
.contact-main .box-title .title {
  text-align: center;
  color: #4F4F4F;
}
@media screen and (min-width: 1600px) {
  .contact-main .box-title .title {
    font-size: 36px;
  }
}
.contact-main .contact--content {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
  justify-content: space-between;
  max-width: 1362px;
  margin: 0 auto;
}
.contact-main .contact--content .contact--items {
  width: calc(33.333% - 20px);
  padding: 62px 0 75px;
  margin-bottom: 79px;
  max-width: 303px;
  position: relative;
}
.contact-main .contact--content .contact--items .contact--items--title {
  display: flex;
  gap: 9px;
  margin-bottom: 22px;
}
.contact-main .contact--content .contact--items .contact--items--title ._icon {
  flex-shrink: 0;
}
.contact-main .contact--content .contact--items .contact--items--title ._icon img {
  max-width: 100%;
}
.contact-main .contact--content .contact--items .contact--items--title .txt {
  width: 100%;
  flex: 1;
  color: #7C0410;
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: 129%;
  text-transform: capitalize;
}
.contact-main .contact--content .contact--items .box--btn {
  position: absolute;
  bottom: 0;
  width: 100%;
}
.contact-main .contact--content .contact--items .box--btn .btn-links {
  font-size: 16px;
}
.contact-main .contact--content .contact--items .contact--items--body p {
  color: #4F4F4F;
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: 129%;
  text-transform: capitalize;
  margin: 0;
}
.contact-main .contact--content .contact--items .contact--items--body p a {
  color: inherit;
}
.contact-main .contact--content .contact--line {
  width: 100%;
  height: 1px;
  background: #D5D5D5;
}
@media screen and (max-width: 1200px) {
  .contact-main .contact--content .contact--items .contact--items--title .txt {
    font-size: 20px;
  }
}
@media screen and (max-width: 991px) {
  .contact-main {
    padding: 40px 10px;
  }
  .contact-main .contact--content .contact--line {
    display: none;
  }
  .contact-main .contact--content .contact--items {
    width: calc(50% - 15px);
    margin: 0;
  }
  .contact-main .contact--content .contact--items .contact--items--title .txt {
    font-size: 18px;
  }
}
@media screen and (max-width: 574px) {
  .contact-main .box-title {
    margin-bottom: 0;
  }
  .contact-main .contact--content .contact--items {
    padding: 30px 0 50px;
    width: 100%;
    max-width: 100%;
  }
}

.block-privilegesmembership {
  width: 100%;
  padding: 82px 10px 104px;
  background: transparent url("../images/bg-privilegesmembership.jpg") no-repeat center center;
  background-size: cover;
}
.block-privilegesmembership .box-title {
  margin-bottom: 94px;
}
.block-privilegesmembership .box-title .title {
  text-align: center;
  color: #EDD0A7;
  margin-bottom: 15px;
}
@media screen and (min-width: 1600px) {
  .block-privilegesmembership .box-title .title {
    font-size: 36px;
  }
}
.block-privilegesmembership .box-title .desc {
  max-width: 640px;
  margin: 0 auto;
  text-align: center;
  color: #fff;
}
.block-privilegesmembership .privilegesmembership--list {
  display: flex;
  flex-wrap: wrap;
  gap: 20px 16px;
  justify-content: space-between;
  margin-bottom: 102px;
}
.block-privilegesmembership .privilegesmembership--list .privilegesmembership--items {
  width: 100%;
  max-width: 240px;
}
.block-privilegesmembership .privilegesmembership--list .privilegesmembership--items .img {
  text-align: center;
  width: 100%;
  margin-bottom: 16px;
}
.block-privilegesmembership .privilegesmembership--list .privilegesmembership--items .img img {
  max-width: 100%;
}
.block-privilegesmembership .privilegesmembership--list .privilegesmembership--items .txt {
  color: #FFF;
  text-align: center;
  font-size: 16px;
  font-style: normal;
  font-weight: 450;
  line-height: 150%;
  text-transform: capitalize;
}
.block-privilegesmembership .privilegesmembership--list .privilegesmembership--items .txt p:last-child {
  margin-bottom: 0;
}
.block-privilegesmembership .box--btn {
  text-align: center;
}
.block-privilegesmembership .box--btn .btn-links {
  color: #fff;
  border-bottom: 1px solid #fff;
}
@media screen and (max-width: 1400px) {
  .block-privilegesmembership .box-title {
    margin-bottom: 60px;
  }
}
@media screen and (max-width: 991px) {
  .block-privilegesmembership {
    padding: 40px 10px;
  }
  .block-privilegesmembership .box-title {
    margin-bottom: 40px;
  }
}
@media screen and (max-width: 574px) {
  .block-privilegesmembership .privilegesmembership--list {
    margin-bottom: 60px;
  }
  .block-privilegesmembership .privilegesmembership--list .privilegesmembership--items {
    width: 100%;
    max-width: 100%;
  }
}

.block-exclusiveoffers .newshome--list {
  margin-bottom: 0 !important;
}

.policy-main {
  width: 100%;
  padding: 100px 10px;
}
.policy-main .policy--other {
  width: 100%;
  max-width: 807px;
  margin: 0 auto;
}
.policy-main .policy--other .policy--content {
  gap: 40px;
}
.policy-main .policy--other .policy--content .detail--content ._items .detail--child {
  padding-top: 30px;
}
.policy-main .box-title {
  margin-bottom: 40px;
  padding-bottom: 40px;
  border-bottom: 1px solid #000;
}
.policy-main .box-title .title {
  text-align: center;
}
@media screen and (max-width: 991px) {
  .policy-main {
    padding: 40px 10px;
  }
}

.policy--box {
  color: #4F4F4F;
  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: 180%;
}
.policy--box p:last-child {
  margin-bottom: 0;
}
.policy--box table {
  width: 100%;
  border: 1px solid #000;
}
.policy--box table tr td {
  border: 1px solid #000;
  padding: 5px;
}
.policy--box img {
  display: block;
  margin: 0 auto;
  width: auto;
  max-width: 100% !important;
  height: auto !important;
}
@media screen and (max-width: 991px) {
  .policy--box {
    font-size: 16px;
  }
}

.account-main {
  width: 100%;
  padding: 126px 10px 205px;
}
@media screen and (max-width: 991px) {
  .account-main {
    padding: 40px 10px;
  }
}

.account-content {
  width: 100%;
  max-width: 804px;
  margin: 0 auto;
}
.account-content .nav {
  gap: 34px;
  margin-bottom: 51px;
}
.account-content .nav .nav-item {
  flex: 1;
  width: 100%;
}
.account-content .nav .nav-item .nav-link {
  border: 0;
  border-radius: 0;
  padding: 0 0 20px;
  border-bottom: 1px solid #9E9E9E;
  width: 100%;
  text-align: left;
  color: #9E9E9E;
  font-size: 36px;
  font-style: normal;
  font-weight: 600;
  line-height: 129%;
  text-transform: capitalize;
}
.account-content .nav .nav-item .nav-link.active {
  color: #4F4F4F;
  border-bottom: 1px solid #AE8751;
  background-color: transparent;
}
.account-content .nav .show > .nav-link {
  color: #4F4F4F;
  border-bottom: 1px solid #AE8751;
  background-color: transparent;
}
.account-content .account-form .--note {
  color: #4F4F4F;
  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: 120%;
  margin-bottom: 51px;
}
.account-content .account-form .form--btn .storesystem--btn {
  width: 100%;
  max-width: 100%;
}
.account-content .account-form.account-form--register .form--content {
  margin-bottom: 109px;
}
@media screen and (max-width: 1600px) {
  .account-content .nav {
    margin-bottom: 40px;
  }
  .account-content .nav .nav-item .nav-link {
    font-size: 32px;
  }
  .account-content .account-form .--note {
    font-size: 18px;
    margin-bottom: 40px;
  }
  .account-content .account-form.account-form--register .form--content {
    margin-bottom: 70px;
  }
}
@media screen and (max-width: 1400px) {
  .account-content .nav .nav-item .nav-link {
    font-size: 26px;
  }
  .account-content .account-form .--note {
    font-size: 16px;
  }
}
@media screen and (max-width: 991px) {
  .account-content .nav .nav-item .nav-link {
    font-size: 22px;
  }
}

.form--check .form--check--title {
  color: #4F4F4F;
  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: 120%;
  margin-bottom: 12px;
}
.form--check .form--name {
  display: flex;
  gap: 10px 20px;
  flex-wrap: wrap;
}
.form--check .form--name .form--name--items label {
  position: relative;
  padding: 8px 18px;
  min-width: 75px;
  color: #4F4F4F;
  text-align: center;
  font-size: 12.624px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  cursor: pointer;
}
.form--check .form--name .form--name--items label span {
  position: absolute;
  display: block;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  border: 1px solid #EDD0A7;
}
.form--check .form--name .form--name--items input:checked ~ label span {
  border: 1px solid #AE8751;
}
@media screen and (max-width: 991px) {
  .form--check .form--check--title {
    font-size: 18px;
  }
  .form--check .form--name .form--name--items label {
    font-size: 12px;
  }
}

.box--verify {
  display: flex;
  flex-wrap: wrap;
  gap: 43px 21px;
}
.box--verify .verify-col {
  flex: 1;
  width: 100%;
}
.box--verify .verify-col + .verify-col {
  padding-left: 28px;
}
.box--verify .form--btn {
  flex-shrink: 0;
  width: 161px;
}
.box--verify .form--btn .storesystem--btn {
  height: 64px;
  font-size: 20px;
}
@media screen and (max-width: 767px) {
  .box--verify .verify-col:first-child {
    flex: none;
  }
  .box--verify .verify-col + .verify-col {
    padding-left: 0;
  }
  .box--verify .form--btn .storesystem--btn {
    font-size: 16px;
  }
}

.check--agree {
  width: 100%;
  padding-top: 34px;
}
.check--agree label {
  display: flex;
  width: 100%;
  gap: 22px;
  margin: 0;
  align-items: center;
}
.check--agree label span {
  border: 1px solid #EDD0A7;
  width: 40px;
  height: 40px;
  position: relative;
}
.check--agree label span::after {
  content: "";
  display: block;
  width: 20px;
  height: 10px;
  transform: rotate(-45deg);
  border: 3px solid #4F4F4F;
  border-top: 0;
  border-right: 0;
  position: absolute;
  top: 11px;
  left: 11px;
  opacity: 0;
  visibility: hidden;
}
.check--agree label .txt {
  flex: 1;
  width: 100%;
  max-width: 540px;
  color: rgba(79, 79, 79, 0.8);
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 120%;
}
.check--agree input:checked ~ label span::after {
  opacity: 1;
  visibility: visible;
}

.cart-main {
  width: 100%;
  padding: 105px 10px 140px;
}
.cart-main.cart-main--success {
  padding: 42px 10px 63px;
}
.cart-main.cart-main--success .cart--other {
  max-width: 1136px;
}
.cart-main .cart--other {
  width: 100%;
  max-width: 1362px;
  margin: 0 auto;
}
.cart-main .cart--other .box-title {
  margin-bottom: 60px;
}
@media screen and (max-width: 1600px) {
  .cart-main {
    padding: 80px 10px;
  }
}
@media screen and (max-width: 991px) {
  .cart-main {
    padding: 40px 10px;
  }
  .cart-main .cart--other .box-title {
    margin-bottom: 30px;
  }
}

.cart--list {
  gap: 30px 60px;
  display: flex;
  flex-wrap: wrap;
  width: 100%;
}
.cart--list ._left {
  width: 100%;
  flex: 1;
}
.cart--list ._right {
  width: 45%;
  max-width: 580px;
}
.cart--list ._right .account-form .box-title {
  margin-bottom: 0;
}
.cart--list ._right .account-form .box-title .title {
  font-size: 30px;
  line-height: 113.333%;
}
.cart--list ._right .account-form .form--content {
  gap: 23px 27px;
  padding: 64px 24px;
  margin-bottom: 40px;
  background-color: rgba(237, 208, 167, 0.1);
}
.cart--list ._right .account-form .form--content .form--row .input--control {
  border-bottom: 1px solid rgba(79, 79, 79, 0.5);
}
.cart--list ._right .form--btn .storesystem--btn {
  max-width: 100%;
}
@media screen and (max-width: 991px) {
  .cart--list {
    flex-wrap: wrap;
  }
  .cart--list ._left {
    flex: none;
  }
  .cart--list ._right {
    width: 100%;
    max-width: 100%;
  }
  .cart--list ._right .account-form .box-title {
    margin-bottom: 0;
  }
  .cart--list ._right .account-form .box-title .title {
    font-size: 20px;
  }
  .cart--list ._right .account-form .form--content {
    padding: 30px 20px;
  }
  .cart--list ._right .form--btn .storesystem--btn {
    max-width: 100%;
  }
}

.cart--box {
  width: 100%;
}
.cart--box .cart--top {
  margin-bottom: 35px;
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: 30px 44px;
  align-items: center;
}
.cart--box .cart--top .img {
  width: 252px;
  height: 252px;
  overflow: hidden;
}
.cart--box .cart--top .img img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
}
.cart--box .cart--top .cart--top---body {
  width: 100%;
  flex: 1;
}
.cart--box .cart--top .cart--top---body .--name {
  color: #4F4F4F;
  font-size: 24px;
  font-style: normal;
  font-weight: 400;
  line-height: 141.667%;
  text-transform: capitalize;
  margin-bottom: 19px;
}
.cart--box .cart--top .cart--top---body .--code {
  color: #4F4F4F;
  font-size: 12px;
  font-style: normal;
  line-height: 140%;
  text-transform: uppercase;
  margin-bottom: 23px;
}
.cart--box .cart--top .cart--top---body .cart--top---select {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: space-between;
  margin-bottom: 50px;
}
.cart--box .cart--top .cart--top---body .cart--top---select > * {
  display: flex;
  gap: 8px;
  align-items: center;
}
.cart--box .cart--top .cart--top---body .cart--top---select > * .txt {
  color: #757575;
  font-size: 14px;
  font-style: normal;
  line-height: 140%;
  min-width: 60px;
}
.cart--box .cart--top .cart--top---body .cart--top---select .pricegold--input {
  width: 75px;
}
.cart--box .cart--top .cart--top---body .cart--top---select .pricegold--input select {
  padding: 0 10px 0 6px;
  text-align: center;
  background-position: right 7px center;
}
.cart--box .cart--top .cart--top---body .cart--top---select .pricegold--input .inp-sl span {
  width: 18px;
}
.cart--box .cart--top .cart--top---body .cart--top---del {
  display: flex;
  justify-content: space-between;
  gap: 10px;
}
.cart--box .cart--top .cart--top---body .cart--top---del .btn-del {
  display: inline-flex;
  gap: 8px;
  align-items: end;
  border: 0;
  box-shadow: none;
  border-radius: 0;
  color: #7C0410;
  font-size: 13.945px;
  font-style: normal;
  font-weight: 300;
  line-height: 150.588%;
  text-transform: capitalize;
  padding: 0;
  background-color: transparent;
}
.cart--box .cart--top .cart--top---body .cart--top---del .btn-del:focus, .cart--box .cart--top .cart--top---body .cart--top---del .btn-del:focus-visible {
  outline: none;
  box-shadow: none;
}
.cart--box .cart--top .cart--top---body .cart--top---del .--price {
  white-space: nowrap;
  color: #AE8751;
  text-align: right;
  font-size: 24px;
  line-height: 116.667%;
}
.cart--box .cart--infor {
  background: rgba(237, 208, 167, 0.1);
  padding: 30px 27px 40px 51px;
  width: 100%;
}
.cart--box .cart--infor .cart--infor---title {
  color: #4F4F4F;
  font-size: 30px;
  font-style: normal;
  font-weight: 600;
  line-height: 113.333%;
  text-transform: capitalize;
  padding-bottom: 26px;
  border-bottom: 1px solid rgba(174, 135, 81, 0.7);
  margin-bottom: 30px;
}
.cart--box .cart--infor .cart--infor---body {
  width: 100%;
}
.cart--box .cart--infor .cart--infor---body .discount--code {
  width: 100%;
}
.cart--box .cart--infor .cart--infor---body .discount--code label {
  color: #4F4F4F;
  font-size: 20px;
  font-style: normal;
  line-height: 150%;
  width: 100%;
  margin: 0 0 5px;
}
.cart--box .cart--infor .cart--infor---body .discount--code .discount--code---input {
  display: flex;
  flex-wrap: wrap;
  gap: 10px 23px;
  margin-bottom: 35px;
}
.cart--box .cart--infor .cart--infor---body .discount--code .discount--code---input input {
  flex: 1;
  width: 100%;
  height: 56px;
  border: 1px solid #EDD0A7;
  padding: 0 10px;
  background-color: transparent;
}
.cart--box .cart--infor .cart--infor---body .discount--code .discount--code---input .storesystem--btn {
  width: 190px;
  height: 56px;
  padding: 5px;
}
@media screen and (max-width: 1600px) {
  .cart--box .cart--top .cart--top---body .--name {
    font-size: 22px;
  }
  .cart--box .cart--top .cart--top---body .cart--top---del .btn-del {
    font-size: 13px;
  }
  .cart--box .cart--top .cart--top---body .cart--top---del .--price {
    font-size: 22px;
  }
  .cart--box .cart--infor .cart--infor---title {
    font-size: 26px;
  }
  .cart--box .cart--infor .cart--infor---body .discount--code label {
    font-size: 18px;
  }
}
@media screen and (max-width: 1200px) {
  .cart--box .cart--top .cart--top---body {
    flex: none;
  }
}
@media screen and (max-width: 991px) {
  .cart--box .cart--top .cart--top---body .--name {
    font-size: 18px;
  }
  .cart--box .cart--top .cart--top---body .cart--top---del .--price {
    font-size: 18px;
  }
  .cart--box .cart--infor {
    padding: 30px 20px;
  }
  .cart--box .cart--infor .cart--infor---title {
    font-size: 22px;
  }
  .cart--box .cart--infor .cart--infor---body .discount--code label {
    font-size: 16px;
  }
}
@media screen and (max-width: 574px) {
  .cart--box .cart--infor .cart--infor---body .discount--code .discount--code---input input {
    flex: auto;
  }
  .cart--box .cart--infor .cart--infor---body .discount--code .discount--code---input .storesystem--btn {
    width: 100%;
    max-width: 100%;
  }
}

.cart--total {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: space-between;
  align-items: start;
}
.cart--total .txt {
  color: #AE8751;
  font-size: 24px;
  font-style: normal;
  line-height: 116.667%;
}
.cart--total .number {
  color: #AE8751;
  text-align: right;
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: 116.667%;
}
.cart--total .number span {
  display: block;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 175%;
}
@media screen and (max-width: 1600px) {
  .cart--total .txt {
    font-size: 22px;
  }
  .cart--total .number {
    font-size: 22px;
  }
}
@media screen and (max-width: 991px) {
  .cart--total .txt {
    font-size: 18px;
  }
  .cart--total .number {
    font-size: 18px;
  }
}

.cart--infor---list {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  padding-bottom: 40px;
  border-bottom: 1px solid rgba(174, 135, 81, 0.7);
  margin-bottom: 22px;
}
.cart--infor---list .cart--infor---row {
  width: 100%;
  display: flex;
  gap: 10px;
  justify-content: space-between;
}
.cart--infor---list .cart--infor---row .txt {
  color: rgba(79, 79, 79, 0.7);
  font-size: 20px;
  font-style: normal;
  line-height: 170%;
}
.cart--infor---list .cart--infor---row .number {
  color: rgba(79, 79, 79, 0.7);
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: 170%;
  text-transform: capitalize;
  text-align: right;
}
@media screen and (max-width: 991px) {
  .cart--infor---list .cart--infor---row .txt {
    font-size: 16px;
  }
  .cart--infor---list .cart--infor---row .number {
    font-size: 16px;
  }
}

.modal--book .modal-dialog {
  max-width: 1015px;
  width: calc(100% - 30px);
}
.modal--book .modal-content {
  border: 0;
  border-radius: 0;
  background: transparent;
}
.modal--book .btn-close {
  position: absolute;
  line-height: 1;
  top: 55px;
  right: 52px;
  width: -moz-max-content;
  width: max-content;
  height: -moz-max-content;
  height: max-content;
  padding: 0;
}
.modal--book .modalbook-content {
  padding: 44px 103px 60px;
  background-color: #fff;
}
.modal--book .modalbook-content .title {
  color: #AE8751;
  text-align: center;
  font-size: 40px;
  font-weight: 700;
  line-height: 120%;
  margin-bottom: 60px;
}
.modal--book .modalbook-content .form--content {
  gap: 20px;
  margin-bottom: 54px;
}
.modal--book .modalbook-content .form--content .form--row .input--control {
  height: 61px;
  border-bottom: 1px solid rgba(79, 79, 79, 0.5);
}
.modal--book .modalbook-content--success {
  padding: 239px 103px 120px;
  background-color: #fff;
}
.modal--book .modalbook-content--success .img {
  text-align: center;
  margin-bottom: 35px;
}
.modal--book .modalbook-content--success .img img {
  max-width: 100%;
}
.modal--book .modalbook-content--success .title {
  color: #4F4F4F;
  text-align: center;
  font-size: 40px;
  font-style: normal;
  font-weight: 600;
  line-height: 120%;
  text-transform: capitalize;
  margin-bottom: 35px;
  opacity: 0.7;
}
.modal--book .modalbook-content--success .desc {
  width: 100%;
  max-width: 646px;
  margin: 0 auto;
  color: #4F4F4F;
  opacity: 0.7;
  text-align: center;
  font-size: 20px;
  line-height: 120%;
}
@media screen and (max-width: 1600px) {
  .modal--book .modalbook-content .title {
    font-size: 32px;
  }
  .modal--book .modalbook-content .form--content {
    margin-bottom: 40px;
  }
  .modal--book .modalbook-content--success .title {
    font-size: 32px;
  }
  .modal--book .modalbook-content--success .desc {
    font-size: 18px;
  }
}
@media screen and (max-width: 991px) {
  .modal--book .btn-close {
    top: 20px;
    right: 15px;
  }
  .modal--book .modalbook-content {
    padding: 35px 25px;
  }
  .modal--book .modalbook-content .title {
    font-size: 24px;
    margin-bottom: 30px;
  }
  .modal--book .modalbook-content .form--content {
    margin-bottom: 30px;
  }
  .modal--book .modalbook-content--success {
    padding: 35px 25px;
  }
  .modal--book .modalbook-content--success .title {
    font-size: 24px;
  }
  .modal--book .modalbook-content--success .desc {
    font-size: 16px;
  }
}

.block--btn {
  width: 100%;
  display: flex;
  gap: 10px 24px;
}
.block--btn > * {
  flex: 1;
  border: 1px solid #B78E6C;
  color: #FFF;
  text-align: center;
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  padding: 16px;
}
.block--btn .btn--cancel {
  color: #B78E6C;
}
.block--btn .btn--book {
  background-color: #B78E6C;
}
.block--btn .btn--book:hover {
  color: #fff;
}
@media screen and (max-width: 991px) {
  .block--btn > * {
    font-size: 16px;
  }
}

.cartsuccess--box {
  width: 100%;
  padding: 41px 61px 79px;
  border: 1px solid rgba(79, 79, 79, 0.7);
  margin-bottom: 65px;
}
.cartsuccess--box .cartsuccess--top {
  width: 100%;
  padding-bottom: 35px;
  border-bottom: 1px solid rgba(79, 79, 79, 0.7);
  margin-bottom: 35px;
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}
.cartsuccess--box .cartsuccess--top .cartsuccess--row {
  display: flex;
  gap: 15px;
  width: 100%;
  align-items: center;
}
.cartsuccess--box .cartsuccess--top .cartsuccess--row .txt {
  width: 150px;
  flex-shrink: 0;
  color: rgba(79, 79, 79, 0.7);
  font-size: 16px;
}
.cartsuccess--box .cartsuccess--top .cartsuccess--row .desc {
  flex: 1;
  width: 100%;
  color: rgba(79, 79, 79, 0.7);
  font-size: 20px;
}
.cartsuccess--box .cartsuccess--body {
  width: 100%;
}
.cartsuccess--box .cartsuccess--body .cartsuccess--body---first {
  display: flex;
  flex-wrap: wrap;
  align-items: start;
}
.cartsuccess--box .cartsuccess--body .cartsuccess--body---first .img {
  width: 242px;
  height: 242px;
  overflow: hidden;
  flex-shrink: 0;
}
.cartsuccess--box .cartsuccess--body .cartsuccess--body---first .img img {
  width: 100%;
  height: 100%;
  -o-object-fit: scale-down;
     object-fit: scale-down;
  -o-object-position: center;
     object-position: center;
}
.cartsuccess--box .cartsuccess--body .cartsuccess--body---first .infor--desc {
  flex: 1;
  width: 100%;
  display: flex;
  gap: 14px 10px;
}
.cartsuccess--box .cartsuccess--body .cartsuccess--body---first .infor--desc .--price {
  flex-shrink: 0;
  color: rgba(79, 79, 79, 0.7);
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: 170%;
  text-transform: capitalize;
  white-space: nowrap;
}
.cartsuccess--box .cartsuccess--body .cartsuccess--body---first .infor--desc .desc {
  flex: 1;
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: 14px;
}
.cartsuccess--box .cartsuccess--body .cartsuccess--body---first .infor--desc .desc .--name {
  color: rgba(79, 79, 79, 0.7);
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: 141.667%;
  text-transform: capitalize;
  width: 100%;
}
.cartsuccess--box .cartsuccess--body .cartsuccess--body---first .infor--desc .desc .--code {
  color: rgba(79, 79, 79, 0.5);
  font-size: 12px;
  line-height: 140%;
  width: 100%;
  text-transform: uppercase;
}
.cartsuccess--box .cartsuccess--body .cartsuccess--body---first .infor--desc .desc .--parameter {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: 14px 37px;
}
.cartsuccess--box .cartsuccess--body .cartsuccess--body---first .infor--desc .desc .--parameter > * {
  color: rgba(79, 79, 79, 0.5);
  font-size: 12px;
  line-height: 140%;
  width: 100%;
  text-transform: uppercase;
  width: -moz-max-content;
  width: max-content;
}
.cartsuccess--box .cartsuccess--body .cartsuccess--body---first .infor--desc .desc .--quantity {
  color: rgba(79, 79, 79, 0.5);
  font-size: 12px;
  line-height: 140%;
  width: 100%;
  text-transform: uppercase;
}
.cartsuccess--box .cartsuccess--body .cart--infor---list {
  gap: 10px;
  border: 0;
  margin-bottom: 0;
  padding-bottom: 30px;
}
.cartsuccess--box .cartsuccess--body .cart--infor---list .cart--infor---row .txt {
  color: rgba(79, 79, 79, 0.5);
}
.cartsuccess--box .cartsuccess--body .cart--total {
  padding: 27px 0 13px;
  position: relative;
  margin-bottom: 15px;
}
.cartsuccess--box .cartsuccess--body .cart--total::after, .cartsuccess--box .cartsuccess--body .cart--total::before {
  content: "";
  display: block;
  width: 100%;
  height: 1px;
  background: transparent url("../images/bg-line.png") repeat-x center;
  position: absolute;
  left: 0;
}
.cartsuccess--box .cartsuccess--body .cart--total::after {
  bottom: 0;
}
.cartsuccess--box .cartsuccess--body .cart--total::before {
  top: 0;
}
.cartsuccess--box .cartsuccess--body .cart--total .number {
  font-size: 30px;
  line-height: 93.333%;
}
.cartsuccess--box .cartsuccess--body .cartsuccess--pay {
  margin-bottom: 40px;
}
.cartsuccess--box .cartsuccess--body .cartsuccess--pay .--title {
  color: rgba(79, 79, 79, 0.7);
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: 141.667%;
  text-transform: capitalize;
  margin-bottom: 17px;
}
.cartsuccess--box .cartsuccess--body .cartsuccess--pay .cart--infor---list {
  padding: 0;
}
@media screen and (max-width: 1400px) {
  .cartsuccess--box .cartsuccess--top .cartsuccess--row .desc {
    font-size: 18px;
  }
  .cartsuccess--box .cartsuccess--body .cartsuccess--body---first .infor--desc .--price {
    font-size: 18px;
  }
  .cartsuccess--box .cartsuccess--body .cartsuccess--body---first .infor--desc .desc .--name {
    font-size: 20px;
  }
  .cartsuccess--box .cartsuccess--body .cart--total .number {
    font-size: 24px;
  }
  .cartsuccess--box .cartsuccess--body .cartsuccess--pay .--title {
    font-size: 22px;
  }
}
@media screen and (max-width: 991px) {
  .cartsuccess--box {
    padding: 40px 20px;
  }
  .cartsuccess--box .cartsuccess--top .cartsuccess--row .desc {
    font-size: 16px;
  }
  .cartsuccess--box .cartsuccess--body .cartsuccess--body---first .infor--desc {
    flex-wrap: wrap;
  }
  .cartsuccess--box .cartsuccess--body .cartsuccess--body---first .infor--desc .--price {
    font-size: 16px;
  }
  .cartsuccess--box .cartsuccess--body .cartsuccess--body---first .infor--desc .desc {
    flex: none;
  }
  .cartsuccess--box .cartsuccess--body .cartsuccess--body---first .infor--desc .desc .--name {
    font-size: 16px;
  }
  .cartsuccess--box .cartsuccess--body .cart--total .number {
    font-size: 20px;
  }
  .cartsuccess--box .cartsuccess--body .cartsuccess--pay .--title {
    font-size: 16px;
  }
}

.cartsuccess--bot {
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
  justify-content: space-between;
  align-items: start;
}
.cartsuccess--bot .desc {
  width: calc(50% - 15px);
  color: rgba(79, 79, 79, 0.7);
  font-size: 16px;
  max-width: 492px;
}
.cartsuccess--bot .desc .txt {
  display: inline;
  color: #571111;
  font-size: 20px;
}
.cartsuccess--bot .desc .txt span {
  font-size: 24px;
}
.cartsuccess--bot .form--btn {
  width: calc(50% - 15px);
  max-width: 551px;
}
.cartsuccess--bot .form--btn .storesystem--btn {
  max-width: 100%;
  display: inline-flex;
  align-items: center;
  gap: 21px;
  justify-content: center;
}
@media screen and (max-width: 991px) {
  .cartsuccess--bot .desc {
    width: 100%;
    max-width: 100%;
  }
  .cartsuccess--bot .desc .txt {
    font-size: 18px;
  }
  .cartsuccess--bot .desc .txt span {
    font-size: 20px;
  }
  .cartsuccess--bot .form--btn {
    width: 100%;
    max-width: 100%;
  }
}

.block-404 {
  width: 100%;
  padding: 200px 10px 250px;
}
.block-404 .block-404--content {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 30px;
}
.block-404 .block-404--content ._left {
  width: calc(50% - 15px);
  color: #7C0410;
  text-align: center;
  font-size: 316.502px;
  font-style: normal;
  font-weight: 200;
  line-height: normal;
}
.block-404 .block-404--content ._right {
  width: calc(50% - 15px);
}
.block-404 .block-404--content ._right .txt {
  color: #000;
  text-align: center;
  font-size: 31px;
  font-style: normal;
  line-height: normal;
  margin-bottom: 55px;
}
.block-404 .block-404--content ._right .form--btn {
  text-align: center;
}
@media screen and (max-width: 1600px) {
  .block-404 .block-404--content ._left {
    font-size: 250px;
  }
  .block-404 .block-404--content ._right .txt {
    font-size: 26px;
    margin-bottom: 40px;
  }
}
@media screen and (max-width: 1400px) {
  .block-404 {
    padding: 100px 10px;
  }
  .block-404 .block-404--content ._left {
    font-size: 200px;
  }
  .block-404 .block-404--content ._right .txt {
    font-size: 24px;
  }
}
@media screen and (max-width: 991px) {
  .block-404 {
    padding: 80px 10px;
  }
  .block-404 .block-404--content ._left {
    width: 100%;
    font-size: 160px;
  }
  .block-404 .block-404--content ._right {
    width: 100%;
  }
  .block-404 .block-404--content ._right .txt {
    font-size: 20px;
  }
}
@media screen and (max-width: 574px) {
  .block-404 .block-404--content ._left {
    font-size: 120px;
  }
}

.block-search {
  padding: 42px 10px 77px;
}
.block-search .product-tabs {
  margin-bottom: 86px;
}
@media screen and (max-width: 991px) {
  .block-search {
    padding: 40px 10px;
  }
  .block-search .product-tabs {
    margin-bottom: 40px;
  }
}

.block-search--top {
  width: 100%;
  padding: 74px 10px 42px;
}
.block-search--top .title {
  color: #000;
  text-align: center;
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 22px;
}
.block-search--top .title span {
  font-size: 24px;
}
.block-search--top .number-search {
  color: #4F4F4F;
  text-align: center;
  font-size: 16px;
  line-height: 140%;
}
@media screen and (max-width: 991px) {
  .block-search--top {
    padding: 40px 10px;
  }
  .block-search--top .title {
    font-size: 18px;
  }
  .block-search--top .title span {
    font-size: 20px;
  }
}

.banner-main {
  width: 100%;
  padding: 0;
}
.banner-main .img {
  overflow: hidden;
  width: 100%;
  position: relative;
  height: 100vh;
}
.banner-main .img::after {
  position: absolute;
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.2);
  z-index: 5;
}
.banner-main .img > img {
  width: 100%;
  height: 100%;
  -o-object-position: center;
     object-position: center;
  -o-object-fit: cover;
     object-fit: cover;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 3;
}
.banner-main .img .play-button {
  width: 78px;
  height: 78px;
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  margin: auto;
  z-index: 4;
  cursor: pointer;
}
.banner-main .img .play-button svg {
  max-width: 100%;
}
.banner-main .img iframe,
.banner-main .img video {
  width: 100% !important;
  height: 100% !important;
  position: absolute;
  -o-object-fit: cover;
     object-fit: cover;
  top: 0;
  left: 0;
}
.banner-main.banner-other .img {
  height: calc(100vh - 182px);
}
@media screen and (max-width: 767px) {
  .banner-main .img {
    height: 50vh;
  }
}

.home-timelessstory {
  width: 100%;
  padding: 98px 10px;
  background: transparent;
}
.home-timelessstory .timelessstory--list {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
  align-items: flex-start;
  justify-content: space-between;
}
.home-timelessstory .timelessstory--list ._left {
  width: calc(50% - 15px);
  max-width: 640px;
  flex: 1;
}
.home-timelessstory .timelessstory--list ._left .timelessstory--body {
  margin-bottom: 50px;
}
.home-timelessstory .timelessstory--list ._left .timelessstory--body .desc {
  color: #4F4F4F;
  font-size: 20px;
  font-style: normal;
  line-height: 150%;
}
.home-timelessstory .timelessstory--list ._left .timelessstory--body .desc p:last-child {
  margin-bottom: 0;
}
.home-timelessstory .timelessstory--list ._right {
  width: calc(50% - 15px);
  max-width: 800px;
}
.home-timelessstory .timelessstory--list ._right .img {
  width: 100%;
  overflow: hidden;
  position: relative;
}
.home-timelessstory .timelessstory--list ._right .img img {
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
  top: 0;
  left: 0;
}
.home-timelessstory.--style2 {
  padding: 50px 10px;
}
.home-timelessstory.--style2 .timelessstory--list ._left {
  max-width: 551px;
  padding-top: 99px;
}
.home-timelessstory.--style2 .timelessstory--list ._left .box-title {
  margin-bottom: 19px;
}
.home-timelessstory.--style2 .timelessstory--list ._left .box-title .desc {
  font-size: 20px;
}
.home-timelessstory.--style2 .timelessstory--list ._right {
  max-width: 806px;
}
.home-timelessstory.block-stories .timelessstory--list ._left {
  padding-top: 40px;
}
.home-timelessstory.block-stories .timelessstory--list ._left .box-title {
  margin-bottom: 19px;
}
.home-timelessstory.block-stories .timelessstory--list ._left .box-title .desc {
  font-size: 20px;
}
@media screen and (max-width: 1400px) {
  .home-timelessstory .timelessstory--list ._left .timelessstory--body .desc {
    font-size: 18px;
  }
}
@media screen and (max-width: 1400px) {
  .home-timelessstory .timelessstory--list ._left .timelessstory--body .desc {
    font-size: 18px;
  }
  .home-timelessstory.--style2 .timelessstory--list ._left {
    padding-top: 70px;
  }
  .home-timelessstory.--style2 .timelessstory--list ._left .box-title {
    margin-bottom: 19px;
  }
}
@media screen and (max-width: 991px) {
  .home-timelessstory {
    padding: 40px 10px;
  }
  .home-timelessstory .timelessstory--list ._left {
    width: 100%;
    max-width: 100%;
  }
  .home-timelessstory .timelessstory--list ._left .timelessstory--body {
    margin-bottom: 30px;
  }
  .home-timelessstory .timelessstory--list ._left .timelessstory--body .desc {
    font-size: 16px;
  }
  .home-timelessstory .timelessstory--list ._right {
    width: 100%;
    max-width: 100%;
  }
  .home-timelessstory.--style2 .timelessstory--list ._left {
    padding-top: 0;
  }
}

.home-product {
  width: 100%;
  padding: 60px 10px 64px;
}
.home-product .box-title {
  margin-bottom: 72px;
}
.home-product .box-title .title {
  margin-bottom: 16px;
}
@media screen and (min-width: 1600px) {
  .home-product .box-title .title {
    line-height: 129%;
    font-size: 36px;
  }
}
.home-product .box-title .desc {
  max-width: 530px;
}
.home-product .producthome--list {
  position: relative;
  margin-bottom: 50px;
}
.home-product .producthome--list .producbox--btn .swiper-button-next,
.home-product .producthome--list .producbox--btn .swiper-button-prev {
  margin: 0;
  top: 30%;
}
.home-product .producthome--list .swiper {
  margin-bottom: 50px;
}
@media screen and (max-width: 991px) {
  .home-product {
    padding: 40px 10px;
  }
  .home-product .box-title {
    margin-bottom: 30px;
  }
  .home-product .box-title .desc {
    max-width: 100%;
  }
}

.home-store {
  padding: 70px 10px 107px;
  width: 100%;
}
@media screen and (max-width: 1400px) {
  .home-store {
    padding: 60px 10px;
  }
}
@media screen and (max-width: 991px) {
  .home-store {
    padding: 40px 10px;
  }
}

.store--list {
  width: 100%;
  position: relative;
  display: flex;
  gap: 30px;
  flex-wrap: wrap;
  justify-content: space-between;
}
.store--list ._left {
  width: calc(33.03% - 15px);
  max-width: 344px;
}
.store--list ._left .box-title {
  margin-bottom: 40px;
}
.store--list ._left .box-title .sub_title {
  margin-bottom: 40px;
}
.store--list ._right {
  width: calc(66.97% - 15px);
}
.store--list .store--box {
  position: relative;
  width: 100%;
}
.store--list .swiper {
  border: 0.5px solid #D9D9D9;
  margin-bottom: 50px;
}
.store--list .swiper .swiper-slide {
  height: auto;
}
.store--list .swiper .productcate--items {
  border: 0;
  border-left: 0.5 solid #D9D9D9;
}
@media screen and (max-width: 1200px) {
  .store--list {
    padding-bottom: 78px;
  }
  .store--list .swiper {
    margin-bottom: 30px;
  }
  .store--list ._left {
    width: 100%;
    max-width: 100%;
  }
  .store--list ._left .box-title {
    margin-bottom: 0;
  }
  .store--list ._left .box-title .sub_title {
    margin-bottom: 30px;
  }
  .store--list ._right {
    width: 100%;
  }
  .store--list .box--btn {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
  }
}

.home-dowryjewelry {
  padding: 140px 10px 123px;
  width: 100%;
}
.home-dowryjewelry .dowryjewelry--list {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  justify-content: space-between;
  gap: 30px;
}
.home-dowryjewelry .dowryjewelry--list ._left {
  width: calc(67.15% - 15px);
}
.home-dowryjewelry .dowryjewelry--list ._right {
  width: calc(32.85% - 15px);
  max-width: 486px;
  padding-right: 50px;
}
.home-dowryjewelry .dowryjewelry--list .box-title {
  margin-bottom: 40px;
}
.home-dowryjewelry .dowryjewelry--list .dowryjewelry--other {
  position: relative;
  border: 1px solid #D9D9D9;
}
.home-dowryjewelry .dowryjewelry--list .dowryjewelry--other .swiper .swiper-slide {
  height: auto;
}
.home-dowryjewelry .dowryjewelry--list .dowryjewelry--other .producbox--panigation {
  position: absolute;
  width: calc(40.8% - 10px);
  bottom: 30px;
  left: 0;
  z-index: 5;
}
@media screen and (max-width: 1400px) {
  .home-dowryjewelry {
    padding: 60px 10px 90px;
  }
}
@media screen and (max-width: 1200px) {
  .home-dowryjewelry .dowryjewelry--list {
    gap: 30px;
  }
  .home-dowryjewelry .dowryjewelry--list ._left {
    width: 100%;
  }
  .home-dowryjewelry .dowryjewelry--list ._right {
    width: 100%;
    max-width: 100%;
    order: -1;
    padding-right: 0;
  }
  .home-dowryjewelry .dowryjewelry--list .box-title {
    margin-bottom: 30px;
  }
  .home-dowryjewelry .dowryjewelry--list .dowryjewelry--other .producbox--panigation {
    width: 100%;
    top: calc(100% + 18px);
  }
}

.dowryjewelry--box {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}
.dowryjewelry--box .dowryjewelry-col1 {
  width: calc(40.8% - 10px);
}
.dowryjewelry--box .dowryjewelry-col1 .product--items {
  border: 0;
  padding-bottom: 96px;
}
.dowryjewelry--box .dowryjewelry-img {
  flex: 1;
  width: 100%;
  overflow: hidden;
}
.dowryjewelry--box .dowryjewelry-img img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
}
@media screen and (max-width: 1200px) {
  .dowryjewelry--box {
    gap: 0;
  }
  .dowryjewelry--box .dowryjewelry-col1 {
    width: 50%;
  }
  .dowryjewelry--box .dowryjewelry-col1 .product--items {
    padding-bottom: 25px;
  }
}
@media screen and (max-width: 414px) {
  .dowryjewelry--box .dowryjewelry-col1 {
    width: 100%;
  }
  .dowryjewelry--box .dowryjewelry-col1 .product--items {
    padding-bottom: 25px;
  }
  .dowryjewelry--box .dowryjewelry-img {
    order: -1;
    flex: none;
  }
}

.home-collection {
  background-color: transparent;
  background-repeat: no-repeat;
  background-position: bottom center;
  min-height: 670px;
  padding: 80px 10px;
}
.home-collection .box-title {
  margin-bottom: 36px;
}
.home-collection .box-title .title {
  color: #EDD0A7;
  line-height: 129%;
}
.home-collection .box-title .sub_title {
  color: #EDD0A7;
  line-height: 129%;
  margin-bottom: 23px;
}
.home-collection .box-title .desc {
  color: #fff;
  max-width: 595px;
}
.home-collection .box--btn .btn-links {
  color: #EDD0A7;
  border-bottom: 1px solid #EDD0A7;
}
.home-collection.present-collection {
  display: flex;
  align-items: center;
  width: 100%;
  min-height: 720px;
  padding: 170px 10px 102px;
}
.home-collection.present-collection .box-title {
  margin-bottom: 15px;
}
.home-collection.present-collection .box-title .title {
  color: #fff;
  margin-bottom: 25px;
}
.home-collection.present-collection .box-title .desc {
  color: #fff;
  max-width: 432px;
}
.home-collection.present-collection .box--btn .btn-links {
  color: #fff;
  border-bottom: 1px solid #fff;
}
@media screen and (max-width: 991px) {
  .home-collection {
    padding: 60px 10px;
    min-height: auto;
  }
  .home-collection.present-collection {
    min-height: auto;
    padding: 40px 10px;
  }
}

.home-brand {
  padding: 135px 10px 65px;
}
.home-brand .brand-content {
  width: 100%;
  min-height: 844px;
  background-color: transparent;
  background-repeat: no-repeat;
  background-position: bottom center;
  padding: 51px 64px 70px;
  display: flex;
  position: relative;
}
.home-brand .brand-content::after {
  width: 100%;
  height: 38.3%;
  content: "";
  display: block;
  background: linear-gradient(0deg, #AF813E 2.24%, rgba(223, 189, 119, 0) 97.26%);
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 2;
}
.home-brand .brand-content .brand-other {
  position: relative;
  z-index: 5;
  width: 100%;
  max-width: 577px;
  display: flex;
  flex-direction: column;
  gap: 30px;
  align-items: start;
  justify-content: space-between;
}
.home-brand .brand-content .brand-other .brand--name {
  color: #FFF;
  font-size: 20px;
  font-style: normal;
  line-height: 150%;
}
.home-brand .brand-content .brand-other .box-title .title {
  color: #fff;
  margin-bottom: 40px;
}
@media screen and (min-width: 1600px) {
  .home-brand .brand-content .brand-other .box-title .title {
    font-size: 36px;
    line-height: 129%;
  }
}
.home-brand .brand-content .brand-other .box-title .desc {
  max-width: 460px;
  color: #fff;
  margin: 0;
}
.home-brand .brand-content .brand-other .box--btn .btn-links {
  color: #fff;
  border-bottom: 1px solid #fff;
}
@media screen and (max-width: 1400px) {
  .home-brand {
    padding: 65px 10px;
  }
  .home-brand .brand-content .brand-other .brand--name {
    font-size: 20px;
  }
  .home-brand .brand-content .brand-other .box-title .title {
    color: #fff;
    margin-bottom: 30px;
  }
}
@media screen and (max-width: 991px) {
  .home-brand {
    padding: 60px 10px;
  }
  .home-brand .brand-content {
    padding: 30px;
    min-height: auto;
  }
  .home-brand .brand-content .brand-other .brand--name {
    font-size: 18px;
  }
}

.home-chart {
  padding: 90px 10px 40px;
  width: 100%;
}
.home-chart .container {
  max-width: 1444px;
}
.home-chart .chart--table---title {
  color: #7C0410;
  font-size: 20px;
  font-style: normal;
  font-weight: 900;
  line-height: normal;
  margin-bottom: 20px;
  padding-left: 26px;
}
.home-chart .chart--table {
  display: flex;
  width: 100%;
  padding-left: 26px;
  gap: 33px;
  justify-content: space-between;
  flex-wrap: wrap;
}
.home-chart .chart--table .account-content .nav .nav-item .nav-link {
  color: rgba(79, 79, 79, 0.2);
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}
.home-chart .chart--table .account-content .nav .nav-item .nav-link.active {
  color: rgba(79, 79, 79, 0.7);
}
.home-chart .chart--table ._left {
  flex: 1;
  width: 100%;
}
.home-chart .chart--table ._right {
  width: 40%;
  max-width: 581px;
  background: linear-gradient(180deg, #EDD0A7 -21.8%, #FFF 106.02%);
  padding: 24px 25px 44px;
}
.home-chart .chart--table .pricegold--quantity .quantity--title {
  color: rgba(79, 79, 79, 0.7);
  font-size: 16px;
  margin-bottom: 12px;
}
.home-chart .chart--table .pricegold--quantity .--note {
  color: rgba(79, 79, 79, 0.7);
  font-size: 15px;
  font-style: normal;
  line-height: normal;
  letter-spacing: -0.6px;
  padding-top: 10px;
}
.home-chart .chart--table .pricegold--quantity .quantity--total {
  display: flex;
  width: 100%;
  height: 61px;
  display: flex;
  gap: 5px;
  line-height: 61px;
  border-bottom: 1px solid #D1B181;
  color: rgba(79, 79, 79, 0.7);
  font-size: 20px;
}
.home-chart .chart--table .pricegold--quantity .quantity--total .number {
  flex-shrink: 1;
  width: 100%;
}
.home-chart .chart--table .pricegold--quantity .quantity--total span {
  flex-shrink: 0;
}
.home-chart .chart--table .pricegold--input {
  max-width: 100%;
}
.home-chart .chart--table .pricegold--input select {
  width: 100%;
  height: 61px;
  color: rgba(79, 79, 79, 0.7);
  font-size: 20px;
}
.home-chart .chart--table .chart--table--btn {
  display: flex;
  align-items: center;
  gap: 10px;
  justify-content: space-between;
}
.home-chart .chart--table .chart--table--btn .txt {
  color: rgba(79, 79, 79, 0.7);
  font-size: 19.197px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}
.home-chart .chart--table .chart--table--btn .form--btn {
  flex-shrink: 0;
}
.home-chart .chart--table .chart--table--btn .form--btn .storesystem--btn {
  width: 191px;
}
@media screen and (max-width: 1200px) {
  .home-chart .chart--table .account-content .nav .nav-item .nav-link {
    font-size: 20px;
  }
  .home-chart .chart--table ._left {
    flex: none;
    width: 100%;
  }
  .home-chart .chart--table ._right {
    width: 100%;
    max-width: 100%;
    padding: 24px 25px;
  }
  .home-chart .chart--table .pricegold--quantity .--note {
    font-size: 14px;
  }
  .home-chart .chart--table .pricegold--quantity .quantity--total {
    height: 56px;
    line-height: 56px;
    font-size: 18px;
  }
  .home-chart .chart--table .pricegold--input select {
    height: 56px;
    font-size: 18px;
  }
  .home-chart .chart--table .chart--table--btn .txt {
    font-size: 16px;
  }
}
@media screen and (max-width: 574px) {
  .home-chart .chart--table .pricegold--input select {
    height: 56px;
    font-size: 18px;
  }
  .home-chart .chart--table .chart--table--btn {
    flex-direction: column;
    align-items: center;
  }
}

.chart--table-box {
  width: 100%;
  border: 0;
  border-spacing: 0;
}
.chart--table-box thead tr {
  border-top: 1px solid #EDD0A7;
}
.chart--table-box thead tr th {
  padding: 30px 0 7px;
}
.chart--table-box tr {
  border-top: 1px solid rgba(79, 79, 79, 0.2);
}
.chart--table-box tr td {
  padding: 19px 0;
  color: rgba(79, 79, 79, 0.7);
  font-size: 18px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}
.chart--table-box tr td .number {
  display: flex;
  align-items: center;
  gap: 10px;
}
.chart--table-box .table--footer {
  display: flex;
  align-items: start;
  gap: 10px;
  justify-content: space-between;
}
.chart--table-box .table--footer > * {
  color: rgba(79, 79, 79, 0.5);
  font-size: 18px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}
.chart--table-box .table--footer p {
  margin: 0;
}
@media screen and (max-width: 991px) {
  .chart--table-box thead tr th {
    padding: 20px 0 7px;
  }
  .chart--table-box tr td {
    font-size: 14px;
  }
  .chart--table-box .table--footer > * {
    font-size: 14px;
  }
}

.chart--fieldset {
  width: 100%;
  border: 1px solid #D5D5D5;
  padding: 43px 42px 45px 65px;
  position: relative;
  margin-bottom: 48px;
}
.chart--fieldset .fieldset--title {
  background-color: #fff;
  padding: 0 10px;
  position: absolute;
  top: -30px;
  left: 0;
  right: 0;
  margin: auto;
  color: #7C0410;
  font-size: 36px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  width: -moz-max-content;
  width: max-content;
}
.chart--fieldset .chart--fieldset---list {
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
}
.chart--fieldset .chart--fieldset---list .--title {
  color: rgba(79, 79, 79, 0.7);
  font-size: 20px;
  font-style: normal;
  font-weight: 900;
  line-height: normal;
  margin-bottom: 20px;
}
.chart--fieldset .chart--fieldset---list .price--discount {
  display: flex;
  flex-wrap: wrap;
  gap: 10px 30px;
  align-items: center;
  margin-bottom: 10px;
}
.chart--fieldset .chart--fieldset---list .price--discount .--number {
  color: rgba(79, 79, 79, 0.7);
  font-size: 29.351px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}
.chart--fieldset .chart--fieldset---list .price--discount .--number--discount {
  display: flex;
  gap: 9px;
  color: #EDD0A7;
  font-size: 17.122px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  align-items: center;
}
.chart--fieldset .chart--fieldset---list ._left {
  width: 100%;
  flex: 1;
  max-width: 839px;
}
.chart--fieldset .chart--fieldset---list ._right {
  width: 401px;
}
.chart--fieldset .chart--fieldset---list ._right .pricegold--input {
  max-width: 100%;
  margin-bottom: 50px;
}
.chart--fieldset .chart--fieldset---list ._right .pricegold--input select {
  height: 64px;
  font-size: 20px;
  color: rgba(79, 79, 79, 0.7);
}
.chart--fieldset .chart--fieldset---list .chart--fieldset---box {
  padding: 0 10px;
}
.chart--fieldset .chart--fieldset---list .chart--fieldset---box .chart--fieldset---check {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: 25px 60px;
  margin-bottom: 70px;
}
.chart--fieldset .chart--fieldset---list .chart--fieldset---box .chart--fieldset---check .--items {
  width: calc(33.3333% - 40px);
}
.chart--fieldset .chart--fieldset---list .chart--fieldset---box .chart--fieldset---check .--items label {
  border-bottom: 1px solid transparent;
  color: #4F4F4F;
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  padding-right: 10px;
  white-space: nowrap;
}
.chart--fieldset .chart--fieldset---list .chart--fieldset---box .chart--fieldset---check .--items input:checked ~ label {
  border-bottom: 1px solid #AE8751;
}
.chart--fieldset .chart--fieldset---list .chart--fieldset---unit {
  display: flex;
  flex-wrap: wrap;
  gap: 20px 30px;
}
.chart--fieldset .chart--fieldset---list .chart--fieldset---unit .--measure {
  flex: 1;
  width: 100%;
  color: #4F4F4F;
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}
.chart--fieldset .chart--fieldset---list .chart--fieldset---unit .--explain {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  flex-shrink: 0;
  flex-direction: column;
}
.chart--fieldset .chart--fieldset---list .chart--fieldset---unit .--explain > * {
  display: flex;
  gap: 12px;
  align-items: center;
  color: #4F4F4F;
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}
.chart--fieldset .chart--fieldset---list .chart--fieldset---unit .--explain > *::before {
  content: "";
  display: block;
  width: 48px;
  height: 10px;
}
.chart--fieldset .chart--fieldset---list .chart--fieldset---unit .--explain .--sell-out::before {
  background: #EDD0A7;
}
.chart--fieldset .chart--fieldset---list .chart--fieldset---unit .--explain .--buy-in::before {
  background: #922E38;
}
@media screen and (max-width: 1600px) {
  .chart--fieldset .fieldset--title {
    font-size: 32px;
  }
  .chart--fieldset .chart--fieldset---list .--title {
    font-size: 18px;
  }
  .chart--fieldset .chart--fieldset---list .price--discount .--number {
    font-size: 26px;
  }
  .chart--fieldset .chart--fieldset---list .price--discount .--number--discount {
    font-size: 16px;
  }
  .chart--fieldset .chart--fieldset---list ._right {
    width: 401px;
  }
  .chart--fieldset .chart--fieldset---list ._right .pricegold--input select {
    height: 60px;
    font-size: 18px;
  }
  .chart--fieldset .chart--fieldset---list .chart--fieldset---box .chart--fieldset---check {
    margin-bottom: 50px;
  }
  .chart--fieldset .chart--fieldset---list .chart--fieldset---box .chart--fieldset---check .--items {
    width: calc(33.3333% - 40px);
  }
  .chart--fieldset .chart--fieldset---list .chart--fieldset---box .chart--fieldset---check .--items label {
    font-size: 16px;
  }
}
@media screen and (max-width: 1400px) {
  .chart--fieldset {
    padding: 40px 30px;
  }
  .chart--fieldset .fieldset--title {
    font-size: 26px;
  }
  .chart--fieldset .chart--fieldset---list .price--discount .--number {
    font-size: 22px;
  }
  .chart--fieldset .chart--fieldset---list ._left {
    flex: none;
    max-width: 100%;
  }
  .chart--fieldset .chart--fieldset---list ._right {
    width: 100%;
  }
  .chart--fieldset .chart--fieldset---list ._right .pricegold--input select {
    height: 56px;
  }
  .chart--fieldset .chart--fieldset---list .chart--fieldset---box .chart--fieldset---check {
    gap: 25px 30px;
  }
  .chart--fieldset .chart--fieldset---list .chart--fieldset---box .chart--fieldset---check .--items {
    width: calc(33.3333% - 20px);
  }
}
@media screen and (max-width: 1200px) {
  .chart--fieldset .fieldset--title {
    font-size: 22px;
  }
  .chart--fieldset .chart--fieldset---list .price--discount .--number {
    font-size: 20px;
  }
}

.home-news {
  padding: 60px 10px 75px;
  width: 100%;
}
.home-news .box-title {
  margin-bottom: 55px;
}
.home-news .box-title .title {
  text-align: center;
}
@media screen and (min-width: 1600px) {
  .home-news .box-title .title {
    font-size: 36px;
    line-height: 129%;
  }
}
.home-news .newshome--list {
  width: 100%;
  margin-bottom: 85px;
  position: relative;
}
.home-news .newshome--list .producbox--panigation .swiper-pagination {
  padding-top: 30px;
}
.home-news .box--btn {
  text-align: center;
}
.home-news.home-news-mh {
  padding: 155px 10px 75px;
}
.home-news.home-news-mh .newshome--list {
  margin-bottom: 0;
}
.home-news.home-news-mh .box-title {
  margin-bottom: 155px;
}
.home-news.home-news-mh .box-title .title {
  margin-bottom: 35px;
  color: #B5751D;
}
.home-news.home-news-mh .box-title .desc {
  max-width: 675px;
  text-align: center;
  margin: 0 auto;
}
.home-news.block-exclusiveoffers .box-title .title {
  margin-bottom: 15px;
}
.home-news.block-exclusiveoffers .box-title .desc {
  max-width: 640px;
  margin: 0 auto;
}
@media screen and (max-width: 991px) {
  .home-news {
    padding: 40px 10px;
  }
  .home-news .box-title {
    margin-bottom: 30px;
  }
  .home-news.home-news-mh {
    padding: 40px 10px;
  }
  .home-news.home-news-mh .box-title {
    margin-bottom: 60px;
  }
  .home-news.home-news-mh .box-title .title {
    margin-bottom: 25px;
  }
}

.home-storesystem {
  padding: 75px 0 60px;
  width: 100%;
  overflow: hidden;
}
.home-storesystem .box-title {
  margin-bottom: 60px;
}
.home-storesystem .box-title .title {
  text-align: center;
}
@media screen and (min-width: 1600px) {
  .home-storesystem .box-title .title {
    font-size: 36px;
    line-height: 129%;
  }
}
.home-storesystem .storesystem--list {
  position: relative;
  width: 90%;
  max-width: 1480px;
  margin: 0 auto;
}
.home-storesystem .storesystem--list .swiper {
  overflow: visible;
}
@media screen and (max-width: 991px) {
  .home-storesystem {
    padding: 40px 10px;
  }
  .home-storesystem .box-title {
    margin-bottom: 30px;
  }
}

.home-policy {
  padding: 30px 0 299px;
  width: 100%;
}
.home-policy .row {
  gap: 30px 0;
}
@media screen and (max-width: 991px) {
  .home-policy {
    padding: 30px 0;
  }
}

.policy--items {
  width: 100%;
}
.policy--items .img {
  width: 100%;
  margin-bottom: 23px;
  text-align: center;
}
.policy--items .img img {
  max-width: 100%;
}
.policy--items .policy--body {
  width: 100%;
}
.policy--items .policy--body .title {
  color: #7C0410;
  text-align: center;
  font-size: 20px;
  font-style: normal;
  line-height: 150%;
  text-transform: capitalize;
  margin: 0 0 8px;
}
.policy--items .policy--body .title a {
  color: inherit;
}
.policy--items .policy--body .desc {
  color: #4F4F4F;
  text-align: center;
  font-size: 16px;
  font-style: normal;
  line-height: 130%;
  margin-bottom: 8px;
}
.policy--items .policy--body .desc p:last-child {
  margin-bottom: 0;
}
.policy--items .policy--body .box--btn {
  width: 100%;
  text-align: center;
}
.policy--items .policy--body .btn-links {
  font-size: 16px;
}
@media screen and (max-width: 991px) {
  .policy--items .policy--body .title {
    font-size: 18px;
  }
}

.storesystem--items {
  display: block;
  width: 100%;
}
.storesystem--items .img {
  width: 100%;
  padding-top: 100%;
  overflow: hidden;
  position: relative;
}
.storesystem--items .img img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
  position: absolute;
  top: 0;
  left: 0;
  transition: all 0.3s;
}
.storesystem--items .img::after {
  background: linear-gradient(0deg, rgba(119, 6, 17, 0.7) 0%, rgba(119, 6, 17, 0.7) 100%);
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  transition: all 0.3s;
  z-index: 2;
  opacity: 0;
  visibility: hidden;
}
.storesystem--items .storesystem--body {
  width: 100%;
  transition: all 0.3s;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;
  justify-content: space-between;
  align-items: center;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 5;
  padding: 73px 27px 55px;
  opacity: 0;
  visibility: hidden;
}
.storesystem--items .storesystem--body .storesystem--base {
  width: 100%;
}
.storesystem--items .storesystem--body .storesystem--base .name {
  color: #FFF;
  text-align: center;
  font-size: 36px;
  font-style: normal;
  font-weight: 700;
  line-height: 129%;
  text-transform: capitalize;
  display: block;
  width: 100%;
  margin-bottom: 16px;
}
.storesystem--items .storesystem--body .storesystem--base .--adress {
  color: #D1B181;
  text-align: center;
  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  line-height: 129%;
  text-transform: capitalize;
}
.storesystem--items:hover .img img {
  transform: scale(1.2);
}
.storesystem--items:hover .img::after {
  opacity: 1;
  visibility: visible;
}
.storesystem--items:hover .storesystem--body {
  opacity: 1;
  visibility: visible;
}
@media screen and (max-width: 991px) {
  .storesystem--items .storesystem--body {
    position: unset;
    padding: 20px 0 0;
    opacity: 1;
    visibility: visible;
  }
  .storesystem--items .storesystem--body .storesystem--base .name {
    font-size: 22px;
    margin-bottom: 10px;
    color: #4F4F4F;
  }
}

.storesystem--btn {
  background: #EDD0A7;
  color: #4F4F4F;
  text-align: center;
  font-size: 20px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  width: 100%;
  max-width: 312px;
  padding: 17px;
  border: 0;
  box-shadow: none;
  display: inline-block;
}
.storesystem--btn:hover {
  color: #7C0410;
}
@media screen and (max-width: 991px) {
  .storesystem--btn {
    font-size: 16px;
    padding: 10px;
  }
}

.block-form {
  width: 100%;
  padding: 110px 10px;
  background: #FFFCF3;
}
.block-form.register--club .form--list ._left {
  width: 50.6%;
}
.block-form.register--club .form--list ._right {
  width: 49.4%;
}
.block-form.register--club .form--list ._right .box-title {
  margin-bottom: 22px;
}
.block-form.register--club .form--list ._right .box-title .title {
  color: #7C0410;
}
.block-form.register--club .box--verify .verify-col:first-child {
  flex: auto;
}
.block-form.register--club .box--verify .verify-col + .verify-col {
  padding-left: 0;
  padding-right: 28px;
}
.block-form.register--club .check--agree {
  padding-top: 60px;
}
.block-form.register--club .form--content {
  margin-bottom: 88px;
}
@media screen and (max-width: 1600px) {
  .block-form {
    padding: 80px 10px;
  }
}
@media screen and (max-width: 991px) {
  .block-form {
    padding: 40px 10px;
  }
  .block-form.register--club .form--list ._right {
    width: 100%;
  }
  .block-form.register--club .box--verify .verify-col + .verify-col {
    padding-right: 20px;
  }
  .block-form.register--club .check--agree {
    padding-top: 40px;
  }
  .block-form.register--club .form--content {
    margin-bottom: 50px;
  }
}

.form--list {
  display: flex;
  flex-wrap: wrap;
  gap: 30px 0;
}
.form--list ._left {
  width: 50%;
  overflow: hidden;
}
.form--list ._left img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
}
.form--list ._right {
  width: 50%;
  padding: 0 59px 0 105px;
}
.form--list ._right .box-title {
  margin-bottom: 30px;
}
.form--list ._right .box-title .title {
  line-height: 129%;
  color: #000000;
  text-align: left;
}
@media screen and (min-width: 1600px) {
  .form--list ._right .box-title .title {
    font-size: 36px;
  }
}
@media screen and (max-width: 1400px) {
  .form--list ._right {
    padding: 0 0px 0 50px;
  }
}
@media screen and (max-width: 991px) {
  .form--list ._left {
    width: 100%;
    display: none;
  }
  .form--list ._right {
    width: 100%;
    padding: 0;
  }
}

.form--content {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: 43px 32px;
  margin-bottom: 56px;
}
.form--content .form--col {
  width: calc(50% - 16px);
}
.form--content .form--col12 {
  width: 100%;
}
.form--content .form--row {
  width: 100%;
}
.form--content .form--row .input--control {
  width: 100%;
  height: 64px;
  border: 0;
  border-bottom: 1px solid #D1B181;
  color: rgb(0, 0, 0);
  font-size: 20px;
  font-weight: 400;
  padding: 0;
  background-color: transparent;
}
.form--content .form--row .input--control::-moz-placeholder {
  color: rgba(0, 0, 0, 0.5);
}
.form--content .form--row .input--control::placeholder {
  color: rgba(0, 0, 0, 0.5);
}
.form--content .form--row textarea.input--control {
  padding: 22px 0;
  height: 80px;
}
.form--content .form--row select.input--control {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  background-image: url("../images/ic-9.png");
  background-repeat: no-repeat;
  background-position: right 14px center;
  padding-right: 35px;
}
.form--content .form--row .txt {
  color: #4F4F4F;
  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: 120%;
  opacity: 0.8;
}
@media screen and (max-width: 991px) {
  .form--content .form--row .input--control {
    font-size: 18px;
  }
  .form--content .form--row .txt {
    font-size: 18px;
  }
}
@media screen and (max-width: 767px) {
  .form--content .form--row .input--control {
    font-size: 16px;
  }
  .form--content .form--row .txt {
    font-size: 16px;
  }
  .form--content .form--col {
    width: 100%;
  }
}/*# sourceMappingURL=style.css.map */