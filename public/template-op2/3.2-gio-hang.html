<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title><PERSON><PERSON><PERSON> thieu</title>
    <!-- mmenu css -->
    <link rel="stylesheet" href="libs/mmenu/jquery.mmenu.all.css">
    <link rel="stylesheet" href="libs/scrollbar/jquery.scrollbar.css">
    <!-- animate css-->
    <link rel="stylesheet" href="libs/animate/animate.min.css">
    <link rel="stylesheet" href="libs/aos/aos.css">
    <!-- swiper css-->
    <link rel="stylesheet" href="libs/swiper/swiper-bundle.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Barlow:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/style.css">
</head>

<body>
    <div id="app">
        <myheader></myheader>
        <main class="">
            <div class="row-breadcrumb">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item">
                                    <a href="#">
                                            Giỏ hàng
                                        </a>
                                </li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
            <div class="cart-main">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="cart--other">
                                <div class="box-title">
                                    <h2 class="title">
                                        Giỏ hàng
                                    </h2>
                                </div>
                                <div class="cart--list">
                                    <div class="_left">
                                        <form action="">
                                            <div class="cart--box">
                                                <div class="cart--top">
                                                    <div class="img">
                                                        <img src="images/img-vdd.png" alt="">
                                                    </div>
                                                    <div class="cart--top---body">
                                                        <div class="--name">
                                                            Dây chuyền khởi sắc thịnh hoàng
                                                        </div>
                                                        <div class="--code">
                                                            VCARA42100
                                                        </div>
                                                        <div class="cart--top---select">
                                                            <div class="">
                                                                <div class="txt">
                                                                    Size:
                                                                </div>
                                                                <div class="pricegold--input">
                                                                    <select name="" id="">
                                                                <option value="">
                                                                    40
                                                                </option>
                                                            </select>
                                                                </div>
                                                            </div>

                                                            <div class="">
                                                                <div class="txt">
                                                                    Số lượng:
                                                                </div>
                                                                <div class="pricegold--input">
                                                                    <div class="inp-sl">
                                                                        <span class="minus">
                                                                    <svg xmlns="http://www.w3.org/2000/svg" width="6" height="2" viewBox="0 0 6 2" fill="none">
  <path d="M0.679688 0.792969H5.90429V1.98599H0.679688V0.792969Z" fill="#4F4F4F"/>
</svg>
                                                                </span>
                                                                        <input type="text" class="num" value="01">
                                                                        <span class="plus">
                                                                    <svg xmlns="http://www.w3.org/2000/svg" width="9" height="9" viewBox="0 0 9 9" fill="none">
<path d="M0 3.82589H3.82589V0H5.01891V3.82589H8.8448V5.01891H5.01891V8.8448H3.82589V5.01891H0V3.82589Z" fill="#4F4F4F"/>
</svg>
                                                                </span>
                                                                    </div>
                                                                </div>
                                                            </div>

                                                        </div>
                                                        <div class="cart--top---del">
                                                            <button type="submit" class="btn-del">
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M6.774 6.4L7.586 20.048C7.59823 20.2514 7.68761 20.4425 7.8359 20.5822C7.9842 20.722 8.18023 20.7999 8.384 20.8H15.616C15.8198 20.7999 16.0158 20.722 16.1641 20.5822C16.3124 20.4425 16.4018 20.2514 16.414 20.048L17.226 6.4H6.774ZM18.429 6.4L17.612 20.119C17.5817 20.6278 17.3582 21.1059 16.9872 21.4555C16.6162 21.8051 16.1258 21.9999 15.616 22H8.384C7.87425 21.9999 7.38377 21.8051 7.01279 21.4555C6.64182 21.1059 6.41833 20.6278 6.388 20.119L5.571 6.4H3.5V5.7C3.5 5.56739 3.55268 5.44021 3.64645 5.34645C3.74021 5.25268 3.86739 5.2 4 5.2H20C20.1326 5.2 20.2598 5.25268 20.3536 5.34645C20.4473 5.44021 20.5 5.56739 20.5 5.7V6.4H18.429ZM14 3C14.1326 3 14.2598 3.05268 14.3536 3.14645C14.4473 3.24021 14.5 3.36739 14.5 3.5V4.2H9.5V3.5C9.5 3.36739 9.55268 3.24021 9.64645 3.14645C9.74021 3.05268 9.86739 3 10 3H14ZM9.5 9H10.7L11.2 18H10L9.5 9ZM13.3 9H14.5L14 18H12.8L13.3 9Z" fill="#7C0410"/>
</svg>Xoá
                                                        </button>
                                                            <div class="--price">
                                                                29.000.000
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="cart--infor">
                                                    <div class="cart--infor---title">
                                                        Tóm tắt đơn hàng
                                                    </div>
                                                    <div class="cart--infor---body">
                                                        <div class="discount--code">
                                                            <label for="">
                                                                Mã giảm giá
                                                            </label>
                                                            <div class="discount--code---input">
                                                                <input type="text" placeholder="">
                                                                <button type="submit" class="storesystem--btn">
                                                            Áp dụng
                                                        </button>
                                                            </div>
                                                        </div>
                                                        <div class="cart--infor---list">
                                                            <div class="cart--infor---row">
                                                                <div class="txt">
                                                                    Tạm tính
                                                                </div>
                                                                <div class="number">
                                                                    29.000.000
                                                                </div>
                                                            </div>
                                                            <div class="cart--infor---row">
                                                                <div class="txt">
                                                                    Phí giao hàng
                                                                </div>
                                                                <div class="number">
                                                                    0
                                                                </div>
                                                            </div>
                                                            <div class="cart--infor---row">
                                                                <div class="txt">
                                                                    Giảm giá
                                                                </div>
                                                                <div class="number">
                                                                    0
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="cart--total">
                                                            <div class="txt">
                                                                Tổng
                                                            </div>
                                                            <div class="number">
                                                                29.000.000
                                                                <span>
                                                                    (VND)
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                    <div class="_right">
                                        <div class="account-form ">
                                            <form action="">
                                                <div class="form--content">
                                                    <div class="form--col12">
                                                        <div class="box-title">
                                                            <h2 class="title">
                                                                Thông tin khách hàng
                                                            </h2>
                                                        </div>
                                                    </div>
                                                    <div class="form--col12">
                                                        <div class="form--row">
                                                            <input type="text" class="input--control" placeholder="Họ và Tên">
                                                        </div>
                                                    </div>
                                                    <div class="form--col12">
                                                        <div class="form--row">
                                                            <input type="text" class="input--control" placeholder="Số điện thoại">
                                                        </div>
                                                    </div>
                                                    <div class="form--col12">
                                                        <div class="form--row">
                                                            <input type="text" class="input--control" placeholder="Email">
                                                        </div>
                                                    </div>
                                                    <div class="form--col12">
                                                        <div class="form--row">
                                                            <select name="" id="" class="input--control">
                                                                    <option value="">
                                                                        Hình thức nhận hàng
                                                                    </option>
                                                                </select>
                                                        </div>
                                                    </div>

                                                    <div class="form--col">
                                                        <div class="form--row">
                                                            <select name="" id="" class="input--control">
                                                                    <option value="">
                                                                        Tỉnh/ Thành phố
                                                                    </option>
                                                                </select>
                                                        </div>
                                                    </div>
                                                    <div class="form--col">
                                                        <div class="form--row">
                                                            <select name="" id="" class="input--control">
                                                                    <option value="">
                                                                        Quận/ Huyện
                                                                    </option>
                                                                </select>
                                                        </div>
                                                    </div>
                                                    <div class="form--col">
                                                        <div class="form--row">
                                                            <select name="" id="" class="input--control">
                                                                    <option value="">
                                                                        Phường/ Xã
                                                                    </option>
                                                                </select>
                                                        </div>
                                                    </div>
                                                    <div class="form--col">
                                                        <div class="form--row">
                                                            <input type="text" class="input--control" placeholder="Địa chỉ">
                                                        </div>
                                                    </div>
                                                    <div class="form--col12">
                                                        <div class="form--row">
                                                            <textarea name="" id="" class="input--control" placeholder="Ghi chú"></textarea>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="form--btn">
                                                    <a href="javascript:void(0);" class="storesystem--btn" onclick="showModalBook();">
                                                            Xác nhận
                                                    </a>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="collection-prodsp product-similar">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="box-title">
                                <h2 class="title">
                                    Có thể bạn sẽ thích
                                </h2>
                            </div>
                            <div class="prodsp--list">
                                <div class="swiper prodsp--slide">
                                    <div class="swiper-wrapper">
                                        <div class="swiper-slide" v-for="n in 5">
                                            <div class="product--items">
                                                <a href="" class="img">
                                                    <img src="images/img-prod3.png" alt="">
                                                </a>
                                                <div class="product--body">
                                                    <h3 class="title">
                                                        <a href="">
                                                                Đồng Kim Gia Bảo - Tùng
                                                            </a>
                                                    </h3>
                                                    <div class="gold--infor">
                                                        1 chỉ | Vàng 24K (999.9)
                                                    </div>
                                                    <div class="price">
                                                        13.000.000
                                                    </div>
                                                    <button class="add--cart" type="submit">
                                                            <svg
                                                                xmlns="http://www.w3.org/2000/svg"
                                                                width="43"
                                                                height="42"
                                                                viewBox="0 0 43 42"
                                                                fill="none"
                                                            >
                                                                <path
                                                                    d="M32.2422 40.833H0.584914V14.2905H32.2422"
                                                                    stroke="#AE8751"
                                                                    stroke-width="1.17444"
                                                                    stroke-miterlimit="10"
                                                                />
                                                                <path
                                                                    d="M8.87572 16.6765V7.37212C8.87572 3.69604 12.243 0.68457 16.4301 0.68457C20.6171 0.68457 23.9844 3.67527 23.9844 7.37212V16.6765"
                                                                    stroke="#AE8751"
                                                                    stroke-width="1.17444"
                                                                    stroke-miterlimit="10"
                                                                />
                                                                <path d="M31.9793 37.1113C26.3145 37.1113 21.7222 32.8061 21.7222 27.4953C21.7222 22.1846 26.3145 17.8794 31.9793 17.8794C37.6441 17.8794 42.2363 22.1846 42.2363 27.4953C42.2363 32.8061 37.6441 37.1113 31.9793 37.1113Z" fill="#AE8751"/>
                                                                <path
                                                                    d="M31.9961 20.7104V34.2309"
                                                                    stroke="white"
                                                                    stroke-width="1.17444"
                                                                    stroke-miterlimit="10"
                                                                />
                                                                <path
                                                                    d="M24.7793 27.478H39.2012"
                                                                    stroke="white"
                                                                    stroke-width="1.17444"
                                                                    stroke-miterlimit="10"
                                                                />
                                                            </svg>
                                                        </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="producbox--btn">
                                    <div class="swiper-button-next swiper-button-next-prodsp">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="10" height="16" viewBox="0 0 10 16" fill="none">
                                                <path
                                                    fill-rule="evenodd"
                                                    clip-rule="evenodd"
                                                    d="M1.92383 15.209L0.675545 13.9607L6.91696 7.71929L0.675546 1.47787L1.92383 0.229586L8.78933 7.09509C8.78937 7.09513 8.78939 7.09514 8.16524 7.71929L8.78933 7.09509L9.41353 7.71929L1.92383 15.209Z"
                                                    fill="#4F4F4F"
                                                />
                                            </svg>
                                    </div>
                                    <div class="swiper-button-prev swiper-button-prev-prodsp">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="10" height="16" viewBox="0 0 10 16" fill="none">
                                                <path
                                                    fill-rule="evenodd"
                                                    clip-rule="evenodd"
                                                    d="M8.07617 15.209L9.32445 13.9607L3.08304 7.71929L9.32445 1.47787L8.07617 0.229586L1.21067 7.09509C1.21063 7.09513 1.21061 7.09514 1.83476 7.71929L1.21067 7.09509L0.586473 7.71929L8.07617 15.209Z"
                                                    fill="#4F4F4F"
                                                />
                                            </svg>
                                    </div>
                                </div>
                                <!-- <div class="producbox--panigation">
                                    <div class="swiper-pagination swiper-pagination-prodsp"></div>
                                </div> -->
                                <!-- <div class="box--btn">
                                    <a href="" class="btn-links">
                                                Xem toàn bộ sản phẩm
                                            </a>
                                </div> -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
        <myfooter></myfooter>
    </div>
    <!-- Đặt lịch đến cửa hàng -->
    <div class="modal fade modal--book" id="modal--book" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Đóng">
                    <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32" fill="none">
<path d="M17.8643 16L32 30.1562L30.1562 32L16 17.8643L1.84379 32L0 30.1562L14.1357 16L0 1.84379L1.84379 0L16 14.1357L30.1562 0L32 1.84379L17.8643 16Z" fill="#C3C3C3"/>
</svg>
                </button>
                <div class="modalbook-content">
                    <h3 class="title">
                        Đặt lịch đến cửa hàng
                    </h3>
                    <form action="">
                        <div class="form--content">
                            <div class="form--col12">
                                <div class="form--row">
                                    <input type="text" class="input--control" placeholder="Họ và Tên">
                                </div>
                            </div>
                            <div class="form--col12">
                                <div class="form--row">
                                    <select name="" id="" class="input--control">
                                                                    <option value="">
                                                                       Cửa hàng
                                                                    </option>
                                                                </select>
                                </div>
                            </div>
                            <div class="form--col12">
                                <div class="form--row">
                                    <select name="" id="" class="input--control">
                                                                    <option value="">
                                                                        Giờ hẹn
                                                                    </option>
                                                                </select>
                                </div>
                            </div>
                            <div class="form--col12">
                                <div class="form--row">
                                    <select name="" id="" class="input--control">
                                                                    <option value="">
                                                                       Dịch vụ
                                                                    </option>
                                                                </select>
                                </div>
                            </div>
                        </div>
                        <div class="block--btn">
                            <a href="" class="btn--cancel">
Huỷ
                            </a>
                            <a href="javascript:void(0)" onclick="showModalBooksuccess();" class="btn--book">
Đặt lịch
                            </a>
                        </div>
                    </form>
                </div>

            </div>
        </div>
    </div>
    <!-- Đặt lịch thành công -->
    <div class="modal fade modal--book" id="modal--book--success" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Đóng">
                    <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32" fill="none">
<path d="M17.8643 16L32 30.1562L30.1562 32L16 17.8643L1.84379 32L0 30.1562L14.1357 16L0 1.84379L1.84379 0L16 14.1357L30.1562 0L32 1.84379L17.8643 16Z" fill="#C3C3C3"/>
</svg>
                </button>
                <div class="modalbook-content--success">
                    <div class="img">
                        <img src="images/img-success.png" alt="">
                    </div>
                    <h3 class="title">
                        Đặt lịch thành công
                    </h3>
                    <div class="desc">
                        Nhân viên Bảo Tín Mạnh Hải sẽ gọi điện xác nhận yêu cầu đặt lịch của bạn và sắp xếp lịch hẹn của bạn nhanh nhất
                    </div>
                </div>

            </div>
        </div>
    </div>
    <script src="js/vue.js"></script>
    <script src="js/httpvueloader.js"></script>
    <script src="js/jquery-3.4.1.js"></script>
    <script src="libs/bootstrap/bootstrap.bundle.min.js"></script>
    <script src="libs/lazyload/jquery.lazy.min.js"></script>
    <script src="libs/scrollbar/jquery.scrollbar.min.js"></script>
    <!-- swiper js-->
    <script src="libs/swiper/swiper-bundle.min.js"></script>
    <script src="libs/mansonry/masonry.pkgd.min.js"></script>
    <!-- mmenu -->
    <script src="libs/mmenu/jquery.mmenu.all.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script type="text/javascript" src="libs/aos/aos.js"></script>
    <script src="js/main.js"></script>
    <script></script>
    <script>
        var app = new Vue({
            el: "#app",
            components: {
                myheader: httpVueLoader("vue_components/header.vue"),
                myfooter: httpVueLoader("vue_components/footer.vue"),
                partner: httpVueLoader("vue_components/partner.vue"),
                locationair: httpVueLoader("vue_components/location-air.vue"),
            },
            data: {

            },

            mounted() {
                this.$nextTick(() => {
                    $(window).bind('load', function() {
                        AOS.init({
                            startEvent: 'load',
                        });
                        var swiper = new Swiper(".mySwiper", {
                            loop: false,
                            spaceBetween: 6,
                            slidesPerView: 4,
                            // freeMode: true,
                            direction: getDirection(),
                            watchSlidesProgress: true,
                            // navigation: {
                            //   nextEl: ".custom-nav-prev",
                            //   prevEl: ".custom-nav-next",
                            // },
                            breakpoints: {
                                310: {
                                    spaceBetween: 10,
                                    slidesPerView: 3,
                                },
                                400: {
                                    spaceBetween: 10,
                                    slidesPerView: 4,
                                },

                            },
                        });

                        function getDirection() {
                            var windowWidth = window.innerWidth;
                            var direction = window.innerWidth <= 1279 ? 'horizontal' : 'vertical';

                            return direction;
                        }
                        var swiper2 = new Swiper(".mySwiper2", {
                            loop: false,
                            spaceBetween: 10,
                            effect: 'fade',
                            autoplay: {
                                delay: 5000,
                                disableOnInteraction: false,
                            },
                            navigation: {
                                nextEl: ".swiper-button-next",
                                prevEl: ".swiper-button-prev",
                            },
                            thumbs: {
                                swiper: swiper,
                            },
                        });

                    });
                });
            },
        });
    </script>
</body>

</html>