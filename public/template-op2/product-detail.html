<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title><PERSON><PERSON><PERSON> thieu</title>
    <!-- mmenu css -->
    <link rel="stylesheet" href="libs/mmenu/jquery.mmenu.all.css">
    <link rel="stylesheet" href="libs/scrollbar/jquery.scrollbar.css">
    <!-- animate css-->
    <link rel="stylesheet" href="libs/animate/animate.min.css">
    <link rel="stylesheet" href="libs/aos/aos.css">
    <!-- swiper css-->
    <link rel="stylesheet" href="libs/swiper/swiper-bundle.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Barlow:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/style.css">
</head>

<body>
    <div id="app">
        <myheader></myheader>
        <main class="">
            <div class="row-breadcrumb">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item">
                                    <a href="#">
                                            Bộ sưu tập son sắc
                                        </a>
                                </li>
                                <li class="breadcrumb-item">
                                    <a href="">Vòng cổ</a>
                                </li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
            <section class="block-product-detail">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="detail-prdt">
                                <div class="box-img-detail">
                                    <div thumbsSlider="" class="swiper mySwiper">
                                        <div class="swiper-wrapper">
                                            <div class="swiper-slide" v-for="n in 4">
                                                <img src="images/img-prod2.png" alt="">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="custom-nav">
                                        <div class="swiper-button-next custom-nav-next">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="10" height="16" viewBox="0 0 10 16" fill="none">
                                                    <path
                                                        fill-rule="evenodd"
                                                        clip-rule="evenodd"
                                                        d="M1.92383 15.209L0.675545 13.9607L6.91696 7.71929L0.675546 1.47787L1.92383 0.229586L8.78933 7.09509C8.78937 7.09513 8.78939 7.09514 8.16524 7.71929L8.78933 7.09509L9.41353 7.71929L1.92383 15.209Z"
                                                        fill="#4F4F4F"
                                                    />
                                                </svg>
                                        </div>
                                        <div class="swiper-button-prev custom-nav-prev">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="10" height="16" viewBox="0 0 10 16" fill="none">
                                                    <path
                                                        fill-rule="evenodd"
                                                        clip-rule="evenodd"
                                                        d="M8.07617 15.209L9.32445 13.9607L3.08304 7.71929L9.32445 1.47787L8.07617 0.229586L1.21067 7.09509C1.21063 7.09513 1.21061 7.09514 1.83476 7.71929L1.21067 7.09509L0.586473 7.71929L8.07617 15.209Z"
                                                        fill="#4F4F4F"
                                                    />
                                                </svg>
                                        </div>
                                    </div>
                                    <div style="--swiper-navigation-color: #fff; --swiper-pagination-color: #fff" class="swiper mySwiper2">
                                        <div class="swiper-wrapper">
                                            <div class="swiper-slide" v-for="n in 4">
                                                <a href="images/img-prdt-detail.jpg" data-fancybox="imgdetail" class="img-detail-prdt">
                                                    <img src="images/img-prod2.png" alt="">
                                                    <!-- <div class="product-img--main" data-scale="1.6" data-image="images/img-prdt-detail.jpg"></div> -->
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="info-detail">
                                    <h1 class="name">
                                        Dây chuyền khởi sắc thịnh hoàng
                                    </h1>
                                    <div class="sub_name">
                                        Chuẩn Vàng 18K, khảm xà cừ
                                    </div>
                                    <div class="prdt_price">
                                        29.000.000
                                    </div>
                                    <div class="product--code">
                                        Mã sản phẩm: VCARA42100
                                    </div>
                                    <div class="row-choose-opt">
                                        <div class="choose-size">
                                            <span class="lbl">
                                                    Size:
                                                </span>
                                            <div class="list">
                                                <label class="form-check form-check-inline">
                                                        <input
                                                            class="form-check-input"
                                                            type="radio"
                                                            name="value_size"
                                                            id=""
                                                            value="option2"
                                                        >
                                                        <span class="form-check-checkmark">38</span>
                                                    </label>
                                                <label class="form-check form-check-inline">
                                                        <input
                                                            class="form-check-input"
                                                            type="radio"
                                                            name="value_size"
                                                            id=""
                                                            value="option3"
                                                        >
                                                        <span class="form-check-checkmark">40</span>
                                                    </label>
                                                <label class="form-check form-check-inline">
                                                        <input
                                                            class="form-check-input"
                                                            type="radio"
                                                            name="value_size"
                                                            id=""
                                                            value="option4"
                                                        >
                                                        <span class="form-check-checkmark">46</span>
                                                    </label>
                                                <label class="form-check form-check-inline">
                                                        <input
                                                            class="form-check-input"
                                                            type="radio"
                                                            name="value_size"
                                                            id=""
                                                            value="option5"
                                                        >
                                                        <span class="form-check-checkmark">48</span>
                                                    </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row-qty-btn-act">
                                        <div class="pricegold--input">
                                            <div class="inp-sl">
                                                <span class="minus">
                                                        <svg
                                                            xmlns="http://www.w3.org/2000/svg"
                                                            width="6"
                                                            height="2"
                                                            viewBox="0 0 6 2"
                                                            fill="none"
                                                        >
                                                            <path d="M0.679688 0.792969H5.90429V1.98599H0.679688V0.792969Z" fill="#4F4F4F"/>
                                                        </svg>
                                                    </span>
                                                <input type="text" class="num" value="01">
                                                <span class="plus">
                                                        <svg
                                                            xmlns="http://www.w3.org/2000/svg"
                                                            width="9"
                                                            height="9"
                                                            viewBox="0 0 9 9"
                                                            fill="none"
                                                        >
                                                            <path d="M0 3.82589H3.82589V0H5.01891V3.82589H8.8448V5.01891H5.01891V8.8448H3.82589V5.01891H0V3.82589Z" fill="#4F4F4F"/>
                                                        </svg>
                                                    </span>
                                            </div>
                                        </div>
                                        <a href="javascript:void(0)" class="btn-addtocart">
                                                Thêm vào giỏ hàng
                                            </a>
                                    </div>
                                    <button class="storesystem--btn" type="submit">
                                            Mua ngay
                                        </button>
                                    <div class="prddetail-sp">
                                        <a href="" class="select--size">
                                                Hướng dẫn chọn size
                                            </a>
                                        <div class="sp--phone">
                                            Liên hệ tư vấn qua hotline:
                                            <a href="tel:024 2233 9999 
">
                                                    024 2233 9999
                                                </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="detail--content">
                                <div class="_items">
                                    <a class="btn" data-bs-toggle="collapse" href="#demoCollapse" role="button" aria-expanded="false" aria-controls="demoCollapse">
                                            Thông số sản phẩm
                                            <span>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="28" height="29" viewBox="0 0 28 29" fill="none">
  <path d="M0 14.7864L28 14.7864" stroke="#D5D5D5"/>
  <path d="M14 0.786377L14 28.7864" stroke="#D5D5D5"/>
</svg>
                                            </span>
                                        </a>
                                    <div class="collapse" id="demoCollapse">
                                        <div class="detail--child">
                                            <div class="detail--other">
                                                <div class="detail--style1">
                                                    <p>
                                                        Chất liệu: Vàng

                                                    </p>
                                                    <p>
                                                        Hàm lượng kim loại: 41.7
                                                    </p>
                                                    <p>
                                                        Loại đá: Không đá
                                                    </p>
                                                </div>
                                                <div class="detail--style1">
                                                    <p>
                                                        Giới tính: Nữ

                                                    </p>
                                                    <p>
                                                        Màu chất liệu: Vàng
                                                    </p>
                                                    <p>
                                                        Tên đá chủ: Không đá
                                                    </p>
                                                </div>
                                                <div class="detail--style1">
                                                    <p>
                                                        Trọng lượng: 3,5 chỉ
                                                    </p>
                                                    <p>
                                                        Đi kèm dây: Có
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="_items">
                                    <a class="btn" data-bs-toggle="collapse" href="#demoCollapse2" role="button" aria-expanded="false" aria-controls="demoCollapse2">
                                            Mô tả sản phẩm
                                            <span>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="28" height="29" viewBox="0 0 28 29" fill="none">
  <path d="M0 14.7864L28 14.7864" stroke="#D5D5D5"/>
  <path d="M14 0.786377L14 28.7864" stroke="#D5D5D5"/>
</svg>
                                            </span>
                                        </a>
                                    <div class="collapse" id="demoCollapse2">
                                        <div class="detail--child">
                                            <div class="detail--other">
                                                <div class="detail--style2">
                                                    "Dây chuyền Khởi Sắc Thịnh Hoàng" là hiện thân của sự bừng sáng, thịnh vượng và quyền quý. Được chế tác từ vàng nguyên khối với đường nét mềm mại nhưng mạnh mẽ, thiết kế mang đậm dấu ấn của một khởi đầu mới đầy khí chất và may mắn. Mỗi chi tiết trên sợi
                                                    dây chuyền là sự kết hợp hài hòa giữa nghệ thuật thủ công và thông điệp phong thủy, như một lời chúc cho sự phát đạt, hưng thịnh và tỏa sáng bền lâu.
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="_items">
                                    <a class="btn" data-bs-toggle="collapse" href="#demoCollapse3" role="button" aria-expanded="false" aria-controls="demoCollapse3">
                                           Hướng dẫn 
                                            <span>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="28" height="29" viewBox="0 0 28 29" fill="none">
  <path d="M0 14.7864L28 14.7864" stroke="#D5D5D5"/>
  <path d="M14 0.786377L14 28.7864" stroke="#D5D5D5"/>
</svg>
                                            </span>
                                        </a>
                                    <div class="collapse" id="demoCollapse3">
                                        <div class="detail--child">
                                            <div class="detail--style3">
                                                <div class="img-detail-logo">
                                                    <img src="images/logo-detail.png" alt="">
                                                </div>
                                                <div class="img-detail-title">
                                                    <span>
                                                        Hướng dẫn bảo quản sản phẩm
                                                    </span>
                                                </div>
                                                <div class="detail--child---list">
                                                    <div class="detail--child---col">
                                                        <div class="img">
                                                            <img src="images/img-fashion/img-hd1.png" alt="">
                                                        </div>
                                                        <div class="desc">
                                                            Giữ sản phẩm<br /> Tránh tiếp xúc với độ ẩm
                                                        </div>
                                                    </div>
                                                    <div class="detail--child---col">
                                                        <div class="img">
                                                            <img src="images/img-fashion/img-hd2.png" alt="">
                                                        </div>
                                                        <div class="desc">
                                                            Tránh tiếp xúc trực tiếp với nước hoa
                                                        </div>
                                                    </div>
                                                    <div class="detail--child---col">
                                                        <div class="img">
                                                            <img src="images/img-fashion/img-hd3.png" alt="">
                                                        </div>
                                                        <div class="desc">
                                                            Không mang trang sức khi vận động mạnh
                                                        </div>
                                                    </div>
                                                    <div class="__line"></div>
                                                    <div class="detail--child---col">
                                                        <div class="img">
                                                            <img src="images/img-fashion/img-hd4.png" alt="">
                                                        </div>
                                                        <div class="desc">
                                                            Không mang trang sức khi đi ngủ
                                                        </div>
                                                    </div>
                                                    <div class="detail--child---col">
                                                        <div class="img">
                                                            <img src="images/img-fashion/img-hd5.png" alt="">
                                                        </div>
                                                        <div class="desc">
                                                            Bảo quản trong túi hoặc hộp kín
                                                        </div>
                                                    </div>
                                                    <div class="detail--child---col">
                                                        <div class="img">
                                                            <img src="images/img-fashion/img-hd6.png" alt="">
                                                        </div>
                                                        <div class="desc">
                                                            Vệ sinh định kỳ tại cửa hàng
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <div class="collection-prodsp product-similar">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="box-title">
                                <h2 class="title">
                                    Sản phẩm tương tự
                                </h2>
                            </div>
                            <div class="prodsp--list">
                                <div class="swiper prodsp--slide">
                                    <div class="swiper-wrapper">
                                        <div class="swiper-slide" v-for="n in 5">
                                            <div class="product--items">
                                                <a href="" class="img">
                                                    <img src="images/img-prod3.png" alt="">
                                                </a>
                                                <div class="product--body">
                                                    <h3 class="title">
                                                        <a href="">
                                                                        Đồng Kim Gia Bảo - Tùng
                                                                    </a>
                                                    </h3>
                                                    <div class="gold--infor">
                                                        1 chỉ | Vàng 24K (999.9)
                                                    </div>
                                                    <div class="price">
                                                        13.000.000
                                                    </div>
                                                    <button class="add--cart" type="submit">
                                                                    <svg
                                                                        xmlns="http://www.w3.org/2000/svg"
                                                                        width="43"
                                                                        height="42"
                                                                        viewBox="0 0 43 42"
                                                                        fill="none"
                                                                    >
                                                                        <path
                                                                            d="M32.2422 40.833H0.584914V14.2905H32.2422"
                                                                            stroke="#AE8751"
                                                                            stroke-width="1.17444"
                                                                            stroke-miterlimit="10"
                                                                        />
                                                                        <path
                                                                            d="M8.87572 16.6765V7.37212C8.87572 3.69604 12.243 0.68457 16.4301 0.68457C20.6171 0.68457 23.9844 3.67527 23.9844 7.37212V16.6765"
                                                                            stroke="#AE8751"
                                                                            stroke-width="1.17444"
                                                                            stroke-miterlimit="10"
                                                                        />
                                                                        <path d="M31.9793 37.1113C26.3145 37.1113 21.7222 32.8061 21.7222 27.4953C21.7222 22.1846 26.3145 17.8794 31.9793 17.8794C37.6441 17.8794 42.2363 22.1846 42.2363 27.4953C42.2363 32.8061 37.6441 37.1113 31.9793 37.1113Z" fill="#AE8751"/>
                                                                        <path
                                                                            d="M31.9961 20.7104V34.2309"
                                                                            stroke="white"
                                                                            stroke-width="1.17444"
                                                                            stroke-miterlimit="10"
                                                                        />
                                                                        <path
                                                                            d="M24.7793 27.478H39.2012"
                                                                            stroke="white"
                                                                            stroke-width="1.17444"
                                                                            stroke-miterlimit="10"
                                                                        />
                                                                    </svg>
                                                                </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                                <div class="producbox--btn">
                                    <div class="swiper-button-next swiper-button-next-prodsp">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="10" height="16" viewBox="0 0 10 16" fill="none">
                                                <path
                                                    fill-rule="evenodd"
                                                    clip-rule="evenodd"
                                                    d="M1.92383 15.209L0.675545 13.9607L6.91696 7.71929L0.675546 1.47787L1.92383 0.229586L8.78933 7.09509C8.78937 7.09513 8.78939 7.09514 8.16524 7.71929L8.78933 7.09509L9.41353 7.71929L1.92383 15.209Z"
                                                    fill="#4F4F4F"
                                                />
                                            </svg>
                                    </div>
                                    <div class="swiper-button-prev swiper-button-prev-prodsp">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="10" height="16" viewBox="0 0 10 16" fill="none">
                                                <path
                                                    fill-rule="evenodd"
                                                    clip-rule="evenodd"
                                                    d="M8.07617 15.209L9.32445 13.9607L3.08304 7.71929L9.32445 1.47787L8.07617 0.229586L1.21067 7.09509C1.21063 7.09513 1.21061 7.09514 1.83476 7.71929L1.21067 7.09509L0.586473 7.71929L8.07617 15.209Z"
                                                    fill="#4F4F4F"
                                                />
                                            </svg>
                                    </div>
                                </div>
                                <!-- <div class="producbox--panigation">
                                    <div class="swiper-pagination swiper-pagination-prodsp"></div>
                                </div> -->
                                <!-- <div class="box--btn">
                                    <a href="" class="btn-links">
                                                Xem toàn bộ sản phẩm
                                            </a>
                                </div> -->

                            </div>

                        </div>
                    </div>
                </div>
            </div>
            <div class="block-weddingjewelry">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-6">
                            <div class="weddingjewelry--other">
                                <div class="weddingjewelry--logo">
                                    <img src="images/logo-mh.png" alt="">
                                </div>
                                <div class="desc">
                                    MH – thương hiệu thời trang hiện đại, tôn vinh tình yêu qua từng thiết kế tinh tế, mang đến vẻ đẹp thanh lịch và ý nghĩa bền vững.
                                </div>
                                <div class="box--btn">
                                    <a href="" class="btn-links">
                                            Xem bộ sưu tập 2025
                                        </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="weddingjewelry--img">
                    <img src="images/img-mh1.jpg" alt="">
                </div>
            </div>
            <myfooter></myfooter>
        </main>
    </div>
    <script src="js/vue.js"></script>
    <script src="js/httpvueloader.js"></script>
    <script src="js/jquery-3.4.1.js"></script>
    <script src="libs/bootstrap/bootstrap.bundle.min.js"></script>
    <script src="libs/lazyload/jquery.lazy.min.js"></script>
    <script src="libs/scrollbar/jquery.scrollbar.min.js"></script>
    <!-- swiper js-->
    <script src="libs/swiper/swiper-bundle.min.js"></script>
    <script src="libs/mansonry/masonry.pkgd.min.js"></script>
    <!-- mmenu -->
    <script src="libs/mmenu/jquery.mmenu.all.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script type="text/javascript" src="libs/aos/aos.js"></script>
    <script src="js/main.js"></script>
    <script></script>
    <script>
        var app = new Vue({
            el: "#app",
            components: {
                myheader: httpVueLoader("vue_components/header.vue"),
                myfooter: httpVueLoader("vue_components/footer.vue"),
                partner: httpVueLoader("vue_components/partner.vue"),
                locationair: httpVueLoader("vue_components/location-air.vue"),
            },
            data: {

            },

            mounted() {
                this.$nextTick(() => {
                    $(window).bind('load', function() {
                        AOS.init({
                            startEvent: 'load',
                        });
                        var swiper = new Swiper(".mySwiper", {
                            loop: false,
                            spaceBetween: 6,
                            slidesPerView: 4,
                            // freeMode: true,
                            direction: getDirection(),
                            watchSlidesProgress: true,
                            // navigation: {
                            //   nextEl: ".custom-nav-prev",
                            //   prevEl: ".custom-nav-next",
                            // },
                            breakpoints: {
                                310: {
                                    spaceBetween: 10,
                                    slidesPerView: 3,
                                },
                                400: {
                                    spaceBetween: 10,
                                    slidesPerView: 4,
                                },

                            },
                        });

                        function getDirection() {
                            var windowWidth = window.innerWidth;
                            var direction = window.innerWidth <= 1279 ? 'horizontal' : 'vertical';

                            return direction;
                        }
                        var swiper2 = new Swiper(".mySwiper2", {
                            loop: false,
                            spaceBetween: 10,
                            effect: 'fade',
                            autoplay: {
                                delay: 5000,
                                disableOnInteraction: false,
                            },
                            navigation: {
                                nextEl: ".swiper-button-next",
                                prevEl: ".swiper-button-prev",
                            },
                            thumbs: {
                                swiper: swiper,
                            },
                        });

                    });
                });
            },
        });
    </script>
</body>

</html>