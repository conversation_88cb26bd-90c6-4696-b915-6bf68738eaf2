<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>home</title>
    <!-- mmenu css -->
    <link rel="stylesheet" href="libs/mmenu/jquery.mmenu.all.css">
    <!-- animate css-->
    <link rel="stylesheet" href="libs/animate/animate.min.css">
    <link rel="stylesheet" href="libs/aos/aos.css">
    <!-- swiper css-->
    <link rel="stylesheet" href="libs/swiper/swiper-bundle.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Barlow:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/style.css">
</head>

<body>
    <div id="app">
        <myheader></myheader>
        <main class="page-news">
            <div class="collection-productintroduction">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="productintroduction--list --style--news">
                                <div class="swiper productintroduction--slide">
                                    <div class="swiper-wrapper">
                                        <div class="swiper-slide" v-for="n in 3">
                                            <div class="productintroduction--other">
                                                <div class="_right">
                                                    <div class="img">
                                                        <img src="images/bg-banner1.jpg" alt="">
                                                    </div>
                                                </div>
                                                <div class="_left">
                                                    <div class="box-title">
                                                        <h2 class="title">
                                                            Đồng vàng Kim Tỵ Cát Tường: Quyền uy ngự trị
                                                        </h2>
                                                        <div class="sub_title">
                                                            Tin tức vàng
                                                        </div>
                                                        <div class="desc">
                                                            Đồng vàng Kim Tỵ Cát Tường – Biểu tượng mạnh mẽ của quyền uy và sự thịnh vượng, được chế tác tinh xảo từ vàng cao cấp. Sản phẩm không chỉ mang đến giá trị nghệ thuật độc đáo mà còn mang ý nghĩa phong thủy sâu sắc, giúp người sở hữu gặp nhiều may mắn,
                                                            tài lộc và thịnh vượng.
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="producbox--btn">
                                    <div class="swiper-button-next swiper-button-next-productintroduction">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="10" height="16" viewBox="0 0 10 16" fill="none">
                                                <path
                                                    fill-rule="evenodd"
                                                    clip-rule="evenodd"
                                                    d="M1.92383 15.209L0.675545 13.9607L6.91696 7.71929L0.675546 1.47787L1.92383 0.229586L8.78933 7.09509C8.78937 7.09513 8.78939 7.09514 8.16524 7.71929L8.78933 7.09509L9.41353 7.71929L1.92383 15.209Z"
                                                    fill="#4F4F4F"
                                                />
                                            </svg>
                                    </div>
                                    <div class="swiper-button-prev swiper-button-prev-productintroduction">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="10" height="16" viewBox="0 0 10 16" fill="none">
                                                <path
                                                    fill-rule="evenodd"
                                                    clip-rule="evenodd"
                                                    d="M8.07617 15.209L9.32445 13.9607L3.08304 7.71929L9.32445 1.47787L8.07617 0.229586L1.21067 7.09509C1.21063 7.09513 1.21061 7.09514 1.83476 7.71929L1.21067 7.09509L0.586473 7.71929L8.07617 15.209Z"
                                                    fill="#4F4F4F"
                                                />
                                            </svg>
                                    </div>
                                </div>
                                <div class="producbox--panigation">
                                    <div class="swiper-pagination swiper-pagination-productintroduction"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="home-news">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="box-title">
                                <h2 class="title">
                                    Tin tức Vàng
                                </h2>
                            </div>
                            <div class="news--list---box">
                                <div class="row gx-lg-4 row-cols-md-3 row-cols-sm-2 row-cols-1">
                                    <div class="col" v-for="n in 6">
                                        <div class="news--items">
                                            <a href="" class="img">
                                                <img src="images/img-news1.jpg" alt="">
                                            </a>
                                            <div class="news--body">
                                                <a href="" class="sub_cate">
                                                            Tin tức vàng
                                                        </a>
                                                <h3 class="title">
                                                    <a href="">
                                                                Đồng vàng Phát Lộc Tài: Tài lộc vượng phát - Đại cát đại lợi
                                                            </a>
                                                </h3>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="box--btn">
                                <a href="" class="btn-links">
                                        Xem thêm tin tức
                                    </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="home-news">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="box-title">
                                <h2 class="title">
                                    Thông tin ưu đãi
                                </h2>
                            </div>
                            <div class="news--list---box">
                                <div class="row gx-lg-4 row-cols-md-3 row-cols-sm-2 row-cols-1">
                                    <div class="col" v-for="n in 6">
                                        <div class="news--items">
                                            <a href="" class="img">
                                                <img src="images/img-news1.jpg" alt="">
                                            </a>
                                            <div class="news--body">
                                                <a href="" class="sub_cate">
                                                            Tin tức vàng
                                                        </a>
                                                <h3 class="title">
                                                    <a href="">
                                                                Đồng vàng Phát Lộc Tài: Tài lộc vượng phát - Đại cát đại lợi
                                                            </a>
                                                </h3>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="box--btn">
                                <a href="" class="btn-links">
                                        Xem thêm tin tức
                                    </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
        <myfooter></myfooter>
    </div>
    <script src="js/vue.js"></script>
    <script src="js/httpvueloader.js"></script>
    <script src="js/jquery-3.4.1.js"></script>
    <script src="libs/bootstrap/bootstrap.bundle.min.js"></script>
    <script src="libs/lazyload/jquery.lazy.min.js"></script>
    <!-- swiper js-->
    <script src="libs/swiper/swiper-bundle.min.js"></script>
    <script src="libs/mansonry/masonry.pkgd.min.js"></script>
    <!-- mmenu -->
    <script src="libs/mmenu/jquery.mmenu.all.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script type="text/javascript" src="libs/aos/aos.js"></script>
    <script src="js/main.js"></script>
    <script></script>
    <script>
        var app = new Vue({
            el: "#app",
            components: {
                myheader: httpVueLoader("vue_components/header.vue"),
                myfooter: httpVueLoader("vue_components/footer.vue"),
                partner: httpVueLoader("vue_components/partner.vue"),
                locationair: httpVueLoader("vue_components/location-air.vue"),
            },
            data: {

            },

            mounted() {

            },
        });
    </script>
</body>

</html>