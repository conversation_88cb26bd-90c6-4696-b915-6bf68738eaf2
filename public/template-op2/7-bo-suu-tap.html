<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>home</title>
    <!-- mmenu css -->
    <link rel="stylesheet" href="libs/mmenu/jquery.mmenu.all.css">
    <!-- animate css-->
    <link rel="stylesheet" href="libs/animate/animate.min.css">
    <link rel="stylesheet" href="libs/aos/aos.css">
    <!-- swiper css-->
    <link rel="stylesheet" href="libs/swiper/swiper-bundle.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Barlow:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/style.css">
</head>

<body>
    <div id="app">
        <myheader></myheader>
        <main>
            <div class="collection-top">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="box-title">
                                <h2 class="title">
                                    Bộ sưu tập
                                </h2>
                                <div class="desc">
                                    Từ những thiết kế hiện đại mang tính biểu tượng đến các bộ sưu tập lấy cảm hứng từ di sản phương Đông, mỗi món trang sức đều kể một câu chuyện riêng biệt. Đây không chỉ là trang sức – mà là sự thể hiện đẳng cấp và dấu ấn cá nhân của riêng bạn.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="collection-all">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="collection-box">
                                <div class="row gx-lg-4 row-cols-lg-4 row-cols-md-3 row-cols-sm-2 row-cols-1">
                                    <div class="col" v-for="n in 8">
                                        <div class="collection--items">
                                            <a href="" class="img">
                                                <span>
                                                    <img src="images/img-1.jpg" alt="">
                                                </span>
                                            </a>
                                            <h3 class="title">
                                                <a href="">
                                                    Anniverasy
                                                </a>
                                            </h3>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="box--btn">
                                <a href="" class="btn-links">
                                        Xem thêm bộ sưu tập
                                    </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
        <myfooter></myfooter>
    </div>
    <script src="js/vue.js"></script>
    <script src="js/httpvueloader.js"></script>
    <script src="js/jquery-3.4.1.js"></script>
    <script src="libs/bootstrap/bootstrap.bundle.min.js"></script>
    <script src="libs/lazyload/jquery.lazy.min.js"></script>
    <!-- swiper js-->
    <script src="libs/swiper/swiper-bundle.min.js"></script>
    <script src="libs/mansonry/masonry.pkgd.min.js"></script>
    <!-- mmenu -->
    <script src="libs/mmenu/jquery.mmenu.all.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script type="text/javascript" src="libs/aos/aos.js"></script>
    <script src="js/main.js"></script>
    <script></script>
    <script>
        var app = new Vue({
            el: "#app",
            components: {
                myheader: httpVueLoader("vue_components/header.vue"),
                myfooter: httpVueLoader("vue_components/footer.vue"),
                partner: httpVueLoader("vue_components/partner.vue"),
                locationair: httpVueLoader("vue_components/location-air.vue"),
            },
            data: {

            },

            mounted() {

            },
        });
    </script>
</body>

</html>