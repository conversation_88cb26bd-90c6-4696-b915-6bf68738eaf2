<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title><PERSON><PERSON><PERSON> thieu</title>
    <!-- mmenu css -->
    <link rel="stylesheet" href="libs/mmenu/jquery.mmenu.all.css">
    <link rel="stylesheet" href="libs/scrollbar/jquery.scrollbar.css">
    <!-- animate css-->
    <link rel="stylesheet" href="libs/animate/animate.min.css">
    <link rel="stylesheet" href="libs/aos/aos.css">
    <!-- swiper css-->
    <link rel="stylesheet" href="libs/swiper/swiper-bundle.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Barlow:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/style.css">
</head>

<body>
    <div id="app">
        <myheader></myheader>
        <main class="">
            <div class="home-timelessstory  --style2">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="timelessstory--list">
                                <div class="_left">
                                    <div class="box-title">
                                        <h2 class="title">
                                            Son Sắc Cùng Thời Gian
                                        </h2>
                                        <div class="sub_title">
                                            Vẻ đẹp không phai
                                        </div>
                                    </div>
                                    <div class="timelessstory--body">
                                        <div class="desc">
                                            <p>
                                                Có những giá trị chẳng hề đổi thay, như ánh vàng ấm áp của tình yêu, của ký ức, và của những khoảnh khắc đời người. Trang sức vàng – với vẻ đẹp son sắc – không chỉ tô điểm cho nhan sắc mà còn là minh chứng cho sự gắn bó vững bền, dù tháng năm có đổi thay.
                                            </p>
                                        </div>
                                    </div>
                                    <div class="box--btn">
                                        <a href="" class="btn-links">
                                                Xem toàn bộ collection
                                            </a>
                                    </div>
                                </div>
                                <div class="_right">
                                    <div class="img">
                                        <img src="images/img-1.jpg" alt="">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="product-tabslist">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="product-tabs">
                                <div class="product-tabs--left">
                                    <div class="btn-opfillter--mb">
                                        <div class="_icon">
                                            <img src="images/ic-fillter.png" alt="">
                                        </div>
                                        Bộ lọc
                                    </div>
                                    <div class="totla--page">
                                        1-20 of 1992 items
                                    </div>
                                    <div class="--line"></div>
                                    <div class="product-tabs--menu ">
                                        <div class="btn-closefillter--mb">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32" fill="none">
  <path d="M17.8643 16L32 30.1562L30.1562 32L16 17.8643L1.84379 32L0 30.1562L14.1357 16L0 1.84379L1.84379 0L16 14.1357L30.1562 0L32 1.84379L17.8643 16Z" fill="#C3C3C3"/>
</svg>
                                        </div>
                                        <div class="items">
                                            <a href="javascript:Void(0);" class="product-tabs--title">
                                                    MỤC ĐÍCH
                                                    <svg
                                                        xmlns="http://www.w3.org/2000/svg"
                                                        width="12"
                                                        height="6"
                                                        viewBox="0 0 12 6"
                                                        fill="none"
                                                    >
                                                        <path
                                                            d="M1 1L6 5L11 1"
                                                            stroke="#4F4F4F"
                                                            stroke-width="1.5"
                                                            stroke-linecap="round"
                                                            stroke-linejoin="round"
                                                        />
                                                    </svg>
                                                </a>
                                            <div class="items--tabsbody">
                                                <div class="items-checkbox">
                                                    <input id="mausac" type="checkbox" hidden>
                                                    <label for="mausac">
                                                        <span></span>
                                                        quà tặng
                                                    </label>
                                                </div>
                                                <div class="items-checkbox">
                                                    <input id="mausac2" type="checkbox" hidden>
                                                    <label for="mausac2">
                                                        <span></span>
                                                        quà tặng sinh nhật
                                                    </label>
                                                </div>
                                                <div class="items-checkbox">
                                                    <input id="mausac3" type="checkbox" hidden>
                                                    <label for="mausac3">
                                                        <span></span>
                                                        khác
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="items">
                                            <a href="javascript:Void(0);" class="product-tabs--title">
                                                   CHẤT LIỆU
                                                   <svg
                                                        xmlns="http://www.w3.org/2000/svg"
                                                        width="12"
                                                        height="6"
                                                        viewBox="0 0 12 6"
                                                        fill="none"
                                                    >
                                                        <path
                                                            d="M1 1L6 5L11 1"
                                                            stroke="#4F4F4F"
                                                            stroke-width="1.5"
                                                            stroke-linecap="round"
                                                            stroke-linejoin="round"
                                                        />
                                                    </svg>
                                                </a>
                                            <div class="items--tabsbody">
                                                <div class="items-checkbox">
                                                    <input id="mausac" type="checkbox" hidden>
                                                    <label for="mausac">
                                                        <span></span>
                                                        Màu vàng
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="items">
                                            <a href="javascript:Void(0);" class="product-tabs--title">
                                                    MÀU SẮC
                                                     <svg
                                                        xmlns="http://www.w3.org/2000/svg"
                                                        width="12"
                                                        height="6"
                                                        viewBox="0 0 12 6"
                                                        fill="none"
                                                    >
                                                        <path
                                                            d="M1 1L6 5L11 1"
                                                            stroke="#4F4F4F"
                                                            stroke-width="1.5"
                                                            stroke-linecap="round"
                                                            stroke-linejoin="round"
                                                        />
                                                    </svg>
                                                </a>
                                            <div class="items--tabsbody">
                                                <div class="items-checkbox">
                                                    <input id="mausac" type="checkbox" hidden>
                                                    <label for="mausac">
                                                        <span></span>
                                                        Màu vàng
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="items">
                                            <a href="javascript:Void(0);" class="product-tabs--title">
                                                    PHONG CÁCH
                                                    <svg
                                                        xmlns="http://www.w3.org/2000/svg"
                                                        width="12"
                                                        height="6"
                                                        viewBox="0 0 12 6"
                                                        fill="none"
                                                    >
                                                        <path
                                                            d="M1 1L6 5L11 1"
                                                            stroke="#4F4F4F"
                                                            stroke-width="1.5"
                                                            stroke-linecap="round"
                                                            stroke-linejoin="round"
                                                        />
                                                    </svg>
                                                </a>
                                            <div class="items--tabsbody">
                                                <div class="items-checkbox">
                                                    <input id="mausac" type="checkbox" hidden>
                                                    <label for="mausac">
                                                        <span></span>
                                                        Màu vàng
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="items">
                                            <a href="javascript:Void(0);" class="product-tabs--title">
                                                   TRỌNG LƯỢNG
                                                   <svg
                                                        xmlns="http://www.w3.org/2000/svg"
                                                        width="12"
                                                        height="6"
                                                        viewBox="0 0 12 6"
                                                        fill="none"
                                                    >
                                                        <path
                                                            d="M1 1L6 5L11 1"
                                                            stroke="#4F4F4F"
                                                            stroke-width="1.5"
                                                            stroke-linecap="round"
                                                            stroke-linejoin="round"
                                                        />
                                                    </svg>
                                                </a>
                                            <div class="items--tabsbody">
                                                <div class="items-checkbox">
                                                    <input id="mausac" type="checkbox" hidden>
                                                    <label for="mausac">
                                                        <span></span>
                                                        Màu vàng
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="items">
                                            <a href="javascript:Void(0);" class="product-tabs--title">
                                                    SẢN PHẨM
                                                    <svg
                                                        xmlns="http://www.w3.org/2000/svg"
                                                        width="12"
                                                        height="6"
                                                        viewBox="0 0 12 6"
                                                        fill="none"
                                                    >
                                                        <path
                                                            d="M1 1L6 5L11 1"
                                                            stroke="#4F4F4F"
                                                            stroke-width="1.5"
                                                            stroke-linecap="round"
                                                            stroke-linejoin="round"
                                                        />
                                                    </svg>
                                                </a>
                                            <div class="items--tabsbody">
                                                <div class="items-checkbox">
                                                    <input id="mausac" type="checkbox" hidden>
                                                    <label for="mausac">
                                                        <span></span>
                                                        Màu vàng
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="filter-select">
                                    <div class="txt">
                                        LỌC THEO:
                                    </div>
                                    <select name="" id="">
                                            <option value="">
                                                BÁN CHẠY NHẤT
                                            </option>
                                        </select>
                                </div>
                            </div>
                            <div class="product--all">
                                <div class="product--col" v-for="n in 11">
                                    <div class="product--items">
                                        <a href="" class="img">
                                            <img src="images/img-prod3.png" alt="">
                                        </a>
                                        <div class="product--body">
                                            <h3 class="title">
                                                <a href="">
                                                                        Đồng Kim Gia Bảo - Tùng
                                                                    </a>
                                            </h3>
                                            <div class="gold--infor">
                                                1 chỉ | Vàng 24K (999.9)
                                            </div>
                                            <div class="price">
                                                13.000.000
                                            </div>
                                            <button class="add--cart" type="submit">
                                                                    <svg
                                                                        xmlns="http://www.w3.org/2000/svg"
                                                                        width="43"
                                                                        height="42"
                                                                        viewBox="0 0 43 42"
                                                                        fill="none"
                                                                    >
                                                                        <path
                                                                            d="M32.2422 40.833H0.584914V14.2905H32.2422"
                                                                            stroke="#AE8751"
                                                                            stroke-width="1.17444"
                                                                            stroke-miterlimit="10"
                                                                        />
                                                                        <path
                                                                            d="M8.87572 16.6765V7.37212C8.87572 3.69604 12.243 0.68457 16.4301 0.68457C20.6171 0.68457 23.9844 3.67527 23.9844 7.37212V16.6765"
                                                                            stroke="#AE8751"
                                                                            stroke-width="1.17444"
                                                                            stroke-miterlimit="10"
                                                                        />
                                                                        <path d="M31.9793 37.1113C26.3145 37.1113 21.7222 32.8061 21.7222 27.4953C21.7222 22.1846 26.3145 17.8794 31.9793 17.8794C37.6441 17.8794 42.2363 22.1846 42.2363 27.4953C42.2363 32.8061 37.6441 37.1113 31.9793 37.1113Z" fill="#AE8751"/>
                                                                        <path
                                                                            d="M31.9961 20.7104V34.2309"
                                                                            stroke="white"
                                                                            stroke-width="1.17444"
                                                                            stroke-miterlimit="10"
                                                                        />
                                                                        <path
                                                                            d="M24.7793 27.478H39.2012"
                                                                            stroke="white"
                                                                            stroke-width="1.17444"
                                                                            stroke-miterlimit="10"
                                                                        />
                                                                    </svg>
                                                                </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="product--panigation">
                                <div class="txt">
                                    11/92 sản phẩm
                                </div>
                                <div class="box--btn">
                                    <a href="" class="btn-links">
                                                Xem toàn bộ sản phẩm
                                            </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="block-weddingjewelry">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-6">
                            <div class="weddingjewelry--other">
                                <div class="weddingjewelry--logo">
                                    <img src="images/logo-mh.png" alt="">
                                </div>
                                <div class="desc">
                                    MH – thương hiệu thời trang hiện đại, tôn vinh tình yêu qua từng thiết kế tinh tế, mang đến vẻ đẹp thanh lịch và ý nghĩa bền vững.
                                </div>
                                <div class="box--btn">
                                    <a href="" class="btn-links">
                                        Xem bộ sưu tập 2025
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="weddingjewelry--img">
                    <img src="images/img-mh1.jpg" alt="">
                </div>
            </div>
            <div class="block-form">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="form--list">
                                <div class="_left">
                                    <img src="images/img-form.jpg" alt="">
                                </div>
                                <div class="_right">
                                    <div class="box-title">
                                        <h2 class="title">
                                            Đặt lịch đến cửa hàng
                                        </h2>
                                    </div>
                                    <form action="">
                                        <div class="form--content">
                                            <div class="form--col12">
                                                <div class="form--row">
                                                    <input type="text" class="input--control" placeholder="Họ và Tên">
                                                </div>
                                            </div>
                                            <div class="form--col">
                                                <div class="form--row">
                                                    <input type="text" class="input--control" placeholder="Số điện thoại">
                                                </div>
                                            </div>
                                            <div class="form--col">
                                                <div class="form--row">
                                                    <input type="text" class="input--control" placeholder="Email">
                                                </div>
                                            </div>
                                            <div class="form--col12">
                                                <div class="form--row">
                                                    <select name="" id="" class="input--control">
                                                        <option value="">Chọn cửa hàng</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="form--col12">
                                                <div class="form--row">
                                                    <select name="" id="" class="input--control">
                                                        <option value="">Chọn dịch vụ</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="form--col12">
                                                <div class="form--row">
                                                    <textarea name="" id="" class="input--control" placeholder="Lời nhắn"></textarea>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form--btn">
                                            <button class="storesystem--btn" type="submit">
Đặt lịch hẹn
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <myfooter></myfooter>
        </main>
    </div>
    <script src="js/vue.js"></script>
    <script src="js/httpvueloader.js"></script>
    <script src="js/jquery-3.4.1.js"></script>
    <script src="libs/bootstrap/bootstrap.bundle.min.js"></script>
    <script src="libs/lazyload/jquery.lazy.min.js"></script>
    <script src="libs/scrollbar/jquery.scrollbar.min.js"></script>
    <!-- swiper js-->
    <script src="libs/swiper/swiper-bundle.min.js"></script>
    <script src="libs/mansonry/masonry.pkgd.min.js"></script>
    <!-- mmenu -->
    <script src="libs/mmenu/jquery.mmenu.all.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script type="text/javascript" src="libs/aos/aos.js"></script>
    <script src="js/main.js"></script>
    <script></script>
    <script>
        var app = new Vue({
            el: "#app",
            components: {
                myheader: httpVueLoader("vue_components/header.vue"),
                myfooter: httpVueLoader("vue_components/footer.vue"),
                partner: httpVueLoader("vue_components/partner.vue"),
                locationair: httpVueLoader("vue_components/location-air.vue"),
            },
            data: {

            },

            mounted() {

            },
        });
    </script>
</body>

</html>