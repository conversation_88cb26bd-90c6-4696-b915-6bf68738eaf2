<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>home</title>
    <!-- mmenu css -->
    <link rel="stylesheet" href="libs/mmenu/jquery.mmenu.all.css">
    <!-- animate css-->
    <link rel="stylesheet" href="libs/animate/animate.min.css">
    <link rel="stylesheet" href="libs/aos/aos.css">
    <!-- swiper css-->
    <link rel="stylesheet" href="libs/swiper/swiper-bundle.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Barlow:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/style.css">
</head>

<body>
    <div id="app">
        <myheader></myheader>
        <main>
            <div class="policy-main">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="policy--other">
                                <div class="box-title">
                                    <h2 class="title">
                                        Chính sách bảo mật thông tin
                                    </h2>
                                </div>
                                <div class="policy--content">
                                    <div class="detail--content">
                                        <div class="_items">
                                            <div class="detail--child">
                                                <div class="policy--box">
                                                    <p>
                                                        <img src="images/img-do-size.jpg" alt="">
                                                    </p>
                                                    <p>
                                                        1. Khách hàng lựa chọn sản phẩm, bảo đảm đúng nhu cầu, mục đích mua sắm , sau khi Khách hàng hoàn thành xong giao dịch, sản phẩm đã mua rồi, Khách hàng không được trả lại.
                                                    </p>
                                                    <p>
                                                        Khách hàng chỉ được phép đổi hoặc bán lại sản phẩm. Không trả lại sản phẩm với bất kì lí do gì (trước khi ra khỏi cửa hàng, khách hàng đã kiểm tra kĩ sản phẩm)
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
        <myfooter></myfooter>
    </div>
    <script src="js/vue.js"></script>
    <script src="js/httpvueloader.js"></script>
    <script src="js/jquery-3.4.1.js"></script>
    <script src="libs/bootstrap/bootstrap.bundle.min.js"></script>
    <script src="libs/lazyload/jquery.lazy.min.js"></script>
    <!-- swiper js-->
    <script src="libs/swiper/swiper-bundle.min.js"></script>
    <script src="libs/mansonry/masonry.pkgd.min.js"></script>
    <!-- mmenu -->
    <script src="libs/mmenu/jquery.mmenu.all.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script type="text/javascript" src="libs/aos/aos.js"></script>
    <script src="js/main.js"></script>
    <script></script>
    <script>
        var app = new Vue({
            el: "#app",
            components: {
                myheader: httpVueLoader("vue_components/header.vue"),
                myfooter: httpVueLoader("vue_components/footer.vue"),
                partner: httpVueLoader("vue_components/partner.vue"),
                locationair: httpVueLoader("vue_components/location-air.vue"),
            },
            data: {

            },

            mounted() {

            },
        });
    </script>
</body>

</html>