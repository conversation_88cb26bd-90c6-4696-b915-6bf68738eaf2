<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>home</title>
    <!-- mmenu css -->
    <link rel="stylesheet" href="libs/mmenu/jquery.mmenu.all.css">
    <!-- animate css-->
    <link rel="stylesheet" href="libs/animate/animate.min.css">
    <link rel="stylesheet" href="libs/aos/aos.css">
    <!-- swiper css-->
    <link rel="stylesheet" href="libs/swiper/swiper-bundle.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Barlow:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/style.css">
</head>

<body>
    <div id="app">
        <myheader></myheader>
        <main class="page-news">
            <div class="row-breadcrumb row-breadcrumb-detail">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item">
                                    <a href="#">
                                           Tin Tức 
                                        </a>
                                </li>
                                <li class="breadcrumb-item">
                                    <a href="">Đồng vàng Kim Tỵ Cát Tường: Quyền uy ngự trị</a>
                                </li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
            <div class="newsdettail-main">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="newsdetail-title">
                                <h1>
                                    Đồng vàng Kim Tỵ Cát Tường:<br /> Quyền uy ngự trị
                                </h1>
                                <div class="date--time">
                                    <div class="txt">
                                        Tin tức vàng
                                    </div>
                                    <div class="--date">
                                        25/10/2024
                                    </div>
                                </div>
                            </div>
                            <div class="newsdettail--content">
                                <div class="desc">
                                    <p>
                                        Trong thế giới trang sức, đồng vàng Kim Tỵ Cát Tường nổi bật như một tác phẩm nghệ thuật tinh xảo, không chỉ sở hữu vẻ đẹp cuốn hút mà còn mang trong mình giá trị phong thủy sâu sắc. Sản phẩm này không chỉ đơn thuần là một món quà xa xỉ mà còn là biểu
                                        tượng mạnh mẽ của quyền uy và sự thịnh vượng, giúp người sở hữu gặp nhiều may mắn và tài lộc trong cuộc sống.ay mắn trong cuộc sống.
                                    </p>
                                </div>
                                <div class="img">
                                    <img src="images/bg-banner-gt.jpg" alt="">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="home-news">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="box-title">
                                <h2 class="title">
                                    Tin tức khác
                                </h2>
                            </div>
                            <div class="newshome--list">
                                <div class="swiper newshome--slide">
                                    <div class="swiper-wrapper">
                                        <div class="swiper-slide" v-for="n in 3">
                                            <div class="news--items">
                                                <a href="" class="img">
                                                    <img src="images/img-news1.jpg" alt="">
                                                </a>
                                                <div class="news--body">
                                                    <a href="" class="sub_cate">
                                                            Tin tức vàng
                                                        </a>
                                                    <h3 class="title">
                                                        <a href="">
                                                                Đồng vàng Phát Lộc Tài: Tài lộc vượng phát - Đại cát đại lợi
                                                            </a>
                                                    </h3>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="producbox--btn">
                                    <div class="swiper-button-next swiper-button-next-newshome">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="10" height="16" viewBox="0 0 10 16" fill="none">
                                                <path
                                                    fill-rule="evenodd"
                                                    clip-rule="evenodd"
                                                    d="M1.92383 15.209L0.675545 13.9607L6.91696 7.71929L0.675546 1.47787L1.92383 0.229586L8.78933 7.09509C8.78937 7.09513 8.78939 7.09514 8.16524 7.71929L8.78933 7.09509L9.41353 7.71929L1.92383 15.209Z"
                                                    fill="#4F4F4F"
                                                />
                                            </svg>
                                    </div>
                                    <div class="swiper-button-prev swiper-button-prev-newshome">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="10" height="16" viewBox="0 0 10 16" fill="none">
                                                <path
                                                    fill-rule="evenodd"
                                                    clip-rule="evenodd"
                                                    d="M8.07617 15.209L9.32445 13.9607L3.08304 7.71929L9.32445 1.47787L8.07617 0.229586L1.21067 7.09509C1.21063 7.09513 1.21061 7.09514 1.83476 7.71929L1.21067 7.09509L0.586473 7.71929L8.07617 15.209Z"
                                                    fill="#4F4F4F"
                                                />
                                            </svg>
                                    </div>
                                </div>
                                <div class="producbox--panigation">
                                    <div class="swiper-pagination swiper-pagination-newshome"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
        <myfooter></myfooter>
    </div>
    <script src="js/vue.js"></script>
    <script src="js/httpvueloader.js"></script>
    <script src="js/jquery-3.4.1.js"></script>
    <script src="libs/bootstrap/bootstrap.bundle.min.js"></script>
    <script src="libs/lazyload/jquery.lazy.min.js"></script>
    <!-- swiper js-->
    <script src="libs/swiper/swiper-bundle.min.js"></script>
    <script src="libs/mansonry/masonry.pkgd.min.js"></script>
    <!-- mmenu -->
    <script src="libs/mmenu/jquery.mmenu.all.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script type="text/javascript" src="libs/aos/aos.js"></script>
    <script src="js/main.js"></script>
    <script></script>
    <script>
        var app = new Vue({
            el: "#app",
            components: {
                myheader: httpVueLoader("vue_components/header.vue"),
                myfooter: httpVueLoader("vue_components/footer.vue"),
                partner: httpVueLoader("vue_components/partner.vue"),
                locationair: httpVueLoader("vue_components/location-air.vue"),
            },
            data: {

            },

            mounted() {

            },
        });
    </script>
</body>

</html>