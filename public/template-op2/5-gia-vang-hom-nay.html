<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title><PERSON><PERSON><PERSON> thieu</title>
    <!-- mmenu css -->
    <link rel="stylesheet" href="libs/mmenu/jquery.mmenu.all.css">
    <link rel="stylesheet" href="libs/scrollbar/jquery.scrollbar.css">
    <!-- animate css-->
    <link rel="stylesheet" href="libs/animate/animate.min.css">
    <link rel="stylesheet" href="libs/aos/aos.css">
    <!-- swiper css-->
    <link rel="stylesheet" href="libs/swiper/swiper-bundle.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Barlow:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/style.css">
</head>

<body>
    <div id="app">
        <myheader></myheader>
        <main class="">
            <div class="home-chart">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="chart--fieldset">
                                <div class="fieldset--title">
                                    Biểu đồ vàng
                                </div>
                                <div class="chart--fieldset---list">
                                    <div class="_left">
                                        <div class="--title">
                                            Nhẫn ép vỉ vàng Rồng Thăng Long
                                        </div>
                                        <div class="price--discount">
                                            <div class="--number">
                                                92.700.000
                                            </div>
                                            <div class="--number--discount">
                                                +20.000 (+0.03%)
                                                <svg xmlns="http://www.w3.org/2000/svg" width="15" height="16" viewBox="0 0 15 16" fill="none">
                                                        <path
                                                            d="M1.65625 8.01159L7.98633 2L14.3164 8.01159"
                                                            stroke="#EDD0A7"
                                                            stroke-width="2"
                                                            stroke-miterlimit="10"
                                                        />
                                                        <path
                                                            d="M7.98828 2L7.98828 15.2122"
                                                            stroke="#EDD0A7"
                                                            stroke-width="2"
                                                            stroke-miterlimit="10"
                                                        />
                                                    </svg>
                                            </div>
                                        </div>
                                        <canvas id="goldChart"></canvas>
                                    </div>
                                    <div class="_right">
                                        <div class="--title">
                                            Loại vàng
                                        </div>
                                        <div class="pricegold--input">
                                            <select name="" id="">
                                                    <option value="">
                                                        Thẻ Vàng Kim Gia Bảo
                                                    </option>
                                                </select>
                                        </div>
                                        <div class="chart--fieldset---box">
                                            <div class="--title">
                                                Xem theo:
                                            </div>
                                            <div class="chart--fieldset---check">
                                                <div class="--items">
                                                    <input id="checkbox1" type="radio" hidden name="1" checked>
                                                    <label for="checkbox1">
                                                        1 ngày
                                                    </label>
                                                </div>
                                                <div class="--items">
                                                    <input id="checkbox2" type="radio" hidden name="1">
                                                    <label for="checkbox2">
                                                        1 tuần
                                                    </label>
                                                </div>
                                                <div class="--items">
                                                    <input id="checkbox3" type="radio" hidden name="1">
                                                    <label for="checkbox3">
                                                        1 tháng
                                                    </label>
                                                </div>
                                                <div class="--items">
                                                    <input id="checkbox4" type="radio" hidden name="1">
                                                    <label for="checkbox4">
                                                        3 tháng
                                                    </label>
                                                </div>
                                                <div class="--items">
                                                    <input id="checkbox5" type="radio" hidden name="1">
                                                    <label for="checkbox5">
                                                        6 tháng
                                                    </label>
                                                </div>
                                                <div class="--items">
                                                    <input id="checkbox6" type="radio" hidden name="1">
                                                    <label for="checkbox6">
                                                        12 tháng
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="chart--fieldset---unit">
                                            <div class="--measure">
                                                Đơn vị tính: <br /> Đồng / Chỉ
                                            </div>
                                            <div class="--explain">
                                                <div class="--sell-out">
                                                    Bán ra
                                                </div>
                                                <div class="--buy-in">
                                                    Mua vào
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="chart--table---title">
                                Thị trường vàng
                            </div>
                            <div class="chart--table">
                                <div class="_left">
                                    <table class="chart--table-box">
                                        <thead>
                                            <tr>
                                                <th>Loại vàng</th>
                                                <th width="154">Mua</th>
                                                <th width="154">Bán</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr v-for="n in 3">
                                                <td>Thẻ Vàng Kim Gia Bảo</td>
                                                <td>
                                                    <div class="number">
                                                        9.220.000
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="15" height="20" viewBox="0 0 15 20" fill="none">
                                                                <path
                                                                    d="M0.597656 9.10916L7.33508 2.00024L14.0725 9.10916"
                                                                    stroke="#2ED151"
                                                                    stroke-width="1.5"
                                                                    stroke-miterlimit="10"
                                                                />
                                                                <path
                                                                    d="M7.33594 2.00024V19.0734"
                                                                    stroke="#2ED151"
                                                                    stroke-width="1.5"
                                                                    stroke-miterlimit="10"
                                                                />
                                                            </svg>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="number">
                                                        9.220.000
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="15" height="20" viewBox="0 0 15 20" fill="none">
                                                                <path
                                                                    d="M14.0703 10.8894L7.33289 17.9983L0.595465 10.8894"
                                                                    stroke="#922E38"
                                                                    stroke-width="1.5"
                                                                    stroke-miterlimit="10"
                                                                />
                                                                <path
                                                                    d="M7.33203 17.9983L7.33203 0.925088"
                                                                    stroke="#922E38"
                                                                    stroke-width="1.5"
                                                                    stroke-miterlimit="10"
                                                                />
                                                            </svg>
                                                    </div>
                                                </td>
                                            </tr>
                                        </tbody>
                                        <tfoot>
                                            <tr>
                                                <td colspan="3">
                                                    <div class="table--footer">
                                                        <p>
                                                            Cập nhật lúc 10:03 04/03/2025
                                                        </p>
                                                        <p>
                                                            Đơn vị tính: Đồng / Chỉ
                                                        </p>
                                                    </div>
                                                </td>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                                <div class="_right">
                                    <div class="account-content">
                                        <ul class="nav nav-pills" id="pills-tab" role="tablist">
                                            <li class="nav-item" role="presentation">
                                                <button class="nav-link active" id="pills-home-tab" data-bs-toggle="pill" data-bs-target="#pills-home" type="button" role="tab" aria-controls="pills-home" aria-selected="true">
                                                        Giao dịch Mua
                                                    </button>
                                            </li>
                                            <li class="nav-item" role="presentation">
                                                <button class="nav-link" id="pills-profile-tab" data-bs-toggle="pill" data-bs-target="#pills-profile" type="button" role="tab" aria-controls="pills-profile" aria-selected="false">
                                                        Giao dịch Bán
                                                    </button>
                                            </li>
                                        </ul>
                                        <div class="tab-content" id="pills-tabContent">
                                            <div class="tab-pane fade show active" id="pills-home" role="tabpanel" aria-labelledby="pills-home-tab">
                                                <form action="">
                                                    <div class="form--content">
                                                        <div class="form--col12">
                                                            <div class="form--row">
                                                                <div class="pricegold--quantity">
                                                                    <div class="quantity--title">
                                                                        Sản phẩm bán
                                                                    </div>
                                                                    <div class="pricegold--input">
                                                                        <select name="" id="">
                                                                                <option value="">
                                                                                    Nhẫn tròn ép vỉ Kim Gia Bảo
                                                                                </option>
                                                                            </select>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="form--col">
                                                            <div class="form--row">
                                                                <div class="pricegold--quantity">
                                                                    <div class="quantity--title">
                                                                        Số lượng
                                                                    </div>
                                                                    <div class="pricegold--input">
                                                                        <select name="" id="">
                                                                                <option value="">
                                                                                    1 chỉ
                                                                                </option>
                                                                            </select>
                                                                    </div>
                                                                    <div class="--note">
                                                                        Số lượng tối đa 10 (chỉ)
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="form--col">
                                                            <div class="form--row">
                                                                <div class="pricegold--quantity">
                                                                    <div class="quantity--title">
                                                                        Thành tiền
                                                                    </div>
                                                                    <div class="quantity--total">
                                                                        <div class="number">
                                                                            9.120.000
                                                                        </div>
                                                                        <span>
                                                                                vnđ
                                                                            </span>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="chart--table--btn">
                                                        <div class="txt">
                                                            Thời gian giữ giá mua: 16s
                                                        </div>
                                                        <div class="form--btn">
                                                            <button type="submit" class="storesystem--btn">
                                                                    Mua
                                                                </button>
                                                        </div>
                                                    </div>
                                                </form>
                                            </div>
                                            <div class="tab-pane fade" id="pills-profile" role="tabpanel" aria-labelledby="pills-profile-tab">
                                                <form action="">
                                                    <div class="form--content">
                                                        <div class="form--col12">
                                                            <div class="form--row">
                                                                <div class="pricegold--quantity">
                                                                    <div class="quantity--title">
                                                                        Sản phẩm bán
                                                                    </div>
                                                                    <div class="pricegold--input">
                                                                        <select name="" id="">
                                                                                <option value="">
                                                                                    Nhẫn tròn ép vỉ Kim Gia Bảo
                                                                                </option>
                                                                            </select>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="form--col">
                                                            <div class="form--row">
                                                                <div class="pricegold--quantity">
                                                                    <div class="quantity--title">
                                                                        Số lượng
                                                                    </div>
                                                                    <div class="pricegold--input">
                                                                        <select name="" id="">
                                                                                <option value="">
                                                                                    1 chỉ
                                                                                </option>
                                                                            </select>
                                                                    </div>
                                                                    <div class="--note">
                                                                        Số lượng tối đa 10 (chỉ)
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="form--col">
                                                            <div class="form--row">
                                                                <div class="pricegold--quantity">
                                                                    <div class="quantity--title">
                                                                        Thành tiền
                                                                    </div>
                                                                    <div class="quantity--total">
                                                                        <div class="number">
                                                                            9.120.000
                                                                        </div>
                                                                        <span>
                                                                                vnđ
                                                                            </span>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="chart--table--btn">
                                                        <div class="txt">
                                                            Thời gian giữ giá mua: 16s
                                                        </div>
                                                        <div class="form--btn">
                                                            <button type="submit" class="storesystem--btn">
                                                                    Mua
                                                                </button>
                                                        </div>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="home-news">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="box-title">
                                <h2 class="title">
                                    Tin tức
                                </h2>
                            </div>
                            <div class="newshome--list">
                                <div class="swiper newshome--slide">
                                    <div class="swiper-wrapper">
                                        <div class="swiper-slide" v-for="n in 3">
                                            <div class="news--items">
                                                <a href="" class="img">
                                                    <img src="images/img-news1.jpg" alt="">
                                                </a>
                                                <div class="news--body">
                                                    <a href="" class="sub_cate">
                                                            Tin tức vàng
                                                        </a>
                                                    <h3 class="title">
                                                        <a href="">
                                                                Đồng vàng Phát Lộc Tài: Tài lộc vượng phát - Đại cát đại lợi
                                                            </a>
                                                    </h3>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="producbox--btn">
                                    <div class="swiper-button-next swiper-button-next-newshome">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="10" height="16" viewBox="0 0 10 16" fill="none">
                                                <path
                                                    fill-rule="evenodd"
                                                    clip-rule="evenodd"
                                                    d="M1.92383 15.209L0.675545 13.9607L6.91696 7.71929L0.675546 1.47787L1.92383 0.229586L8.78933 7.09509C8.78937 7.09513 8.78939 7.09514 8.16524 7.71929L8.78933 7.09509L9.41353 7.71929L1.92383 15.209Z"
                                                    fill="#4F4F4F"
                                                />
                                            </svg>
                                    </div>
                                    <div class="swiper-button-prev swiper-button-prev-newshome">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="10" height="16" viewBox="0 0 10 16" fill="none">
                                                <path
                                                    fill-rule="evenodd"
                                                    clip-rule="evenodd"
                                                    d="M8.07617 15.209L9.32445 13.9607L3.08304 7.71929L9.32445 1.47787L8.07617 0.229586L1.21067 7.09509C1.21063 7.09513 1.21061 7.09514 1.83476 7.71929L1.21067 7.09509L0.586473 7.71929L8.07617 15.209Z"
                                                    fill="#4F4F4F"
                                                />
                                            </svg>
                                    </div>
                                </div>
                                <div class="producbox--panigation">
                                    <div class="swiper-pagination swiper-pagination-newshome"></div>
                                </div>
                            </div>
                            <div class="box--btn">
                                <a href="" class="btn-links">
                                        Xem thêm tin tức
                                    </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
        <myfooter></myfooter>
    </div>
    <!-- Đặt lịch đến cửa hàng -->
    <div class="modal fade modal--book" id="modal--book" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Đóng">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="32"
                            height="32"
                            viewBox="0 0 32 32"
                            fill="none"
                        >
                            <path d="M17.8643 16L32 30.1562L30.1562 32L16 17.8643L1.84379 32L0 30.1562L14.1357 16L0 1.84379L1.84379 0L16 14.1357L30.1562 0L32 1.84379L17.8643 16Z" fill="#C3C3C3"/>
                        </svg>
                    </button>
                <div class="modalbook-content">
                    <h3 class="title">
                        Đặt lịch đến cửa hàng
                    </h3>
                    <form action="">
                        <div class="form--content">
                            <div class="form--col12">
                                <div class="form--row">
                                    <input type="text" class="input--control" placeholder="Họ và Tên">
                                </div>
                            </div>
                            <div class="form--col12">
                                <div class="form--row">
                                    <select name="" id="" class="input--control">
                                            <option value="">
                                                Cửa hàng
                                            </option>
                                        </select>
                                </div>
                            </div>
                            <div class="form--col12">
                                <div class="form--row">
                                    <select name="" id="" class="input--control">
                                            <option value="">
                                                Giờ hẹn
                                            </option>
                                        </select>
                                </div>
                            </div>
                            <div class="form--col12">
                                <div class="form--row">
                                    <select name="" id="" class="input--control">
                                            <option value="">
                                                Dịch vụ
                                            </option>
                                        </select>
                                </div>
                            </div>
                        </div>
                        <div class="block--btn">
                            <a href="" class="btn--cancel">
                                    Huỷ
                                </a>
                            <a href="javascript:void(0)" onclick="showModalBooksuccess();" class="btn--book">
                                    Đặt lịch
                                </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <!-- Đặt lịch thành công -->
    <div class="modal fade modal--book" id="modal--book--success" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Đóng">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="32"
                            height="32"
                            viewBox="0 0 32 32"
                            fill="none"
                        >
                            <path d="M17.8643 16L32 30.1562L30.1562 32L16 17.8643L1.84379 32L0 30.1562L14.1357 16L0 1.84379L1.84379 0L16 14.1357L30.1562 0L32 1.84379L17.8643 16Z" fill="#C3C3C3"/>
                        </svg>
                    </button>
                <div class="modalbook-content--success">
                    <div class="img">
                        <img src="images/img-success.png" alt="">
                    </div>
                    <h3 class="title">
                        Đặt lịch thành công
                    </h3>
                    <div class="desc">
                        Nhân viên Bảo Tín Mạnh Hải sẽ gọi điện xác nhận yêu cầu đặt lịch của bạn và sắp xếp lịch hẹn của bạn nhanh nhất
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="js/vue.js"></script>
    <script src="js/httpvueloader.js"></script>
    <script src="js/jquery-3.4.1.js"></script>
    <script src="libs/bootstrap/bootstrap.bundle.min.js"></script>
    <script src="libs/lazyload/jquery.lazy.min.js"></script>
    <script src="libs/scrollbar/jquery.scrollbar.min.js"></script>
    <!-- swiper js-->
    <script src="libs/swiper/swiper-bundle.min.js"></script>
    <script src="libs/mansonry/masonry.pkgd.min.js"></script>
    <!-- mmenu -->
    <script src="libs/mmenu/jquery.mmenu.all.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script type="text/javascript" src="libs/aos/aos.js"></script>
    <script src="js/main.js"></script>
    <script></script>
    <script>
        var app = new Vue({
            el: "#app",
            components: {
                myheader: httpVueLoader("vue_components/header.vue"),
                myfooter: httpVueLoader("vue_components/footer.vue"),
                partner: httpVueLoader("vue_components/partner.vue"),
                locationair: httpVueLoader("vue_components/location-air.vue"),
            },
            data: {

            },

            mounted() {
                this.$nextTick(() => {
                    $(window).bind('load', function() {
                        AOS.init({
                            startEvent: 'load',
                        });
                        var swiper = new Swiper(".mySwiper", {
                            loop: false,
                            spaceBetween: 6,
                            slidesPerView: 4,
                            // freeMode: true,
                            direction: getDirection(),
                            watchSlidesProgress: true,
                            // navigation: {
                            //   nextEl: ".custom-nav-prev",
                            //   prevEl: ".custom-nav-next",
                            // },
                            breakpoints: {
                                310: {
                                    spaceBetween: 10,
                                    slidesPerView: 3,
                                },
                                400: {
                                    spaceBetween: 10,
                                    slidesPerView: 4,
                                },

                            },
                        });

                        function getDirection() {
                            var windowWidth = window.innerWidth;
                            var direction = window.innerWidth <= 1279 ? 'horizontal' : 'vertical';

                            return direction;
                        }
                        var swiper2 = new Swiper(".mySwiper2", {
                            loop: false,
                            spaceBetween: 10,
                            effect: 'fade',
                            autoplay: {
                                delay: 5000,
                                disableOnInteraction: false,
                            },
                            navigation: {
                                nextEl: ".swiper-button-next",
                                prevEl: ".swiper-button-prev",
                            },
                            thumbs: {
                                swiper: swiper,
                            },
                        });

                    });
                });
            },
        });
    </script>
</body>

</html>