<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title><PERSON><PERSON><PERSON> thieu</title>
    <!-- mmenu css -->
    <link rel="stylesheet" href="libs/mmenu/jquery.mmenu.all.css">
    <link rel="stylesheet" href="libs/scrollbar/jquery.scrollbar.css">
    <!-- animate css-->
    <link rel="stylesheet" href="libs/animate/animate.min.css">
    <link rel="stylesheet" href="libs/aos/aos.css">
    <!-- swiper css-->
    <link rel="stylesheet" href="libs/swiper/swiper-bundle.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Barlow:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/style.css">
</head>

<body>
    <div id="app">
        <myheader></myheader>
        <main class="">
            <div class="row-breadcrumb">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item">
                                    <a href="#">
                                            Trang chủ
                                        </a>
                                </li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
            <div class="cart-main cart-main--success">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="cart--other">
                                <div class="box-title">
                                    <h2 class="title">
                                        Đặt hàng thành công
                                    </h2>
                                </div>
                                <div class="cartsuccess--box">
                                    <div class="cartsuccess--top">
                                        <div class="cartsuccess--row">
                                            <div class="txt">
                                                Đơn hàng
                                            </div>
                                            <div class="desc">
                                                #HA220472KYCH
                                            </div>
                                        </div>
                                        <div class="cartsuccess--row">
                                            <div class="txt">
                                                Thời gian đặt hàng:
                                            </div>
                                            <div class="desc">
                                                29/04/2025 - 15:48:39
                                            </div>
                                        </div>
                                    </div>
                                    <div class="cartsuccess--body">
                                        <div class="cartsuccess--body---first">
                                            <div class="img">
                                                <img src="images/img-vdd.png" alt="">
                                            </div>
                                            <div class="infor--desc">
                                                <div class="desc">
                                                    <div class="--name">
                                                        Vòng tay khởi sắc thịnh hoàng
                                                    </div>
                                                    <div class="--code">
                                                        VCARA42100
                                                    </div>
                                                    <div class="--parameter">
                                                        <span>Màu: vàng gold</span>
                                                        <span>Size: 36</span>
                                                    </div>
                                                    <div class="--quantity">
                                                        Số lượng: 1
                                                    </div>
                                                </div>
                                                <div class="--price">
                                                    29.000.000
                                                </div>
                                            </div>
                                        </div>
                                        <div class="cart--infor---list">
                                            <div class="cart--infor---row">
                                                <div class="txt">
                                                    Tạm tính
                                                </div>
                                                <div class="number">
                                                    29.000.000
                                                </div>
                                            </div>
                                            <div class="cart--infor---row">
                                                <div class="txt">
                                                    Phí giao hàng
                                                </div>
                                                <div class="number">
                                                    0
                                                </div>
                                            </div>
                                            <div class="cart--infor---row">
                                                <div class="txt">
                                                    Giảm giá
                                                </div>
                                                <div class="number">
                                                    0
                                                </div>
                                            </div>
                                        </div>
                                        <div class="cart--total">
                                            <div class="txt">
                                                Tổng
                                            </div>
                                            <div class="number">
                                                29.000.000
                                                <span>
                                                                    (VND)
                                                                </span>
                                            </div>
                                        </div>
                                        <div class="cartsuccess--pay">
                                            <div class="--title">
                                                Thanh toán
                                            </div>
                                            <div class="cart--infor---list">
                                                <div class="cart--infor---row">
                                                    <div class="txt">
                                                        Hình thức thanh toán
                                                    </div>
                                                    <div class="number">
                                                        Thanh toán khi nhận hàng
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="cartsuccess--pay mb-0">
                                            <div class="--title">
                                                Thông tin nhận hàng
                                            </div>
                                            <div class="cart--infor---list">
                                                <div class="cart--infor---row">
                                                    <div class="txt">
                                                        Họ tên
                                                    </div>
                                                    <div class="number">
                                                        Nguyễn văn A
                                                    </div>
                                                </div>
                                                <div class="cart--infor---row">
                                                    <div class="txt">
                                                        Số điện thoại
                                                    </div>
                                                    <div class="number">
                                                        0123456789
                                                    </div>
                                                </div>
                                                <div class="cart--infor---row">
                                                    <div class="txt">
                                                        Địa chỉ nhận hàng
                                                    </div>
                                                    <div class="number">
                                                        Số 14/553/108 Giải Phóng, Phường Giáp Bát - Quận Hoàng Mai - Tp. Hà Nội
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="cartsuccess--bot">
                                    <div class="desc">
                                        Cám ơn quý khách đã mua hàng tại
                                        <div class="txt">
                                            <span>Bảo Tín Mạnh Hải</span> Đơn hàng #HA220472KYCH
                                        </div> đã được tiếp nhận và đang trong quá trình xử lý. Chúng tôi sẽ liên hệ với quý khách để xử lý đơn hàng ngay lập tức.
                                    </div>
                                    <div class="form--btn">
                                        <a href="javascript:void(0);" class="storesystem--btn">
                                                            Tải hóa đơn về<svg xmlns="http://www.w3.org/2000/svg" width="20" height="18" viewBox="0 0 20 18" fill="none">
  <g clip-path="url(#clip0_3711_16651)">
    <path d="M0.765625 11.2004V17.105H19.2366V11.2004" stroke="#4F4F4F" stroke-miterlimit="10"/>
    <path d="M10 13.1869V0" stroke="#4F4F4F" stroke-miterlimit="10"/>
    <path d="M15.7485 6.87061L9.99924 13.5982L4.25 6.87061" stroke="#4F4F4F" stroke-miterlimit="10"/>
  </g>
  <defs>
    <clipPath id="clip0_3711_16651">
      <rect width="20" height="18" fill="white"/>
    </clipPath>
  </defs>
</svg>
                                                    </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <myfooter></myfooter>
        </main>
    </div>
    <!-- Đặt lịch đến cửa hàng -->
    <div class="modal fade modal--book" id="modal--book" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Đóng">
                    <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32" fill="none">
<path d="M17.8643 16L32 30.1562L30.1562 32L16 17.8643L1.84379 32L0 30.1562L14.1357 16L0 1.84379L1.84379 0L16 14.1357L30.1562 0L32 1.84379L17.8643 16Z" fill="#C3C3C3"/>
</svg>
                </button>
                <div class="modalbook-content">
                    <h3 class="title">
                        Đặt lịch đến cửa hàng
                    </h3>
                    <form action="">
                        <div class="form--content">
                            <div class="form--col12">
                                <div class="form--row">
                                    <input type="text" class="input--control" placeholder="Họ và Tên">
                                </div>
                            </div>
                            <div class="form--col12">
                                <div class="form--row">
                                    <select name="" id="" class="input--control">
                                                                    <option value="">
                                                                       Cửa hàng
                                                                    </option>
                                                                </select>
                                </div>
                            </div>
                            <div class="form--col12">
                                <div class="form--row">
                                    <select name="" id="" class="input--control">
                                                                    <option value="">
                                                                        Giờ hẹn
                                                                    </option>
                                                                </select>
                                </div>
                            </div>
                            <div class="form--col12">
                                <div class="form--row">
                                    <select name="" id="" class="input--control">
                                                                    <option value="">
                                                                       Dịch vụ
                                                                    </option>
                                                                </select>
                                </div>
                            </div>
                        </div>
                        <div class="block--btn">
                            <a href="" class="btn--cancel">
Huỷ
                            </a>
                            <a href="javascript:void(0)" onclick="showModalBooksuccess();" class="btn--book">
Đặt lịch
                            </a>
                        </div>
                    </form>
                </div>

            </div>
        </div>
    </div>
    <!-- Đặt lịch thành công -->
    <div class="modal fade modal--book" id="modal--book--success" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Đóng">
                    <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32" fill="none">
<path d="M17.8643 16L32 30.1562L30.1562 32L16 17.8643L1.84379 32L0 30.1562L14.1357 16L0 1.84379L1.84379 0L16 14.1357L30.1562 0L32 1.84379L17.8643 16Z" fill="#C3C3C3"/>
</svg>
                </button>
                <div class="modalbook-content--success">
                    <div class="img">
                        <img src="images/img-success.png" alt="">
                    </div>
                    <h3 class="title">
                        Đặt lịch thành công
                    </h3>
                    <div class="desc">
                        Nhân viên Bảo Tín Mạnh Hải sẽ gọi điện xác nhận yêu cầu đặt lịch của bạn và sắp xếp lịch hẹn của bạn nhanh nhất
                    </div>
                </div>

            </div>
        </div>
    </div>
    <script src="js/vue.js"></script>
    <script src="js/httpvueloader.js"></script>
    <script src="js/jquery-3.4.1.js"></script>
    <script src="libs/bootstrap/bootstrap.bundle.min.js"></script>
    <script src="libs/lazyload/jquery.lazy.min.js"></script>
    <script src="libs/scrollbar/jquery.scrollbar.min.js"></script>
    <!-- swiper js-->
    <script src="libs/swiper/swiper-bundle.min.js"></script>
    <script src="libs/mansonry/masonry.pkgd.min.js"></script>
    <!-- mmenu -->
    <script src="libs/mmenu/jquery.mmenu.all.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script type="text/javascript" src="libs/aos/aos.js"></script>
    <script src="js/main.js"></script>
    <script></script>
    <script>
        var app = new Vue({
            el: "#app",
            components: {
                myheader: httpVueLoader("vue_components/header.vue"),
                myfooter: httpVueLoader("vue_components/footer.vue"),
                partner: httpVueLoader("vue_components/partner.vue"),
                locationair: httpVueLoader("vue_components/location-air.vue"),
            },
            data: {

            },

            mounted() {
                this.$nextTick(() => {
                    $(window).bind('load', function() {
                        AOS.init({
                            startEvent: 'load',
                        });
                        var swiper = new Swiper(".mySwiper", {
                            loop: false,
                            spaceBetween: 6,
                            slidesPerView: 4,
                            // freeMode: true,
                            direction: getDirection(),
                            watchSlidesProgress: true,
                            // navigation: {
                            //   nextEl: ".custom-nav-prev",
                            //   prevEl: ".custom-nav-next",
                            // },
                            breakpoints: {
                                310: {
                                    spaceBetween: 10,
                                    slidesPerView: 3,
                                },
                                400: {
                                    spaceBetween: 10,
                                    slidesPerView: 4,
                                },

                            },
                        });

                        function getDirection() {
                            var windowWidth = window.innerWidth;
                            var direction = window.innerWidth <= 1279 ? 'horizontal' : 'vertical';

                            return direction;
                        }
                        var swiper2 = new Swiper(".mySwiper2", {
                            loop: false,
                            spaceBetween: 10,
                            effect: 'fade',
                            autoplay: {
                                delay: 5000,
                                disableOnInteraction: false,
                            },
                            navigation: {
                                nextEl: ".swiper-button-next",
                                prevEl: ".swiper-button-prev",
                            },
                            thumbs: {
                                swiper: swiper,
                            },
                        });

                    });
                });
            },
        });
    </script>
</body>

</html>