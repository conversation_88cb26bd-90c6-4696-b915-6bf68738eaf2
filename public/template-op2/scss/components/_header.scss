body {
    word-break: break-word;
    font-family: "SVN-Artifex CF", sans-serif;
    font-size: 16px;
    font-weight: 400;
}

.container {
    // max-width: 1200px;
}

.box--language {
    display: flex;
    align-items: center;
    color: #9E9E9E;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    button {
        border: 0;
        border-radius: 0;
        box-shadow: none;
        background: transparent;
        color: #9E9E9E;
        font-size: 16px;
        font-style: normal;
        line-height: normal;
        &.active {
            text-decoration-line: underline;
            text-decoration-style: solid;
            text-decoration-skip-ink: auto;
            text-decoration-thickness: auto;
            text-underline-offset: auto;
            text-underline-position: from-font;
        }
        &:focus,
        &:focus-visible {
            outline: none;
            box-shadow: none;
        }
    }
    @media screen and (max-width: 991px) {
        button {
            font-size: 14px;
            padding: 0;
        }
    }
}

main {
    // margin-top: 131px;
}

.mm-page {
    main {
        // margin-top: 0 !important;
    }
}

header {
    z-index: 1000;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: #fff;
    .header-top-main {
        display: flex;
        width: 100%;
        justify-content: space-between;
        align-items: center;
        padding: 32px 0 65px;
        position: relative;
        .header--action {
            display: flex;
            align-items: center;
            gap: 8px;
            ._icon {
                flex-shrink: 0;
            }
            .txt {
                color: #9E9E9E;
                font-size: 16px;
                font-style: normal;
                line-height: normal;
                font-weight: 600;
            }
        }
        .header-sp {
            display: flex;
            gap: 32px;
            align-items: center;
        }
        .header-sp-r {
            display: flex;
            gap: 32px;
            align-items: center;
            .box--language {
                display: none;
            }
            &>.search-head {
                max-width: 146px;
                width: 100%;
                height: auto;
                .box-search {
                    max-width: 100%;
                    display: block;
                    position: unset;
                    .search-form {
                        position: relative;
                        input {
                            background: transparent;
                        }
                    }
                }
            }
        }
    }
    .header-top-wrap {
        width: 100%;
        // padding: 0 10px;
        .header-logo {
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
            margin: auto;
            height: max-content;
            width: max-content;
            max-width: 208px;
            img {
                &.img-df {
                    display: block;
                }
                &.img-scr {
                    display: none;
                }
            }
        }
    }
    &.header-static {
        position: static;
        box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.10);
        .logo-scroll {
            display: block;
            @media (max-width: 480px) {
                // display: none;
            }
        }
        .logo-df {
            display: none;
            @media (max-width: 480px) {
                // display: block;
            }
        }
        .header-top {
            padding: 14px 0 20px;
            @media (max-width: 480px) {
                // padding: 0;
            }
        }
        .header-menu {
            .header-menu-wrap {
                .header-menu-root {
                    .menu-root {
                        color: #333;
                        svg {
                            path {
                                stroke: #333;
                            }
                        }
                        &:hover,
                        &.active {
                            color: #1A68B3;
                            svg {
                                path {
                                    stroke: #1A68B3;
                                }
                            }
                        }
                    }
                    // .sub-menu {
                    //     .menu-root {
                    //         color: #828282;
                    //     }
                    // }
                }
            }
        }
        @media (min-width: 320px) {
            .search-head {
                background: transparent url(../images/ic-5.png) no-repeat center center;
            }
            .box_head_right {
                .gamuda_language {
                    .gamuda_language_child {
                        a {
                            color: #333;
                            &.active {}
                            &:first-child {
                                &::after {
                                    background: #333;
                                }
                            }
                        }
                    }
                }
            }
            .bar {
                background: #333;
                &:before {
                    background: #333;
                }
                &:after {
                    background: #333;
                }
            }
        }
    }
    .banner-header-top {
        height: 16px;
        @media (max-width: 1366px) {
            height: 12px;
        }
        img {
            height: 16px;
            width: 100%;
            vertical-align: top;
            @media (max-width: 1366px) {
                height: 12px;
            }
        }
    }
    .header-top {
        @media (max-width: 991px) {
            display: none;
        }
        @media (max-width: 480px) {}
        .header-top-wrapbot {
            width: 100%;
            display: flex;
            flex-wrap: wrap;
            // position: relative;
            align-items: center;
            .header-menu {}
        }
    }
    .menu-line {
        position: relative;
        margin: 0 auto;
        height: max-content;
        width: 32px;
        @media (max-width: 480px) {}
        @media (max-width: 400px) {
            width: 25px;
        }
        .lines {
            cursor: pointer;
            // position: absolute;
            // z-index: 2;
            // height: 100%;
            // width: 100%;
            // top: 50%;
            // right: 10px;
            -webkit-transition: all 0.2s;
            -o-transition: all 0.2s;
            transition: all 0.2s;
            margin-bottom: 0;
        }
        .line {
            position: absolute;
            height: 2px;
            width: 100%;
            background-color: #2c2c35;
            -webkit-transition: all 0.3s;
            -o-transition: all 0.3s;
            transition: all 0.3s;
            margin-left: 0 !important;
        }
        .diagonal.part-1 {
            position: relative;
            float: left;
        }
        .diagonal.part-2 {
            position: relative;
            float: left;
            margin-top: 6px;
        }
        .horizontal {
            position: relative;
            float: left;
            margin-top: 6px;
        }
    }
    .mobile-ham {
        display: none;
        @media (max-width: 991px) {
            display: block;
            height: max-content;
        }
        @media (max-width: 1024px) {
            // top: 23px;
        }
        @media (max-width: 570px) {
            // top: 18px;
        }
        a.icon-menu-mb {
            display: inline-block;
            svg {
                transform: rotate(180deg);
            }
        }
    }
    &.header--home {
        background: transparent;
        .header-menu {
            .header-menu-wrap {
                .header-menu-root {
                    .menu-root {
                        color: #fff;
                    }
                }
            }
        }
        .box--language {
            color: #fff;
            button {
                color: #fff;
            }
        }
        .mobile-ham a.icon-menu-mb svg {
            path {
                fill: #fff;
            }
        }
        .header-top-main {
            .header--action {
                ._icon {
                    svg {
                        path {
                            stroke: #fff;
                        }
                    }
                }
                .txt {
                    color: #fff;
                }
            }
            .header-sp-r>.search-head .box-search .search-form {
                button {
                    svg {
                        path {
                            stroke: #fff;
                        }
                    }
                }
                input {
                    border-bottom: 1px solid #fff;
                    color: #fff;
                    &::placeholder {
                        color: #fff;
                    }
                }
            }
            @media screen and (max-width: 991px) {
                .search-head {
                    background: transparent url(../images/ic-5-1.png) no-repeat center center;
                }
            }
        }
        .header-top-wrap {
            .header-logo {
                img {
                    &.img-df {
                        display: none;
                    }
                    &.img-scr {
                        display: block;
                    }
                }
            }
        }
        &.fixed {
            .header-top-wrap {
                .header-logo {
                    img {
                        &.img-df {
                            display: block;
                        }
                        &.img-scr {
                            display: none;
                        }
                    }
                }
            }
            .box--language {
                color: #9E9E9E;
                button {
                    color: #9E9E9E;
                }
            }
            .mobile-ham a.icon-menu-mb svg {
                path {
                    fill: #7E7E7E;
                }
            }
            .header-top-main {
                .header--action {
                    ._icon {
                        svg {
                            path {
                                stroke: #9E9E9E;
                            }
                        }
                    }
                    .txt {
                        color: #9E9E9E;
                    }
                }
                .header-sp-r>.search-head .box-search .search-form {
                    button {
                        svg {
                            path {
                                stroke: #9E9E9E;
                            }
                        }
                    }
                    input {
                        border-bottom: 1px solid #9E9E9E;
                        color: #4f4f4f;
                        &::placeholder {
                            color: #4f4f4f;
                        }
                    }
                }
                @media screen and (max-width: 991px) {
                    .search-head {
                        background: transparent url(../images/ic-5.png) no-repeat center center;
                    }
                }
            }
        }
    }
    &.fixed {
        position: fixed;
        width: 100%;
        top: 0;
        left: 0;
        filter: drop-shadow(0px 4px 20px rgba(0, 0, 0, 0.08));
        z-index: 1000;
        background: #fff;
        animation: MoveDown .6s;
        @media (max-width: 480px) {
            // background: #1A68B3;
        }
        .banner-header-top {
            display: none;
        }
        .header-top {
            @media (max-width: 480px) {
                // padding: 0;
            }
        }
        .header-menu {
            .header-menu-wrap {
                .header-menu-root {
                    .menu-root {
                        color: #4F4F4F;
                        span {
                            bottom: calc(100% - 15px);
                        }
                    }
                }
            }
        }
        .header-top-main {
            @media screen and (min-width: 992px) {
                display: none;
            }
        }
    }
    @media screen and (max-width: 991px) {
        background: #fff;
        .header-top-main {
            padding: 32px 0;
            &>.search-head {
                display: none;
            }
            .header--action {
                .txt {
                    display: none;
                }
            }
            .header-sp {
                gap: 10px;
                .box--language,
                .header--action {
                    display: none;
                }
            }
            .header-sp-r {
                gap: 10px;
                .box--language {
                    display: block;
                }
                &>.search-head {
                    display: none !important;
                }
                .box_head_right {
                    display: none;
                }
            }
        }
        .header-logo {
            display: block;
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
            margin: auto;
            width: max-content;
            height: max-content;
        }
    }
    @media screen and (max-width: 767px) {
        .header-top-main {
            padding: 20px 0;
        }
        .header-logo {
            img {
                max-width: 120px;
            }
        }
    }
    @media screen and (max-width: 375px) {
        .header-logo {
            img {
                max-width: 104px;
            }
        }
    }
}

.boxmenu-style {
    .boxmenu--other {
        width: 100%;
        text-align: left;
        max-width: 314px;
        .title--menu {
            color: #7C0410;
            text-align: center;
            font-size: 20px;
            font-style: normal;
            font-weight: 600;
            line-height: normal;
            margin: 0 0 30px;
            display: block;
            text-align: left;
        }
        .desc--menu {
            color: #4F4F4F;
            font-size: 18px;
            font-style: normal;
            font-weight: 400;
            line-height: 150%;
            margin-bottom: 70px;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 3;
            overflow: hidden;
            min-height: 81px;
        }
        .boxmenu-style--list {
            display: flex;
            flex-wrap: wrap;
            margin-bottom: 54px;
            >* {
                width: 100%;
                color: #4F4F4F;
                font-size: 18px;
                font-style: normal;
                font-weight: 400;
                line-height: normal;
                text-transform: capitalize;
                &:hover,
                &.active {
                    color: #7C0410;
                }
            }
        }
        .box--btn {
            .btn-links {
                font-size: 20px;
            }
        }
    }
    &.boxmenu-style2 {
        display: flex;
        gap: 100px;
        .boxmenu-style--items {
            width: max-content;
            max-width: calc(33.3333% - 66.6666px);
        }
    }
    @media screen and (max-width: 1400px) {
        .boxmenu--other {
            .title--menu {
                font-size: 18px;
            }
            .desc--menu {
                font-size: 16px;
                min-height: 81px;
            }
            .boxmenu-style--list {
                >* {
                    font-size: 16px;
                }
            }
            .box--btn {
                .btn-links {
                    font-size: 18px;
                }
            }
        }
    }
    @media screen and (max-width: 1200px) {
        .boxmenu--other {
            .box--btn {
                .btn-links {
                    font-size: 16px;
                }
            }
        }
        &.boxmenu-style2 {
            gap: 50px;
            .boxmenu-style--items {
                max-width: calc(33.3333% - 33.3333px);
            }
        }
    }
}

main.page-home {
    padding-top: 0 !important;
}

// .position_static {
//     position: static;
//     .header-menu {
//         .header-menu-wrap {
//             .header-menu-root {
//                 .menu-root {
//                     color: var(--gray-1, #333);
//                 }
//             }
//         }
//     }
//     .logo-scroll {
//         display: block;
//         @media (max-width: 480px) {
//             display: none;
//         }
//     }
//     .logo-df {
//         display: none;
//         @media (max-width: 480px) {
//             display: block;
//         }
//     }
//     @media (min-width: 481px) {
//         .search-head {
//             // filter: brightness(0);
//             // -webkit-filter: brightness(0);
//         }
//         .box_head_right {
//             .gamuda_language {
//                 .gamuda_language_child {
//                     a {
//                         color: rgba(51, 51, 51, 0.50);
//                         &::after {
//                             background-color: #333 !important;
//                         }
//                         &.active {
//                             color: var(--gray-1, #333);
//                         }
//                     }
//                 }
//             }
//         }
//     }
// }
.logo-scroll {
    display: none;
}

#mmenu:not(.mm-menu) {
    display: none;
}

.img-menu {
    display: block;
    width: 100%;
    padding-top: 72.2%;
    overflow: hidden;
    position: relative;
    img {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        object-fit: cover;
        object-position: center;
    }
}

.header-menu {
    width: 100%;
    .header-menu-wrap {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .header-menu-root {
            text-align: center;
            @media (min-width: 1500px) {}
            @media (max-width: 1280px) {}
            &:last-child {
                margin-right: 0px;
            }
            &:first-child {
                // .menu-root {
                //     img {
                //         margin-top: -6px;
                //     }
                // }
            }
            .menu-root {
                color: #4F4F4F;
                text-align: center;
                font-size: 20px;
                font-style: normal;
                line-height: normal;
                position: relative;
                font-weight: 600;
                padding: 15px 0;
                display: block;
                &::after {
                    content: '';
                    display: block;
                    width: 100%;
                    height: 3px;
                    background: #7C0410;
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    opacity: 0;
                    visibility: hidden;
                }
                span {
                    display: block;
                    width: 29px;
                    height: 29px;
                    position: absolute;
                    bottom: calc(100% - 3px);
                    left: 100%;
                    img {
                        max-width: 100%;
                    }
                }
                @media (max-width: 1500px) {
                    font-size: 15px;
                }
                svg {
                    path {
                        // stroke: #fff;
                    }
                }
                &:hover,
                &.active {
                    color: #7C0410;
                    svg {
                        path {
                            stroke: #000;
                        }
                    }
                    &::after {
                        opacity: 1;
                        visibility: visible;
                    }
                }
                @media (max-width: 1280px) {}
                svg {
                    margin-left: 4px;
                }
                &.menu-home {
                    .ic-home {
                        display: block;
                    }
                    .ic-home-ac {
                        display: none;
                    }
                    &::before {
                        display: none !important;
                    }
                    &.active,
                    &:hover {
                        .ic-home {
                            display: none;
                        }
                        .ic-home-ac {
                            display: block;
                        }
                    }
                }
                &:hover {
                    .icon-dropdown svg path {
                        fill: #FFF;
                    }
                }
                &.active {
                    &::before {
                        width: 100%;
                        transition: all 0.5s;
                    }
                    .icon-dropdown svg path {
                        fill: #FFF;
                    }
                }
            }
            &>.menu-root {
                position: relative;
            }
            &.has-sub {
                &:hover {
                    &>.menu-root {
                        color: #7C0410;
                        &::after {
                            opacity: 1;
                            visibility: visible;
                        }
                    }
                }
            }
            .sub-menu {
                opacity: 0;
                visibility: hidden;
                position: absolute;
                top: calc(100% - 1px);
                left: 0;
                transform: translateX(0);
                transition: all 0.4s;
                z-index: 200;
                width: 100%;
                background: #fff;
                border-top: 1px solid #D5D5D5;
                padding: 58px 0 73px;
                .container {
                    padding: 0 15px;
                }
                .col-lg-8 {
                    width: 70.4%;
                }
                .col-lg-8 {
                    width: 29.6%;
                }
            }
        }
        .has-sub {
            // position: relative;
            // .icon-dropdown {
            //     position: absolute;
            //     top: 13px;
            //     right: 0px;
            //     svg path {
            //         fill: #FFF;
            //     }
            //     &.rotate-90 {
            //         svg {
            //             transform: rotate(-90deg);
            //         }
            //     }
            // }
            &:hover {
                // .icon-dropdown svg path {
                //     fill: #FFF;
                // }
                &>.sub-menu {
                    opacity: 1;
                    visibility: visible;
                }
                &>.menu-root {
                    color: #000;
                    &::before {
                        width: 100%;
                    }
                }
            }
            &.menu-child {
                // &>.sub-menu {
                //     left: 100%;
                //     right: -30px;
                //     width: calc(100% + 52px);
                //     top: 0px;
                //     overflow-y: auto;
                //     max-height: 400px;
                //     &::-webkit-scrollbar {
                //         height: 6px;
                //         width: 6px;
                //     }
                //     &::-webkit-scrollbar-thumb {
                //         background-color: #cedceb;
                //         border-radius: 3px;
                //     }
                //     &::-webkit-scrollbar-track {
                //         background: transparent;
                //     }
                //     &::selection {
                //         background: #02c8c6;
                //         color: #fff;
                //     }
                // }
            }
        }
    }
    @media (max-width: 1200px) {
        .header-menu-wrap {}
    }
    @media (max-width: 991px) {
        display: none;
    }
}

.language {
    &.language-mb {
        display: none;
        @media (max-width: 992px) {
            display: block;
            position: absolute;
            right: 42px;
            top: 8px;
            .dropdown-menu {
                padding: 10px;
            }
            .langitem {
                font-weight: 400 !important;
                font-size: 14px;
                &.current-lang {
                    text-transform: uppercase;
                    font-weight: 400 !important;
                }
            }
        }
    }
    .dropdown-toggle {
        display: flex;
        align-items: center;
        padding: 0px;
        line-height: 20px;
        height: 20px;
        @media (max-width: 1199px) {
            padding: 0px 25px;
            margin-right: 42px;
        }
        @media (max-width: 400px) {
            margin-right: 20px;
        }
        .icon-dropdown {
            margin-left: 0px;
        }
        &::after {
            display: none;
        }
    }
    .dropdown-menu {
        min-width: 80px;
        padding: 0;
        border-radius: 0;
        box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.15);
        border: none;
        margin-top: 7px !important;
        border-top: 4px solid #DCEAFE;
        li {
            border-bottom: 0.5px solid #C9CBCD;
            &:last-child {
                border-bottom: none;
            }
            .langitem {
                border-right: none;
                padding: 11px 0;
                padding-right: 0px;
                white-space: nowrap;
                justify-content: center;
                text-transform: uppercase;
            }
            &:hover,
            &.active {
                .langitem {
                    color: #FFA01C;
                }
            }
        }
    }
    .langitem {
        display: flex;
        align-items: center;
        font-weight: 400;
        font-size: 18px;
        line-height: 21px;
        color: #000;
        padding-right: 5px;
        &.current-lang {
            color: #5B5B65;
        }
        span.icon {
            display: flex;
            margin-right: 5px;
            width: 26px;
            height: 26px;
            border-radius: 0px;
            overflow: hidden;
        }
    }
    @media (max-width: 768px) {
        .dropdown-toggle {
            padding: 7px 12px;
        }
    }
}

.hamburger-menu {
    position: absolute;
    top: 0%;
    right: 0;
    bottom: 0;
    margin: auto;
    height: 25px;
    cursor: pointer;
    -webkit-tap-highlight-color: rgba(255, 255, 255, 0);
    display: none;
    @media (max-width: 1199px) {
        display: block;
    }
}

.bar,
.bar:after,
.bar:before {
    width: 14px;
    height: 2px;
}

.hamburger-menu {
    .bar,
    .bar:after,
    .bar:before {
        width: 32px;
        height: 2px;
    }
}

.bar {
    position: relative;
    transform: translateY(10px);
    background: #000;
    transition: all 0ms 300ms;
    &:before {
        content: "";
        position: absolute;
        left: 0;
        bottom: 10px;
        background: #fff;
        transition: bottom 300ms 300ms cubic-bezier(0.23, 1, 0.32, 1), transform 300ms cubic-bezier(0.23, 1, 0.32, 1);
    }
    &:after {
        content: "";
        position: absolute;
        left: 0;
        top: 10px;
        background: #fff;
        transition: top 300ms 300ms cubic-bezier(0.23, 1, 0.32, 1), transform 300ms cubic-bezier(0.23, 1, 0.32, 1);
    }
    &.animate {
        background: rgba(255, 255, 255, 0);
        &:after {
            top: 0;
            transform: rotate(45deg);
            transition: top 300ms cubic-bezier(0.23, 1, 0.32, 1), transform 300ms 300ms cubic-bezier(0.23, 1, 0.32, 1);
        }
        &:before {
            bottom: 0;
            transform: rotate(-45deg);
            transition: bottom 300ms cubic-bezier(0.23, 1, 0.32, 1), transform 300ms 300ms cubic-bezier(0.23, 1, 0.32, 1);
        }
    }
}


/*---------------------
  Mobiles Menu 
  ----------------------*/


/*---------------------
      Mobiles Menu - Design 
      ----------------------*/

.mobile-menu ul {
    margin: 0;
    padding: 0;
    &.mobile-menu-list {
        width: 100%;
        height: 100%;
        overflow-y: scroll;
        overflow-x: hidden;
    }
}

.mobile-menu {
    .logo-mobile {
        text-align: center;
        a.header-logo {
            display: none !important;
        }
        #closeButton {
            background: rgba(0, 0, 0, 0);
            border: none;
            position: absolute;
            top: 24px;
            right: 13px;
        }
    }
    li {
        font-style: normal;
        font-weight: 500;
        font-size: 16px;
        line-height: 22px;
        list-style: none;
        border-bottom: 1px solid #F2F2F2;
        padding: 12px 0;
        position: relative;
        color: #333;
        &:hover {
            color: #262262;
        }
    }
    .gamuda_language {
        display: block;
        position: absolute;
        left: 20px;
        bottom: 40px;
        padding-left: 0;
        .gamuda_language_child {
            a {
                color: rgba(51, 51, 51, 0.5);
                font-size: 21px !important;
                &.active {
                    color: rgba(51, 51, 51, 1);
                }
                &:first-child::after {
                    background-color: rgba(51, 51, 51, 1);
                }
            }
        }
    }
}

.mobile-menu li:first-child {
    margin-top: 20px;
}

.mobile-menu li {
    a {
        display: block;
        width: 100%;
        text-decoration: none;
        color: #333;
        &:hover,
        &.active {
            color: #1A68B3;
            &~.icon-arrow {
                svg {
                    path {
                        fill: #1A68B3;
                    }
                }
            }
        }
        &.icon-arrow {
            transition: .6s;
            -webkit-transition: .6s;
            -moz-transition: .6s;
            width: 30px;
            height: 50px;
            position: absolute;
            display: flex;
            top: 0;
            right: 0;
            align-items: center;
            justify-content: center;
            svg {
                path {
                    fill: #333;
                }
            }
            &.open {
                transform: rotate(-180deg);
                -webkit-transform: rotate(-180deg);
                -moz-transform: rotate(-180deg);
                svg {
                    path {
                        fill: #1A68B3;
                    }
                }
            }
        }
    }
}


/*---------------------
      Mobiles Menu - Slide IN 
      ----------------------*/

.mobile-menu {
    top: 0;
    max-width: 336px;
    right: -100%;
    width: 100%;
    background: #fff;
    color: black;
    padding: 24px 20px;
    height: 100vh;
    position: fixed;
    z-index: 9997;
    overflow-y: auto;
    -webkit-transform: translate3d(0, 0, 205px);
    -moz-transform: translate3d(0, 0, 205px);
    transform: translate3d(0, 0, 205px);
    -webkit-transition: all 500ms ease-in-out;
    -moz-transition: all 500ms ease-in-out;
    transition: all 500ms ease-in-out;
    .children {
        li {
            padding-left: 10px;
            border-top: 1px solid #F2F2F2;
            border-bottom: none;
            &:last-child {
                padding-bottom: 0;
            }
            &:first-child {
                margin-top: 14px;
            }
        }
    }
}

.mobile-menu.active {
    right: 0;
    -webkit-transform: translate3d(0, 0, 0);
    -moz-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    -webkit-transition: all 500ms ease-in-out;
    -moz-transition: all 500ms ease-in-out;
    transition: all 500ms ease-in-out;
    padding-bottom: 90px;
}


/*---------------------
      Mobiles Menu - Dropdown Submenu
      ----------------------*/

.has-children:hover {
    cursor: hand;
}

.children {
    display: none;
}

.mm-navbar__title {
    text-indent: -99999px;
    background-image: url('../images/image-home/logo-alina.png');
    background-repeat: no-repeat;
    background-position: center;
    background-size: 90px auto;
}

.box_head_right {
    display: flex;
    align-items: center;
    display: none;
    @media (max-width: 991px) {
        display: block;
    }
}

.gamuda_language {
    padding-left: 20px;
    display: flex;
    align-items: center;
    @media (max-width: 1500px) {
        padding-left: 10px;
    }
    @media (max-width: 1200px) {
        display: none;
    }
    .gamuda_language_child {
        a {
            display: inline-block;
            text-transform: uppercase;
            color: rgba(255, 255, 255, 0.5);
            margin: 0 10px;
            position: relative;
            font-size: 21px;
            @media (max-width: 1500px) {
                font-size: 15px;
            }
            @media (max-width: 1300px) {
                font-size: 13px;
            }
            &.active {
                color: rgba(255, 255, 255, 1);
                font-weight: 700;
            }
            &:first-child {
                margin-left: 0;
            }
            &:last-child {
                margin-right: 0;
            }
            &:first-child::after {
                display: block;
                content: '';
                width: 3px;
                height: 65%;
                position: absolute;
                top: 0;
                right: -13px;
                bottom: 0;
                margin: auto;
                background-color: rgba(255, 255, 255, 1);
            }
        }
    }
}

.search-head {
    position: relative;
    z-index: 50;
    width: 15px;
    height: 23px;
    .search-head-ic {
        display: block;
        width: 100%;
        height: 100%;
    }
    .link-search {
        img {
            margin-top: -3px;
        }
    }
    .box-search {
        display: none;
        position: fixed;
        z-index: 100;
        width: 270px;
        background: transparent;
        right: 0;
        left: 0;
        margin: auto;
        top: 100px;
        transition: 0.85s;
        border-radius: 29px;
        @media (max-width: 1199px) {}
        @media (max-width: 480px) {
            position: fixed;
            top: 90px;
            width: calc(100% - 30px);
            left: 0;
            right: 0;
            margin: auto;
        }
        .search-form {
            position: relative;
            z-index: 5;
            input {
                color: #4f4f4f;
                height: 28px;
                width: 100%;
                border: 0;
                padding: 0 4px 0 30px;
                border-radius: 0;
                border-bottom: 1px solid #9E9E9E;
                background: #FFF;
                font-size: 16px;
                &::placeholder {
                    color: #9E9E9E;
                    opacity: 1;
                    line-height: 20px;
                }
                &:-internal-autofill-selected {
                    background: #fff !important;
                }
            }
            button {
                border: none;
                background-color: transparent;
                outline: none;
                padding: 0;
                left: 3px;
                bottom: 4px;
                margin: auto;
                position: absolute;
                align-items: center;
                justify-content: center;
                width: 20px;
                height: 20px;
                @media (max-width: 820px) {
                    display: flex;
                }
                span.icon img {
                    max-width: 100%;
                }
                &:active,
                &:focus-visible,
                &:focus {
                    outline: none;
                    box-shadow: none;
                }
            }
        }
        &.active {
            display: block;
            &::after {
                content: '';
                display: block;
                width: 100%;
                height: 100vh;
                background-color: rgba(0, 0, 0, 0.3);
                overflow: hidden;
                position: fixed;
                top: 0;
                left: 0;
                z-index: 1;
            }
            input {
                padding: 0 5px 0 30px;
                background-color: #f7f3f3;
            }
        }
    }
    @media (max-width: 991px) {
        background: transparent url('../images/ic-5.png') no-repeat center center;
        background-size: auto;
    }
    @media (max-width: 480px) {
        .box-search {
            // width: 300px;
        }
    }
}

@-webkit-keyframes MoveDown {
    0% {
        -webkit-transform: translateY(-100%);
        transform: translateY(-100%);
    }
    100% {
        -webkit-transform: translateY(0);
        transform: translateY(0);
    }
}

@keyframes MoveDown {
    0% {
        -webkit-transform: translateY(-100%);
        transform: translateY(-100%);
    }
    100% {
        -webkit-transform: translateY(0);
        transform: translateY(0);
    }
}

@-webkit-keyframes MoveUp {
    0% {
        -webkit-transform: translateY(100%);
        transform: translateY(100%);
    }
    100% {
        -webkit-transform: translateY(0);
        transform: translateY(0);
    }
}

@keyframes MoveUp {
    0% {
        -webkit-transform: translateY(100%);
        transform: translateY(100%);
    }
    100% {
        -webkit-transform: translateY(0);
        transform: translateY(0);
    }
}

.mm-listitem__text {
    text-transform: capitalize;
}

.mm-navbar {
    background: #f3f3f3;
    a {
        color: #333 !important;
    }
}

.mm-listview {
    background: #f3f3f3;
    a {
        color: #333;
    }
}

.mm-menu .item-menu-mb {
    color: #333 !important;
}

.mm-menu .item-menu-mb.active {
    color: #1A68B3 !important;
}

.mm-btn_next:after {
    right: 33px;
    border-color: #003828;
}

.mm-btn_next .mm-counter {
    min-width: auto;
}

.mm-listitem {
    border-color: rgba(0, 0, 0, 0.1) !important;
    &::after {
        right: 20px !important;
        border-color: rgba(0, 0, 0, 0.1);
    }
}

.mm-btn_prev:before {
    border-color: #003828;
}

.mm-panels {
    .sub-menu {
        .menu-child {
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            .menu-root {
                font-weight: 400;
                font-size: 16px;
                line-height: 138%;
                color: #333333;
                display: block;
                padding: 10px 0;
            }
        }
    }
}

.mm-panels>.mm-panel>.mm-listview:first-child,
.mm-panels>.mm-panel>.mm-navbar+.mm-listview {
    margin-top: 0;
}

.mm-panel {
    padding: 0 15px;
    &:after,
    &:before {
        display: none;
    }
}

.mm-btn_close:after,
.mm-btn_close:before {
    border-color: #003828;
}

.mm-panel:not(.mm-hidden) {
    background: #f3f3f3;
}

.bgover-white {
    position: absolute;
    top: 100%;
    height: 50px;
    background-color: #fff;
    width: 100%;
    left: 0;
    display: none;
}

.mm-btn_next .mm-counter {
    display: none;
}

.mm-navbars_bottom {
    border: 0;
}