.footer {
    width: 100%;
    background-color: #fff;
    padding: 57px 0 50px;
    .footer--top {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        gap: 20px 80px;
        margin-bottom: 75px;
        .footer--col {
            flex: 1;
            width: 100%;
            display: flex;
            flex-wrap: wrap;
            flex-direction: column;
            justify-content: space-between;
            .footer--title {
                width: 100%;
                color: #4F4F4F;
                font-size: 20px;
                font-style: normal;
                font-weight: 900;
                line-height: normal;
                padding-bottom: 17px;
                border-bottom: 1px solid #D5D5D5;
                margin-bottom: 52px;
            }
            .footer--body {
                width: 100%;
                .name--company {
                    color: #4F4F4F;
                    font-size: 18px;
                    font-style: normal;
                    font-weight: 700;
                    line-height: normal;
                    text-transform: capitalize;
                    max-width: 330px;
                    margin: 0 0 29px;
                }
                .footer--address {
                    width: 100%;
                    display: flex;
                    flex-wrap: wrap;
                    gap: 25px 44px;
                    ._left {
                        width: calc(55% - 22px);
                        .item {
                            width: 100%;
                            margin-bottom: 25px;
                            color: #4F4F4F;
                            font-size: 16px;
                            font-style: normal;
                            line-height: normal;
                            font-weight: 600;
                            &:last-child {
                                margin-bottom: 0;
                            }
                        }
                    }
                    ._right {
                        width: calc(45% - 22px);
                        .item {
                            color: #4F4F4F;
                            font-size: 16px;
                            font-style: normal;
                            line-height: normal;
                            margin-bottom: 16px;
                            font-weight: 600;
                            &:last-child {
                                margin-bottom: 0;
                            }
                            .txt {
                                @extend .item;
                                margin: 0;
                            }
                            a {
                                @extend .item;
                                font-size: 20px;
                                line-height: 135%;
                            }
                        }
                    }
                }
                ul {
                    padding: 0;
                    margin: 0;
                    display: flex;
                    flex-wrap: wrap;
                    gap: 0 30px;
                    width: 100%;
                    max-width: 550px;
                    li {
                        width: calc(50% - 15px);
                        list-style: none;
                        a {
                            color: #4F4F4F;
                            font-size: 20px;
                            font-style: normal;
                            line-height: 195%;
                            font-weight: 600;
                            &.active,
                            &:hover {
                                color: #7C0410;
                            }
                        }
                    }
                }
            }
        }
        .footer--logo {
            flex-shrink: 0;
        }
    }
    .footer--bot {
        width: 100%;
        position: relative;
        .ic-dmca {
            display: block;
            max-width: 185px;
            position: absolute;
            top: 27px;
            left: 0;
            img {
                max-width: 100%;
            }
        }
        .title {
            text-align: center;
            position: relative;
            span {
                display: inline-block;
                width: max-content;
                background-color: #fff;
                position: relative;
                z-index: 3;
                color: #4F4F4F;
                text-align: center;
                font-size: 20px;
                font-style: normal;
                font-weight: 900;
                line-height: normal;
                padding: 0 30px;
            }
            &::after {
                content: '';
                display: block;
                width: 100%;
                position: absolute;
                height: 1px;
                background-color: #D5D5D5;
                top: 0;
                left: 0;
                bottom: 0;
                margin: auto;
            }
        }
        .footer--bot---main {
            width: 100%;
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 10px 131px;
            position: relative;
            padding-top: 47px;
            a {
                color: #4F4F4F;
                text-align: center;
                font-size: 14px;
                font-style: normal;
                line-height: 142.857%;
                letter-spacing: 1.5px;
                text-transform: uppercase;
            }
        }
    }
    @media screen and (max-width: 1600px) {
        .footer--top {
            .footer--col {
                .footer--title {
                    font-size: 18px;
                }
                .footer--body {
                    .name--company {
                        font-size: 16px;
                    }
                    .footer--address {
                        ._right {
                            .item {
                                a {
                                    font-size: 18px;
                                }
                            }
                        }
                    }
                    ul {
                        li {
                            a {
                                font-size: 18px;
                            }
                        }
                    }
                }
            }
        }
        .footer--bot {
            .title {
                span {
                    font-size: 18px;
                }
            }
        }
    }
    @media screen and (max-width: 1200px) {
        .footer--top {
            .footer--col {
                .footer--body {
                    .footer--address {
                        ._left {
                            width: 100%;
                        }
                        ._right {
                            width: 100%;
                        }
                    }
                }
            }
        }
        .footer--bot {
            .footer--bot---main {
                gap: 10px 60px;
            }
        }
    }
    @media screen and (max-width: 991px) {
        padding: 40px 0;
        .footer--top {
            .footer--col {
                .footer--title {
                    font-size: 16px;
                }
                .footer--body {
                    .name--company {
                        font-size: 14px;
                    }
                    .footer--address {
                        ._right {
                            .item {
                                a {
                                    font-size: 16px;
                                }
                            }
                        }
                    }
                    ul {
                        li {
                            a {
                                font-size: 16px;
                            }
                        }
                    }
                }
            }
        }
        .footer--bot {
            .footer--bot---main {
                gap: 10px 40px;
                flex-wrap: wrap;
            }
            .title {
                span {
                    font-size: 16px;
                }
            }
            .ic-dmca {
                position: unset;
                margin: 20px auto 0;
                width: 100%;
                max-width: 100%;
                text-align: center;
            }
        }
    }
    @media screen and (max-width: 767px) {
        .footer--top {
            margin-bottom: 40px;
            .footer--logo {
                display: flex;
                order: -1;
                margin: 0 auto;
            }
            .footer--col {
                width: 100%;
                flex: auto;
                .footer--title {
                    margin-bottom: 30px;
                }
            }
        }
        .footer--bot {
            .footer--bot---main {
                padding-top: 35px;
            }
        }
    }
    @media screen and (max-width: 480px) {
        .footer--top {
            .footer--col {
                .footer--body {
                    ul {
                        li {
                            width: 100%;
                        }
                    }
                }
            }
        }
    }
}