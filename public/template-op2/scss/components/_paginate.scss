:root {
    --scale-px: 1px;
}

.mg-auto {
    margin: 0 auto;
}

.tx-center {
    text-align: center;
}

.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px 16px;
    margin: 82px 0 0;
    @media screen and (max-width: 991px) {
        margin: 40px 0 0;
    }
    .page-item {
        border: none;
        border-radius: 100%;
        &:last-child,
        &:first-child {
            .page-link {
                line-height: 21px !important;
                background-color: transparent !important;
                color: #4F4F4F !important;
                border: 0 !important;
                &::after {
                    display: none;
                }
            }
        }
        &.disabled {
            cursor: not-allowed;
        }
        &.active,
        &:hover {
            .page-link {
                border-bottom: 1px solid #C50;
                color: #C50;
                svg {
                    path {
                        fill: #fff;
                    }
                }
            }
        }
        .page-link {
            border: 1px solid transparent;
            background: transparent;
            color: #4F4F4F;
            font-family: "Nunito Sans";
            font-size: 16px;
            font-style: normal;
            font-weight: 600;
            line-height: normal;
            display: flex;
            justify-content: center;
            align-items: center;
            box-shadow: none;
            width: 40px;
            height: 40px;
            svg {
                vertical-align: inherit;
            }
        }
    }
}

@-webkit-keyframes shake-bottom {
    0%,
    100% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
        -webkit-transform-origin: 50% 100%;
        transform-origin: 50% 100%
    }
    10% {
        -webkit-transform: rotate(2deg);
        transform: rotate(2deg)
    }
    20%,
    40%,
    60% {
        -webkit-transform: rotate(-4deg);
        transform: rotate(-4deg)
    }
    30%,
    50%,
    70% {
        -webkit-transform: rotate(4deg);
        transform: rotate(4deg)
    }
    80% {
        -webkit-transform: rotate(-2deg);
        transform: rotate(-2deg)
    }
    90% {
        -webkit-transform: rotate(2deg);
        transform: rotate(2deg)
    }
}

@keyframes shake-bottom {
    0%,
    100% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
        -webkit-transform-origin: 50% 100%;
        transform-origin: 50% 100%
    }
    10% {
        -webkit-transform: rotate(2deg);
        transform: rotate(2deg)
    }
    20%,
    40%,
    60% {
        -webkit-transform: rotate(-4deg);
        transform: rotate(-4deg)
    }
    30%,
    50%,
    70% {
        -webkit-transform: rotate(4deg);
        transform: rotate(4deg)
    }
    80% {
        -webkit-transform: rotate(-2deg);
        transform: rotate(-2deg)
    }
    90% {
        -webkit-transform: rotate(2deg);
        transform: rotate(2deg)
    }
}

@-webkit-keyframes heartbeat {
    from {
        -webkit-transform: scale(1);
        transform: scale(1);
        -webkit-transform-origin: center center;
        transform-origin: center center;
        -webkit-animation-timing-function: ease-out;
        animation-timing-function: ease-out
    }
    10% {
        -webkit-transform: scale(.91);
        transform: scale(.91);
        -webkit-animation-timing-function: ease-in;
        animation-timing-function: ease-in
    }
    17% {
        -webkit-transform: scale(.98);
        transform: scale(.98);
        -webkit-animation-timing-function: ease-out;
        animation-timing-function: ease-out
    }
    33% {
        -webkit-transform: scale(.87);
        transform: scale(.87);
        -webkit-animation-timing-function: ease-in;
        animation-timing-function: ease-in
    }
    45% {
        -webkit-transform: scale(1);
        transform: scale(1);
        -webkit-animation-timing-function: ease-out;
        animation-timing-function: ease-out
    }
}

@keyframes heartbeat {
    from {
        -webkit-transform: scale(1);
        transform: scale(1);
        -webkit-transform-origin: center center;
        transform-origin: center center;
        -webkit-animation-timing-function: ease-out;
        animation-timing-function: ease-out
    }
    10% {
        -webkit-transform: scale(.91);
        transform: scale(.91);
        -webkit-animation-timing-function: ease-in;
        animation-timing-function: ease-in
    }
    17% {
        -webkit-transform: scale(.98);
        transform: scale(.98);
        -webkit-animation-timing-function: ease-out;
        animation-timing-function: ease-out
    }
    33% {
        -webkit-transform: scale(.87);
        transform: scale(.87);
        -webkit-animation-timing-function: ease-in;
        animation-timing-function: ease-in
    }
    45% {
        -webkit-transform: scale(1);
        transform: scale(1);
        -webkit-animation-timing-function: ease-out;
        animation-timing-function: ease-out
    }
}

@-webkit-keyframes vibrate-1 {
    0% {
        -webkit-transform: translate(0);
        transform: translate(0)
    }
    20% {
        -webkit-transform: translate(-2px, 2px);
        transform: translate(-2px, 2px)
    }
    40% {
        -webkit-transform: translate(-2px, -2px);
        transform: translate(-2px, -2px)
    }
    60% {
        -webkit-transform: translate(2px, 2px);
        transform: translate(2px, 2px)
    }
    80% {
        -webkit-transform: translate(2px, -2px);
        transform: translate(2px, -2px)
    }
    100% {
        -webkit-transform: translate(0);
        transform: translate(0)
    }
}

@keyframes vibrate-1 {
    0% {
        -webkit-transform: translate(0);
        transform: translate(0)
    }
    20% {
        -webkit-transform: translate(-2px, 2px);
        transform: translate(-2px, 2px)
    }
    40% {
        -webkit-transform: translate(-2px, -2px);
        transform: translate(-2px, -2px)
    }
    60% {
        -webkit-transform: translate(2px, 2px);
        transform: translate(2px, 2px)
    }
    80% {
        -webkit-transform: translate(2px, -2px);
        transform: translate(2px, -2px)
    }
    100% {
        -webkit-transform: translate(0);
        transform: translate(0)
    }
}

@-webkit-keyframes width-border {
    0% {
        width: 0;
    }
    100% {
        width: 100%;
    }
}

@keyframes width-border {
    0% {
        width: 0;
    }
    100% {
        width: 100%;
    }
}

@-webkit-keyframes width-bg {
    0% {
        right: -100%;
    }
    100% {
        left: 0;
    }
}

@keyframes width-bg {
    0% {
        right: -100%;
    }
    100% {
        left: 0;
    }
}

@keyframes trackBallSlide {
    0% {
        opacity: 1;
        transform: translateY(12px)
    }
    15% {
        opacity: 0;
        transform: translateY(-8px)
    }
    30% {
        opacity: 1;
        transform: translateY(12px)
    }
    50% {
        opacity: 0;
        transform: translateY(-8px)
    }
    60% {
        opacity: 1;
        transform: translateY(12px)
    }
    100% {
        opacity: 1;
        transform: translateY(12px)
    }
}

@media (min-width: 768px) {
    .container {
        // max-width: calc(100% - 80px);
    }
}

@media (min-width: 1600px) {
    .container {
        max-width: 1670px;
    }
}

@keyframes rotateMouse {
    0%,
    100%,
    30% {
        -webkit-transform: rotateZ(0);
        transform: rotateZ(0)
    }
    10% {
        -webkit-transform: rotateZ(10deg);
        transform: rotateZ(10deg)
    }
    20% {
        -webkit-transform: rotateZ(-10deg);
        transform: rotateZ(-10deg)
    }
}

@keyframes shake {
    10%,
    90% {
        transform: translate3d(-1px, -0, -1px);
    }
    20%,
    80% {
        transform: translate3d(2px, 0, 2px);
    }
    30%,
    50%,
    70% {
        transform: translate3d(-4px, -0, -4px);
    }
    40%,
    60% {
        transform: translate3d(4px, 0, 4px);
    }
}

.bg_overlay {
    position: fixed;
    width: 100%;
    height: 100vh;
    top: 0;
    right: -100%;
    z-index: 9996;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    &.active {
        display: block;
        right: 0;
        transform: translate3d(0, 0, 0);
        transition: all 500ms ease-in-out;
    }
}

.scrollbar-macosx>.scroll-element.scroll-x {
    height: 7px;
}

.scroll-wrapper>.scroll-content::-webkit-scrollbar {
    height: 0;
    width: 0;
}

.scrollbar-macosx>.scroll-element.scroll-x .scroll-bar {
    top: 0;
}

.scrollbar-macosx:hover>.scroll-element .scroll-bar,
.scrollbar-macosx>.scroll-element.scroll-draggable .scroll-bar {
    opacity: 0.7;
}

._boxfixed {
    width: 65px;
    position: fixed;
    display: flex;
    gap: 6px;
    right: 30px;
    bottom: 150px;
    flex-direction: column;
    align-items: center;
    z-index: 55;
    .icon-mess {
        width: 65px;
        height: 65px;
        display: block;
        img {
            max-width: 100%;
        }
    }
    .img-banner--fix {
        width: max-content;
        height: max-content;
        position: absolute;
        bottom: calc(100% + 12px);
        right: 0;
    }
    @media screen and (max-width: 767px) {
        width: 40px;
        right: 15px;
        .icon-mess {
            width: 40px;
            height: 40px;
        }
        .img-banner--fix {
            width: 80px;
        }
    }
}

.back-top {
    width: 40px;
    height: 40px;
    z-index: 888;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: 0.2s;
    border-radius: 50%;
    background: #fff;
    box-shadow: 0 0 7px 0px rgba(0, 0, 0, 0.1);
}

.icon-mess {
    position: relative;
    .icon-mess-other {
        position: absolute;
        bottom: -35px;
        right: calc(100% + 17px);
        border-radius: 8px;
        background: #FFF;
        box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.06);
        padding: 0 20px;
        width: 220px;
        display: flex;
        flex-wrap: wrap;
        a {
            width: 100%;
            padding: 20px 0 17px;
            border-bottom: 1px solid #E9E9E9;
            display: flex;
            gap: 15px;
            align-items: center;
            &:last-child {
                padding: 17px 0 20px;
                border-bottom: 0;
            }
            .icon {
                width: 40px;
                flex-shrink: 0;
                .img {
                    max-width: 100%;
                }
            }
            .txt {
                color: rgba(0, 0, 0, 0.80);
                font-size: 18px;
                font-style: normal;
                font-weight: 700;
                line-height: normal;
                letter-spacing: 0.36px;
                flex: 1;
                width: 100%;
                span {
                    display: block;
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: normal;
                    letter-spacing: 0.28px;
                    width: 100%;
                }
            }
        }
    }
    @media screen and (max-width: 767px) {
        .icon-mess-other {
            padding: 0 15px;
            width: 180px;
            a {
                padding: 15px 0;
                gap: 10px;
                &:last-child {
                    padding: 15px 0;
                    border-bottom: 0;
                }
                .icon {
                    width: 30px;
                }
                .txt {
                    font-size: 15px;
                    span {
                        font-size: 12px;
                    }
                }
            }
        }
    }
}

#fp-nav,
.fp-slidesNav {
    ul {
        li {
            margin: 10px 7px;
            height: auto;
            a {
                background: #2da0d0;
                width: 10px;
                height: 10px;
                border-radius: 50%;
                span {
                    width: 11px;
                    height: 11px;
                    display: none;
                }
                &.active {
                    border-radius: 50px;
                    height: 30px;
                    background: #0ED3FE;
                }
            }
            &:hover {
                a {
                    background: #0ED3FE;
                    height: 30px;
                    border-radius: 50px;
                    &.active {
                        height: 30px;
                        border-radius: 50px;
                    }
                }
            }
        }
    }
}

#fp-nav {
    ul {
        li {
            .fp-tooltip {
                max-width: fit-content;
                color: #262262;
                text-align: center;
                font-size: 16px;
                font-weight: 700;
                line-height: 137.5%;
                padding: 8px 20px;
                background: #fff;
            }
        }
    }
}

.btn_all {
    display: inline-flex;
    gap: 10px;
    position: relative;
    align-items: center;
    color: #000;
    font-size: 14px;
    line-height: 171.429%;
    background-color: transparent;
    transition: all 0.3s;
    padding-left: 0;
    overflow: hidden;
    border-radius: 40px;
    span {
        position: relative;
        z-index: 2;
        &.icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 42px;
            height: 42px;
            border-radius: 50%;
            background-color: #1A68B3;
            img {
                max-width: 100%;
            }
            svg {
                path {
                    fill: #fff;
                }
            }
        }
    }
    &:hover {
        color: #fff;
        background-color: #1A68B3;
        padding-left: 17px;
        span.icon {
            background-color: transparent;
            svg {
                path {}
            }
        }
    }
}

@keyframes BeProud {
    100% {
        background-position: 100vw 0px
    }
}

@keyframes bg-banner {
    100% {
        background-position: 100% 0%;
    }
}

.news_item {
    width: 100%;
    height: 100%;
    overflow: hidden;
    .news_item_top {
        width: 100%;
        margin-bottom: 36px;
        display: block;
        .img {
            @include box-img(60.5%, center, cover);
        }
        @media screen and (max-width: 1550px) and (min-width: 1200px) {
            margin-bottom: 20px;
        }
    }
    .news_item_body {
        width: 100%;
        padding-bottom: 30px;
        .news_item_tagdate {
            width: 100%;
            display: flex;
            color: #828282;
            font-size: 16px;
            text-transform: uppercase;
            flex-wrap: wrap;
            gap: 5px 10px;
            margin-bottom: 10px;
            @media screen and (max-width: 767px) {
                font-size: 14px;
            }
            a {
                color: #828282;
            }
        }
        .title {
            color: #333;
            font-size: 24px;
            font-weight: 700;
            line-height: 125%;
            letter-spacing: -0.48px;
            @include line-clamp(2, auto);
            margin-bottom: 30px;
            &:hover {
                color: #262262;
            }
            @media screen and (max-width: 1550px) and (min-width: 1200px) {
                font-size: 20px !important;
            }
        }
        .txt {
            color: #828282;
            font-size: 18px;
            @include line-clamp(3, auto);
        }
    }
}

.modalall {
    .modal-dialog {
        max-width: 600px;
        .modal-content {
            border: 0;
            border-radius: 0;
            box-shadow: none;
            padding: 68px 30px 90px;
            background: #fff;
            .btn-close {
                position: absolute;
                width: 27px;
                height: 27px;
                border: 0;
                padding: 0;
                top: 10px;
                opacity: 1;
                right: 10px;
                background: transparent;
                img {
                    max-width: 100%;
                }
            }
            .modal_main {
                width: 100%;
                max-width: 470px;
                margin: 0 auto;
                .title {
                    color: #262262;
                    text-align: center;
                    font-size: 26px;
                    font-weight: 700;
                    text-transform: uppercase;
                    margin-bottom: 10px;
                }
                .txt {
                    color: #828282;
                    text-align: center;
                    font-size: 18px;
                    letter-spacing: -0.36px;
                }
                .img {
                    text-align: center;
                    margin-bottom: 40px;
                    img {
                        max-width: 100%;
                    }
                }
            }
        }
    }
}

.loading-atg-other {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 105vh;
    z-index: 99999;
    .loading-atg-child {
        position: relative;
        width: 100%;
        height: 100%;
    }
}

.loading-atg {
    position: sticky;
    top: 0;
    left: 0;
    width: 100%!important;
    height: 100vh!important;
    display: flex;
    align-items: center;
    justify-content: center;
    background: transparent url('../images/img-ldhome.jpg?v=1.1') no-repeat center center;
    background-size: cover;
    canvas {
        @media screen and (max-width: 767px) {
            width: 85% !important;
            height: auto !important;
        }
    }
    img {
        max-width: 100%;
        @media screen and (max-width: 574px) {
            max-width: 70%;
        }
    }
}

body.fix-height {
    // overflow: hidden;
    // background-color: #453d76;
    // .page-home {
    //     opacity: 0;
    // }
    // header {
    //     opacity: 0;
    // }
}

.row-breadcrumb {
    width: 100%;
    background-color: transparent;
    padding-top: 20px;
    padding-bottom: 0;
    nav {
        width: 100%;
        float: left;
    }
    @media (max-width: 767px) {
        padding: 10px 5px;
    }
    .breadcrumb {
        display: flex;
        flex-wrap: wrap;
        align-items: flex-start;
        justify-content: flex-start;
        padding: 0px 0 29px;
        border-bottom: 1px solid #D5D5D5;
        margin-bottom: 0px;
        list-style: none;
        background-color: transparent;
        border-radius: 0;
        width: 100%;
        .breadcrumb-item {
            margin-bottom: 0px;
            color: #757575;
            font-size: 12px;
            font-style: normal;
            line-height: 140%;
            text-align: left;
            margin-right: 20px;
            padding-left: 0;
            &:last-child {
                margin-right: 0;
                a {
                    overflow: hidden;
                    display: -webkit-box;
                    -webkit-line-clamp: 1;
                    -webkit-box-orient: vertical;
                    white-space: normal;
                }
            }
            a {
                color: inherit;
                text-transform: none;
                &:hover {
                    color: #7C0410;
                    svg {
                        path {
                            fill: #7C0410;
                        }
                    }
                }
            }
        }
        .breadcrumb-item+.breadcrumb-item::before {
            content: '|';
            margin-right: 20px;
            color: #BDBDBD;
            padding-right: 0;
        }
    }
    &.row-breadcrumb-detail {
        padding-top: 15px;
        .breadcrumb {
            border: 0;
        }
    }
    @media (max-width: 1200px) {}
    @media (max-width: 767px) {
        .breadcrumb .breadcrumb-item {
            a {}
        }
    }
}