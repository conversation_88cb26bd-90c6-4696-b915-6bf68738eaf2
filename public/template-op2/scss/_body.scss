@charset "UTF-8";
body {
    background: #fff;
    font-weight: 400;
    font-size: 16px;
    line-height: 1.42;
    color: #000;
    overflow-x: hidden;
    word-break: break-word;
}

@mixin line-clamp($a, $b) {
    display: -webkit-box;
    -webkit-line-clamp: $a;
    -webkit-box-orient: vertical;
    overflow: hidden;
    min-height: $b;
}

@mixin after($width, $height, $background, $z-index) {
    content: '';
    width: $width;
    height: $height;
    background: $background;
    position: absolute;
    top: 0;
    left: 0;
    z-index: $z-index;
}

@mixin box-img($padding, $position, $fit) {
    display: block;
    width: 100%;
    position: relative;
    padding-top: $padding;
    overflow: hidden;
    img {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        object-position: $position;
        object-fit: $fit;
        transition: 0.85s;
    }
}

@mixin col-w($width) {
    flex: 0 0 $width;
    max-width: $width;
}

@mixin font($color, $size, $height) {
    color: $color;
    font-weight: $weight;
    font-size: $size;
    line-height: $height;
}

$white: #fff;
$black: #000000;
$red: #aa1f23;
$themegray:rgba(79, 79, 79, 0.7);
$themegray-50:rgba(79, 79, 79, 0.5);
$themegray-20:rgba(79, 79, 79, 0.2);
$theme: #EC1C24;
$color-blue: #1f4f9f;
$color-orange: #f48220;
img {
    max-width: 100%;
    vertical-align: middle;
}

.error-massage-input {
    display: block;
    color: red;
    font-size: 14px;
    margin: 10px 0;
}

.is-invalid {
    display: block;
}

.line-though {
    text-decoration: line-through;
}

.line-through {
    border-top: 1px solid #dadce0;
    display: inline-block;
    width: 100%;
}

a {
    text-decoration: none;
    transition: 0.3s all;
    &:focus,
    &:hover {
        text-decoration: none;
        transition: 0.3s all;
    }
}

.form-control:focus,
.lending-sidebar-right .sidebar input:focus,
.lending-sidebar-right .sidebar select:focus {
    border-color: $theme;
    box-shadow: 0 0 0 0.2rem rgba($color: $theme, $alpha: 0.25);
}

.hover-scale {
    transition: all 0.3s;
    position: relative;
    overflow: hidden;
    img {
        transition: all 0.3s;
    }
    &:hover {
        img {
            transform: scale(1.1);
        }
    }
}

.back-to-top {
    position: fixed;
    bottom: 50px;
    right: 30px;
    background-color: #f36f21;
    width: 50px;
    height: 50px;
    line-height: 50px;
    border-radius: 100%;
    text-align: center;
    font-size: 30px;
    color: #fff;
    z-index: 111;
}

.title-theme {
    color: $theme;
}

.page_static .wrap_content {
    padding: 30px 0 60px;
}

.scrollbar-style-asset {
    overflow-y: auto;
    &::-webkit-scrollbar {
        width: 8px;
        height: 3px;
        border-radius: 20px;
    }
    &::-webkit-scrollbar-track {
        background: #D9D9D9;
        border-radius: 20px;
    }
    &::-webkit-scrollbar-thumb {
        background: var(--xanh, #1A68B3);
        border-radius: 20px;
    }
}


/*============================
==============================
==============================
error 404
==============================
==============================
============================*/

.page_404 {
    min-height: 100vh;
    padding-top: 20px;
    padding-bottom: 20px;
    @extend .d-flex,
    .align-items-center;
    img {
        max-width: 100%;
    }
    h4 {
        font-size: 18px;
        color: rgba(65, 65, 65, 255);
        margin-top: 20px;
        margin-bottom: 15px;
    }
    a {
        background: #388cf5;
        color: #fff;
        font-weight: normal;
    }
}

@media (max-width: 768px) {
    .back-to-top {
        display: none;
    }
    .page_static .wrap_content {
        padding: 0px 0 30px;
    }
}

@media (max-width: 439px) {
    .type-title-1 p {
        font-size: 22px;
    }
}

@keyframes loader-rotate {
    100% {
        transform: rotate(360deg);
    }
}

@keyframes loader-dash {
    0% {
        stroke-dasharray: 1, 200;
        stroke-dashoffset: 0;
    }
    50% {
        stroke-dasharray: 89, 200;
        stroke-dashoffset: -35px;
    }
    100% {
        stroke-dasharray: 89, 200;
        stroke-dashoffset: -136px;
    }
}

button {
    cursor: pointer;
}

button,
input,
optgroup,
select,
textarea {
    outline: none;
    box-shadow: 0;
    &:focus,
    &:active,
    &:hover {
        outline: none;
        box-shadow: 0;
    }
}

.modal-open {
    .mm-slideout {
        z-index: unset;
    }
}

.lazy[src='images/loading-icon.jpeg'] {
    object-fit: scale-down !important;
    max-width: 20px;
}

@each $breakpoint in map-keys($grid-breakpoints) {
    @include media-breakpoint-up($breakpoint) {
        $infix: breakpoint-infix($breakpoint, $grid-breakpoints);
        @for $i from 10 through 50 {
            .fs#{$infix}-#{$i} {
                font-size: #{$i}px;
            }
        }
    }
}

.nav-breadcrumb {
    margin: 20px 0;
    .breadcrumb {
        .breadcrumb-item {
            font-size: 12px;
            a {
                color: #606368;
                &:hover {
                    color: $theme;
                }
            }
        }
    }
}

// line-clamp
@for $i from 1 through 10 {
    .line-clamp-#{$i} {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: #{$i};
        overflow: hidden;
    }
}

.box-title {
    width: 100%;
    margin-bottom: 34px;
    .title {
        color: #7C0410;
        font-size: 40px;
        font-style: normal;
        line-height: 150%;
        text-transform: capitalize;
        font-weight: 600;
        margin: 0;
    }
    .sub_title {
        color: #7C0410;
        font-size: 30px;
        font-style: normal;
        line-height: 150%;
        text-transform: capitalize;
        font-weight: 600;
    }
    .desc {
        color: #4F4F4F;
        font-size: 20px;
        font-style: normal;
        line-height: 150%;
        font-weight: 600;
    }
    @media screen and (max-width: 1600px) {
        .sub_title {
            font-size: 26px;
        }
        .title {
            font-size: 36px;
        }
    }
    @media screen and (max-width: 1400px) {
        .sub_title {
            font-size: 24px;
        }
        .title {
            font-size: 32px;
        }
        .desc {
            font-size: 18px;
        }
    }
    @media screen and (max-width: 1200px) {
        .title {
            font-size: 28px;
        }
        .sub_title {
            font-size: 20px;
        }
    }
    @media screen and (max-width: 991px) {
        .title {
            font-size: 26px;
        }
        .sub_title {
            font-size: 18px;
        }
        .desc {
            font-size: 16px;
        }
    }
    @media screen and (max-width: 767px) {
        .title {
            font-size: 22px;
        }
    }
}

.btn-links {
    color: #7C0410;
    font-size: 24px;
    font-style: normal;
    line-height: 140%;
    padding-bottom: 7px;
    display: inline-block;
    border-bottom: 1px solid #7C0410;
    font-weight: 500;
    &:hover {
        color: #7C0410;
    }
    @media screen and (max-width: 1400px) {
        font-size: 22px;
    }
    @media screen and (max-width: 991px) {
        font-size: 18px;
    }
    @media screen and (max-width: 768px) {
        font-size: 16px;
    }
}

.producbox--btn {
    .swiper-button-next,
    .swiper-button-prev {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        border: 1px solid #D5D5D5;
        background: #FFF;
        opacity: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        &::after {
            display: none;
        }
        &.swiper-button-lock {
            display: none;
        }
    }
    .swiper-button-next {
        right: -18px;
    }
    .swiper-button-prev {
        left: -18px;
    }
}

.producbox--panigation {
    .swiper-pagination {
        position: unset;
        width: 100%;
        .swiper-pagination-bullet {
            background-color: #9E9E9E;
            opacity: 0.5;
            &.swiper-pagination-bullet-active {
                background-color: #CCB26A;
                opacity: 1;
            }
        }
    }
}

.productcate--items {
    width: 100%;
    .img {
        display: block;
        overflow: hidden;
        border: 0.5px solid #D9D9D9;
        background: linear-gradient(180deg, #620004 0%, #840207 100%);
        padding: 5px;
        margin-bottom: 16px;
        span {
            display: block;
            width: 100%;
            position: relative;
            padding-top: 100%;
            overflow: hidden;
            img {
                width: 100%;
                height: 100%;
                position: absolute;
                top: 0;
                left: 0;
                object-fit: scale-down;
                object-position: center;
                transition: all 0.3s;
            }
        }
    }
    .cate--title {
        color: #4F4F4F;
        font-size: 20px;
        font-style: normal;
        font-weight: 700;
        line-height: normal;
        margin: 0;
        a {
            color: inherit;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
        }
    }
    &:hover {
        .img {
            span {
                img {
                    transform: scale(1.1);
                }
            }
        }
        .cate--title {
            color: #7C0410;
        }
    }
    @media screen and (max-width: 1400px) {
        .cate--title {
            font-size: 18px;
        }
    }
}

.product--items {
    width: 100%;
    display: block;
    height: 100%;
    border: 1px solid #D5D5D5;
    padding: 33px 28px 27px 33px;
    .img {
        display: block;
        width: 100%;
        position: relative;
        overflow: hidden;
        padding-top: 100%;
        margin-bottom: 55px;
        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
            position: absolute;
            top: 0;
            left: 0;
            transition: all .3s;
        }
    }
    .product--body {
        position: relative;
        width: 100%;
        .title {
            color: #4F4F4F;
            font-size: 20.074px;
            font-style: normal;
            font-weight: 700;
            line-height: 150%;
            margin: 0 0 13px;
            a {
                color: inherit;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 2;
                overflow: hidden;
            }
        }
        .gold--infor {
            display: flex;
            align-items: center;
            color: #AE8751;
            font-size: 16.059px;
            line-height: 150%;
            margin: 0 0 12px;
        }
        .price {
            color: #4F4F4F;
            font-size: 20.074px;
            font-style: normal;
            font-weight: 700;
            line-height: 122.304%;
            padding-right: 43px;
        }
        .add--cart {
            border: 0;
            border-radius: 0;
            box-shadow: none;
            background: transparent;
            margin: 0;
            padding: 0;
            position: absolute;
            bottom: 0;
            right: 0;
            width: max-content;
            height: max-content;
        }
    }
    &:hover {
        .img {
            img {
                transform: scale(1.2);
            }
        }
        .product--body {
            .title {
                color: #7C0410;
            }
            .price {
                color: #7C0410;
            }
        }
    }
    @media screen and (max-width:1600px) {
        padding: 25px 20px;
        .product--body {
            .title {
                font-size: 18px;
            }
            .gold--infor {
                font-size: 16px;
            }
            .price {
                font-size: 18px;
            }
            .add--cart {
                svg {
                    max-width: 35px;
                    height: auto;
                }
            }
        }
    }
    @media screen and (max-width:991px) {
        padding: 25px 20px;
        .img {
            margin-bottom: 30px;
        }
        .product--body {
            .title {
                font-size: 18px;
            }
            .gold--infor {
                font-size: 16px;
            }
            .price {
                font-size: 16px;
            }
            .add--cart {
                svg {
                    max-width: 35px;
                    height: auto;
                }
            }
        }
    }
    @media screen and (max-width: 767px) {
        padding: 17px 15px;
        .img {
            margin-bottom: 34px;
        }
        .product--body {
            .title {
                font-size: 9.317px;
                margin-bottom: 6px;
            }
            .gold--infor {
                font-size: 7.454px;
                margin-bottom: 5px;
            }
            .price {
                font-size: 9.317px;
            }
            .add--cart {
                svg {
                    max-width: 18px;
                    height: auto;
                }
            }
        }
    }
}

.news--items {
    width: 100%;
    height: 100%;
    display: block;
    .img {
        display: block;
        width: 100%;
        overflow: hidden;
        padding-top: 62.4%;
        position: relative;
        margin-bottom: 40px;
        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
            position: absolute;
            top: 0;
            left: 0;
            transition: all 0.3s;
        }
    }
    .news--body {
        width: 100%;
        .sub_cate {
            display: block;
            width: 100%;
            margin-bottom: 8px;
            color: #AE8751;
            font-size: 16px;
            font-style: normal;
            line-height: 129%;
            text-transform: capitalize;
        }
        .title {
            width: 100%;
            color: #4F4F4F;
            font-size: 20px;
            font-weight: 700;
            line-height: 129%;
            text-transform: capitalize;
            a {
                color: inherit;
            }
        }
    }
    &:hover {
        .img {
            img {
                transform: scale(1.2);
            }
        }
        .news--body {
            .title {
                color: #7C0410;
            }
        }
    }
    @media screen and (max-width: 991px) {
        .img {
            margin-bottom: 20px;
        }
        .news--body {
            .title {
                width: 100%;
                font-size: 18px;
            }
        }
    }
    @media screen and (max-width: 767px) {
        .news--body {
            .title {
                font-size: 16px;
            }
        }
    }
}