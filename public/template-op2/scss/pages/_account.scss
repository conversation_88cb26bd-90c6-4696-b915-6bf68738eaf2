.account-main {
    width: 100%;
    padding: 126px 10px 205px;
    @media screen and (max-width: 991px) {
        padding: 40px 10px;
    }
}

.account-content {
    width: 100%;
    max-width: 804px;
    margin: 0 auto;
    .nav {
        gap: 34px;
        margin-bottom: 51px;
        .nav-item {
            flex: 1;
            width: 100%;
            .nav-link {
                border: 0;
                border-radius: 0;
                padding: 0 0 20px;
                border-bottom: 1px solid #9E9E9E;
                width: 100%;
                text-align: left;
                color: #9E9E9E;
                font-size: 36px;
                font-style: normal;
                font-weight: 600;
                line-height: 129%;
                text-transform: capitalize;
                &.active {
                    color: #4F4F4F;
                    border-bottom: 1px solid #AE8751;
                    background-color: transparent;
                }
            }
        }
        .show>.nav-link {
            color: #4F4F4F;
            border-bottom: 1px solid #AE8751;
            background-color: transparent
        }
    }
    .account-form {
        .--note {
            color: #4F4F4F;
            font-size: 20px;
            font-style: normal;
            font-weight: 400;
            line-height: 120%;
            margin-bottom: 51px;
        }
        .form--btn {
            .storesystem--btn {
                width: 100%;
                max-width: 100%;
            }
        }
        &.account-form--register {
            .form--content {
                margin-bottom: 109px;
            }
        }
    }
    @media screen and (max-width: 1600px) {
        .nav {
            margin-bottom: 40px;
            .nav-item {
                .nav-link {
                    font-size: 32px;
                }
            }
        }
        .account-form {
            .--note {
                font-size: 18px;
                margin-bottom: 40px;
            }
            &.account-form--register {
                .form--content {
                    margin-bottom: 70px;
                }
            }
        }
    }
    @media screen and (max-width: 1400px) {
        .nav {
            .nav-item {
                .nav-link {
                    font-size: 26px;
                }
            }
        }
        .account-form {
            .--note {
                font-size: 16px;
            }
        }
    }
    @media screen and (max-width: 991px) {
        .nav {
            .nav-item {
                .nav-link {
                    font-size: 22px;
                }
            }
        }
    }
}

.form--check {
    .form--check--title {
        color: #4F4F4F;
        font-size: 20px;
        font-style: normal;
        font-weight: 400;
        line-height: 120%;
        margin-bottom: 12px;
    }
    .form--name {
        display: flex;
        gap: 10px 20px;
        flex-wrap: wrap;
        .form--name--items {
            label {
                position: relative;
                padding: 8px 18px;
                min-width: 75px;
                color: #4F4F4F;
                text-align: center;
                font-size: 12.624px;
                font-style: normal;
                font-weight: 600;
                line-height: normal;
                cursor: pointer;
                span {
                    position: absolute;
                    display: block;
                    width: 100%;
                    height: 100%;
                    top: 0;
                    left: 0;
                    border: 1px solid #EDD0A7;
                }
            }
            input:checked~label span {
                border: 1px solid #AE8751;
            }
        }
    }
    @media screen and (max-width: 991px) {
        .form--check--title {
            font-size: 18px;
        }
        .form--name {
            .form--name--items {
                label {
                    font-size: 12px;
                }
            }
        }
    }
}

.box--verify {
    display: flex;
    flex-wrap: wrap;
    gap: 43px 21px;
    .verify-col {
        flex: 1;
        width: 100%;
        &+.verify-col {
            padding-left: 28px;
        }
    }
    .form--btn {
        flex-shrink: 0;
        width: 161px;
        .storesystem--btn {
            height: 64px;
            font-size: 20px;
        }
    }
    @media screen and (max-width: 767px) {
        .verify-col {
            &:first-child {
                flex: none;
            }
            &+.verify-col {
                padding-left: 0;
            }
        }
        .form--btn {
            .storesystem--btn {
                font-size: 16px;
            }
        }
    }
}

.check--agree {
    width: 100%;
    padding-top: 34px;
    label {
        display: flex;
        width: 100%;
        gap: 22px;
        margin: 0;
        align-items: center;
        span {
            border: 1px solid #EDD0A7;
            width: 40px;
            height: 40px;
            position: relative;
            &::after {
                content: "";
                display: block;
                width: 20px;
                height: 10px;
                transform: rotate(-45deg);
                border: 3px solid #4F4F4F;
                border-top: 0;
                border-right: 0;
                position: absolute;
                top: 11px;
                left: 11px;
                opacity: 0;
                visibility: hidden;
            }
        }
        .txt {
            flex: 1;
            width: 100%;
            max-width: 540px;
            color: rgba(79, 79, 79, 0.8);
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 120%;
        }
    }
    input:checked~label span {
        &::after {
            opacity: 1;
            visibility: visible;
        }
    }
}