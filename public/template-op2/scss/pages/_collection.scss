.collection-top {
    width: 100%;
    padding: 90px 10px;
    .box-title {
        width: 100%;
        max-width: 673px;
        margin: 0 auto;
        .title {
            line-height: 129%;
            margin-bottom: 20px;
            text-align: center;
            @media screen and (min-width: 1600px) {
                font-size: 36px;
            }
        }
        .desc {
            text-align: center;
        }
    }
    @media screen and (max-width: 1600px) {
        padding: 80px 10px;
    }
    @media screen and (max-width: 991px) {
        padding: 40px 10px;
    }
}

.collection-perfectvalue {
    width: 100%;
    padding: 90px 10px 60px;
    .perfectvalue--list {
        display: flex;
        flex-wrap: wrap;
        gap: 30px 70px;
        .perfectvalue--items {
            width: calc(33.3333% - 46.6666px);
            .box-title {
                margin: 0;
                display: flex;
                align-items: center;
                height: 100%;
                .title {
                    line-height: 129%;
                    margin-bottom: 0;
                    width: 100%;
                    text-align: center;
                    @media screen and (min-width: 1600px) {
                        font-size: 36px;
                    }
                }
            }
            .img {
                width: 100%;
                overflow: hidden;
                position: relative;
                padding-top: 125%;
                margin-bottom: 35px;
                img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                    object-position: center;
                    position: absolute;
                    top: 0;
                    left: 0;
                }
            }
            .desc {
                color: #4F4F4F;
                font-size: 18px;
                font-style: normal;
                line-height: 150%;
                max-width: 401px;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 3;
                overflow: hidden;
                p {
                    &:last-child {
                        margin-bottom: 0;
                    }
                }
            }
        }
    }
    @media screen and (max-width: 1600px) {
        padding: 80px 10px 60px;
        .perfectvalue--list {
            .perfectvalue--items {
                .img {
                    margin-bottom: 30px;
                }
                .desc {
                    font-size: 18px;
                }
            }
        }
    }
    @media screen and (max-width: 1400px) {
        .perfectvalue--list {
            gap: 30px 40px;
            .perfectvalue--items {
                width: calc(33.3333% - 26.6666px);
                .desc {
                    font-size: 18px;
                }
            }
        }
    }
    @media screen and (max-width: 991px) {
        padding: 40px 10px;
        .perfectvalue--list {
            gap: 30px;
            .perfectvalue--items {
                width: calc(50% - 15px);
                &:nth-child(2) {
                    width: 100%;
                    order: -1;
                }
                .desc {
                    font-size: 16px;
                }
            }
        }
    }
    @media screen and (max-width: 574px) {
        .perfectvalue--list {
            .perfectvalue--items {
                width: 100%;
            }
        }
    }
}

.collection-productintroduction {
    width: 100%;
    padding: 64px 10px 138px;
    .productintroduction--list {
        position: relative;
        .producbox--panigation {
            width: 40%;
            max-width: 401px;
            position: absolute;
            bottom: 20px;
            left: 0;
            z-index: 5;
        }
        .productintroduction--other {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            gap: 30px;
            justify-content: space-between;
            ._left {
                width: 40%;
                max-width: 401px;
                flex-shrink: 0;
                padding: 60px 0;
                .box-title {
                    .title {
                        line-height: 129%;
                        margin-bottom: 52px;
                        @media screen and (min-width: 1600px) {
                            font-size: 36px;
                        }
                    }
                    .desc {
                        display: -webkit-box;
                        -webkit-box-orient: vertical;
                        -webkit-line-clamp: 5;
                        overflow: hidden;
                        p {
                            &:last-child {
                                margin-bottom: 0;
                            }
                        }
                        @media screen and (min-width: 1600px) {
                            font-size: 18px;
                        }
                    }
                }
            }
            ._right {
                width: 100%;
                flex: 1;
                max-width: 1130px;
                .img {
                    width: 100%;
                    overflow: hidden;
                    padding-top: 59.2%;
                    position: relative;
                    img {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                        object-position: center;
                        position: absolute;
                        top: 0;
                        left: 0;
                    }
                }
            }
        }
        &.--style--news {
            .producbox--panigation {
                width: 40%;
                max-width: 593px;
                left: auto;
                right: 0;
            }
            .productintroduction--other {
                align-items: flex-start;
                ._left {
                    max-width: 593px;
                    padding-top: 0;
                    .box-title {
                        .title {
                            margin-bottom: 23px;
                            color: #4F4F4F;
                        }
                        .sub_title {
                            color: #D1B181;
                            font-size: 16px;
                            margin-bottom: 34px;
                        }
                        .desc {
                            max-width: 542px;
                        }
                    }
                }
                ._right {
                    max-width: 946px;
                    .img {
                        padding-top: 59.5%;
                    }
                }
            }
        }
    }
    @media screen and (max-width: 1600px) {
        .productintroduction--list {
            .producbox--panigation {}
            .productintroduction--other {
                ._left {
                    .box-title {
                        .title {
                            margin-bottom: 40px;
                        }
                    }
                }
            }
            &.--style--news {
                .productintroduction--other {
                    ._left {
                        .box-title {
                            .title {
                                margin-bottom: 20px;
                            }
                            .sub_title {
                                margin-bottom: 30px;
                            }
                        }
                    }
                }
            }
        }
    }
    @media screen and (max-width: 991px) {
        padding: 40px 10px;
        .productintroduction--list {
            .producbox--panigation {}
            .productintroduction--other {
                ._left {
                    .box-title {
                        .title {
                            margin-bottom: 30px;
                        }
                    }
                }
            }
        }
    }
    @media screen and (max-width: 767px) {
        padding: 40px 10px;
        .productintroduction--list {
            .producbox--panigation {
                position: unset;
                width: 100%;
                max-width: 100%;
            }
            .productintroduction--other {
                ._right {
                    flex: auto;
                    order: -1;
                }
                ._left {
                    padding: 0;
                    max-width: 100%;
                    width: 100%;
                    .box-title {
                        .title {
                            margin-bottom: 20px;
                        }
                    }
                }
            }
            &.--style--news {
                .producbox--panigation {
                    width: 100%;
                    max-width: 100%;
                    position: unset;
                }
            }
        }
    }
}

.collection-prodsp {
    width: 100%;
    padding: 60px 10px;
    &.product-similar {
        padding: 90px 10px;
        .box-title {
            margin-bottom: 80px;
            .title {
                line-height: 129%;
                margin-bottom: 0;
                width: 100%;
                text-align: center;
                @media screen and (min-width: 1600px) {
                    font-size: 36px;
                }
            }
        }
    }
    @media screen and (max-width: 991px) {
        &.product-similar {
            padding: 40px 10px;
            .box-title {
                margin-bottom: 40px;
            }
        }
    }
}

.prodsp--list {
    position: relative;
    .swiper {
        margin-bottom: 40px;
        .swiper-slide {
            height: auto;
        }
    }
    .box--btn {
        padding-top: 40px;
        text-align: center;
    }
}

.collection-video {
    padding: 180px 10px 80px;
    width: 100%;
    @media screen and (max-width: 1600px) {
        padding: 120px 10px 80px;
    }
    @media screen and (max-width: 991px) {
        padding: 40px 10px;
    }
}

.video--other {
    width: 100%;
    position: relative;
    overflow: hidden;
    padding-top: 56.22%;
    iframe,
    video {
        width: 100% !important;
        height: 100% !important;
        object-fit: cover;
        position: absolute;
        top: 0;
        left: 0;
    }
    .img {
        width: 100%;
        padding-top: 56.22%;
        position: absolute;
        overflow: hidden;
        top: 0;
        left: 0;
        z-index: 2;
        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
            position: absolute;
            top: 0;
            left: 0;
        }
    }
    .desc {
        position: absolute;
        top: 0;
        left: 0;
        display: flex;
        width: 100%;
        height: 100%;
        align-items: center;
        justify-content: center;
        z-index: 45;
        span {
            color: #EDD0A7;
            text-align: center;
            font-size: 36px;
            font-style: normal;
            font-weight: 600;
            line-height: 129%;
            text-transform: capitalize;
        }
    }
    @media screen and (max-width: 1600px) {
        .desc {
            span {
                font-size: 32px;
            }
        }
    }
    @media screen and (max-width: 1400px) {
        .desc {
            span {
                font-size: 26px;
            }
        }
    }
    @media screen and (max-width: 991px) {
        .desc {
            span {
                font-size: 22px;
            }
        }
    }
}

.product-tabslist {
    width: 100%;
    padding: 88px 10px 100px;
    @media screen and (max-width: 991px) {
        padding: 40px 10px;
    }
}

.product-tabs {
    width: 100%;
    border-top: 0.5px solid #4F4F4F;
    border-bottom: 0.5px solid #4F4F4F;
    display: flex;
    gap: 10px 20px;
    justify-content: space-between;
    margin-bottom: 44px;
    .product-tabs--left {
        width: calc(100% - 225px);
        display: flex;
        align-items: center;
        gap: 24px;
        .totla--page {
            color: rgba(79, 79, 79, 0.7);
            font-size: 16px;
            flex-shrink: 0;
        }
        .--line {
            width: 1px;
            height: 24px;
            background: rgba(79, 79, 79, 0.7);
        }
        .product-tabs--menu {
            width: 100%;
            flex: 1;
            display: flex;
            gap: 40px;
            .items {
                position: relative;
                padding: 20px 0;
                a {
                    white-space: nowrap;
                    color: #4F4F4F;
                    text-align: center;
                    font-size: 16px;
                    font-style: normal;
                    text-transform: uppercase;
                    flex: 1;
                    width: 100%;
                    display: flex;
                    align-items: center;
                    gap: 19px;
                    &.active {
                        color: #7C0410;
                    }
                }
                .items--tabsbody {
                    position: absolute;
                    top: 100%;
                    left: 0;
                    width: max-content;
                    min-width: 100%;
                    flex-direction: column;
                    padding: 23px 15px;
                    display: none;
                    flex-wrap: wrap;
                    gap: 16px;
                    background-color: #fff;
                    visibility: hidden;
                    opacity: 0;
                    border: 1px solid #9E9E9E;
                    z-index: 4;
                    >* {
                        font-size: 14px;
                        text-align: left;
                    }
                }
                &:hover {
                    &>a {
                        // color: #7C0410;
                    }
                    .items--tabsbody {
                        opacity: 1;
                        visibility: visible;
                        display: flex;
                        a:hover,
                        a.active {
                            // color: #7C0410;
                        }
                    }
                }
            }
        }
    }
    .filter-select {
        width: 205px;
        flex-shrink: 0;
        gap: 10px;
        display: flex;
        align-items: center;
        .txt {
            color: #4F4F4F;
            font-size: 10px;
            font-style: normal;
            font-weight: 450;
            line-height: 130%;
            text-transform: uppercase;
            white-space: nowrap;
            flex-shrink: 0;
        }
        select {
            height: 24px;
            flex: 1;
            padding-right: 14px;
            background-image: url('../images/ic-8.png');
            background-repeat: no-repeat;
            background-position: right center;
            border: 0;
            appearance: none;
            color: #4F4F4F;
            font-size: 12px;
            font-style: normal;
            text-transform: uppercase;
            line-height: normal;
            white-space: nowrap;
            width: 100%;
            text-overflow: ellipsis;
        }
    }
    @media screen and (max-width: 1600px) {
        .product-tabs--left {
            gap: 10px;
            .totla--page {
                font-size: 14px;
            }
            .--line {
                height: 12px;
            }
            .product-tabs--menu {
                gap: 10px;
                .items {
                    a {
                        font-size: 14px;
                        gap: 5px;
                    }
                    .items--tabsbody {
                        >* {
                            font-size: 14px;
                        }
                    }
                }
            }
        }
        .filter-select {
            .txt {
                font-size: 10px;
            }
            select {
                font-size: 12px;
            }
        }
    }
    @media screen and (max-width: 1200px) {
        .product-tabs--left {
            gap: 10px;
            .totla--page {
                font-size: 12px;
            }
            .--line {
                height: 12px;
            }
            .product-tabs--menu {
                gap: 10px;
                .items {
                    a {
                        font-size: 12px;
                    }
                    .items--tabsbody {
                        >* {
                            font-size: 14px;
                        }
                    }
                }
            }
        }
        .filter-select {
            width: 125px;
            .txt {
                display: none;
            }
            select {
                font-size: 12px;
            }
        }
    }
    @media screen and (max-width: 991px) {
        .product-tabs--left {
            .product-tabs--menu {
                display: none;
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100vh;
                overflow: scroll;
                background: #fff;
                padding: 20px;
                z-index: 1001;
                .items {
                    padding: 0;
                    margin-bottom: 10px;
                    &:last-child {
                        margin-bottom: 0;
                    }
                    .items--tabsbody {
                        position: unset;
                        box-shadow: none;
                        display: flex;
                        opacity: 1;
                        visibility: visible;
                        padding: 10px 0 0;
                        border: 0;
                        flex-direction: row;
                        &>* {
                            width: calc(50% - 8px);
                        }
                    }
                }
                &.active {
                    display: block;
                }
            }
            .totla--page,
            .--line {
                display: none;
            }
        }
    }
    @media screen and (max-width: 574px) {
        .product-tabs--left {
            .product-tabs--menu {
                .items {
                    .items--tabsbody {
                        &>* {
                            width: 100%;
                        }
                    }
                }
            }
        }
    }
}

.product--all {
    display: flex;
    flex-wrap: wrap;
    gap: 0;
    .product--col {
        width: 25%;
        &:nth-child(9n+7) {
            width: 50%;
            .product--items {
                .img {
                    padding-top: 45.84%;
                }
            }
        }
    }
    @media screen and (max-width: 1200px) {
        .product--col {
            &:nth-child(9n+7) {
                .product--items {
                    .img {
                        padding-top: 44.84%;
                    }
                }
            }
        }
    }
    @media screen and (max-width: 991px) {
        .product--col {
            width: 33.3333%;
            &:nth-child(9n+7) {
                width: 66.6667%;
                .product--items {
                    .img {
                        padding-top: 44.84%;
                    }
                }
            }
        }
    }
    @media screen and (max-width: 574px) {
        .product--col {
            width: 50%;
            &:nth-child(9n+7) {
                width: 100%;
            }
        }
    }
}

.btn-closefillter--mb {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 5;
    svg {
        width: 20px;
        height: auto;
    }
}

.btn-opfillter--mb {
    display: none;
    align-items: center;
    gap: 12px;
    color: #4F4F4F;
    font-size: 12px;
    font-style: normal;
    text-transform: uppercase;
    white-space: nowrap;
    ._icon {
        flex-shrink: 0;
    }
    @media screen and (max-width: 991px) {
        display: flex;
    }
}

.items-checkbox {
    width: 100%;
    label {
        width: 100%;
        display: flex;
        align-items: center;
        gap: 16px;
        color: #4F4F4F;
        text-align: center;
        font-size: 16px;
        font-style: normal;
        text-transform: uppercase;
        span {
            width: 24px;
            height: 24px;
            border: 1px solid rgba(79, 79, 79, 0.5);
            position: relative;
            &::after {
                content: '';
                display: block;
                width: 12px;
                height: 12px;
                position: absolute;
                background: rgba(79, 79, 79, 0.7);
                opacity: 0;
                visibility: hidden;
                top: 0;
                left: 0;
                bottom: 0;
                right: 0;
                margin: auto;
            }
        }
    }
    input:checked~label {
        span {
            &::after {
                opacity: 1;
                visibility: visible;
            }
        }
    }
    @media screen and (max-width: 1600px) {
        label {
            font-size: 12px;
        }
    }
    @media screen and (max-width: 991px) {
        label {
            font-size: 12px;
        }
    }
}

.block-weddingjewelry {
    width: 100%;
    padding: 60px 0 0;
    position: relative;
    min-height: 983px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    .weddingjewelry--other {
        width: 100%;
        max-width: 695px;
        .weddingjewelry--logo {
            width: 100%;
            text-align: center;
            margin-bottom: 35px;
            img {
                max-width: 100%;
                max-width: 285px;
            }
        }
        .desc {
            color: #6C6C6C;
            text-align: center;
            font-size: 20px;
            font-style: normal;
            line-height: 140%;
            max-width: 421px;
            margin: 0 auto 33px;
            p {
                &:last-child {
                    margin-bottom: 0;
                }
            }
        }
        .box--btn {
            text-align: center;
            .btn-links {
                color: #CD8B30;
                border-bottom: 1px solid #CD8B30;
            }
        }
    }
    .weddingjewelry--img {
        width: 50%;
        height: 100%;
        overflow: hidden;
        position: absolute;
        top: 0;
        right: 0;
        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
        }
    }
    &.--style2 {
        background: var(--Main-Red, #7C0410);
        .weddingjewelry--logo {
            margin-bottom: 100px;
        }
        .weddingjewelry--other {
            .desc {
                color: #fff;
                font-size: 16px;
                max-width: 435px;
                margin: 0 auto 45px;
            }
        }
    }
    @media screen and (max-width: 1400px) {
        min-height: auto;
        padding: 60px 0;
    }
    @media screen and (max-width: 991px) {
        padding: 40px 0 0;
        .weddingjewelry--other {
            margin-bottom: 30px;
            padding: 0 10px;
            .desc {
                font-size: 18px;
            }
        }
        .weddingjewelry--img {
            width: 100%;
            height: auto;
            position: unset;
        }
        &.--style2 {}
    }
}

.collection-all {
    width: 100%;
    padding: 0 10px 88px;
    .box--btn {
        padding-top: 105px;
        text-align: center;
        .btn-links {
            color: #4F4F4F;
            border-bottom: 1px solid #4F4F4F;
        }
    }
    @media screen and (max-width: 991px) {
        padding-bottom: 40px;
        .box--btn {
            padding-top: 40px;
        }
    }
}

.collection--items {
    width: 100%;
    height: 100%;
    display: block;
    .img {
        background: #FFFCF3;
        padding: 56px 40px 60px;
        display: block;
        margin-bottom: 20px;
        span {
            display: block;
            width: 100%;
            padding-top: 133.3333%;
            overflow: hidden;
            position: relative;
            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                object-position: center;
                position: absolute;
                top: 0;
                left: 0;
                transition: all 0.3s;
            }
        }
    }
    .title {
        width: 100%;
        color: #4F4F4F;
        font-size: 24px;
        font-style: normal;
        line-height: 150%;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3;
        overflow: hidden;
        a {
            color: inherit;
        }
    }
    &:hover {
        .img {
            span {
                img {
                    transform: scale(1.2);
                }
            }
        }
        .title {
            color: #7C0410;
        }
    }
    @media screen and (max-width: 1600px) {
        .img {
            padding: 56px 40px 60px;
        }
        .title {
            font-size: 22px;
        }
    }
    @media screen and (max-width: 1200px) {
        .img {
            padding: 50px 40px 60px;
        }
        .title {
            font-size: 20px;
        }
    }
    @media screen and (max-width: 991px) {
        .img {
            padding: 40px 30px 30px;
        }
        .title {
            font-size: 18px;
        }
    }
}

.collection-box {
    width: 100%;
    .gx-lg-4 {
        --bs-gutter-x: 30px;
        gap: 30px 0;
    }
}