.store-top {
    width: 100%;
    padding: 100px 10px 50px;
    .box-title {
        margin-bottom: 50px;
        .title {
            text-align: center;
            color: #4F4F4F;
            @media screen and (min-width: 1600px) {
                font-size: 36px;
            }
        }
    }
    @media screen and (max-width: 991px) {
        padding: 50px 10px 0;
    }
}

.store--fillter {
    width: 100%;
    max-width: 1247px;
    margin: 0 auto;
    display: flex;
    flex-wrap: wrap;
    gap: 10px 21px;
    .store--fillter--col {
        flex: 1;
        width: 100%;
        select {
            width: 100%;
            border: 1px solid #AE8751;
            height: 64px;
            padding: 0 35px 0 17px;
            color: #4F4F4F;
            font-size: 20px;
            font-style: normal;
            font-weight: 600;
            line-height: normal;
            appearance: none;
            background-image: url('../images/ic-10.png');
            background-repeat: no-repeat;
            background-position: right 18px center;
        }
    }
    @media screen and (max-width: 1400px) {
        .store--fillter--col {
            select {
                height: 52px;
                font-size: 16px;
            }
        }
    }
    @media screen and (max-width: 991px) {
        .store--fillter--col {
            flex: none;
            select {
                height: 52px;
                font-size: 16px;
            }
        }
    }
}

.store-box {
    width: 100%;
    padding: 50px 10px 95px;
    .store-list {
        .row {
            gap: 96px 0;
            @media screen and (min-width: 1600px) {
                --bs-gutter-x: 26px;
            }
        }
    }
    @media screen and (max-width: 1400px) {
        .store-list {
            .row {
                gap: 30px 0;
            }
        }
    }
    @media screen and (max-width: 991px) {
        padding: 50px 10px;
    }
}

.store--items {
    display: block;
    width: 100%;
    .img {
        display: block;
        width: 100%;
        padding-top: 61.1%;
        overflow: hidden;
        position: relative;
        margin-bottom: 24px;
        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
            position: absolute;
            top: 0;
            left: 0;
            transition: all 0.3s;
        }
        &::after {
            content: '';
            display: block;
            width: 100%;
            height: 100%;
            position: absolute;
            background: rgba(124, 4, 16, 0.30);
            top: 0;
            left: 0;
        }
    }
    .store--items---body {
        width: 100%;
        .store--items--base {
            color: #4F4F4F;
            font-size: 20px;
            font-style: normal;
            font-weight: 600;
            line-height: normal;
            margin-bottom: 17px;
        }
        .store--items--address {
            color: #4F4F4F;
            font-size: 16px;
            font-style: normal;
            font-weight: 600;
            line-height: 129%;
            text-transform: capitalize;
            margin-bottom: 17px;
        }
        .box--btn {
            .btn-links {
                font-size: 14px;
                line-height: 140%;
            }
        }
    }
    &:hover {
        .img {
            img {
                transform: scale(1.2);
            }
        }
    }
    @media screen and (max-width: 991px) {
        .store--items---body {
            .store--items--base {
                font-size: 18px;
            }
        }
    }
}

.contact-main {
    width: 100%;
    padding: 100px 10px 47px;
    .box-title {
        margin-bottom: 52px;
        .title {
            text-align: center;
            color: #4F4F4F;
            @media screen and (min-width: 1600px) {
                font-size: 36px;
            }
        }
    }
    .contact--content {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        gap: 30px;
        justify-content: space-between;
        max-width: 1362px;
        margin: 0 auto;
        .contact--items {
            width: calc(33.333% - 20px);
            padding: 62px 0 75px;
            margin-bottom: 79px;
            max-width: 303px;
            position: relative;
            .contact--items--title {
                display: flex;
                gap: 9px;
                margin-bottom: 22px;
                ._icon {
                    flex-shrink: 0;
                    img {
                        max-width: 100%;
                    }
                }
                .txt {
                    width: 100%;
                    flex: 1;
                    color: #7C0410;
                    font-size: 24px;
                    font-style: normal;
                    font-weight: 600;
                    line-height: 129%;
                    text-transform: capitalize;
                }
            }
            .box--btn {
                position: absolute;
                bottom: 0;
                width: 100%;
                .btn-links {
                    font-size: 16px;
                }
            }
            .contact--items--body {
                p {
                    color: #4F4F4F;
                    font-size: 16px;
                    font-style: normal;
                    font-weight: 600;
                    line-height: 129%;
                    text-transform: capitalize;
                    margin: 0;
                    a {
                        color: inherit;
                    }
                }
            }
        }
        .contact--line {
            width: 100%;
            height: 1px;
            background: #D5D5D5;
        }
    }
    @media screen and (max-width:1200px) {
        .contact--content {
            .contact--items {
                .contact--items--title {
                    .txt {
                        font-size: 20px;
                    }
                }
            }
        }
    }
    @media screen and (max-width: 991px) {
        padding: 40px 10px;
        .contact--content {
            .contact--line {
                display: none;
            }
            .contact--items {
                width: calc(50% - 15px);
                margin: 0;
                .contact--items--title {
                    .txt {
                        font-size: 18px;
                    }
                }
            }
        }
    }
    @media screen and (max-width: 574px) {
        .box-title {
            margin-bottom: 0;
        }
        .contact--content {
            .contact--items {
                padding: 30px 0 50px;
                width: 100%;
                max-width: 100%;
            }
        }
    }
}

.block-privilegesmembership {
    width: 100%;
    padding: 82px 10px 104px;
    background: transparent url('../images/bg-privilegesmembership.jpg') no-repeat center center;
    background-size: cover;
    .box-title {
        margin-bottom: 94px;
        .title {
            text-align: center;
            color: #EDD0A7;
            margin-bottom: 15px;
            @media screen and (min-width: 1600px) {
                font-size: 36px;
            }
        }
        .desc {
            max-width: 640px;
            margin: 0 auto;
            text-align: center;
            color: #fff;
        }
    }
    .privilegesmembership--list {
        display: flex;
        flex-wrap: wrap;
        gap: 20px 16px;
        justify-content: space-between;
        margin-bottom: 102px;
        .privilegesmembership--items {
            width: 100%;
            max-width: 240px;
            .img {
                text-align: center;
                width: 100%;
                margin-bottom: 16px;
                img {
                    max-width: 100%;
                }
            }
            .txt {
                color: #FFF;
                text-align: center;
                font-size: 16px;
                font-style: normal;
                font-weight: 450;
                line-height: 150%;
                text-transform: capitalize;
                p {
                    &:last-child {
                        margin-bottom: 0;
                    }
                }
            }
        }
    }
    .box--btn {
        text-align: center;
        .btn-links {
            color: #fff;
            border-bottom: 1px solid #fff;
        }
    }
    @media screen and (max-width: 1400px) {
        .box-title {
            margin-bottom: 60px;
        }
    }
    @media screen and (max-width: 991px) {
        padding: 40px 10px;
        .box-title {
            margin-bottom: 40px;
        }
    }
    @media screen and (max-width: 574px) {
        .privilegesmembership--list {
            margin-bottom: 60px;
            .privilegesmembership--items {
                width: 100%;
                max-width: 100%;
            }
        }
    }
}

.block-exclusiveoffers {
    .newshome--list {
        margin-bottom: 0 !important;
    }
}

.policy-main {
    width: 100%;
    padding: 100px 10px;
    .policy--other {
        width: 100%;
        max-width: 807px;
        margin: 0 auto;
        .policy--content {
            gap: 40px;
            .detail--content {
                ._items {
                    .detail--child {
                        padding-top: 30px;
                    }
                }
            }
        }
    }
    .box-title {
        margin-bottom: 40px;
        padding-bottom: 40px;
        border-bottom: 1px solid #000;
        .title {
            text-align: center;
        }
    }
    @media screen and (max-width: 991px) {
        padding: 40px 10px;
    }
}

.policy--box {
    color: #4F4F4F;
    font-size: 20px;
    font-style: normal;
    font-weight: 400;
    line-height: 180%;
    p {
        &:last-child {
            margin-bottom: 0;
        }
    }
    table {
        width: 100%;
        border: 1px solid #000;
        tr {
            td {
                border: 1px solid #000;
                padding: 5px;
            }
        }
    }
    img {
        display: block;
        margin: 0 auto;
        width: auto;
        max-width: 100% !important;
        height: auto !important;
    }
    @media screen and (max-width: 991px) {
        font-size: 16px;
    }
}