.banner-main {
    width: 100%;
    padding: 0;
    .img {
        overflow: hidden;
        width: 100%;
        position: relative;
        height: 100vh;
        &::after {
            position: absolute;
            content: '';
            display: block;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.2);
            z-index: 5;
        }
        &>img {
            width: 100%;
            height: 100%;
            object-position: center;
            object-fit: cover;
            position: absolute;
            top: 0;
            left: 0;
            z-index: 3;
        }
        .play-button {
            width: 78px;
            height: 78px;
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
            margin: auto;
            z-index: 4;
            cursor: pointer;
            svg {
                max-width: 100%;
            }
        }
        iframe,
        video {
            width: 100% !important;
            height: 100% !important;
            position: absolute;
            object-fit: cover;
            top: 0;
            left: 0;
        }
    }
    &.banner-other {
        .img {
            height: calc(100vh - 182px);
        }
    }
    @media screen and (max-width: 991px) {}
    @media screen and (max-width: 767px) {
        .img {
            height: 50vh;
        }
    }
}

.home-timelessstory {
    width: 100%;
    padding: 98px 10px;
    background: transparent;
    .timelessstory--list {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        gap: 30px;
        align-items: flex-start;
        justify-content: space-between;
        ._left {
            width: calc(50% - 15px);
            max-width: 640px;
            flex: 1;
            .timelessstory--body {
                margin-bottom: 50px;
                .desc {
                    color: #4F4F4F;
                    font-size: 20px;
                    font-style: normal;
                    line-height: 150%;
                    p {
                        &:last-child {
                            margin-bottom: 0;
                        }
                    }
                }
            }
        }
        ._right {
            width: calc(50% - 15px);
            max-width: 800px;
            .img {
                width: 100%;
                // padding-top: 100.5%;
                overflow: hidden;
                position: relative;
                img {
                    width: 100%;
                    // height: 100%;
                    object-fit: cover;
                    object-position: center;
                    // position: absolute;
                    top: 0;
                    left: 0;
                }
            }
        }
    }
    &.--style2 {
        padding: 50px 10px;
        .timelessstory--list {
            ._left {
                max-width: 551px;
                padding-top: 99px;
                .box-title {
                    margin-bottom: 19px;
                    .desc {
                        font-size: 20px;
                    }
                }
            }
            ._right {
                max-width: 806px;
            }
        }
    }
    &.block-stories {
        .timelessstory--list {
            ._left {
                padding-top: 40px;
                .box-title {
                    margin-bottom: 19px;
                    .desc {
                        font-size: 20px;
                    }
                }
            }
        }
    }
    @media screen and (max-width: 1400px) {
        .timelessstory--list {
            ._left {
                .timelessstory--body {
                    .desc {
                        font-size: 18px;
                    }
                }
            }
            ._right {}
        }
    }
    @media screen and (max-width: 1400px) {
        .timelessstory--list {
            ._left {
                .timelessstory--body {
                    .desc {
                        font-size: 18px;
                    }
                }
            }
        }
        &.--style2 {
            .timelessstory--list {
                ._left {
                    padding-top: 70px;
                    .box-title {
                        margin-bottom: 19px;
                    }
                }
            }
        }
    }
    @media screen and (max-width: 991px) {
        padding: 40px 10px;
        .timelessstory--list {
            ._left {
                width: 100%;
                max-width: 100%;
                .timelessstory--body {
                    margin-bottom: 30px;
                    .desc {
                        font-size: 16px;
                    }
                }
            }
            ._right {
                // order: -1;
                width: 100%;
                max-width: 100%;
            }
        }
        &.--style2 {
            .timelessstory--list {
                ._left {
                    padding-top: 0;
                }
            }
        }
    }
}

.home-product {
    width: 100%;
    padding: 60px 10px 64px;
    .box-title {
        margin-bottom: 72px;
        .title {
            margin-bottom: 16px;
            @media screen and (min-width: 1600px) {
                line-height: 129%;
                font-size: 36px;
            }
        }
        .desc {
            max-width: 530px;
        }
    }
    .producthome--list {
        position: relative;
        margin-bottom: 50px;
        .producbox--btn {
            .swiper-button-next,
            .swiper-button-prev {
                margin: 0;
                top: 30%;
            }
        }
        .swiper {
            margin-bottom: 50px;
        }
    }
    @media screen and (max-width:1600px) {}
    @media screen and (max-width: 991px) {
        padding: 40px 10px;
        .box-title {
            margin-bottom: 30px;
            .desc {
                max-width: 100%;
            }
        }
    }
}

.home-store {
    padding: 70px 10px 107px;
    width: 100%;
    @media screen and (max-width: 1400px) {
        padding: 60px 10px;
    }
    @media screen and (max-width: 991px) {
        padding: 40px 10px;
    }
}

.store--list {
    width: 100%;
    position: relative;
    display: flex;
    gap: 30px;
    flex-wrap: wrap;
    justify-content: space-between;
    ._left {
        width: calc(33.03% - 15px);
        max-width: 344px;
        .box-title {
            margin-bottom: 40px;
            .sub_title {
                margin-bottom: 40px;
            }
        }
    }
    ._right {
        width: calc(66.97% - 15px);
    }
    .store--box {
        position: relative;
        width: 100%;
    }
    .swiper {
        border: 0.5px solid #D9D9D9;
        margin-bottom: 50px;
        .swiper-slide {
            height: auto;
        }
        .productcate--items {
            border: 0;
            border-left: 0.5 solid #D9D9D9;
        }
    }
    @media screen and (max-width: 1200px) {
        padding-bottom: 78px;
        .swiper {
            margin-bottom: 30px;
        }
        ._left {
            width: 100%;
            max-width: 100%;
            .box-title {
                margin-bottom: 0;
                .sub_title {
                    margin-bottom: 30px;
                }
            }
        }
        ._right {
            width: 100%;
        }
        .store--box {}
        .box--btn {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
        }
    }
}

.home-dowryjewelry {
    padding: 140px 10px 123px;
    width: 100%;
    .dowryjewelry--list {
        display: flex;
        flex-wrap: wrap;
        width: 100%;
        justify-content: space-between;
        gap: 30px;
        ._left {
            width: calc(67.15% - 15px);
        }
        ._right {
            width: calc(32.85% - 15px);
            max-width: 486px;
            padding-right: 50px;
        }
        .box-title {
            margin-bottom: 40px;
        }
        .dowryjewelry--other {
            position: relative;
            border: 1px solid #D9D9D9;
            .swiper {
                .swiper-slide {
                    height: auto;
                }
            }
            .producbox--panigation {
                position: absolute;
                width: calc(40.8% - 10px);
                bottom: 30px;
                left: 0;
                z-index: 5;
            }
        }
    }
    @media screen and (max-width: 1400px) {
        padding: 60px 10px 90px;
    }
    @media screen and (max-width: 1200px) {
        .dowryjewelry--list {
            gap: 30px;
            ._left {
                width: 100%;
            }
            ._right {
                width: 100%;
                max-width: 100%;
                order: -1;
                padding-right: 0;
            }
            .box-title {
                margin-bottom: 30px;
            }
            .dowryjewelry--other {
                .producbox--panigation {
                    width: 100%;
                    top: calc(100% + 18px);
                }
            }
        }
    }
}

.dowryjewelry--box {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
    .dowryjewelry-col1 {
        width: calc(40.8% - 10px);
        .product--items {
            border: 0;
            padding-bottom: 96px;
        }
    }
    .dowryjewelry-img {
        flex: 1;
        width: 100%;
        overflow: hidden;
        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
        }
    }
    @media screen and (max-width: 1200px) {
        gap: 0;
        .dowryjewelry-col1 {
            width: 50%;
            .product--items {
                padding-bottom: 25px;
            }
        }
        .dowryjewelry-img {}
    }
    @media screen and (max-width: 414px) {
        .dowryjewelry-col1 {
            width: 100%;
            .product--items {
                padding-bottom: 25px;
            }
        }
        .dowryjewelry-img {
            order: -1;
            flex: none;
        }
    }
}

.home-collection {
    background-color: transparent;
    background-repeat: no-repeat;
    background-position: bottom center;
    min-height: 670px;
    padding: 80px 10px;
    .box-title {
        margin-bottom: 36px;
        .title {
            color: #EDD0A7;
            line-height: 129%;
        }
        .sub_title {
            color: #EDD0A7;
            line-height: 129%;
            margin-bottom: 23px;
        }
        .desc {
            color: #fff;
            max-width: 595px;
        }
    }
    .box--btn {
        .btn-links {
            color: #EDD0A7;
            border-bottom: 1px solid #EDD0A7;
        }
    }
    &.present-collection {
        display: flex;
        align-items: center;
        width: 100%;
        min-height: 720px;
        padding: 170px 10px 102px;
        .box-title {
            margin-bottom: 15px;
            .title {
                color: #fff;
                margin-bottom: 25px;
            }
            .desc {
                color: #fff;
                max-width: 432px;
            }
        }
        .box--btn {
            .btn-links {
                color: #fff;
                border-bottom: 1px solid #fff;
            }
        }
    }
    @media screen and (max-width: 991px) {
        padding: 60px 10px;
        min-height: auto;
        &.present-collection {
            min-height: auto;
            padding: 40px 10px;
        }
    }
}

.home-brand {
    padding: 135px 10px 65px;
    .brand-content {
        width: 100%;
        min-height: 844px;
        background-color: transparent;
        background-repeat: no-repeat;
        background-position: bottom center;
        padding: 51px 64px 70px;
        display: flex;
        position: relative;
        &::after {
            width: 100%;
            height: 38.3%;
            content: '';
            display: block;
            background: linear-gradient(0deg, #AF813E 2.24%, rgba(223, 189, 119, 0.00) 97.26%);
            position: absolute;
            bottom: 0;
            left: 0;
            z-index: 2;
        }
        .brand-other {
            position: relative;
            z-index: 5;
            width: 100%;
            max-width: 577px;
            display: flex;
            flex-direction: column;
            gap: 30px;
            align-items: start;
            justify-content: space-between;
            .brand--name {
                color: #FFF;
                font-size: 20px;
                font-style: normal;
                line-height: 150%;
            }
            .box-title {
                .title {
                    color: #fff;
                    margin-bottom: 40px;
                    @media screen and (min-width: 1600px) {
                        font-size: 36px;
                        line-height: 129%;
                    }
                }
                .desc {
                    max-width: 460px;
                    color: #fff;
                    margin: 0;
                }
            }
            .box--btn {
                .btn-links {
                    color: #fff;
                    border-bottom: 1px solid #fff;
                }
            }
        }
    }
    @media screen and (max-width: 1400px) {
        padding: 65px 10px;
        .brand-content {
            .brand-other {
                .brand--name {
                    font-size: 20px;
                }
                .box-title {
                    .title {
                        color: #fff;
                        margin-bottom: 30px;
                    }
                    .desc {}
                }
            }
        }
    }
    @media screen and (max-width: 991px) {
        padding: 60px 10px;
        .brand-content {
            padding: 30px;
            min-height: auto;
            .brand-other {
                .brand--name {
                    font-size: 18px;
                }
            }
        }
    }
}

.home-chart {
    padding: 90px 10px 40px;
    width: 100%;
    .container {
        max-width: 1444px;
    }
    .chart--table---title {
        color: #7C0410;
        font-size: 20px;
        font-style: normal;
        font-weight: 900;
        line-height: normal;
        margin-bottom: 20px;
        padding-left: 26px;
    }
    .chart--table {
        display: flex;
        width: 100%;
        padding-left: 26px;
        gap: 33px;
        justify-content: space-between;
        flex-wrap: wrap;
        .account-content {
            .nav {
                .nav-item {
                    .nav-link {
                        color: $themegray-20;
                        font-size: 24px;
                        font-style: normal;
                        font-weight: 600;
                        line-height: normal;
                        &.active {
                            color: $themegray;
                        }
                    }
                }
            }
        }
        ._left {
            flex: 1;
            width: 100%;
        }
        ._right {
            width: 40%;
            max-width: 581px;
            background: linear-gradient(180deg, #EDD0A7 -21.8%, #FFF 106.02%);
            padding: 24px 25px 44px;
        }
        .pricegold--quantity {
            .quantity--title {
                color: $themegray;
                font-size: 16px;
                margin-bottom: 12px;
            }
            .--note {
                color: $themegray;
                // font-family: "Work Sans";
                font-size: 15px;
                font-style: normal;
                line-height: normal;
                letter-spacing: -0.6px;
                padding-top: 10px;
            }
            .quantity--total {
                display: flex;
                width: 100%;
                height: 61px;
                display: flex;
                gap: 5px;
                line-height: 61px;
                border-bottom: 1px solid #D1B181;
                color: $themegray;
                font-size: 20px;
                .number {
                    flex-shrink: 1;
                    width: 100%;
                }
                span {
                    flex-shrink: 0;
                }
            }
        }
        .pricegold--input {
            max-width: 100%;
            select {
                width: 100%;
                height: 61px;
                color: $themegray;
                font-size: 20px;
            }
        }
        .chart--table--btn {
            display: flex;
            align-items: center;
            gap: 10px;
            justify-content: space-between;
            .txt {
                color: $themegray;
                font-size: 19.197px;
                font-style: normal;
                font-weight: 600;
                line-height: normal;
            }
            .form--btn {
                flex-shrink: 0;
                .storesystem--btn {
                    width: 191px;
                }
            }
        }
    }
    @media screen and (max-width: 1200px) {
        .chart--table {
            .account-content {
                .nav {
                    .nav-item {
                        .nav-link {
                            font-size: 20px;
                        }
                    }
                }
            }
            ._left {
                flex: none;
                width: 100%;
            }
            ._right {
                width: 100%;
                max-width: 100%;
                padding: 24px 25px;
            }
            .pricegold--quantity {
                .--note {
                    font-size: 14px;
                }
                .quantity--total {
                    height: 56px;
                    line-height: 56px;
                    font-size: 18px;
                }
            }
            .pricegold--input {
                select {
                    height: 56px;
                    font-size: 18px;
                }
            }
            .chart--table--btn {
                .txt {
                    font-size: 16px;
                }
            }
        }
    }
    @media screen and (max-width: 574px) {
        .chart--table {
            .pricegold--input {
                select {
                    height: 56px;
                    font-size: 18px;
                }
            }
            .chart--table--btn {
                flex-direction: column;
                align-items: center;
            }
        }
    }
}

.chart--table-box {
    width: 100%;
    border: 0;
    border-spacing: 0;
    thead {
        tr {
            border-top: 1px solid #EDD0A7;
            th {
                padding: 30px 0 7px;
            }
        }
    }
    tr {
        border-top: 1px solid $themegray-20;
        td {
            padding: 19px 0;
            color: $themegray;
            font-size: 18px;
            font-style: normal;
            font-weight: 600;
            line-height: normal;
            .number {
                display: flex;
                align-items: center;
                gap: 10px;
            }
        }
    }
    .table--footer {
        display: flex;
        align-items: start;
        gap: 10px;
        justify-content: space-between;
        &>* {
            color: $themegray-50;
            font-size: 18px;
            font-style: normal;
            font-weight: 600;
            line-height: normal;
        }
        p {
            margin: 0;
        }
    }
    @media screen and (max-width: 991px) {
        thead {
            tr {
                th {
                    padding: 20px 0 7px;
                }
            }
        }
        tr {
            td {
                font-size: 14px;
            }
        }
        .table--footer {
            &>* {
                font-size: 14px;
            }
        }
    }
}

.chart--fieldset {
    width: 100%;
    border: 1px solid #D5D5D5;
    padding: 43px 42px 45px 65px;
    position: relative;
    margin-bottom: 48px;
    .fieldset--title {
        background-color: #fff;
        padding: 0 10px;
        position: absolute;
        top: -30px;
        left: 0;
        right: 0;
        margin: auto;
        color: #7C0410;
        font-size: 36px;
        font-style: normal;
        font-weight: 700;
        line-height: normal;
        width: max-content;
    }
    .chart--fieldset---list {
        display: flex;
        flex-wrap: wrap;
        gap: 30px;
        .--title {
            color: $themegray;
            font-size: 20px;
            font-style: normal;
            font-weight: 900;
            line-height: normal;
            margin-bottom: 20px;
        }
        .price--discount {
            display: flex;
            flex-wrap: wrap;
            gap: 10px 30px;
            align-items: center;
            margin-bottom: 10px;
            .--number {
                color: $themegray;
                font-size: 29.351px;
                font-style: normal;
                font-weight: 600;
                line-height: normal;
            }
            .--number--discount {
                display: flex;
                gap: 9px;
                color: #EDD0A7;
                font-size: 17.122px;
                font-style: normal;
                font-weight: 600;
                line-height: normal;
                align-items: center;
            }
        }
        ._left {
            width: 100%;
            flex: 1;
            max-width: 839px;
        }
        ._right {
            width: 401px;
            .pricegold--input {
                max-width: 100%;
                margin-bottom: 50px;
                select {
                    height: 64px;
                    font-size: 20px;
                    color: $themegray;
                }
            }
        }
        .chart--fieldset---box {
            padding: 0 10px;
            .chart--fieldset---check {
                width: 100%;
                display: flex;
                flex-wrap: wrap;
                gap: 25px 60px;
                margin-bottom: 70px;
                .--items {
                    width: calc(33.3333% - 40px);
                    label {
                        border-bottom: 1px solid transparent;
                        color: #4F4F4F;
                        font-size: 20px;
                        font-style: normal;
                        font-weight: 600;
                        line-height: normal;
                        padding-right: 10px;
                        white-space: nowrap;
                    }
                    input:checked~label {
                        border-bottom: 1px solid #AE8751;
                    }
                }
            }
        }
        .chart--fieldset---unit {
            display: flex;
            flex-wrap: wrap;
            gap: 20px 30px;
            .--measure {
                flex: 1;
                width: 100%;
                color: #4F4F4F;
                font-size: 16px;
                font-style: normal;
                font-weight: 600;
                line-height: normal;
            }
            .--explain {
                display: flex;
                flex-wrap: wrap;
                gap: 5px;
                flex-shrink: 0;
                flex-direction: column;
                &>* {
                    display: flex;
                    gap: 12px;
                    align-items: center;
                    color: #4F4F4F;
                    font-size: 16px;
                    font-style: normal;
                    font-weight: 600;
                    line-height: normal;
                    &::before {
                        content: '';
                        display: block;
                        width: 48px;
                        height: 10px;
                    }
                }
                .--sell-out {
                    &::before {
                        background: #EDD0A7;
                    }
                }
                .--buy-in {
                    &::before {
                        background: #922E38;
                    }
                }
            }
        }
    }
    @media screen and (max-width: 1600px) {
        .fieldset--title {
            font-size: 32px;
        }
        .chart--fieldset---list {
            .--title {
                font-size: 18px;
            }
            .price--discount {
                .--number {
                    font-size: 26px;
                }
                .--number--discount {
                    font-size: 16px;
                }
            }
            ._left {}
            ._right {
                width: 401px;
                .pricegold--input {
                    select {
                        height: 60px;
                        font-size: 18px;
                    }
                }
            }
            .chart--fieldset---box {
                .chart--fieldset---check {
                    margin-bottom: 50px;
                    .--items {
                        width: calc(33.3333% - 40px);
                        label {
                            font-size: 16px;
                        }
                    }
                }
            }
            .chart--fieldset---unit {
                .--measure {}
            }
        }
    }
    @media screen and (max-width: 1400px) {
        padding: 40px 30px;
        .fieldset--title {
            font-size: 26px;
        }
        .chart--fieldset---list {
            .price--discount {
                .--number {
                    font-size: 22px;
                }
            }
            ._left {
                flex: none;
                max-width: 100%;
            }
            ._right {
                width: 100%;
                .pricegold--input {
                    select {
                        height: 56px;
                    }
                }
            }
            .chart--fieldset---box {
                .chart--fieldset---check {
                    gap: 25px 30px;
                    .--items {
                        width: calc(33.3333% - 20px);
                    }
                }
            }
            .chart--fieldset---unit {
                .--measure {}
            }
        }
    }
    @media screen and (max-width: 1200px) {
        .fieldset--title {
            font-size: 22px;
        }
        .chart--fieldset---list {
            .price--discount {
                .--number {
                    font-size: 20px;
                }
            }
            .chart--fieldset---unit {
                .--measure {}
            }
        }
    }
}

.home-news {
    padding: 60px 10px 75px;
    width: 100%;
    .box-title {
        margin-bottom: 55px;
        .title {
            text-align: center;
            @media screen and (min-width: 1600px) {
                font-size: 36px;
                line-height: 129%;
            }
        }
    }
    .newshome--list {
        width: 100%;
        margin-bottom: 85px;
        position: relative;
        .producbox--panigation {
            .swiper-pagination {
                padding-top: 30px;
            }
        }
    }
    .box--btn {
        text-align: center;
    }
    &.home-news-mh {
        padding: 155px 10px 75px;
        .newshome--list {
            margin-bottom: 0;
        }
        .box-title {
            margin-bottom: 155px;
            .title {
                margin-bottom: 35px;
                color: #B5751D;
            }
            .desc {
                max-width: 675px;
                text-align: center;
                margin: 0 auto;
            }
        }
    }
    &.block-exclusiveoffers {
        .box-title {
            .title {
                margin-bottom: 15px;
            }
            .desc {
                max-width: 640px;
                margin: 0 auto;
            }
        }
    }
    @media screen and (max-width: 991px) {
        padding: 40px 10px;
        .box-title {
            margin-bottom: 30px;
        }
        &.home-news-mh {
            padding: 40px 10px;
            .box-title {
                margin-bottom: 60px;
                .title {
                    margin-bottom: 25px;
                }
            }
        }
    }
}

.home-storesystem {
    padding: 75px 0 60px;
    width: 100%;
    overflow: hidden;
    .box-title {
        margin-bottom: 60px;
        .title {
            text-align: center;
            @media screen and (min-width: 1600px) {
                font-size: 36px;
                line-height: 129%;
            }
        }
    }
    .storesystem--list {
        position: relative;
        width: 90%;
        max-width: 1480px;
        margin: 0 auto;
        .swiper {
            overflow: visible;
        }
    }
    @media screen and (max-width: 991px) {
        padding: 40px 10px;
        .box-title {
            margin-bottom: 30px;
        }
    }
}

.home-policy {
    padding: 30px 0 299px;
    width: 100%;
    .row {
        gap: 30px 0;
    }
    @media screen and (max-width: 991px) {
        padding: 30px 0;
    }
}

.policy--items {
    width: 100%;
    .img {
        width: 100%;
        margin-bottom: 23px;
        text-align: center;
        img {
            max-width: 100%;
        }
    }
    .policy--body {
        width: 100%;
        .title {
            color: #7C0410;
            text-align: center;
            font-size: 20px;
            font-style: normal;
            line-height: 150%;
            text-transform: capitalize;
            margin: 0 0 8px;
            a {
                color: inherit;
            }
        }
        .desc {
            color: #4F4F4F;
            text-align: center;
            font-size: 16px;
            font-style: normal;
            line-height: 130%;
            margin-bottom: 8px;
            p {
                &:last-child {
                    margin-bottom: 0;
                }
            }
        }
        .box--btn {
            width: 100%;
            text-align: center;
        }
        .btn-links {
            font-size: 16px;
        }
    }
    @media screen and (max-width: 991px) {
        .policy--body {
            .title {
                font-size: 18px;
            }
        }
    }
}

.storesystem--items {
    display: block;
    width: 100%;
    .img {
        width: 100%;
        padding-top: 100%;
        overflow: hidden;
        position: relative;
        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
            position: absolute;
            top: 0;
            left: 0;
            transition: all 0.3s;
        }
        &::after {
            background: linear-gradient(0deg, rgba(119, 6, 17, 0.70) 0%, rgba(119, 6, 17, 0.70) 100%);
            content: '';
            display: block;
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
            transition: all 0.3s;
            z-index: 2;
            opacity: 0;
            visibility: hidden;
        }
    }
    .storesystem--body {
        width: 100%;
        transition: all 0.3s;
        height: 100%;
        display: flex;
        flex-direction: column;
        gap: 20px;
        justify-content: space-between;
        align-items: center;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 5;
        padding: 73px 27px 55px;
        opacity: 0;
        visibility: hidden;
        .storesystem--base {
            width: 100%;
            .name {
                color: #FFF;
                text-align: center;
                font-size: 36px;
                font-style: normal;
                font-weight: 700;
                line-height: 129%;
                text-transform: capitalize;
                display: block;
                width: 100%;
                margin-bottom: 16px;
            }
            .--adress {
                color: #D1B181;
                text-align: center;
                font-size: 16px;
                font-style: normal;
                font-weight: 700;
                line-height: 129%;
                text-transform: capitalize;
            }
        }
    }
    &:hover {
        .img {
            img {
                transform: scale(1.2);
            }
            &::after {
                opacity: 1;
                visibility: visible;
            }
        }
        .storesystem--body {
            opacity: 1;
            visibility: visible;
        }
    }
    @media screen and (max-width: 991px) {
        .storesystem--body {
            position: unset;
            padding: 20px 0 0;
            opacity: 1;
            visibility: visible;
            .storesystem--base {
                .name {
                    font-size: 22px;
                    margin-bottom: 10px;
                    color: #4F4F4F;
                }
            }
        }
    }
}

.storesystem--btn {
    background: #EDD0A7;
    color: #4F4F4F;
    text-align: center;
    font-size: 20px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    width: 100%;
    max-width: 312px;
    padding: 17px;
    border: 0;
    box-shadow: none;
    display: inline-block;
    &:hover {
        color: #7C0410;
    }
    @media screen and (max-width: 991px) {
        font-size: 16px;
        padding: 10px;
    }
}

.block-form {
    width: 100%;
    padding: 110px 10px;
    background: #FFFCF3;
    &.register--club {
        .form--list {
            ._left {
                width: 50.6%;
            }
            ._right {
                width: 49.4%;
                .box-title {
                    margin-bottom: 22px;
                    .title {
                        color: #7C0410;
                    }
                }
            }
        }
        .box--verify {
            .verify-col {
                &:first-child {
                    flex: auto;
                }
                &+.verify-col {
                    padding-left: 0;
                    padding-right: 28px;
                }
            }
        }
        .check--agree {
            padding-top: 60px;
        }
        .form--content {
            margin-bottom: 88px;
        }
    }
    @media screen and (max-width: 1600px) {
        padding: 80px 10px;
    }
    @media screen and (max-width: 991px) {
        padding: 40px 10px;
        &.register--club {
            .form--list {
                ._right {
                    width: 100%;
                }
            }
            .box--verify {
                .verify-col {
                    &+.verify-col {
                        padding-right: 20px;
                    }
                }
            }
            .check--agree {
                padding-top: 40px;
            }
            .form--content {
                margin-bottom: 50px;
            }
        }
    }
}

.form--list {
    display: flex;
    flex-wrap: wrap;
    gap: 30px 0;
    ._left {
        width: 50%;
        overflow: hidden;
        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
        }
    }
    ._right {
        width: 50%;
        padding: 0 59px 0 105px;
        .box-title {
            margin-bottom: 30px;
            .title {
                line-height: 129%;
                color: #000000;
                text-align: left;
                @media screen and (min-width: 1600px) {
                    font-size: 36px;
                }
            }
        }
        .form--btn {}
    }
    @media screen and (max-width: 1400px) {
        ._right {
            padding: 0 0px 0 50px;
        }
    }
    @media screen and (max-width: 991px) {
        ._left {
            width: 100%;
            display: none;
        }
        ._right {
            width: 100%;
            padding: 0;
        }
    }
}

.form--content {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    gap: 43px 32px;
    margin-bottom: 56px;
    .form--col {
        width: calc(50% - 16px);
    }
    .form--col12 {
        width: 100%;
    }
    .form--row {
        width: 100%;
        .input--control {
            width: 100%;
            height: 64px;
            border: 0;
            border-bottom: 1px solid #D1B181;
            color: rgba(0, 0, 0, 1);
            font-size: 20px;
            font-weight: 400;
            padding: 0;
            background-color: transparent;
            &::placeholder {
                color: rgba(0, 0, 0, 0.5);
            }
        }
        textarea.input--control {
            padding: 22px 0;
            height: 80px;
        }
        select.input--control {
            appearance: none;
            background-image: url('../images/ic-9.png');
            background-repeat: no-repeat;
            background-position: right 14px center;
            padding-right: 35px;
        }
        .txt {
            color: #4F4F4F;
            font-size: 20px;
            font-style: normal;
            font-weight: 400;
            line-height: 120%;
            opacity: 0.8;
        }
    }
    @media screen and (max-width: 991px) {
        .form--row {
            .input--control {
                font-size: 18px;
            }
            .txt {
                font-size: 18px;
            }
        }
    }
    @media screen and (max-width: 767px) {
        .form--row {
            .input--control {
                font-size: 16px;
            }
            .txt {
                font-size: 16px;
            }
        }
        .form--col {
            width: 100%;
        }
    }
}