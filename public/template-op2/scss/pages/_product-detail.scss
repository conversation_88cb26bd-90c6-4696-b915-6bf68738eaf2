.block-product-detail {
    padding: 50px 0px 60px;
    width: 100%;
    overflow: hidden;
    position: relative;
    @media screen and (max-width: 991px) {
        padding: 40px 0;
    }
}

.detail-prdt {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    position: relative;
    max-width: 1360px;
    margin: 0 auto 108px;
    gap: 30px;
    justify-content: space-between;
    .box-img-detail {
        width: calc(57% - 15px);
        height: 480px;
        display: flex;
        position: relative;
        justify-content: space-between;
        margin-bottom: 50px;
        max-width: 651px;
        .img-detail-prdt {
            width: 100%;
            height: 100%;
            background: #F6F6F6;
            border: 1px solid #f6f6f6;
            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                object-position: center;
            }
        }
        .swiper {
            width: 100%;
            height: 100%;
        }
        .swiper-button-next::after,
        .swiper-rtl .swiper-button-prev::after {
            display: none;
        }
        .swiper-button-prev::after,
        .swiper-rtl .swiper-button-next::after {
            display: none;
        }
        .swiper-slide {
            text-align: center;
            font-size: 18px;
            background: #fff;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .swiper-slide img {
            display: block;
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .swiper {
            width: 100%;
            height: 700px;
            margin-left: auto;
            margin-right: auto;
        }
        .swiper-slide {
            background-size: cover;
            background-position: center;
        }
        .mySwiper2 {
            height: 100%;
            width: calc(100% - 171px);
            margin-left: auto;
            margin-right: 0px;
        }
        .mySwiper {
            height: 100%;
            width: 113px;
            margin: 0px;
            box-sizing: border-box;
            padding: 0px;
            position: relative;
        }
        .mySwiper .swiper-slide {
            width: 100%;
            height: 100%;
            overflow: hidden;
            opacity: 0.9;
            border: 1px solid #f6f6f6;
            background: #F6F6F6;
            cursor: pointer;
        }
        .mySwiper .swiper-slide-thumb-active {
            opacity: 1;
            border: 2px solid #DEDEDE;
        }
        .swiper-slide img {
            display: block;
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .custom-nav-next {
            position: absolute;
            bottom: 0;
            width: 36px;
            height: 36px;
            margin: 0px;
            padding: 0px;
            left: 40px;
            bottom: -17px;
            margin-left: 0px;
            display: flex;
            align-items: center;
            justify-content: center;
            top: auto;
            right: auto;
            z-index: 10;
            border: 1px solid #DEDEDE;
            border-radius: 50%;
            transform: rotate(90deg);
            background-color: #fff;
        }
        .custom-nav-prev {
            position: absolute;
            bottom: 0;
            width: 36px;
            height: 36px;
            margin: 0px;
            padding: 0px;
            left: 40px;
            top: -17px;
            margin-left: 0px;
            display: flex;
            align-items: center;
            justify-content: center;
            bottom: auto;
            right: auto;
            z-index: 10;
            border: 1px solid #DEDEDE;
            border-radius: 50%;
            transform: rotate(90deg);
            background-color: #fff;
        }
        @media (max-width:1400px) {}
        @media (max-width:1279px) {
            flex-direction: column;
            justify-content: space-between;
            .mySwiper {
                width: calc(100% - 30px);
                margin-left: 15px;
                height: 90px;
                order: 2;
                margin-top: 0px;
            }
            .mySwiper2 {
                width: 100%;
                height: 430px;
                order: 1;
                margin-bottom: 30px;
            }
            .custom-nav-prev {
                top: auto;
                left: 0;
                right: auto;
                bottom: 26px;
                transform: rotate(0);
            }
            .custom-nav-next {
                left: auto;
                right: 0;
                transform: rotate(0);
                top: auto;
                bottom: 26px;
            }
        }
        @media (max-width:992px) {
            height: 500px;
            .mySwiper {
                height: 90px;
            }
            .mySwiper2 {
                height: 390px;
                margin-bottom: 20px;
            }
        }
        @media (max-width:767px) {}
    }
    .custom-nav {
        // display: none;
    }
    .info-detail {
        width: calc(43% - 15px);
        padding: 0 0 20px 102px;
        border-left: 1px solid #D5D5D5;
        display: flex;
        flex-direction: column;
        position: sticky;
        max-width: 527px;
        .name {
            color: #4F4F4F;
            font-size: 23.867px;
            font-style: normal;
            line-height: 142.455%;
            text-transform: capitalize;
            margin: 0 0 10px;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
        }
        .sub_name {
            color: #AE8751;
            font-size: 16px;
            line-height: 130%;
            margin-bottom: 20px;
        }
        .prdt_price {
            color: #AE8751;
            font-size: 24px;
            line-height: 116.667%;
            margin-bottom: 17px;
        }
        .product--code {
            color: #757575;
            font-size: 16px;
            line-height: 140%;
            margin-bottom: 50px;
        }
        .row-choose-opt {
            width: 100%;
            margin-bottom: 17px;
        }
        .choose-color {
            width: 100%;
            display: flex;
            align-items: center;
            margin-bottom: 26px;
            .lbl {
                width: 120px;
                font-family: 'SVN-Gilroy';
                font-size: 14px;
                font-weight: 400;
                line-height: 14.7px;
                text-align: left;
                color: #6A6769;
            }
            .list {
                display: flex;
                flex-wrap: wrap;
                width: calc(100% - 120px);
                gap: 18px;
                .form-check {
                    display: block;
                    width: 36px;
                    height: 36px;
                    margin: 0px;
                    cursor: pointer;
                    &-input {
                        display: none;
                        &:checked~ {
                            .form-check-checkmark {
                                border: 1px solid transparent;
                                &::before {
                                    border: none;
                                    display: block;
                                }
                                &::after {
                                    content: '';
                                    position: absolute;
                                    top: -1px;
                                    left: -1px;
                                    height: 37px;
                                    width: 37px;
                                    // transform: scale(1.5);
                                    border: 3px solid #DEDEDE;
                                    border-radius: 50%;
                                }
                            }
                            .txt {
                                color: var(--grey);
                            }
                        }
                    }
                    &-checkmark {
                        width: 36px;
                        height: 36px;
                        border-radius: 50%;
                        border: 1px solid #DEDEDE;
                        top: 0px;
                        display: block;
                        position: absolute;
                        left: 0px;
                    }
                }
            }
            .form-check-checkmark {
                border: 1px solid #ccc;
            }
        }
        .choose-size {
            width: 100%;
            display: flex;
            align-items: center;
            margin-bottom: 26px;
            .lbl {
                width: 55px;
                color: #757575;
                font-size: 16px;
                font-style: normal;
                line-height: 140%;
            }
            .list {
                display: flex;
                flex-wrap: wrap;
                max-width: calc(100% - 55px);
                margin-left: auto;
                gap: 10px 20px;
                justify-content: flex-start;
                .form-check {
                    display: block;
                    width: auto;
                    height: 35px;
                    margin: 0px;
                    padding: 0px;
                    cursor: pointer;
                    &-input {
                        display: none;
                        &:checked~ {
                            .form-check-checkmark {
                                border: 1px solid #AE8751;
                                &::before {
                                    border: none;
                                    display: block;
                                }
                                &::after {
                                    display: none;
                                }
                            }
                            .txt {
                                color: var(--grey);
                            }
                        }
                    }
                    &-checkmark {
                        width: auto;
                        min-width: 75px;
                        text-align: center;
                        line-height: 35px;
                        height: 35px;
                        border-radius: 0px;
                        border: 1px solid #EDD0A7;
                        top: 0px;
                        display: block;
                        position: relative;
                        left: 0px;
                        white-space: nowrap;
                        padding: 0px 10px;
                        color: #4F4F4F;
                        font-size: 12.624px;
                        font-weight: 600;
                    }
                }
            }
            .form-check-checkmark {
                border: 1px solid #ccc;
            }
        }
        .row-qty-btn-act {
            width: 100%;
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
            margin-bottom: 20px;
            .pricegold--input {
                flex-shrink: 0;
                .inp-sl {
                    .num {
                        height: 56px;
                    }
                }
            }
        }
        .prddetail-sp {
            width: 100%;
            padding-top: 30px;
            display: flex;
            flex-direction: column;
            gap: 25px;
            align-items: center;
            .select--size {
                color: #4F4F4F;
                text-align: center;
                font-size: 12.797px;
                font-style: normal;
                line-height: 156.288%;
                border-bottom: 1px solid #4F4F4F;
                width: max-content;
            }
            .sp--phone {
                color: #4F4F4F;
                text-align: center;
                font-size: 12.188px;
                font-style: normal;
                line-height: 164.103%;
                a {
                    color: inherit;
                    display: inline-block;
                    border-bottom: 1px solid #4F4F4F;
                }
            }
        }
        .storesystem--btn {
            max-width: 100%;
            padding: 12px;
        }
        .btn-addtocart {
            color: #4F4F4F;
            text-align: center;
            font-size: 20px;
            font-style: normal;
            font-weight: 600;
            line-height: 56px;
            height: 56px;
            flex: 1;
            width: 100%;
            border: 1px solid #EDD0A7;
            &:hover {
                color: #7C0410;
                background: #EDD0A7;
            }
        }
    }
    @media (max-width:1400px) {
        .info-detail {
            padding-left: 30px;
            .name {
                font-size: 24px;
                line-height: 34px;
            }
            .prdt_price,
            .desc-detail {
                padding-bottom: 20px;
                margin-bottom: 20px;
            }
            .row-choose-opt {
                margin-bottom: 20px;
                padding-bottom: 0px;
                .choose-color,
                .choose-size {
                    margin-bottom: 15px;
                    .lbl {
                        width: 100px;
                    }
                    .list {
                        gap: 10px;
                    }
                }
            }
        }
    }
    @media (max-width:1279px) {
        .box-img-detail {
            height: auto;
            margin-bottom: 0;
        }
        .info-detail {
            margin-bottom: 40px;
            .name {
                font-size: 20px;
                line-height: 30px;
            }
            .row-qty-btn-act {
                flex-wrap: wrap;
                justify-content: space-between;
                .btn-addtocart {
                    font-size: 18px;
                }
            }
        }
    }
    @media (max-width:992px) {
        margin-bottom: 60px;
        .info-detail {
            width: 100%;
            max-width: 100%;
            border: 0;
            padding: 0;
            .prdt_price {
                font-size: 20px;
            }
            .name {
                font-size: 18px;
            }
        }
        .box-img-detail {
            width: 100%;
            max-width: 100%;
        }
    }
    @media (max-width:767px) {
        padding-top: 0px;
        .box-img-detail {
            width: 100%;
            margin-bottom: 30px;
            padding: 0px;
        }
        .info-detail {
            width: 100%;
            padding: 0px;
            max-width: 100%;
            margin-bottom: 30px;
        }
    }
    @media (max-width: 570px) {
        padding-top: 0px;
        .box-img-detail {
            width: 100%;
            margin-bottom: 30px;
            padding: 0px;
            height: 400px;
            .mySwiper {
                height: 80px;
            }
            .mySwiper {
                height: 80px;
            }
            .mySwiper2 {
                height: 300px;
            }
        }
        .info-detail {
            width: 100%;
            padding: 0px;
            max-width: 100%;
            .row-qty-btn-act {
                flex-wrap: wrap;
                flex-direction: column;
                .pricegold--input {
                    max-width: 100%;
                }
            }
        }
    }
    @media (max-width:375px) {}
    @media (max-width:320px) {}
}

.detail--content {
    max-width: 1360px;
    margin: 0 auto;
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    gap: 50px;
    ._items {
        width: 100%;
        .btn {
            width: 100%;
            color: #4F4F4F;
            font-size: 23.867px;
            font-style: normal;
            line-height: 142.455%;
            text-transform: capitalize;
            text-align: left;
            padding: 0 0 12px;
            border-radius: 0;
            border-bottom: 1px solid #D5D5D5;
            padding-right: 30px;
            position: relative;
            span {
                display: block;
                width: max-content;
                height: max-content;
                position: absolute;
                top: 0;
                right: 0;
                transition: all 0.3s;
            }
            &[aria-expanded="true"] {
                span {
                    transform: rotate(-45deg);
                }
            }
            &:focus,
            &:focus-visible {
                box-shadow: none;
            }
        }
        .detail--child {
            padding: 62px 0 0;
            .detail--other {
                width: 100%;
                max-width: 1074px;
                display: flex;
                flex-wrap: wrap;
                gap: 30px;
                align-items: flex-start;
                margin: 0 auto;
                justify-content: space-between;
                .detail--style1 {
                    width: max-content;
                    &>* {
                        white-space: nowrap;
                        margin: 0;
                    }
                }
                .detail--style2 {
                    width: 100%;
                    color: #4F4F4F;
                    text-align: center;
                    font-size: 20px;
                    font-style: normal;
                    font-weight: 600;
                    line-height: normal;
                }
            }
            .detail--style3 {
                background: #FBF6ED;
                padding: 45px 80px 3px;
                .img-detail-logo {
                    text-align: center;
                    margin-bottom: 135px;
                }
                .img-detail-title {
                    position: relative;
                    text-align: center;
                    span {
                        display: inline-block;
                        padding: 0 20px;
                        background-color: #FBF6ED;
                        color: #7C0410;
                        text-align: center;
                        font-size: 28px;
                        line-height: normal;
                        text-transform: uppercase;
                        position: relative;
                        z-index: 5;
                    }
                    &::after {
                        width: 100%;
                        height: 1px;
                        background-color: #D5D5D5;
                        content: '';
                        display: block;
                        top: 0;
                        left: 0;
                        bottom: 0;
                        right: 0;
                        margin: auto;
                        position: absolute;
                    }
                }
                .detail--child---list {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 0 30px;
                    justify-content: space-between;
                    .detail--child---col {
                        width: calc(33.3333% - 20px);
                        max-width: 240px;
                        padding: 73px 0 95px;
                        .img {
                            text-align: center;
                            margin-bottom: 19px;
                            img {
                                max-width: 90px !important;
                                height: auto !important;
                            }
                        }
                        .desc {
                            color: #4F4F4F;
                            text-align: center;
                            font-size: 20px;
                            line-height: normal;
                        }
                    }
                    .__line {
                        width: 100%;
                        height: 1px;
                        background: #D5D5D5;
                    }
                }
            }
        }
    }
    @media screen and (max-width: 1600px) {
        ._items {
            .btn {
                font-size: 20px;
            }
            .detail--child {
                padding: 40px 0 0;
                .detail--other {
                    .detail--style2 {
                        font-size: 18px;
                    }
                }
                .detail--style3 {
                    .img-detail-logo {
                        text-align: center;
                        margin-bottom: 80px;
                    }
                    .img-detail-title {
                        span {
                            font-size: 24px;
                        }
                    }
                    .detail--child---list {
                        .detail--child---col {
                            .desc {
                                font-size: 18px;
                            }
                        }
                    }
                }
            }
        }
    }
    @media screen and (max-width: 1200px) {
        ._items {
            .btn {
                font-size: 18px;
            }
            .detail--child {
                padding: 40px 0 0;
                .detail--other {
                    .detail--style2 {
                        font-size: 16px;
                    }
                }
                .detail--style3 {
                    .img-detail-logo {
                        text-align: center;
                        margin-bottom: 80px;
                    }
                    .img-detail-title {
                        span {
                            font-size: 20px;
                        }
                    }
                    .detail--child---list {
                        .detail--child---col {
                            .desc {
                                font-size: 16px;
                            }
                        }
                    }
                }
            }
        }
    }
    @media screen and (max-width: 574px) {
        ._items {
            .detail--child {
                .detail--style1 {
                    width: 100%;
                }
                .detail--style3 {
                    padding: 20px;
                    .img-detail-logo {
                        margin-bottom: 50px;
                    }
                    .detail--child---list {
                        justify-content: center;
                        .detail--child---col {
                            width: 100%;
                            padding: 20px 0;
                        }
                        .__line {
                            display: none;
                        }
                    }
                }
            }
        }
    }
}