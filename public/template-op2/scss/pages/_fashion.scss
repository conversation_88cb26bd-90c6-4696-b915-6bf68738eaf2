.fashion-latestcollection {
    width: 100%;
    padding: 96px 10px 39px;
    .box-title {
        margin-bottom: 144px;
        .title {
            color: #B5751D;
            margin: 0 0 40px;
            text-align: center;
        }
        .desc {
            max-width: 677px;
            margin: 0 auto;
            text-align: center;
        }
    }
    .store--list {
        ._left {
            max-width: 514px;
        }
        .store--box {
            padding-top: 10px;
        }
    }
    .latestcollection--springday {
        width: 100%;
        background: linear-gradient(180deg, #7C0410 0%, #3B000C 100%);
        .title {
            text-align: center;
            font-size: 45px;
            font-style: normal;
            line-height: 150%;
            letter-spacing: 18px;
            text-transform: capitalize;
            color: #EDD0A7;
            background: var(--Gradient-Gold---Light, linear-gradient(180deg, #EDD0A7 0%, #FFF 100%));
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            padding: 20px 0;
            margin: 0 0 20px;
        }
        .latestcollection--body {
            padding: 0 20px 40px;
            .img {
                display: block;
                margin: 0 auto 43px;
                width: 56%;
                img {
                    width: 100% !important;
                    height: auto !important;
                }
            }
            .box--btn {
                text-align: center;
                .btn-links {
                    color: #FFF;
                    border-bottom: 1px solid #fff;
                }
            }
        }
    }
    &.fashion-latestcollection-mh {
        .box-title {
            .title {}
            .desc {
                max-width: 421px;
            }
        }
        .latestcollection--springday {
            background: linear-gradient(180deg, #CB892E 0%, #F4B85D 100%);
            .title {
                color: #FBDEB1;
            }
            .latestcollection--body {
                padding: 0 20px 40px;
                .img {
                    display: block;
                    margin: 0 auto 43px;
                    width: 56%;
                    img {
                        width: 100% !important;
                        height: auto !important;
                    }
                }
                .box--btn {
                    text-align: center;
                    .btn-links {
                        color: #4F4F4F;
                        border-bottom: 1px solid #4F4F4F;
                    }
                }
            }
        }
    }
    @media screen and (max-width: 1200px) {
        .box-title {
            margin-bottom: 40px;
            .title {
                margin: 0 0 30px;
            }
        }
        .store--list {
            padding: 0;
            ._left {
                max-width: 100%;
            }
        }
        .latestcollection--springday {
            .title {
                font-size: 36px;
            }
            .latestcollection--body {
                padding: 0 20px 40px;
                .img {
                    display: block;
                    margin: 0 auto 30px;
                    width: 100%;
                }
            }
        }
        &.fashion-latestcollection-mh {
            .latestcollection--springday {
                .latestcollection--body {}
            }
        }
    }
    @media screen and (max-width: 991px) {
        padding: 40px 10px;
    }
    @media screen and (max-width: 767px) {
        &.fashion-latestcollection-mh {
            .latestcollection--springday {
                .latestcollection--body {
                    .img {
                        width: 75%;
                    }
                }
            }
        }
    }
}