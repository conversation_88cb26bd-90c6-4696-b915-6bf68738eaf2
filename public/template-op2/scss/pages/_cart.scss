.cart-main {
    width: 100%;
    padding: 105px 10px 140px;
    &.cart-main--success {
        padding: 42px 10px 63px;
        .cart--other {
            max-width: 1136px;
        }
    }
    .cart--other {
        width: 100%;
        max-width: 1362px;
        margin: 0 auto;
        .box-title {
            margin-bottom: 60px;
        }
    }
    @media screen and (max-width: 1600px) {
        padding: 80px 10px;
    }
    @media screen and (max-width: 991px) {
        padding: 40px 10px;
        .cart--other {
            .box-title {
                margin-bottom: 30px;
            }
        }
    }
}

.cart--list {
    gap: 30px 60px;
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    ._left {
        width: 100%;
        flex: 1;
    }
    ._right {
        width: 45%;
        max-width: 580px;
        .account-form {
            .box-title {
                margin-bottom: 0;
                .title {
                    font-size: 30px;
                    line-height: 113.333%;
                }
            }
            .form--content {
                gap: 23px 27px;
                padding: 64px 24px;
                margin-bottom: 40px;
                background-color: rgba(237, 208, 167, 0.1);
                .form--row {
                    .input--control {
                        border-bottom: 1px solid rgba(79, 79, 79, 0.5);
                    }
                }
            }
        }
        .form--btn {
            .storesystem--btn {
                max-width: 100%;
            }
        }
    }
    @media screen and (max-width: 991px) {
        flex-wrap: wrap;
        ._left {
            flex: none;
        }
        ._right {
            width: 100%;
            max-width: 100%;
            .account-form {
                .box-title {
                    margin-bottom: 0;
                    .title {
                        font-size: 20px;
                    }
                }
                .form--content {
                    padding: 30px 20px;
                }
            }
            .form--btn {
                .storesystem--btn {
                    max-width: 100%;
                }
            }
        }
    }
}

.cart--box {
    width: 100%;
    .cart--top {
        margin-bottom: 35px;
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        gap: 30px 44px;
        align-items: center;
        .img {
            width: 252px;
            height: 252px;
            overflow: hidden;
            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                object-position: center;
            }
        }
        .cart--top---body {
            width: 100%;
            flex: 1;
            .--name {
                color: #4F4F4F;
                font-size: 24px;
                font-style: normal;
                font-weight: 400;
                line-height: 141.667%;
                text-transform: capitalize;
                margin-bottom: 19px;
            }
            .--code {
                color: #4F4F4F;
                font-size: 12px;
                font-style: normal;
                line-height: 140%;
                text-transform: uppercase;
                margin-bottom: 23px;
            }
            .cart--top---select {
                display: flex;
                flex-wrap: wrap;
                gap: 10px;
                justify-content: space-between;
                margin-bottom: 50px;
                &>* {
                    display: flex;
                    gap: 8px;
                    align-items: center;
                    .txt {
                        color: #757575;
                        font-size: 14px;
                        font-style: normal;
                        line-height: 140%;
                        min-width: 60px;
                    }
                }
                .pricegold--input {
                    width: 75px;
                    select {
                        padding: 0 10px 0 6px;
                        text-align: center;
                        background-position: right 7px center;
                    }
                    .inp-sl {
                        span {
                            width: 18px;
                        }
                    }
                }
            }
            .cart--top---del {
                display: flex;
                justify-content: space-between;
                gap: 10px;
                .btn-del {
                    display: inline-flex;
                    gap: 8px;
                    align-items: end;
                    border: 0;
                    box-shadow: none;
                    border-radius: 0;
                    color: #7C0410;
                    font-size: 13.945px;
                    font-style: normal;
                    font-weight: 300;
                    line-height: 150.588%;
                    text-transform: capitalize;
                    padding: 0;
                    background-color: transparent;
                    &:focus,
                    &:focus-visible {
                        outline: none;
                        box-shadow: none;
                    }
                }
                .--price {
                    white-space: nowrap;
                    color: #AE8751;
                    text-align: right;
                    font-size: 24px;
                    line-height: 116.667%;
                }
            }
        }
    }
    .cart--infor {
        background: rgba(237, 208, 167, 0.10);
        padding: 30px 27px 40px 51px;
        width: 100%;
        .cart--infor---title {
            color: #4F4F4F;
            font-size: 30px;
            font-style: normal;
            font-weight: 600;
            line-height: 113.333%;
            text-transform: capitalize;
            padding-bottom: 26px;
            border-bottom: 1px solid rgba(174, 135, 81, 0.7);
            margin-bottom: 30px;
        }
        .cart--infor---body {
            width: 100%;
            .discount--code {
                width: 100%;
                label {
                    color: #4F4F4F;
                    font-size: 20px;
                    font-style: normal;
                    line-height: 150%;
                    width: 100%;
                    margin: 0 0 5px;
                }
                .discount--code---input {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 10px 23px;
                    margin-bottom: 35px;
                    input {
                        flex: 1;
                        width: 100%;
                        height: 56px;
                        border: 1px solid #EDD0A7;
                        padding: 0 10px;
                        background-color: transparent;
                    }
                    .storesystem--btn {
                        width: 190px;
                        height: 56px;
                        padding: 5px;
                    }
                }
            }
        }
    }
    @media screen and (max-width: 1600px) {
        .cart--top {
            .img {}
            .cart--top---body {
                .--name {
                    font-size: 22px;
                }
                .cart--top---del {
                    .btn-del {
                        font-size: 13px;
                    }
                    .--price {
                        font-size: 22px;
                    }
                }
            }
        }
        .cart--infor {
            .cart--infor---title {
                font-size: 26px;
            }
            .cart--infor---body {
                .discount--code {
                    label {
                        font-size: 18px;
                    }
                }
            }
        }
    }
    @media screen and (max-width: 1200px) {
        .cart--top {
            .img {}
            .cart--top---body {
                flex: none;
            }
        }
    }
    @media screen and (max-width: 991px) {
        .cart--top {
            .img {}
            .cart--top---body {
                .--name {
                    font-size: 18px;
                }
                .cart--top---del {
                    .--price {
                        font-size: 18px;
                    }
                }
            }
        }
        .cart--infor {
            padding: 30px 20px;
            .cart--infor---title {
                font-size: 22px;
            }
            .cart--infor---body {
                .discount--code {
                    label {
                        font-size: 16px;
                    }
                }
            }
        }
    }
    @media screen and (max-width: 574px) {
        .cart--infor {
            .cart--infor---body {
                .discount--code {
                    .discount--code---input {
                        input {
                            flex: auto;
                        }
                        .storesystem--btn {
                            width: 100%;
                            max-width: 100%;
                        }
                    }
                }
            }
        }
    }
}

.cart--total {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: space-between;
    align-items: start;
    .txt {
        color: #AE8751;
        font-size: 24px;
        font-style: normal;
        line-height: 116.667%;
    }
    .number {
        color: #AE8751;
        text-align: right;
        font-size: 24px;
        font-style: normal;
        font-weight: 600;
        line-height: 116.667%;
        span {
            display: block;
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 175%;
        }
    }
    @media screen and (max-width: 1600px) {
        .txt {
            font-size: 22px;
        }
        .number {
            font-size: 22px;
        }
    }
    @media screen and (max-width: 991px) {
        .txt {
            font-size: 18px;
        }
        .number {
            font-size: 18px;
        }
    }
}

.cart--infor---list {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    padding-bottom: 40px;
    border-bottom: 1px solid rgba(174, 135, 81, 0.7);
    margin-bottom: 22px;
    .cart--infor---row {
        width: 100%;
        display: flex;
        gap: 10px;
        justify-content: space-between;
        .txt {
            color: $themegray;
            font-size: 20px;
            font-style: normal;
            line-height: 170%;
        }
        .number {
            color: $themegray;
            font-size: 20px;
            font-style: normal;
            font-weight: 600;
            line-height: 170%;
            text-transform: capitalize;
            text-align: right;
        }
    }
    @media screen and (max-width: 991px) {
        .cart--infor---row {
            .txt {
                font-size: 16px;
            }
            .number {
                font-size: 16px;
            }
        }
    }
}

.modal--book {
    .modal-dialog {
        max-width: 1015px;
        width: calc(100% - 30px);
    }
    .modal-content {
        border: 0;
        border-radius: 0;
        background: transparent;
    }
    .btn-close {
        position: absolute;
        line-height: 1;
        top: 55px;
        right: 52px;
        width: max-content;
        height: max-content;
        padding: 0;
    }
    .modalbook-content {
        padding: 44px 103px 60px;
        background-color: #fff;
        .title {
            color: #AE8751;
            text-align: center;
            font-size: 40px;
            font-weight: 700;
            line-height: 120%;
            margin-bottom: 60px;
        }
        .form--content {
            gap: 20px;
            margin-bottom: 54px;
            .form--row {
                .input--control {
                    height: 61px;
                    border-bottom: 1px solid rgba(79, 79, 79, 0.5);
                }
            }
        }
    }
    .modalbook-content--success {
        padding: 239px 103px 120px;
        background-color: #fff;
        .img {
            text-align: center;
            margin-bottom: 35px;
            img {
                max-width: 100%;
            }
        }
        .title {
            color: #4F4F4F;
            text-align: center;
            font-size: 40px;
            font-style: normal;
            font-weight: 600;
            line-height: 120%;
            text-transform: capitalize;
            margin-bottom: 35px;
            opacity: 0.7;
        }
        .desc {
            width: 100%;
            max-width: 646px;
            margin: 0 auto;
            color: #4F4F4F;
            opacity: 0.7;
            text-align: center;
            font-size: 20px;
            line-height: 120%;
        }
    }
    @media screen and (max-width: 1600px) {
        .modalbook-content {
            .title {
                font-size: 32px;
            }
            .form--content {
                margin-bottom: 40px;
            }
        }
        .modalbook-content--success {
            .title {
                font-size: 32px;
            }
            .desc {
                font-size: 18px;
            }
        }
    }
    @media screen and (max-width: 991px) {
        .btn-close {
            top: 20px;
            right: 15px;
        }
        .modalbook-content {
            padding: 35px 25px;
            .title {
                font-size: 24px;
                margin-bottom: 30px;
            }
            .form--content {
                margin-bottom: 30px;
            }
        }
        .modalbook-content--success {
            padding: 35px 25px;
            .title {
                font-size: 24px;
            }
            .desc {
                font-size: 16px;
            }
        }
    }
}

.block--btn {
    width: 100%;
    display: flex;
    gap: 10px 24px;
    &>* {
        flex: 1;
        border: 1px solid #B78E6C;
        color: #FFF;
        text-align: center;
        font-size: 20px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
        padding: 16px;
    }
    .btn--cancel {
        color: #B78E6C;
    }
    .btn--book {
        background-color: #B78E6C;
        &:hover {
            color: #fff;
        }
    }
    @media screen and (max-width:991px) {
        &>* {
            font-size: 16px;
        }
    }
}

.cartsuccess--box {
    width: 100%;
    padding: 41px 61px 79px;
    border: 1px solid rgba(79, 79, 79, 0.7);
    margin-bottom: 65px;
    .cartsuccess--top {
        width: 100%;
        padding-bottom: 35px;
        border-bottom: 1px solid rgba(79, 79, 79, 0.7);
        margin-bottom: 35px;
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        .cartsuccess--row {
            display: flex;
            gap: 15px;
            width: 100%;
            align-items: center;
            .txt {
                width: 150px;
                flex-shrink: 0;
                color: rgba(79, 79, 79, 0.7);
                font-size: 16px;
            }
            .desc {
                flex: 1;
                width: 100%;
                color: rgba(79, 79, 79, 0.7);
                font-size: 20px;
            }
        }
    }
    .cartsuccess--body {
        width: 100%;
        .cartsuccess--body---first {
            display: flex;
            flex-wrap: wrap;
            align-items: start;
            .img {
                width: 242px;
                height: 242px;
                overflow: hidden;
                flex-shrink: 0;
                img {
                    width: 100%;
                    height: 100%;
                    object-fit: scale-down;
                    object-position: center;
                }
            }
            .infor--desc {
                flex: 1;
                width: 100%;
                display: flex;
                gap: 14px 10px;
                .--price {
                    flex-shrink: 0;
                    color: $themegray;
                    font-size: 20px;
                    font-style: normal;
                    font-weight: 600;
                    line-height: 170%;
                    text-transform: capitalize;
                    white-space: nowrap;
                }
                .desc {
                    flex: 1;
                    width: 100%;
                    display: flex;
                    flex-wrap: wrap;
                    gap: 14px;
                    .--name {
                        color: $themegray;
                        font-size: 24px;
                        font-style: normal;
                        font-weight: 600;
                        line-height: 141.667%;
                        text-transform: capitalize;
                        width: 100%;
                    }
                    .--code {
                        color: $themegray-50;
                        font-size: 12px;
                        line-height: 140%;
                        width: 100%;
                        text-transform: uppercase;
                    }
                    .--parameter {
                        width: 100%;
                        display: flex;
                        flex-wrap: wrap;
                        gap: 14px 37px;
                        &>* {
                            color: $themegray-50;
                            font-size: 12px;
                            line-height: 140%;
                            width: 100%;
                            text-transform: uppercase;
                            width: max-content;
                        }
                    }
                    .--quantity {
                        color: $themegray-50;
                        font-size: 12px;
                        line-height: 140%;
                        width: 100%;
                        text-transform: uppercase;
                    }
                }
            }
        }
        .cart--infor---list {
            gap: 10px;
            border: 0;
            margin-bottom: 0;
            padding-bottom: 30px;
            .cart--infor---row {
                .txt {
                    color: $themegray-50;
                }
            }
        }
        .cart--total {
            padding: 27px 0 13px;
            position: relative;
            margin-bottom: 15px;
            &::after,
            &::before {
                content: '';
                display: block;
                width: 100%;
                height: 1px;
                background: transparent url('../images/bg-line.png') repeat-x center;
                position: absolute;
                left: 0;
            }
            &::after {
                bottom: 0;
            }
            &::before {
                top: 0;
            }
            .number {
                font-size: 30px;
                line-height: 93.333%;
            }
        }
        .cartsuccess--pay {
            margin-bottom: 40px;
            .--title {
                color: $themegray;
                font-size: 24px;
                font-style: normal;
                font-weight: 600;
                line-height: 141.667%;
                text-transform: capitalize;
                margin-bottom: 17px;
            }
            .cart--infor---list {
                padding: 0;
            }
        }
    }
    @media screen and (max-width: 1400px) {
        .cartsuccess--top {
            .cartsuccess--row {
                .desc {
                    font-size: 18px;
                }
            }
        }
        .cartsuccess--body {
            .cartsuccess--body---first {
                .img {}
                .infor--desc {
                    .--price {
                        font-size: 18px;
                    }
                    .desc {
                        .--name {
                            font-size: 20px;
                        }
                        .--parameter {}
                    }
                }
            }
            .cart--infor---list {}
            .cart--total {
                .number {
                    font-size: 24px;
                }
            }
            .cartsuccess--pay {
                .--title {
                    font-size: 22px;
                }
            }
        }
    }
    @media screen and (max-width: 991px) {
        padding: 40px 20px;
        .cartsuccess--top {
            .cartsuccess--row {
                .desc {
                    font-size: 16px;
                }
            }
        }
        .cartsuccess--body {
            .cartsuccess--body---first {
                .img {}
                .infor--desc {
                    flex-wrap: wrap;
                    .--price {
                        font-size: 16px;
                    }
                    .desc {
                        flex: none;
                        .--name {
                            font-size: 16px;
                        }
                        .--parameter {}
                    }
                }
            }
            .cart--infor---list {}
            .cart--total {
                .number {
                    font-size: 20px;
                }
            }
            .cartsuccess--pay {
                .--title {
                    font-size: 16px;
                }
            }
        }
    }
}

.cartsuccess--bot {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    justify-content: space-between;
    align-items: start;
    .desc {
        width: calc(50% - 15px);
        color: $themegray;
        font-size: 16px;
        max-width: 492px;
        .txt {
            display: inline;
            color: #571111;
            font-size: 20px;
            span {
                font-size: 24px;
            }
        }
    }
    .form--btn {
        width: calc(50% - 15px);
        max-width: 551px;
        .storesystem--btn {
            max-width: 100%;
            display: inline-flex;
            align-items: center;
            gap: 21px;
            justify-content: center;
        }
    }
    @media screen and (max-width: 991px) {
        .desc {
            width: 100%;
            max-width: 100%;
            .txt {
                font-size: 18px;
                span {
                    font-size: 20px;
                }
            }
        }
        .form--btn {
            width: 100%;
            max-width: 100%;
        }
    }
}