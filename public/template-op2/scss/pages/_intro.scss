.intro-top {
    width: 100%;
    padding: 110px 10px 80px;
    .box-title {
        margin-bottom: 85px;
        .title {
            text-align: center;
        }
    }
    .desc-txt {
        color: #4F4F4F;
        text-align: center;
        font-size: 20px;
        font-style: normal;
        line-height: 150%;
        width: 100%;
        max-width: 808px;
        margin: 0 auto 85px;
        p {
            &:last-child {
                margin-bottom: 0;
            }
        }
    }
    .img {
        text-align: center;
        img {
            max-width: 100%;
        }
    }
    @media screen and (max-width: 1600px) {
        padding: 80px 10px;
        .box-title {
            margin-bottom: 60px;
        }
    }
    @media screen and (max-width: 991px) {
        padding: 60px 10px;
        .box-title {
            margin-bottom: 30px;
            .title {
                img {
                    width: 80%;
                    max-width: 339px;
                }
            }
        }
        .desc-txt {
            margin: 0 auto 40px;
            font-size: 18px;
        }
    }
}

.intro-corevalues {
    width: 100%;
    padding: 60px 10px 100px;
    .corevalues--list {
        display: flex;
        flex-wrap: wrap;
        width: 100%;
        max-width: 1355px;
        margin: 0 auto;
        gap: 30px;
        ._left {
            width: calc(35% - 15px);
            .box-title {
                margin: 0;
                .title {
                    @media screen and (min-width: 1600px) {
                        font-size: 36px;
                        line-height: 129%;
                    }
                }
            }
        }
        ._right {
            width: calc(65% - 15px);
            max-width: 800px;
        }
    }
    @media screen and (max-width: 991px) {
        padding: 60px 10px;
        .corevalues--list {
            ._left {
                width: 100%;
                .box-title {
                    margin: 0;
                    .title {
                        text-align: center;
                    }
                }
            }
            ._right {
                width: 100%;
            }
        }
    }
}

.corevalues--box {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    .corevalues--items {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        padding: 36px 0;
        border-top: 1px solid #AE8751;
        &:last-child {
            padding-bottom: 0;
        }
        &:first-child {
            padding-top: 0;
            border-top: 0;
        }
        .corevalues--col {
            flex: 1;
            width: 100%;
            .title {
                color: #7C0410;
                font-size: 30px;
                font-style: normal;
                font-weight: 700;
                line-height: normal;
                text-transform: capitalize;
            }
            .txt {
                color: #4F4F4F;
                font-size: 20px;
                font-style: normal;
                line-height: 150%;
                p {
                    &:last-child {
                        margin-bottom: 0;
                    }
                }
            }
        }
    }
    @media screen and (max-width: 1600px) {
        .corevalues--items {
            .corevalues--col {
                .title {
                    font-size: 26px;
                }
                .txt {
                    font-size: 18px;
                }
            }
        }
    }
    @media screen and (max-width: 991px) {
        .corevalues--items {
            .corevalues--col {
                .title {
                    font-size: 22px;
                }
                .txt {
                    font-size: 16px;
                }
            }
        }
    }
    @media screen and (max-width: 767px) {
        .corevalues--items {
            padding: 20px 0;
            .corevalues--col {
                .title {
                    font-size: 18px;
                }
            }
        }
    }
    @media screen and (max-width: 574px) {
        .corevalues--items {
            .corevalues--col {
                flex: none;
            }
        }
    }
}

.home-brandvision {
    width: 100%;
    padding: 78px 10px 118px;
    background: transparent url('../images/bg-brand vision.jpg') no-repeat center center;
    background-size: cover;
    .brandvision--list {
        display: flex;
        flex-wrap: wrap;
        gap: 30px;
        align-items: center;
        width: 100%;
        max-width: 1355px;
        margin: 0 auto;
        ._left {
            width: 35%;
            max-width: 531px;
            img {
                text-align: center;
                max-width: 100% !important;
                height: auto !important;
            }
        }
        ._right {
            flex: 1;
            width: 100%;
            .brandvision--body {
                width: 100%;
                max-width: 546px;
                margin: 0 auto;
                .box-title {
                    margin-bottom: 43px;
                    .title {
                        color: #EDD0A7;
                        text-align: center;
                        @media screen and (min-width: 1600px) {
                            font-size: 36px;
                            line-height: 129%;
                        }
                    }
                }
                .desc {
                    color: #FFF;
                    text-align: center;
                    font-size: 20px;
                    font-style: normal;
                    line-height: 150%;
                    margin-bottom: 55px;
                    p {
                        &:last-child {
                            margin-bottom: 0;
                        }
                    }
                }
                .infor--author {
                    color: #EDD0A7;
                    text-align: center;
                    font-size: 16px;
                    font-style: normal;
                    line-height: 150%;
                }
            }
        }
    }
    @media screen and (max-width: 991px) {
        padding: 60px 10px;
        .brandvision--list {
            ._left {
                width: 100%;
                text-align: center;
                max-width: 100%;
            }
            ._right {
                width: 100%;
                flex: none;
                order: -1;
                .brandvision--body {
                    .box-title {
                        margin-bottom: 30px;
                    }
                    .desc {
                        font-size: 18px;
                        margin-bottom: 30px;
                    }
                }
            }
        }
    }
}

.intro-historyestablishment {
    padding: 121px 10px 80px;
    background: transparent url('../images/bg-lsht.jpg') no-repeat center center;
    background-size: cover;
    .box-title {
        margin-bottom: 82px;
        .title {
            color: #FFF;
        }
    }
    @media screen and (max-width: 1400px) {
        padding: 80px 10px;
        .box-title {
            margin-bottom: 60px;
        }
    }
    @media screen and (max-width: 991px) {
        padding: 60px 10px;
        .box-title {
            margin-bottom: 30px;
        }
    }
}

.historyestablishment--list {
    width: calc(100% - (100% - 1670px)/2);
    margin-left: auto;
    padding-left: 15px;
    .historyestablishment--top {
        position: relative;
        margin-bottom: 57px;
        .producbox--btn {
            .swiper-button-next {
                right: 10px;
                &.swiper-button-disabled {
                    display: none !important;
                }
            }
            .swiper-button-prev {
                left: -15px;
                &.swiper-button-disabled {
                    display: none !important;
                }
            }
        }
        .historyestablishment--other {
            width: 100%;
            overflow: hidden;
            padding-left: 381px;
            .swiper {
                overflow: visible;
                height: auto;
                width: 100%;
                max-width: 459px;
                margin-left: 0;
                .swiper-slide {
                    height: auto;
                    padding: 0 96px 65px 78px;
                    &.swiper-slide-prev,
                    &.swiper-slide-next,
                    &.swiper-slide-active {
                        border-right: 1px solid #FFE9CA;
                    }
                    &:last-child {
                        border-right: 0;
                    }
                    &.swiper-slide-active {
                        padding: 0 31px 65px 36px;
                        .historyestablishment--items {
                            .img {
                                display: none;
                            }
                            .years {
                                margin-bottom: 40px;
                                font-size: 80px;
                                font-style: normal;
                                font-weight: 200;
                                line-height: 130.839%;
                            }
                            .historyestablishment--body {
                                .desc {
                                    -webkit-line-clamp: unset;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    .historyestablishment--bot {
        .swiper {
            .swiper-slide {
                width: max-content;
                color: rgba(255, 255, 255, 0.40);
                font-size: 15.047px;
                font-style: normal;
                line-height: 179.506%;
                cursor: pointer;
                &.swiper-slide-thumb-active {
                    color: #FFF;
                }
            }
        }
    }
    @media screen and (max-width: 1670px) {
        width: calc(100% - 20px);
        margin-right: auto;
        padding-left: 0;
        .historyestablishment--top {
            .producbox--btn {
                .swiper-button-next {
                    right: -15px;
                }
            }
        }
    }
    @media screen and (max-width: 1600px) {
        .historyestablishment--top {
            .historyestablishment--other {
                .swiper {
                    .swiper-slide {
                        &.swiper-slide-active {
                            .historyestablishment--items {
                                .years {
                                    margin-bottom: 30px;
                                    font-size: 60px;
                                }
                            }
                        }
                    }
                }
            }
        }
        .historyestablishment--bot {
            .swiper {
                .swiper-slide {
                    font-size: 14px;
                }
            }
        }
    }
    @media screen and (max-width: 991px) {
        .historyestablishment--top {
            .historyestablishment--other {
                padding: 0;
                .swiper {
                    .swiper-slide {
                        padding-bottom: 30px;
                        &.swiper-slide-active {
                            padding-bottom: 30px;
                            .historyestablishment--items {
                                .years {
                                    font-size: 50px;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    @media screen and (max-width: 574px) {
        .historyestablishment--top {
            .historyestablishment--other {
                padding: 0;
                .swiper {
                    .swiper-slide {
                        padding-bottom: 30px;
                        &.swiper-slide-prev,
                        &.swiper-slide-next,
                        &.swiper-slide-active {
                            border-right: 0;
                        }
                        &.swiper-slide-active {
                            padding-bottom: 30px;
                            .historyestablishment--items {
                                .years {
                                    font-size: 45px;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

.historyestablishment--items {
    width: 100%;
    .img {
        padding-top: 100%;
        width: 100%;
        position: relative;
        margin-bottom: 55px;
        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
            position: absolute;
            top: 0;
            left: 0;
        }
    }
    .years {
        color: #FFE9CA;
        font-size: 30px;
        font-style: normal;
        line-height: 166.667%;
        margin-bottom: 25px;
    }
    .historyestablishment--body {
        .title {
            color: #FFF;
            font-size: 20px;
            font-style: normal;
            font-weight: 700;
            line-height: 139.95%;
            margin-bottom: 20px;
        }
        .desc {
            color: #FFF;
            font-size: 16px;
            font-style: normal;
            line-height: 150%;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 4;
            overflow: hidden;
            p {
                &:last-child {
                    margin-bottom: 0;
                }
            }
        }
    }
    @media screen and (max-width: 991px) {
        .years {
            font-size: 22px;
        }
        .historyestablishment--body {
            .title {
                font-size: 18px;
            }
            .desc {
                font-size: 16px;
            }
        }
    }
    @media screen and (max-width: 767px) {
        .img {
            margin-bottom: 30px;
        }
    }
    @media screen and (max-width: 574px) {
        .img {
            display: none;
        }
    }
}

.intro-philosophy {
    padding: 123px 10px 96px;
    width: 100%;
    .box-title {
        margin-bottom: 80px;
        .title {
            text-align: center;
            margin-bottom: 15px;
        }
        .desc {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }
    }
    .philosophy--list {
        position: relative;
        margin-bottom: 100px;
    }
    .box--btn {
        text-align: center;
    }
    @media screen and (max-width: 1600px) {
        padding: 96px 10px;
        .box-title {
            margin-bottom: 60px;
        }
        .philosophy--list {
            position: relative;
            margin-bottom: 60px;
        }
    }
    @media screen and (max-width: 991px) {
        padding: 60px 10px;
        .box-title {
            margin-bottom: 30px;
        }
        .philosophy--list {
            position: relative;
            margin-bottom: 40px;
        }
    }
}

.philosophy--items {
    width: 100%;
    .img {
        width: 100%;
        padding-top: 100%;
        overflow: hidden;
        position: relative;
        display: block;
        margin-bottom: 65px;
        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
            position: absolute;
            top: 0;
            left: 0;
        }
    }
    .desc {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3;
        overflow: hidden;
        color: #4F4F4F;
        font-size: 20px;
        line-height: 140%;
    }
    @media screen and (max-width: 991px) {
        padding-bottom: 20px;
        .img {
            margin-bottom: 30px;
        }
        .desc {
            font-size: 18px;
        }
    }
}

.bannerslide-main {
    width: 100%;
    .swiper-slide {
        height: auto;
    }
    .bannerslide--items {
        width: 100%;
        height: 100%;
        padding: 80px 10px;
        background-repeat: no-repeat;
        background-position: center;
        background-size: cover;
        display: flex;
        align-items: center;
        height: calc(100vh - 183px);
        .bannerslide--other {
            width: 100%;
            max-width: 401px;
            .box-title {
                margin-bottom: 44px;
                .title {
                    color: #EDD0A7;
                }
                .sub_title {
                    color: #EDD0A7;
                    margin-bottom: 34px;
                }
                .desc {
                    color: #fff;
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 6;
                    overflow: hidden;
                }
            }
            .box--btn {
                .btn-links {
                    color: #EDD0A7;
                    border-bottom: 1px solid #EDD0A7;
                }
            }
        }
        .goldmain-title {
            color: #4F4F4F;
            text-align: center;
            font-family: "Pinyon Script";
            font-size: 131.842px;
            font-style: normal;
            font-weight: 400;
            line-height: 110%;
            text-transform: capitalize;
            margin: 0;
        }
        &.style--2 {
            .bannerslide--other {
                .box-title {
                    .title {
                        color: #4F4F4F;
                    }
                    .sub_title {
                        color: #4F4F4F;
                    }
                    .desc {
                        color: #4F4F4F;
                        font-size: 18px;
                    }
                }
                .box--btn {
                    .btn-links {
                        color: #4F4F4F;
                        border-bottom: 1px solid #4F4F4F;
                    }
                }
            }
        }
    }
    .producbox--panigation {
        .swiper-pagination {
            position: absolute;
        }
    }
    &.bannerslide-boxstyle2 {
        .bannerslide--items {
            .bannerslide--other {
                margin-right: 126px;
            }
        }
    }
    &.bannerslide-child {
        .bannerslide--items {
            .bannerslide--other {
                .box-title {
                    color: #EDD0A7;
                    font-size: 18px;
                }
            }
        }
    }
    &.bannerslide-main-club {
        .bannerslide--items {
            .bannerslide--other {
                max-width: 684px;
                .box-title {
                    margin-bottom: 24px;
                    .title {
                        color: #fff;
                        margin-bottom: 24px;
                    }
                    .desc {
                        color: #fff;
                        font-size: 18px;
                    }
                }
                .box--btn {
                    .btn-links {
                        color: #FFFFFF;
                        border-bottom: 1px solid #FFFFFF;
                    }
                }
            }
        }
    }
    @media screen and (max-width: 1600px) {
        .bannerslide--items {
            padding: 60px 10px;
            .bannerslide--other {
                .box-title {
                    margin-bottom: 40px;
                    .sub_title {
                        color: #EDD0A7;
                        margin-bottom: 30px;
                    }
                }
            }
            .goldmain-title {
                font-size: 120px;
            }
        }
    }
    @media screen and (max-width: 1400px) {
        .bannerslide--items {
            .bannerslide--other {
                .box-title {
                    margin-bottom: 30px;
                    .sub_title {
                        margin-bottom: 25px;
                    }
                }
            }
            .goldmain-title {
                font-size: 110px;
            }
        }
    }
    @media screen and (max-width: 1200px) {
        .bannerslide--items {
            .goldmain-title {
                font-size: 100px;
            }
        }
    }
    @media screen and (max-width: 991px) {
        .bannerslide--items {
            .goldmain-title {
                font-size: 80px;
            }
        }
    }
    @media screen and (max-width: 767px) {
        .bannerslide--items {
            .goldmain-title {
                font-size: 60px;
                margin-bottom: 30px;
            }
            .col-md-6 {
                &:last-child {
                    order: -1;
                }
            }
        }
    }
    @media screen and (max-width: 574px) {
        .bannerslide--items {
            .goldmain-title {
                font-size: 40px;
            }
        }
    }
}

.introchild-collections {
    width: 100%;
    padding: 50px 10px 137px;
    overflow: hidden;
    .box-title {
        margin-bottom: 38px;
        .title {
            text-align: center;
            @media screen and (min-width: 1600px) {
                font-size: 36px;
            }
        }
        .desc {
            font-size: 16.855px;
            text-align: center;
        }
    }
    .collections--list {
        position: relative;
        width: 100%;
        max-width: 803px;
        margin: 0 auto;
        .producbox--panigation {
            position: absolute;
            width: 100%;
            bottom: -55px;
            .swiper-pagination {
                .swiper-pagination-bullet {
                    &.swiper-pagination-bullet-active {
                        background-color: #4F4F4F;
                    }
                }
            }
        }
        &::after {
            width: 1px;
            height: calc(50% - 18px);
            background: #AE8751;
            content: '';
            display: block;
            position: absolute;
            top: 0;
            left: 0;
            z-index: 3;
        }
        &::before {
            width: 1px;
            height: calc(50% - 18px);
            background: #AE8751;
            content: '';
            display: block;
            position: absolute;
            bottom: 0;
            left: 0;
            z-index: 3;
        }
        .producbox--btn {
            .swiper-button-next,
            .swiper-button-prev {
                margin-top: 0;
                top: calc(50% - 18px);
                border: 1px solid #AE8751;
                background-color: transparent;
                svg {
                    path {
                        fill: #AE8751;
                    }
                }
            }
        }
        .swiper {
            overflow: visible;
            &::after {
                width: 1px;
                height: calc(50% - 18px);
                background: #AE8751;
                content: '';
                display: block;
                position: absolute;
                top: 0;
                right: 0;
                z-index: 3;
            }
            &::before {
                width: 1px;
                height: calc(50% - 18px);
                background: #AE8751;
                content: '';
                display: block;
                position: absolute;
                bottom: 0;
                right: 0;
                z-index: 3;
            }
            .swiper-slide {
                &.swiper-slide-active {
                    .collections--items {
                        &::after {
                            top: auto;
                            left: 0;
                            bottom: 0;
                            background: linear-gradient(180deg, rgba(127, 18, 21, 0.00) 0%, #7F1215 100%);
                            height: 200px;
                        }
                        .collections--body {
                            display: block;
                        }
                    }
                }
            }
        }
    }
    @media screen and (max-width: 991px) {
        padding: 40px 10px;
    }
}

.collections--items {
    width: 100%;
    display: block;
    overflow: hidden;
    position: relative;
    padding-top: 62.765%;
    &>img {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        object-fit: cover;
        object-position: center;
        transition: all 0.3s;
    }
    &::after {
        display: block;
        content: '';
        width: 100%;
        height: 100%;
        z-index: 2;
        position: absolute;
        top: 0;
        left: 0;
        background: linear-gradient(0deg, rgba(119, 6, 17, 0.70) 0%, rgba(119, 6, 17, 0.70) 100%);
    }
    .collections--body {
        width: 100%;
        height: max-content;
        padding: 34px 20px;
        position: absolute;
        z-index: 5;
        bottom: 0;
        left: 0;
        display: block;
        text-align: center;
        display: none;
        transition: all 0.3s;
        .title {
            color: #EDD0A7;
            text-align: center;
            font-size: 30px;
            font-style: normal;
            font-weight: 600;
            line-height: 129%;
            text-transform: capitalize;
            display: block;
            width: 100%;
            margin-bottom: 7px;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
        }
        .btn-links {
            margin: 0 auto;
            color: #EDD0A7;
            border-bottom: 1px solid #EDD0A7;
            font-size: 15px;
            font-style: normal;
            line-height: 140%;
        }
    }
    @media screen and (max-width: 1600px) {
        .collections--body {
            .title {
                font-size: 26px;
            }
            .btn-links {}
        }
    }
    @media screen and (max-width: 1400px) {
        .collections--body {
            .title {
                font-size: 22px;
            }
            .btn-links {
                font-size: 14px;
            }
        }
    }
    @media screen and (max-width: 991px) {
        .collections--body {
            .title {
                font-size: 20px;
            }
        }
    }
    @media screen and (max-width: 767px) {
        .collections--body {
            .title {
                font-size: 18px;
            }
        }
    }
}

.block-virtue {
    width: 100%;
    padding: 40px 10px;
    position: relative;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    &::after {
        content: '';
        display: block;
        width: 50%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        background: transparent url('../images/bg-vtt1.jpg') no-repeat top center;
        background-size: cover;
    }
    &::before {
        content: '';
        display: block;
        width: 50%;
        height: 100%;
        position: absolute;
        top: 0;
        right: 0;
        background: transparent url('../images/bg-vtt2.jpg') no-repeat top center;
        background-size: cover;
    }
    .container {
        position: relative;
        z-index: 5;
        width: 100%;
        max-width: 100%;
    }
    .virtue--content {
        text-align: center;
        img {
            max-width: 100%;
        }
        .line {
            width: 100%;
            max-width: 374px;
            height: 1px;
            margin: 56px auto 46px;
            background-color: #AE8751;
        }
        .desc {
            width: 100%;
            max-width: 360px;
            margin: 0 auto;
            color: #EDD0A7;
            text-align: center;
            font-size: 20px;
            font-style: normal;
            font-weight: 600;
            line-height: 150%;
            text-transform: capitalize;
            p {
                &:last-child {
                    margin-bottom: 0;
                }
            }
        }
    }
    @media screen and (max-width: 1600px) {
        .virtue--content {
            .line {
                margin: 40px auto;
            }
            .desc {
                font-size: 18px;
            }
        }
    }
    @media screen and (max-width: 1200px) {
        .virtue--content {
            .desc {
                font-size: 18px;
            }
            img {
                width: 70%;
                max-width: 344px;
            }
        }
    }
    @media screen and (max-width: 991px) {
        height: auto;
        min-height: auto;
        padding: 40px 10px;
    }
    @media screen and (max-width: 767px) {
        .virtue--content {
            margin-bottom: 30px;
            .desc {
                font-size: 16px;
            }
        }
    }
}

.virtue--video {
    width: 100%;
    max-width: 678px;
    margin: 0 auto;
    .virtue--other {
        width: 100%;
        padding-top: 56.34%;
        overflow: hidden;
        position: relative;
        video,
        iframe {
            width: 100% !important;
            height: 100% !important;
            position: absolute;
            top: 0;
            left: 0;
        }
        .buttonclick-autoplay {
            width: 100%;
            display: flex;
            height: 100%;
            align-items: center;
            justify-content: center;
            background-position: center;
            background-repeat: no-repeat;
            position: absolute;
            top: 0;
            left: 0;
            cursor: pointer;
            span {
                color: #FFE9CA;
                font-size: 24px;
                line-height: 140%;
                border-bottom: 1px solid #FFE9CA;
            }
        }
    }
    @media screen and (max-width: 1600px) {
        .virtue--other {
            .buttonclick-autoplay {
                span {
                    font-size: 20px;
                }
            }
        }
    }
    @media screen and (max-width: 991px) {
        .virtue--other {
            .buttonclick-autoplay {
                span {
                    font-size: 18px;
                }
            }
        }
    }
}

.block-pricegold {
    width: 100%;
    padding: 40px 10px;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    &::after {
        content: '';
        display: block;
        width: 50%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        // background: linear-gradient(to bottom, #BA2025 0%, #A3191F 50%, #460D0F 100%);
        background: linear-gradient(180deg, #BA2025 0%, #460D0F 100%);
    }
    &::before {
        content: '';
        display: block;
        width: 50%;
        height: 100%;
        position: absolute;
        top: 0;
        right: 0;
        background: linear-gradient(180deg, #FFF 0%, #F9EDCD 100%);
    }
    .container {
        position: relative;
        z-index: 5;
        width: 100%;
        max-width: 100%;
    }
    .pricegold--list {
        width: 100%;
        max-width: 642px;
        margin: 0 auto;
        .swiper {
            margin-bottom: 50px;
            .swiper-slide {
                height: auto;
            }
        }
        .box-title {
            margin-bottom: 50px;
            .title {
                text-align: center;
                color: #EDD0A7;
            }
            .sub_title {
                text-align: center;
                color: #EDD0A7;
                @media screen and (min-width: 1600px) {
                    font-size: 33px;
                }
            }
        }
        .img {
            width: 100%;
            padding-top: 87.85%;
            overflow: hidden;
            position: relative;
            margin-bottom: 30px;
            img {
                width: 100%;
                height: 100%;
                object-fit: scale-down;
                object-position: center;
                position: absolute;
                top: 0;
                left: 0;
            }
        }
        .desc {
            color: #EDD0A7;
            text-align: center;
            font-size: 18px;
            font-style: normal;
            line-height: 150%;
        }
    }
    @media screen and (max-width: 991px) {
        &::after {
            width: 100%;
        }
        &::before {
            display: none;
        }
        .pricegold--list {
            margin-bottom: 30px;
        }
    }
}

.pricegold--right {
    width: 100%;
    padding: 20px 20px 20px 79px;
    .pricegold--other {
        width: 100%;
        max-width: 726px;
        .title {
            color: #7C0410;
            font-size: 33px;
            font-style: normal;
            font-weight: 600;
            line-height: 150%;
            margin-bottom: 42px;
            text-transform: capitalize;
        }
        .pricegold--form {
            width: 100%;
            display: flex;
            flex-wrap: wrap;
            margin-bottom: 20px;
            .pricegold--row {
                width: 100%;
                display: flex;
                flex-wrap: wrap;
                gap: 10px 30px;
                align-items: center;
                padding: 36px 0;
                border-bottom: 1px solid #D1B181;
                &:last-child {
                    border-bottom: 0;
                }
                .pricegold--col1 {
                    width: calc(15.5% - 22.5px);
                    img {
                        max-width: 100%;
                    }
                }
                .pricegold--col2 {
                    width: calc(29.5% - 22.5px);
                }
                .pricegold--col3 {
                    width: calc(27.5% - 22.5px);
                }
            }
            .pricegold--box {
                width: 100%;
                .name {
                    color: #4F4F4F;
                    font-size: 16px;
                    font-style: normal;
                    line-height: 150%;
                    margin-bottom: 10px;
                }
                .parameter {
                    color: #D1B181;
                    font-size: 16px;
                    font-style: normal;
                    line-height: 150%;
                    margin-bottom: 10px;
                }
                .price {
                    color: #4F4F4F;
                    font-size: 20px;
                    font-style: normal;
                    font-weight: 600;
                    line-height: 197.085%;
                }
            }
            .pricegold--quantity {
                .quantity--title {
                    color: #212121;
                    font-size: 16px;
                    font-style: normal;
                    line-height: 150%;
                    margin-bottom: 46px;
                }
            }
        }
        .pricegold--total {
            width: 100%;
            display: flex;
            flex-wrap: wrap;
            align-items: end;
            justify-content: space-between;
            gap: 20px 10px;
            .number {
                .txt {
                    color: #000;
                    font-size: 24px;
                    font-style: normal;
                    line-height: 150%;
                    text-transform: capitalize;
                }
                .total--price {
                    color: #7C0410;
                    font-size: 36px;
                    font-style: normal;
                    font-weight: 800;
                    line-height: 109.492%;
                }
            }
        }
    }
    @media screen and (max-width: 1600px) {
        .pricegold--other {
            .title {
                font-size: 26px;
                margin-bottom: 35px;
            }
            .pricegold--form {
                .pricegold--box {
                    .price {
                        font-size: 18px;
                    }
                }
            }
            .pricegold--total {
                .number {
                    .txt {
                        font-size: 22px;
                    }
                    .total--price {
                        font-size: 32px;
                    }
                }
            }
        }
    }
    @media screen and (max-width: 1400px) {
        padding: 20px 20px 20px 50px;
        .pricegold--other {
            .title {
                font-size: 22px;
            }
            .pricegold--form {
                .pricegold--row {
                    gap: 10px 20px;
                    .pricegold--col1 {
                        width: calc(15.5% - 15px);
                    }
                    .pricegold--col2 {
                        width: calc(29.5% - 15px);
                    }
                    .pricegold--col3 {
                        width: calc(27.5% - 15px);
                    }
                }
                .pricegold--box {
                    .price {
                        font-size: 18px;
                    }
                }
            }
            .pricegold--total {
                .number {
                    .txt {
                        font-size: 20px;
                    }
                    .total--price {
                        font-size: 26px;
                    }
                }
            }
        }
    }
    @media screen and (max-width: 991px) {
        padding: 30px 20px;
        background: linear-gradient(180deg, #FFF 0%, #F9EDCD 100%);
        .pricegold--other {
            max-width: 100%;
            .pricegold--form {}
            .pricegold--total {
                .number {
                    .txt {
                        font-size: 18px;
                    }
                    .total--price {
                        font-size: 22px;
                    }
                }
            }
        }
    }
    @media screen and (max-width: 767px) {
        .pricegold--other {
            .title {
                margin-bottom: 10px;
            }
            .pricegold--form {
                .pricegold--row {
                    flex-direction: column;
                    align-items: start;
                    .pricegold--col1 {
                        width: 100%;
                    }
                    .pricegold--col2 {
                        width: 100%;
                    }
                    .pricegold--col3 {
                        width: 100%;
                    }
                }
            }
            .pricegold--form {
                .pricegold--quantity {
                    .quantity--title {
                        margin-bottom: 10px;
                    }
                }
            }
        }
    }
}

.pricegold--input {
    border: 0.68px solid #D1B181;
    max-width: 128px;
    select {
        width: 100%;
        height: 41px;
        border: 0;
        padding: 0 26px 0 18px;
        color: #4F4F4F;
        font-size: 13.593px;
        font-style: normal;
        font-weight: 600;
        appearance: none;
        background-image: url('../images/ic-7.png');
        background-repeat: no-repeat;
        background-position: right 18px center;
        background-color: transparent;
    }
    .inp-sl {
        width: 100%;
        display: flex;
        .num {
            width: auto;
            flex: 1;
            width: 100%;
            height: 41px;
            border: 0;
            border-radius: 0;
            box-shadow: none;
            background: transparent;
            text-align: center;
        }
        span {
            width: 35px;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }
}