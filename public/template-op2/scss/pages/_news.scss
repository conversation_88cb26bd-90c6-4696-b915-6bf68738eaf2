.page-news {
    .collection-productintroduction {
        padding: 100px 10px 30px;
    }
    @media screen and (max-width: 991px) {
        .collection-productintroduction {
            padding: 40px 10px;
        }
    }
}

.news--list---box {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    margin-bottom: 80px;
    .gx-lg-4 {
        --bs-gutter-x: 70px;
        gap: 86px 0;
    }
    @media screen and (max-width: 1400px) {
        .gx-lg-4 {
            --bs-gutter-x: 20px;
            gap: 30px 0;
        }
    }
}

.newsdettail-main {
    padding: 80px 10px 60px;
    .newsdetail-title {
        margin-bottom: 52px;
        h1 {
            color: #4F4F4F;
            text-align: center;
            font-size: 36px;
            font-style: normal;
            font-weight: 600;
            line-height: 129%;
            text-transform: capitalize;
            margin: 0 0 70px;
        }
        .date--time {
            display: flex;
            justify-content: center;
            color: #D1B181;
            font-size: 16px;
            font-style: normal;
            font-weight: 600;
            line-height: 129%;
            text-transform: capitalize;
            gap: 10px 37px;
            .txt {}
        }
    }
    .newsdettail--content {
        width: 100%;
        max-width: 1084px;
        margin: 0 auto;
        color: #4F4F4F;
        font-size: 20px;
        font-style: normal;
        line-height: 180%;
        table {
            border: 1px solid #4F4F4F;
            width: 100%;
            tr {
                td {
                    padding: 5px;
                }
            }
        }
        .desc {
            width: 100%;
            max-width: 806px;
            margin: 0 auto;
        }
        .img {
            width: auto !important;
            max-width: 100% !important;
            height: auto !important;
            text-align: center;
            margin-bottom: 15px;
        }
    }
    @media screen and (max-width: 1600px) {
        .newsdetail-title {
            margin-bottom: 40px;
            h1 {
                font-size: 32px;
            }
        }
        .newsdettail--content {
            font-size: 18px;
        }
    }
    @media screen and (max-width: 1400px) {
        .newsdetail-title {
            h1 {
                font-size: 28px;
            }
        }
    }
    @media screen and (max-width: 1200px) {
        .newsdetail-title {
            h1 {
                font-size: 24px;
            }
        }
    }
    @media screen and (max-width: 991px) {
        padding: 40px 10px;
        .newsdetail-title {
            h1 {
                font-size: 20px;
            }
        }
        .newsdettail--content {
            font-size: 16px;
        }
    }
}