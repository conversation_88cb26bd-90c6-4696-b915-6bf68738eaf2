.block-product {
    width: 100%;
    padding: 116px 10px 96px;
    .box-title {
        .title {
            text-align: center;
            @media screen and (min-width: 1600px) {
                font-size: 36px;
            }
        }
    }
    .product--list {
        .gx-lg-4 {
            --bs-gutter-x: 0;
        }
    }
    @media screen and (max-width: 1600px) {
        padding: 80px 10px;
    }
    @media screen and (max-width: 991px) {
        padding: 40px 10px;
    }
}

.product--panigation {
    padding-top: 80px;
    .txt {
        color: #4F4F4F;
        text-align: center;
        font-size: 16px;
        font-style: normal;
        line-height: 140%;
        margin-bottom: 14px;
    }
    .box--btn {
        text-align: center;
        .btn-links {
            color: #4F4F4F;
            font-size: 20px;
            line-height: 140%;
            border-bottom: 1px solid #4F4F4F;
        }
    }
    @media screen and (max-width: 991px) {
        padding-top: 40px;
        .box--btn {
            .btn-links {
                font-size: 16px;
            }
        }
    }
}

.retrojewelry-block {
    padding: 20px 10px;
    width: 100%;
    .retrojewelry--list {
        width: 100%;
        max-width: 1354px;
        margin: 0 auto;
        .img {
            width: 100%;
            overflow: hidden;
            padding-top: 53.18%;
            position: relative;
            margin-bottom: 80px;
            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                object-position: center;
                position: absolute;
                top: 0;
                left: 0;
            }
        }
        .box-title {
            margin: 0 auto;
            max-width: 910px;
            .title {
                text-align: center;
                line-height: 129%;
                @media screen and (min-width: 1600px) {
                    font-size: 36px;
                }
            }
        }
    }
}