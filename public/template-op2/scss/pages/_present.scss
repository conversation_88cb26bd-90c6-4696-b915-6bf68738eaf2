.present-box {
    width: 100%;
    padding: 68px 10px 130px;
    .row {
        gap: 30px 0;
        @media screen and (min-width: 1600px) {
            margin: 0 -35px;
            .col-lg-4 {
                padding: 0 35px;
            }
        }
    }
    @media screen and (max-width: 991px) {
        padding: 40px 10px;
    }
}

.present--items {
    display: block;
    width: 100%;
    .img {
        display: block;
        padding-top: 100%;
        overflow: hidden;
        position: relative;
        margin-bottom: 40px;
        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
            position: absolute;
            top: 0;
            left: 0;
            transition: all 0.3s;
        }
    }
    .present--body {
        width: 100%;
        .title {
            color: #4F4F4F;
            font-size: 30px;
            font-style: normal;
            font-weight: 600;
            line-height: 129%;
            text-transform: capitalize;
            margin: 0 0 20px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            a {
                color: inherit;
            }
        }
        .desc {
            color: #4F4F4F;
            font-size: 20px;
            font-style: normal;
            line-height: 140%;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            max-width: 432px;
            p {
                &:last-child {
                    margin-bottom: 0;
                }
            }
        }
    }
    &.--style2 {
        .img {
            margin-bottom: 54px;
        }
        .present--body {
            .title {
                margin: 0 0 30px;
            }
        }
    }
    &:hover {
        .img {
            img {
                transform: scale(1.2);
            }
        }
        .present--body {
            .title {
                color: #7C0410;
            }
        }
    }
    @media screen and (max-width: 1600px) {
        .present--body {
            .title {
                font-size: 26px;
            }
            .desc {
                font-size: 16px;
            }
        }
    }
    @media screen and (max-width: 1400px) {
        .img {
            margin-bottom: 30px;
        }
        .present--body {
            .title {
                font-size: 22px;
            }
        }
    }
}