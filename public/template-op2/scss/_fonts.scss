@import "../fonts/awesome/scss/variables";
@import "../fonts/awesome/scss/mixins";
@import "../fonts/awesome/scss/path";
@import "../fonts/awesome/scss/core";
@import "../fonts/awesome/scss/larger";
@import "../fonts/awesome/scss/fixed-width";
@import "../fonts/awesome/scss/list";
@import "../fonts/awesome/scss/bordered-pulled";
@import "../fonts/awesome/scss/animated";
@import "../fonts/awesome/scss/rotated-flipped";
@import "../fonts/awesome/scss/stacked";
@import "../fonts/awesome/scss/icons";
@import "../fonts/awesome/scss/screen-reader";
@import url('https://fonts.googleapis.com/css2?family=Sarabun:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Nunito+Sans:ital,opsz,wght@0,6..12,200..1000;1,6..12,200..1000&display=swap');
@font-face {
    font-family: 'SVN-Artifex CF';
    font-weight: 400;
    font-style: normal;
    src: url('../fonts/Artifex/SVN-ArtifexCF-Regular.otf') format('opentype');
}

@font-face {
    font-family: 'SVN-Artifex CF';
    font-weight: 400;
    font-style: italic;
    src: url('../fonts/Artifex/SVN-ArtifexCF-RegularItalic.otf') format('opentype');
}

@font-face {
    font-family: 'SVN-Artifex CF';
    font-weight: 200;
    font-style: normal;
    src: url('../fonts/Artifex/SVN-ArtifexCF-Light.otf') format('opentype');
}

@font-face {
    font-family: 'SVN-Artifex CF';
    font-weight: 200;
    font-style: italic;
    src: url('../fonts/Artifex/SVN-ArtifexCF-ExtraLightItalic.otf') format('opentype');
}

@font-face {
    font-family: 'SVN-Artifex CF';
    font-weight: 300;
    font-style: normal;
    src: url('../fonts/Artifex/SVN-ArtifexCF-Light.otf') format('opentype');
}

@font-face {
    font-family: 'SVN-Artifex CF';
    font-weight: 300;
    font-style: italic;
    src: url('../fonts/Artifex/SVN-ArtifexCF-LightItalic.otf') format('opentype');
}

@font-face {
    font-family: 'SVN-Artifex CF';
    font-weight: 500;
    font-style: normal;
    src: url('../fonts/Artifex/SVN-ArtifexCF-Book.otf') format('opentype');
}

@font-face {
    font-family: 'SVN-Artifex CF';
    font-weight: 500;
    font-style: italic;
    src: url('../fonts/Artifex/SVN-ArtifexCF-BookItalic.otf') format('opentype');
}

@font-face {
    font-family: 'SVN-Artifex CF';
    font-weight: 600;
    font-style: normal;
    src: url('../fonts/Artifex/SVN-ArtifexCF-DemiBold.otf') format('opentype');
}

@font-face {
    font-family: 'SVN-Artifex CF';
    font-weight: 600;
    font-style: italic;
    src: url('../fonts/Artifex/SVN-ArtifexCF-DemiBoldItalic.otf') format('opentype');
}

@font-face {
    font-family: 'SVN-Artifex CF';
    font-weight: 700;
    font-style: normal;
    src: url('../fonts/Artifex/SVN-ArtifexCF-Bold.otf') format('opentype');
}

@font-face {
    font-family: 'SVN-Artifex CF';
    font-weight: 700;
    font-style: italic;
    src: url('../fonts/Artifex/SVN-ArtifexCF-BoldItalic.otf') format('opentype');
}

@font-face {
    font-family: 'SVN-Artifex CF';
    font-weight: 800;
    font-style: normal;
    src: url('../fonts/Artifex/SVN-ArtifexCF-ExtraBold.otf') format('opentype');
}

@font-face {
    font-family: 'SVN-Artifex CF';
    font-weight: 800;
    font-style: italic;
    src: url('../fonts/Artifex/SVN-ArtifexCF-BoldItalic.otf') format('opentype');
}

@font-face {
    font-family: 'SVN-Artifex CF';
    font-weight: 900;
    font-style: normal;
    src: url('../fonts/Artifex/SVN-ArtifexCF-Heavy.otf') format('opentype');
}

@font-face {
    font-family: 'SVN-Artifex CF';
    font-weight: 900;
    font-style: italic;
    src: url('../fonts/Artifex/SVN-ArtifexCF-HeavyItalic.otf') format('opentype');
}

// Pinyon Script
@font-face {
    font-family: "Pinyon Script";
    font-weight: 900;
    font-style: italic;
    src: url('../fonts/Pinyon_Script/PinyonScript-Regular.ttf');
}