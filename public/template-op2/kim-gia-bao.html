<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title><PERSON><PERSON><PERSON> thieu</title>
    <!-- mmenu css -->
    <link rel="stylesheet" href="libs/mmenu/jquery.mmenu.all.css">
    <!-- animate css-->
    <link rel="stylesheet" href="libs/animate/animate.min.css">
    <link rel="stylesheet" href="libs/aos/aos.css">
    <!-- swiper css-->
    <link rel="stylesheet" href="libs/swiper/swiper-bundle.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Barlow:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/style.css">
</head>

<body>
    <div id="app">
        <myheader></myheader>
        <main class="">
            <div class="bannerslide-main">
                <div class="swiper bannerslide--slide">
                    <div class="swiper-wrapper">
                        <div class="swiper-slide" v-for="n in 2">
                            <div class="bannerslide--items" style="background-image: url('images/img-kgb.jpg');">
                                <div class="container">
                                    <div class="row">
                                        <div class="col-lg-12">
                                            <div class="bannerslide--other">
                                                <div class="box-title">
                                                    <h2 class="title">
                                                        Kim gia bảo
                                                    </h2>
                                                    <div class="sub_title">
                                                        Vàng tích trữ
                                                    </div>
                                                    <div class="desc">
                                                        Với hàm lượng chuẩn, trọng lượng đa dạng và mẫu mã tinh gọn, mỗi sản phẩm là sự lựa chọn đáng tin cậy cho những ai muốn bảo toàn giá trị và truyền lại gia sản cho thế hệ mai sau.
                                                    </div>
                                                </div>
                                                <div class="box--btn">
                                                    <a href="" class="btn-links">
                                                Xem toàn bộ sản phẩm
                                            </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-slide">
                            <div class="bannerslide--items" style="background-image: url('images/img-kgb.jpg');">
                                <div class="container">
                                    <div class="row">
                                        <div class="col-lg-12">
                                            <div class="bannerslide--other">
                                                <div class="box-title">
                                                    <h2 class="title">
                                                        Kim gia bảo
                                                    </h2>
                                                    <div class="sub_title">
                                                        Vàng tích trữ
                                                    </div>
                                                    <div class="desc">
                                                        Với hàm lượng chuẩn, trọng lượng đa dạng và mẫu mã tinh gọn, mỗi sản phẩm l
                                                    </div>
                                                </div>
                                                <div class="box--btn">
                                                    <a href="" class="btn-links">
                                                         Xem toàn bộ sản phẩm
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="producbox--panigation">
                        <div class="swiper-pagination swiper-pagination-bannerslide"></div>
                    </div>
                </div>
            </div>
            <div class="bannerslide-main bannerslide-boxstyle2">
                <div class="swiper bannerslide--slide2">
                    <div class="swiper-wrapper">
                        <div class="swiper-slide" v-for="n in 2">
                            <div class="bannerslide--items" style="background-image: url('images/img-kgb2.jpg');">
                                <div class="container">
                                    <div class="row">
                                        <div class="col-lg-12  justify-content-end d-flex">
                                            <div class="bannerslide--other">
                                                <div class="box-title">
                                                    <h2 class="title">
                                                        Kim gia bảo
                                                    </h2>
                                                    <div class="sub_title">
                                                        Vàng tích trữ
                                                    </div>
                                                    <div class="desc">
                                                        Với hàm lượng chuẩn, trọng lượng đa dạng và mẫu mã tinh gọn, mỗi sản phẩm là sự lựa chọn đáng tin cậy cho những ai muốn bảo toàn giá trị và truyền lại gia sản cho thế hệ mai sau.
                                                    </div>
                                                </div>
                                                <div class="box--btn">
                                                    <a href="" class="btn-links">
                                                Xem toàn bộ sản phẩm
                                            </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-slide">
                            <div class="bannerslide--items" style="background-image: url('images/img-kgb2.jpg');">
                                <div class="container">
                                    <div class="row">
                                        <div class="col-lg-12   justify-content-end d-flex">
                                            <div class="bannerslide--other">
                                                <div class="box-title">
                                                    <h2 class="title">
                                                        Kim gia bảo
                                                    </h2>
                                                    <div class="sub_title">
                                                        Vàng tích trữ
                                                    </div>
                                                    <div class="desc">
                                                        Với hàm lượng chuẩn, trọng lượng đa dạng và mẫu mã tinh gọn, mỗi sản phẩm l
                                                    </div>
                                                </div>
                                                <div class="box--btn">
                                                    <a href="" class="btn-links">
                                                         Xem toàn bộ sản phẩm
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="producbox--panigation">
                        <div class="swiper-pagination swiper-pagination-bannerslide2"></div>
                    </div>
                </div>
            </div>
            <div class="introchild-collections">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="box-title">
                                <h2 class="title">
                                    Các Bộ sưu tập
                                </h2>
                                <div class="desc">
                                    Chế tác tuyệt phẩm từ Bảo Tín Mạnh hải
                                </div>
                            </div>
                            <div class="collections--list">
                                <div class="swiper collections--slide">
                                    <div class="swiper-wrapper">
                                        <div class="swiper-slide" v-for="n in 4">
                                            <a href="" class="collections--items">
                                                <img src="images/img-colec1.jpg" alt="">
                                                <span class="collections--body">
                                                    <span class="title">
Bộ sưu tập: Lá ngọc Cành Vàng
                                                    </span>
                                                <span class="btn-links">
Xem bộ sưu tập
                                                    </span>
                                                </span>
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <div class="producbox--btn">
                                    <div class="swiper-button-next swiper-button-next-collections">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="10" height="16" viewBox="0 0 10 16" fill="none">
                                                        <path
                                                            fill-rule="evenodd"
                                                            clip-rule="evenodd"
                                                            d="M1.92383 15.209L0.675545 13.9607L6.91696 7.71929L0.675546 1.47787L1.92383 0.229586L8.78933 7.09509C8.78937 7.09513 8.78939 7.09514 8.16524 7.71929L8.78933 7.09509L9.41353 7.71929L1.92383 15.209Z"
                                                            fill="#4F4F4F"
                                                        />
                                                    </svg>
                                    </div>
                                    <div class="swiper-button-prev swiper-button-prev-collections">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="10" height="16" viewBox="0 0 10 16" fill="none">
                                                        <path
                                                            fill-rule="evenodd"
                                                            clip-rule="evenodd"
                                                            d="M8.07617 15.209L9.32445 13.9607L3.08304 7.71929L9.32445 1.47787L8.07617 0.229586L1.21067 7.09509C1.21063 7.09513 1.21061 7.09514 1.83476 7.71929L1.21067 7.09509L0.586473 7.71929L8.07617 15.209Z"
                                                            fill="#4F4F4F"
                                                        />
                                                    </svg>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <myfooter></myfooter>
        </main>
    </div>
    <script src="js/vue.js"></script>
    <script src="js/httpvueloader.js"></script>
    <script src="js/jquery-3.4.1.js"></script>
    <script src="libs/bootstrap/bootstrap.bundle.min.js"></script>
    <script src="libs/lazyload/jquery.lazy.min.js"></script>
    <!-- swiper js-->
    <script src="libs/swiper/swiper-bundle.min.js"></script>
    <script src="libs/mansonry/masonry.pkgd.min.js"></script>
    <!-- mmenu -->
    <script src="libs/mmenu/jquery.mmenu.all.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script type="text/javascript" src="libs/aos/aos.js"></script>
    <script src="js/main.js"></script>
    <script></script>
    <script>
        var app = new Vue({
            el: "#app",
            components: {
                myheader: httpVueLoader("vue_components/header.vue"),
                myfooter: httpVueLoader("vue_components/footer.vue"),
                partner: httpVueLoader("vue_components/partner.vue"),
                locationair: httpVueLoader("vue_components/location-air.vue"),
            },
            data: {

            },

            mounted() {

            },
        });
    </script>
</body>

</html>