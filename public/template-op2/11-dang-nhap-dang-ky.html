<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>home</title>
    <!-- mmenu css -->
    <link rel="stylesheet" href="libs/mmenu/jquery.mmenu.all.css">
    <!-- animate css-->
    <link rel="stylesheet" href="libs/animate/animate.min.css">
    <link rel="stylesheet" href="libs/aos/aos.css">
    <!-- swiper css-->
    <link rel="stylesheet" href="libs/swiper/swiper-bundle.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Barlow:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/style.css">
</head>

<body>
    <div id="app">
        <myheader></myheader>
        <main>
            <div class="account-main">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="account-content">
                                <ul class="nav nav-pills" id="pills-tab" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link active" id="pills-home-tab" data-bs-toggle="pill" data-bs-target="#pills-home" type="button" role="tab" aria-controls="pills-home" aria-selected="true">
                                                Đăng nhập
                                            </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="pills-profile-tab" data-bs-toggle="pill" data-bs-target="#pills-profile" type="button" role="tab" aria-controls="pills-profile" aria-selected="false">
                                                Đăng ký
                                            </button>
                                    </li>
                                </ul>
                                <div class="tab-content" id="pills-tabContent">
                                    <div class="tab-pane fade show active" id="pills-home" role="tabpanel" aria-labelledby="pills-home-tab">
                                        <div class="account-form">
                                            <div class="--note">
                                                Đăng nhập vào tài khoản Bảo Tín Mạnh Hải.
                                            </div>
                                            <form action="">
                                                <div class="form--content">
                                                    <div class="form--col12">
                                                        <div class="form--row">
                                                            <input type="text" class="input--control" placeholder="Số điện thoại">
                                                        </div>
                                                    </div>
                                                    <div class="form--col12">
                                                        <div class="form--row">
                                                            <input type="password" class="input--control" placeholder="Mật khẩu">
                                                        </div>
                                                    </div>
                                                    <div class="form--col12">
                                                        <div class="form--row">
                                                            <div class="txt">
                                                                Quên mật khẩu? Chọn hỗ trợ tại đây.
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="form--btn">
                                                    <button type="submit" class="storesystem--btn">
                                                            Đăng nhập
                                                        </button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                    <div class="tab-pane fade" id="pills-profile" role="tabpanel" aria-labelledby="pills-profile-tab">
                                        <div class="account-form account-form--register">
                                            <form action="">
                                                <div class="form--content">
                                                    <div class="form--col12">
                                                        <div class="form--check">
                                                            <div class="form--check--title">
                                                                Danh xưng
                                                            </div>
                                                            <div class="form--name">
                                                                <div class="form--name--items">
                                                                    <input id="radio1" type="radio" hidden name="1">
                                                                    <label for="radio1">
                                                                        <span></span>
                                                                        Ông
                                                                    </label>
                                                                </div>
                                                                <div class="form--name--items">
                                                                    <input id="radio2" type="radio" hidden name="1">
                                                                    <label for="radio2">
                                                                        <span></span>
                                                                        Bà
                                                                    </label>
                                                                </div>
                                                                <div class="form--name--items">
                                                                    <input id="radio3" type="radio" hidden name="1">
                                                                    <label for="radio3">
                                                                        <span></span>
                                                                        Cô
                                                                    </label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="form--col12">
                                                        <div class="form--row">
                                                            <input type="text" class="input--control" placeholder="Họ và Tên">
                                                        </div>
                                                    </div>
                                                    <div class="form--col12">
                                                        <div class="form--row">
                                                            <input type="text" class="input--control date-picker" placeholder="Ngày sinh">
                                                        </div>
                                                    </div>
                                                    <div class="form--col12">
                                                        <div class="box--verify">
                                                            <div class="verify-col">
                                                                <div class="form--row">
                                                                    <input type="text" class="input--control" placeholder="Số điện thoại">
                                                                </div>
                                                            </div>
                                                            <div class="verify-col">
                                                                <div class="form--row">
                                                                    <input type="text" class="input--control date-picker" placeholder="Nhập mã xác minh">
                                                                </div>
                                                            </div>
                                                            <div class="form--btn">
                                                                <button type="submit" class="storesystem--btn">
                                                            Xác minh
                                                        </button>
                                                            </div>
                                                        </div>
                                                    </div>


                                                    <div class="form--col12">
                                                        <div class="form--row">
                                                            <input type="text" class="input--control" placeholder="Email">
                                                        </div>
                                                    </div>
                                                    <div class="form--col12">
                                                        <div class="form--row">
                                                            <input type="text" class="input--control" placeholder="Mã giới thiệu">
                                                        </div>
                                                        <div class="check--agree">
                                                            <input id="checkbox-agree" type="checkbox" hidden>
                                                            <label for="checkbox-agree">
                                                                <span></span>
                                                                <div class="txt">
                                                                    Tôi đồng ý tham gia Chương trình thành viên của Bảo Tín Mạnh Hải.
"Điều khoản và điều kiện thành viên Mh Club" và "Chính sách bảo mật".
                                                                </div>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="form--btn">
                                                    <button type="submit" class="storesystem--btn">
                                                            Đăng ký
                                                        </button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
        <myfooter></myfooter>
    </div>
    <script src="js/vue.js"></script>
    <script src="js/httpvueloader.js"></script>
    <script src="js/jquery-3.4.1.js"></script>
    <script src="libs/bootstrap/bootstrap.bundle.min.js"></script>
    <script src="libs/lazyload/jquery.lazy.min.js"></script>
    <!-- swiper js-->
    <script src="libs/swiper/swiper-bundle.min.js"></script>
    <script src="libs/mansonry/masonry.pkgd.min.js"></script>
    <!-- mmenu -->
    <script src="libs/mmenu/jquery.mmenu.all.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script type="text/javascript" src="libs/aos/aos.js"></script>
    <script src="js/main.js"></script>
    <script></script>
    <script>
        var app = new Vue({
            el: "#app",
            components: {
                myheader: httpVueLoader("vue_components/header.vue"),
                myfooter: httpVueLoader("vue_components/footer.vue"),
                partner: httpVueLoader("vue_components/partner.vue"),
                locationair: httpVueLoader("vue_components/location-air.vue"),
            },
            data: {

            },

            mounted() {

            },
        });
    </script>
</body>

</html>