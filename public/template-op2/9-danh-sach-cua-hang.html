<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>home</title>
    <!-- mmenu css -->
    <link rel="stylesheet" href="libs/mmenu/jquery.mmenu.all.css">
    <!-- animate css-->
    <link rel="stylesheet" href="libs/animate/animate.min.css">
    <link rel="stylesheet" href="libs/aos/aos.css">
    <!-- swiper css-->
    <link rel="stylesheet" href="libs/swiper/swiper-bundle.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Barlow:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/style.css">
</head>

<body>
    <div id="app">
        <myheader></myheader>
        <main>
            <div class="store-top">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="box-title">
                                <h2 class="title">
                                    Danh sách cửa hàng
                                </h2>
                            </div>
                            <div class="store--fillter">
                                <div class="store--fillter--col">
                                    <select name="" id="">
                                        <option value="">
                                            Chọn Tỉnh / Thành Phố
                                        </option>
                                    </select>
                                </div>
                                <div class="store--fillter--col">
                                    <select name="" id="">
                                        <option value="">
                                            Chọn Quận / Huyện
                                        </option>
                                    </select>
                                </div>
                                <div class="store--fillter--col">
                                    <select name="" id="">
                                        <option value="">
                                            Chọn theo khu vực
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="store-box">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="store-list">
                                <div class="row gx-lg-4 row-cols-md-3 row-cols-sm-2 row-cols-1">
                                    <div class="col" v-for="n in 9">
                                        <div class="store--items">
                                            <a href="" class="img">
                                                <img src="images/img-1.jpg" alt="">
                                            </a>
                                            <div class="store--items---body">
                                                <div class="store--items--base">
                                                    Cơ sở Nguyễn Trãi
                                                </div>
                                                <div class="store--items--address">
                                                    Địa chỉ 39 Nguyễn Trãi, Thanh Xuân - Hà Nội Điện thoại 024 2233 9999
                                                </div>
                                                <div class="box--btn">
                                                    <a href="" class="btn-links">
                                        Xem trên bản đồ
                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="block-form">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="form--list">
                                <div class="_left">
                                    <img src="images/img-form.jpg" alt="">
                                </div>
                                <div class="_right">
                                    <div class="box-title">
                                        <h2 class="title">
                                            Đặt lịch đến cửa hàng
                                        </h2>
                                    </div>
                                    <form action="">
                                        <div class="form--content">
                                            <div class="form--col12">
                                                <div class="form--row">
                                                    <input type="text" class="input--control" placeholder="Họ và Tên">
                                                </div>
                                            </div>
                                            <div class="form--col">
                                                <div class="form--row">
                                                    <input type="text" class="input--control" placeholder="Số điện thoại">
                                                </div>
                                            </div>
                                            <div class="form--col">
                                                <div class="form--row">
                                                    <input type="text" class="input--control" placeholder="Email">
                                                </div>
                                            </div>
                                            <div class="form--col12">
                                                <div class="form--row">
                                                    <select name="" id="" class="input--control">
                                                        <option value="">Chọn cửa hàng</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="form--col12">
                                                <div class="form--row">
                                                    <select name="" id="" class="input--control">
                                                        <option value="">Chọn dịch vụ</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="form--col12">
                                                <div class="form--row">
                                                    <textarea name="" id="" class="input--control" placeholder="Lời nhắn"></textarea>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form--btn">
                                            <button class="storesystem--btn" type="submit">
Đặt lịch hẹn
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
        <myfooter></myfooter>
    </div>
    <script src="js/vue.js"></script>
    <script src="js/httpvueloader.js"></script>
    <script src="js/jquery-3.4.1.js"></script>
    <script src="libs/bootstrap/bootstrap.bundle.min.js"></script>
    <script src="libs/lazyload/jquery.lazy.min.js"></script>
    <!-- swiper js-->
    <script src="libs/swiper/swiper-bundle.min.js"></script>
    <script src="libs/mansonry/masonry.pkgd.min.js"></script>
    <!-- mmenu -->
    <script src="libs/mmenu/jquery.mmenu.all.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script type="text/javascript" src="libs/aos/aos.js"></script>
    <script src="js/main.js"></script>
    <script></script>
    <script>
        var app = new Vue({
            el: "#app",
            components: {
                myheader: httpVueLoader("vue_components/header.vue"),
                myfooter: httpVueLoader("vue_components/footer.vue"),
                partner: httpVueLoader("vue_components/partner.vue"),
                locationair: httpVueLoader("vue_components/location-air.vue"),
            },
            data: {

            },

            mounted() {

            },
        });
    </script>
</body>

</html>