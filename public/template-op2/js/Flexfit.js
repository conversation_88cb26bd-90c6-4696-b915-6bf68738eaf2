(function (cjs, an) {

var p; // shortcut to reference prototypes
var lib={};var ss={};var img={};
lib.ssMetadata = [];


// symbols:



(lib.Bitmap1 = function() {
	this.initialize(img.Bitmap1);
}).prototype = p = new cjs.Bitmap();
p.nominalBounds = new cjs.Rectangle(0,0,465,885);


(lib.Bitmap1copy = function() {
	this.initialize(img.Bitmap1copy);
}).prototype = p = new cjs.Bitmap();
p.nominalBounds = new cjs.Rectangle(0,0,136,136);


(lib.Bitmap11 = function() {
	this.initialize(img.Bitmap11);
}).prototype = p = new cjs.Bitmap();
p.nominalBounds = new cjs.Rectangle(0,0,646,119);


(lib.Bitmap2copy = function() {
	this.initialize(img.Bitmap2copy);
}).prototype = p = new cjs.Bitmap();
p.nominalBounds = new cjs.Rectangle(0,0,93,93);


(lib.Bitmap3copy = function() {
	this.initialize(img.Bitmap3copy);
}).prototype = p = new cjs.Bitmap();
p.nominalBounds = new cjs.Rectangle(0,0,174,174);


(lib.Bitmap5copy = function() {
	this.initialize(img.Bitmap5copy);
}).prototype = p = new cjs.Bitmap();
p.nominalBounds = new cjs.Rectangle(0,0,147,147);// helper functions:

function mc_symbol_clone() {
	var clone = this._cloneProps(new this.constructor(this.mode, this.startPosition, this.loop));
	clone.gotoAndStop(this.currentFrame);
	clone.paused = this.paused;
	clone.framerate = this.framerate;
	return clone;
}

function getMCSymbolPrototype(symbol, nominalBounds, frameBounds) {
	var prototype = cjs.extend(symbol, cjs.MovieClip);
	prototype.clone = mc_symbol_clone;
	prototype.nominalBounds = nominalBounds;
	prototype.frameBounds = frameBounds;
	return prototype;
	}


(lib.Tween18 = function(mode,startPosition,loop) {
	this.initialize(mode,startPosition,loop,{});

	// Layer_1
	this.shape = new cjs.Shape();
	this.shape.graphics.f("#E1E1E1").s().p("AiiJSQh3h8AAitIAAjrQAAioB1h5QB0h4ClgEQCqgDB7B+QB5B8AACuIAACiIo/AAIAABDQAABIAvA0QAwA0BFACQAzACArgcQAqgaAVguQAOgcAbgRQAagRAgAAICbAAIgBAKQgICkh1ByQh2BxijAAQinAAh3h8gAALhBQgvAsgHBBIFQAAQgHhBgwgsQgwgthBAAQhCAAgwAtgATuKpQgnAAgigTQgigTgUgiIg5hgQgZgwAHg/QAGg/Ahg4IAAAAIABgBIBgijQgNAaAAAeQAAAhARAdIEIG8gAJAKpIEIm8QARgdAAghQAAghgRgdIkIm7IDLAAQAnAAAhATQAiATAUAiIA3BdQAcAxgHBCQgHA9ghA5IAAAAIAAAAIltJlgArcKpIAA12IEFAAIAAUIQAAAuggAgQggAggtAAgA0uKpIAAsIIiJAAIAAjrICJAAIAAhAQAAiGBdhfQBdheCDAAIDGAAIAADqIidAAQgoAAgcAdQgdAeAAApIAAA1IDHAAIAADrIjHAAIAAKaQAAAuggAgQgfAgguAAgARvkCQAKgRAOgNQgOAOgKAQIgLASg");
	this.shape.setTransform(-89.025,0.875);

	this.shape_1 = new cjs.Shape();
	this.shape_1.graphics.f("#2DA0D0").s().p("A02LFIABgBIFsplIAAgBIABAAQAgg4AHg9QAHhCgcgyIA9BmIBniwIAMgSQAJgRAOgNIAegXQAigTAngBIDKAAIkLHCIhhCiIAAABIgBAAQghA5gGA+QgHBAAZAwIACAEIg7hmIh0DBIgBADIAAABIgCACIAAAAIgdAhQgKAIgLAHQgjATgmABgAOJLEIAAsJIinAAIAAjrICnAAIAAkXIEFAAIAAEXICpAAIAADrIipAAIAAKaQAAAugfAhQghAggtAAgAFPLEIAAsJIlOAAIAAKaQAAAvggAgQgfAggtAAIiYAAIAAsJIiKAAIAAjrICKAAIAAg/QgBiGBdheQBehfCCgBIDFAAIAADrIicAAQgoAAgdAdQgdAdABApIAAA1IJTAAIAAOFQAAAuggAhQggAggtAAgAFzndQgmgoAAg4QAAg4AmgnQAngoA3AAQA4AAAnAoQAmAnAAA4QAAA4gnAoQgmAng4AAQg3AAgngng");
	this.shape_1.setTransform(102.05,-1.8);

	this.timeline.addTween(cjs.Tween.get({}).to({state:[{t:this.shape_1},{t:this.shape}]}).wait(1));

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-235.4,-72.6,470.9,145.3);


(lib.Tween17 = function(mode,startPosition,loop) {
	this.initialize(mode,startPosition,loop,{});

	// Layer_1
	this.shape = new cjs.Shape();
	this.shape.graphics.f("#E1E1E1").s().p("AiiJSQh3h8AAitIAAjrQAAioB1h5QB0h4ClgEQCqgDB7B+QB5B8AACuIAACiIo/AAIAABDQAABIAvA0QAwA0BFACQAzACArgcQAqgaAVguQAOgcAbgRQAagRAgAAICbAAIgBAKQgICkh1ByQh2BxijAAQinAAh3h8gAALhBQgvAsgHBBIFQAAQgHhBgwgsQgwgthBAAQhCAAgwAtgATuKpQgnAAgigTQgigTgUgiIg5hgQgZgwAHg/QAGg/Ahg4IAAAAIABgBIBgijQgNAaAAAeQAAAhARAdIEIG8gAJAKpIEIm8QARgdAAghQAAghgRgdIkIm7IDLAAQAnAAAhATQAiATAUAiIA3BdQAcAxgHBCQgHA9ghA5IAAAAIAAAAIltJlgArcKpIAA12IEFAAIAAUIQAAAuggAgQggAggtAAgA0uKpIAAsIIiJAAIAAjrICJAAIAAhAQAAiGBdhfQBdheCDAAIDGAAIAADqIidAAQgoAAgcAdQgdAeAAApIAAA1IDHAAIAADrIjHAAIAAKaQAAAuggAgQgfAgguAAgARvkCQAKgRAOgNQgOAOgKAQIgLASg");
	this.shape.setTransform(-89.025,0.875);

	this.shape_1 = new cjs.Shape();
	this.shape_1.graphics.f("#2DA0D0").s().p("A02LFIABgBIFsplIAAgBIABAAQAgg4AHg9QAHhCgcgyIA9BmIBniwIAMgSQAJgRAOgNIAegXQAigTAngBIDKAAIkLHCIhhCiIAAABIgBAAQghA5gGA+QgHBAAZAwIACAEIg7hmIh0DBIgBADIAAABIgCACIAAAAIgdAhQgKAIgLAHQgjATgmABgAOJLEIAAsJIinAAIAAjrICnAAIAAkXIEFAAIAAEXICpAAIAADrIipAAIAAKaQAAAugfAhQghAggtAAgAFPLEIAAsJIlOAAIAAKaQAAAvggAgQgfAggtAAIiYAAIAAsJIiKAAIAAjrICKAAIAAg/QgBiGBdheQBehfCCgBIDFAAIAADrIicAAQgoAAgdAdQgdAdABApIAAA1IJTAAIAAOFQAAAuggAhQggAggtAAgAFzndQgmgoAAg4QAAg4AmgnQAngoA3AAQA4AAAnAoQAmAnAAA4QAAA4gnAoQgmAng4AAQg3AAgngng");
	this.shape_1.setTransform(102.05,-1.8);

	this.timeline.addTween(cjs.Tween.get({}).to({state:[{t:this.shape_1},{t:this.shape}]}).wait(1));

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-235.4,-72.6,470.9,145.3);


(lib.Tween16 = function(mode,startPosition,loop) {
	this.initialize(mode,startPosition,loop,{});

	// Layer_1
	this.shape = new cjs.Shape();
	this.shape.graphics.f("#2DA0D0").s().p("AiBKGIAAsIIioAAIAAjrICoAAIAAkYIEEAAIAAEYICnAAIAADqIinAAIAAKaQgBAuggAhQgfAgguAAgAkWiWICpAAIAAMIICCAAQAmAAAZgbQAagaAAgmIAAquICoAAIAAjDIioAAIAAkXIjbAAIAAEXIipAAg");

	this.timeline.addTween(cjs.Tween.get(this.shape).wait(1));

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-29.8,-64.6,59.7,129.2);


(lib.Tween15 = function(mode,startPosition,loop) {
	this.initialize(mode,startPosition,loop,{});

	// Layer_1
	this.shape = new cjs.Shape();
	this.shape.graphics.f("#2DA0D0").s().p("AiBKGIAAsIIioAAIAAjrICoAAIAAkYIEEAAIAAEYICnAAIAADqIinAAIAAKaQgBAuggAhQgfAgguAAgAkWiWICpAAIAAMIICCAAQAmAAAZgbQAagaAAgmIAAquICoAAIAAjDIioAAIAAkXIjbAAIAAEXIipAAg");

	this.timeline.addTween(cjs.Tween.get(this.shape).wait(1));

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-29.8,-64.6,59.7,129.2);


(lib.Tween14 = function(mode,startPosition,loop) {
	this.initialize(mode,startPosition,loop,{});

	// Layer_1
	this.shape = new cjs.Shape();
	this.shape.graphics.f("#2DA0D0").s().p("ADrLEIAAsIIlOAAIAAKaQAAAugfAgQggAgguAAIiYAAIAAsIIiJAAIAAjrICJAAIAAhAQABiGBdhfQAUgUAVgQQBQg6BnAAIDEAAIAADqIicAAQgoAAgcAdQgcAegBApIAAA1IJTAAIAAOFQAAAtggAhQggAggtAAgAD/hYIAAMIICEAAQAlAAAagaQAagbAAglIAAtxIpTAAIAAhJQAAgyAjgjQAigjAwAAICIAAIAAjCIiwAAQhoAAhPA/QgNAMgOANQhYBZAAB+IAABUIiJAAIAADDICJAAIAAMIICEAAQAmAAAZgaQAagaAAgmIAAqugAFOm5QgegHgZgWIgIgHQgmgoAAg4QAAg3AmgoIAIgHQAZgWAegHQAQgDAQAAQA2AAAnAnQAnAoAAA3QAAA4gnAoQgnAng2AAQgQAAgQgDgAFOqrQgbAIgVAVIgHAHQgaAgAAAqQAAArAaAgIAHAHQAVAVAbAIQAPAEARAAQAuAAAhghQAhgiAAgwQAAgvghgiQghghguAAQgRAAgPAEg");
	this.shape.setTransform(0,-0.025);

	this.timeline.addTween(cjs.Tween.get(this.shape).wait(1));

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-49.8,-70.8,99.6,141.6);


(lib.Tween12 = function(mode,startPosition,loop) {
	this.initialize(mode,startPosition,loop,{});

	// Layer_1
	this.shape = new cjs.Shape();
	this.shape.graphics.f("#2DA0D0").s().p("Am7H6IAAAAIEtn6IBAhqIAAAAQAeg1AAg9QAAg7gcgyIgCgFIARgKIA4BfIAFAKIBzjCQAKgQANgOIAegXQAXgNAYgEQANgDANAAIDLAAIkMHDIgiA4Ig/BqIAAABIgBAAQgeA0AAA9QAAA4AaAwIACAEIADAGIgRAKIg+hoIhyDBIAAAAQgUAigiATQgiAUgmgBgAg+hfIAAAAIhjCnIjvGRIgDAHIgFAHICoAAQARgBAPgEIAegMQAdgRASgdIB+jUIAFgLIAeAzIAAAAQgCgQAAgPQAAhDAig5IAAgBIFapFIinAAQgNAAgNACQgTAEgSALQgdAQgRAeIiEDdIgdgxQACAPAAAPQAABDgiA5IAAABIgFgDg");

	this.shape_1 = new cjs.Shape();
	this.shape_1.graphics.f("#FFFFFF").s().p("ADxH6QgNAAgNgCQgZgFgWgMQghgTgVgiIg0hZIARgKIgDgFIgCgFIA6BjQARAdAdARQASAKATAEQANACANAAICnAAIj1meQgVghAAgmIAig4QgNAaAAAdQgBAhASAdIEIG8gAm7H6IEIm8QARgdAAghQAAgggRgdIkIm8IDLAAQAnAAAhATQAiATAUAiIA1BZIgRAKIg1hYIAAAAQgSgdgdgRQgdgRghAAIioAAID3GeQATAhAAAmIktH6gAmYHnIAFgHIgFAGICoAAQARAAAPgEQgPAFgRAAgAAADKIgFAKIAFgLIAeAzIAAAAgAgFj5IAFAKIByjCQALgRANgNQgNAOgKAQIhzDCg");
	this.shape_1.setTransform(0,-0.025);

	this.timeline.addTween(cjs.Tween.get({}).to({state:[{t:this.shape_1},{t:this.shape}]}).wait(1));

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-44.4,-50.6,88.8,101.30000000000001);


(lib.Tween11 = function(mode,startPosition,loop) {
	this.initialize(mode,startPosition,loop,{});

	// Layer_1
	this.shape = new cjs.Shape();
	this.shape.graphics.f("#2DA0D0").s().p("Am7H6IAAAAIEtn6IBAhqIAAAAQAeg1AAg9QAAg7gcgyIgCgFIARgKIA4BfIAFAKIBzjCQAKgQANgOIAegXQAXgNAYgEQANgDANAAIDLAAIkMHDIgiA4Ig/BqIAAABIgBAAQgeA0AAA9QAAA4AaAwIACAEIADAGIgRAKIg+hoIhyDBIAAAAQgUAigiATQgiAUgmgBgAg+hfIAAAAIhjCnIjvGRIgDAHIgFAHICoAAQARgBAPgEIAegMQAdgRASgdIB+jUIAFgLIAeAzIAAAAQgCgQAAgPQAAhDAig5IAAgBIFapFIinAAQgNAAgNACQgTAEgSALQgdAQgRAeIiEDdIgdgxQACAPAAAPQAABDgiA5IAAABIgFgDg");

	this.shape_1 = new cjs.Shape();
	this.shape_1.graphics.f("#FFFFFF").s().p("ADxH6QgNAAgNgCQgZgFgWgMQghgTgVgiIg0hZIARgKIgDgFIgCgFIA6BjQARAdAdARQASAKATAEQANACANAAICnAAIj1meQgVghAAgmIAig4QgNAaAAAdQgBAhASAdIEIG8gAm7H6IEIm8QARgdAAghQAAgggRgdIkIm8IDLAAQAnAAAhATQAiATAUAiIA1BZIgRAKIg1hYIAAAAQgSgdgdgRQgdgRghAAIioAAID3GeQATAhAAAmIktH6gAmYHnIAFgHIgFAGICoAAQARAAAPgEQgPAFgRAAgAAADKIgFAKIAFgLIAeAzIAAAAgAgFj5IAFAKIByjCQALgRANgNQgNAOgKAQIhzDCg");
	this.shape_1.setTransform(0,-0.025);

	this.timeline.addTween(cjs.Tween.get({}).to({state:[{t:this.shape_1},{t:this.shape}]}).wait(1));

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-44.4,-50.6,88.8,101.30000000000001);


(lib.Tween8 = function(mode,startPosition,loop) {
	this.initialize(mode,startPosition,loop,{});

	// Layer_1
	this.shape = new cjs.Shape();
	this.shape.graphics.f("#FFFFFF").s().p("AhbBcQgmgmAAg2QAAg1AmgmQAmgmA1AAQA2AAAmAmQAmAmAAA1QAAA2gmAmQgmAmg2AAQg1AAgmgmg");

	this.timeline.addTween(cjs.Tween.get(this.shape).wait(1));

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-13,-13,26,26);


(lib.Tween7 = function(mode,startPosition,loop) {
	this.initialize(mode,startPosition,loop,{});

	// Layer_1
	this.shape = new cjs.Shape();
	this.shape.graphics.f("#FFFFFF").s().p("AhbBcQgmgmAAg2QAAg1AmgmQAmgmA1AAQA2AAAmAmQAmAmAAA1QAAA2gmAmQgmAmg2AAQg1AAgmgmg");

	this.timeline.addTween(cjs.Tween.get(this.shape).wait(1));

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-13,-13,26,26);


(lib.Tween6 = function(mode,startPosition,loop) {
	this.initialize(mode,startPosition,loop,{});

	// Layer_1
	this.shape = new cjs.Shape();
	this.shape.graphics.f("#FFFFFF").s().p("AhrBrIAAjVIDWAAIAADVg");

	this.timeline.addTween(cjs.Tween.get(this.shape).wait(1));

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-10.7,-10.7,21.5,21.5);


(lib.Tween5 = function(mode,startPosition,loop) {
	this.initialize(mode,startPosition,loop,{});

	// Layer_1
	this.shape = new cjs.Shape();
	this.shape.graphics.f("#FFFFFF").s().p("AhrBrIAAjVIDWAAIAADVg");

	this.timeline.addTween(cjs.Tween.get(this.shape).wait(1));

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-10.7,-10.7,21.5,21.5);


(lib.Tween4 = function(mode,startPosition,loop) {
	this.initialize(mode,startPosition,loop,{});

	// Layer_1
	this.shape = new cjs.Shape();
	this.shape.graphics.f("#FFFFFF").s().p("AhdBeQgmgnAAg3QAAg2AmgnQAngmA2AAQA3AAAnAmQAnAngBA2QABA3gnAnQgnAng3gBQg2ABgngng");

	this.timeline.addTween(cjs.Tween.get(this.shape).wait(1));

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-13.2,-13.2,26.5,26.5);


(lib.Tween3 = function(mode,startPosition,loop) {
	this.initialize(mode,startPosition,loop,{});

	// Layer_1
	this.shape = new cjs.Shape();
	this.shape.graphics.f("#FFFFFF").s().p("AhdBeQgmgnAAg3QAAg2AmgnQAngmA2AAQA3AAAnAmQAnAngBA2QABA3gnAnQgnAng3gBQg2ABgngng");

	this.timeline.addTween(cjs.Tween.get(this.shape).wait(1));

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-13.2,-13.2,26.5,26.5);


(lib.Tween2 = function(mode,startPosition,loop) {
	this.initialize(mode,startPosition,loop,{});

	// Layer_1
	this.shape = new cjs.Shape();
	this.shape.graphics.f("#FFFFFF").s().p("AhSBTQgjgiAAgxQAAgwAjgiQAigjAwAAQAwAAAjAjQAjAigBAwQABAxgjAiQgjAjgwAAQgwAAgigjg");

	this.timeline.addTween(cjs.Tween.get(this.shape).wait(1));

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-11.7,-11.7,23.5,23.5);


(lib.Tween1 = function(mode,startPosition,loop) {
	this.initialize(mode,startPosition,loop,{});

	// Layer_1
	this.shape = new cjs.Shape();
	this.shape.graphics.f("#FFFFFF").s().p("AhSBTQgjgiAAgxQAAgwAjgiQAigjAwAAQAwAAAjAjQAjAigBAwQABAxgjAiQgjAjgwAAQgwAAgigjg");

	this.timeline.addTween(cjs.Tween.get(this.shape).wait(1));

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-11.7,-11.7,23.5,23.5);


(lib.Symbol1 = function(mode,startPosition,loop) {
	this.initialize(mode,startPosition,loop,{});

	// Layer_1
	this.instance = new lib.Bitmap1();
	this.instance.parent = this;
	this.instance.setTransform(-232.5,-442.5);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(1));

}).prototype = getMCSymbolPrototype(lib.Symbol1, new cjs.Rectangle(-232.5,-442.5,465,885), null);


(lib.sprite14 = function(mode,startPosition,loop) {
	this.initialize(mode,startPosition,loop,{});

	// Layer_1
	this.instance = new lib.Bitmap11();
	this.instance.parent = this;
	this.instance.setTransform(-15.75,-62.8);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(1));

}).prototype = getMCSymbolPrototype(lib.sprite14, new cjs.Rectangle(-15.7,-62.8,646,119), null);


(lib.LineDo = function(mode,startPosition,loop) {
	this.initialize(mode,startPosition,loop,{});

	// Layer_1
	this.shape = new cjs.Shape();
	this.shape.graphics.f("#FF0000").s().p("AwdJaQh2h8AAitIAAjqQAAipB0h4QB0h5CmgDQCrgEB6B+QB6B9AACuIAACiIpBAAIAABDQAABHAwA0QAwA1BFACQAzABArgbQAqgbAWgtQANgdAbgRQAbgRAfAAICcAAIgBALQgICkh2BxQh2ByiiAAQipAAh3h9gAsElCQieADhuByQhvBzAAChIAADqQAAClBxB2QBxB3CgAAQCaAABwhsQBshpAMiVIiHAAQgaAAgVAOQgWAOgLAXIAAAAQgYAzgvAeQgwAfg6gCQhNgCg2g7Qg1g6AAhPIAAhXIJBAAIAAiOQAAimh0h3Qhxh0icAAIgJAAgAk5KyIAAAAIAAAAIEIm9QARgcAAghQAAgigRgcIkIm8IDKAAQAnAAAiAUQAhATAUAiIBzDBIByjBQAKgRAOgNIAegXQAigUAnAAIDLAAIkMHCQgOAaAAAeQAAAhARAcIEJG9IjLAAQgnAAgigUQghgTgVgiIhyjAIhzDAIAAABQgUAhghAUQgiATgnAAgAIaKeIj2meQgUgiAAgmIg/BqIAAABIgBABQgeA0AAA9QAAA3AZAxIA7BiQARAdAdARQAdARAiAAICnAAgABDBYIAAAAIhjCoIjuGRIgEAGIgEAHICnAAQARAAAQgEIAegNQAdgRARgdICEjeIAeAyQgCgPAAgPQAAhDAig5IAAgBIFapGIinAAQghAAgeARQgdARgRAdIiEDeIgegyQACAPAAAQQAABBgiA6IgBAAIgEgDgAggBwQAUAhAAAnIA+hrIAAgBQAfg0AAg8QAAg7gcgyIg2heIgBAAQgRgdgdgRQgdgRgiAAIinAAgAeGKyIAAsJIioAAIAAjrICoAAIAAkXIEFAAIAAEXICoAAIAADrIioAAIAAKaQAAAuggAgQggAhgtAAgAbyhrICoAAIAAMJICEAAQAlAAAagbQAagaAAgmIAAquICoAAIAAjDIioAAIAAkXIjdAAIAAEXIioAAgAVLKyIAAsJIlOAAIAAKaQAAAuggAgQggAhgtAAIiYAAIAAsJIiKAAIAAjrICKAAIAAhAQAAiGBdheQBdhfCDAAIDFAAIAADrIicAAQgoAAgdAdQgcAdAAApIAAA1IJTAAIAAOFQAAAuggAgQggAhgtAAgAVfhrIAAMJICEAAQAlAAAagbQAagaAAgmIAAtxIpTAAIAAhJQAAgxAigjQAigjAxAAICIAAIAAjDIixAAQh7AAhXBZQhXBZAAB9IAABUIiKAAIAADDICKAAIAAMJICEAAQAlAAAagbQAagaAAgmIAAqugA5WKyIAA13IEEAAIAAUIQAAAuggAgQgfAhguAAgA5CKeICDAAQAlAAAagbQAagaAAgmIAAz0IjcAAgEgioAKyIAAsJIiKAAIAAjrICKAAIAAhAQAAiGBdheQBchfCEAAIDFAAIAADrIicAAQgoAAgdAdQgcAdAAApIAAA1IDGAAIAADrIjHAAIAAKaQAAAugfAgQggAhgtAAgEgkegBrICKAAIAAMJICEAAQAlAAAZgbQAagaAAgmIAAquIDHAAIAAjDIjGAAIAAhJQAAgxAigjQAigjAxAAICIAAIAAjDIixAAQh7AAhXBZQhXBZAAB9IAABUIiKAAgAu7BIIABgKQADhMA3g1QA3g2BMAAQBMAAA3A2QA3A1ADBMIABAKgApUA0QgHg9gtgsQgxgwhEAAQhEAAgxAwQgtAsgGA9IFRAAIAAAAgAVwnwQgngnAAg4QAAg4AngnQAngoA3AAQA3AAAnAoQAmAnAAA4QAAA4gnAnQgmAog3AAQg3AAgngogAV+qgQghAhAAAwQAAAvAhAiQAhAiAvAAQAuAAAhgiQAhgiAAgvQAAgwghghQghgiguAAQgvAAghAig");
	this.shape.setTransform(0.025,0.025);

	this.timeline.addTween(cjs.Tween.get(this.shape).wait(1));

}).prototype = getMCSymbolPrototype(lib.LineDo, new cjs.Rectangle(-235.4,-72.6,470.9,145.3), null);


(lib.ClipGroup = function(mode,startPosition,loop) {
	this.initialize(mode,startPosition,loop,{});

	// Layer_2 (mask)
	var mask = new cjs.Shape();
	mask._off = true;
	mask.graphics.p("EgiaABwIAAjfMBE1AAAIAADfg");
	mask.setTransform(220.3,11.175);

	// Layer_3
	this.shape = new cjs.Shape();
	this.shape.graphics.f("#E1E1E1").s().p("AYjBTQgcgeAAg0QAAggAJgWQAIgRAMgNQAOgOAOgFQAWgJAZAAQAwAAAdAeQAcAeAAAzQAAA1gcAdQgcAegxAAQgwAAgcgdgAZEg3QgQASAAAlQgBAlASATQAQATAaAAQAaAAARgTQARgTAAglQAAgkgRgTQgPgSgcAAQgbAAgQASgANVBpQgPgHgLgMQgIgLgDgNQgFgSAAglIAAhyIAsAAIAAB0QAAAcABAIQAEAOAKAIQAMAIAQAAQAUAAAIgIQALgIABgLQACgKAAgaIAAh3IAsAAIAABxQAAAngEAQQgDAPgKAMQgKALgPAHQgQAGgaAAQghAAgOgHgAJTBTQgcgeAAg0QAAgfAKgXQAIgSAMgMQAMgNAQgGQAVgJAaAAQAwAAAcAeQAdAeAAAzQAAA1gdAdQgbAegxAAQgwAAgdgdgAJ0g3QgQASAAAlQAAAkARAUQARATAaAAQAbAAAQgTQARgTAAglQAAgkgQgTQgRgSgbAAQgbAAgRASgAnJBTQgcgegBg0QABgfAJgXQAJgSAMgMQAMgNAQgGQAUgJAaAAQAwAAAcAeQAdAeABAzQAAA1gdAdQgcAegwAAQgxAAgcgdgAmog3QgRASAAAlQABAlAQATQASATAaAAQAaAAARgTQARgUAAgkQgBgkgQgTQgQgSgbAAQgbAAgRASgAf3BsIAAjXICgAAIAAAkIh1AAIAAAwIBtAAIAAAkIhtAAIAAA7IB5AAIAAAkgAeiBsIAAipIgqCpIgqAAIgsipIAACpIgoAAIAAjXIBBAAIAnCSIAoiSIBBAAIAADXgAW2BsIAAheIhWAAIAABeIgrAAIAAjXIArAAIAABVIBWAAIAAhVIAsAAIAADXgASVBsIgggvIgXggQgIgGgFgDQgIgCgOAAIgJAAIAABaIgsAAIAAjXIBcAAQAjAAAQAGQAQAGAIAOQAKAPAAAUQAAAYgOAQQgOAPgdAEQANAHALALQAKANAOAWIAaAqgAQygQIAgAAQAgAAAIgCQAHgDAFgGQAEgHAAgKQAAgKgGgHQgFgHgLgCQgHgBgZAAIgiAAgAGvBsIAAhbIhPh8IAzAAIAzBVIAyhVIAzAAIhRB8IAABbgACfBsIAAizIg/AAIAAgkICsAAIAAAkIhAAAIAACzgAAZBsIAAjXIAsAAIAADXgAidBsIAAjXICUAAIAAAkIhoAAIAAA0IBaAAIAAAjIhaAAIAABcgApfBsIAAizIhAAAIAAgkICsAAIAAAkIhAAAIAACzgAuuBsIAAjXICgAAIAAAkIh0AAIAAAwIBsAAIAAAkIhsAAIAAA7IB5AAIAAAkgAxmBsIAAjVIAsAAIAACxIBtAAIAAAkgA1BBsIAAjXIBWAAQAZAAAOACQALACALAHQAKAGAHAMQAHALAAAOQgBAQgHAMQgJANgOAGQAUAGALANQAKAPABASQAAANgIAPQgGAOgNAJQgMAIgQACQgMABgpAAgA0VBIIAoAAIAegBQAKgDAGgGQAGgHAAgMQAAgJgFgHQgFgHgIgDQgJgDgeAAIgjAAgA0VgVIA8gBQAMgBAFgGQAHgHAAgKQAAgKgGgHQgFgHgLAAQgIgBgeAAIgYAAgA2XBsIAAjXIAsAAIAADXgA3hBsIgvhLIgxBLIg0AAIBKhwIhDhnIAzAAIAsBFIAqhFIAyAAIhDBpIBKBugA8pBsIAAjXICgAAIAAAkIh0AAIAAAwIBsAAIAAAkIhsAAIAAA7IB5AAIAAAkgA/hBsIAAjVIAsAAIAACxIBtAAIAAAkgEgiaABsIAAjXICUAAIAAAkIhpAAIAAA0IBbAAIAAAjIhbAAIAABcg");
	this.shape.setTransform(220.3,11.175);

	var maskedShapeInstanceList = [this.shape];

	for(var shapedInstanceItr = 0; shapedInstanceItr < maskedShapeInstanceList.length; shapedInstanceItr++) {
		maskedShapeInstanceList[shapedInstanceItr].mask = mask;
	}

	this.timeline.addTween(cjs.Tween.get(this.shape).wait(1));

}).prototype = getMCSymbolPrototype(lib.ClipGroup, new cjs.Rectangle(0,0,440.6,22.4), null);


(lib.shape71 = function(mode,startPosition,loop) {
	this.initialize(mode,startPosition,loop,{});

	// Layer_1
	this.instance = new lib.Bitmap1copy();
	this.instance.parent = this;
	this.instance.setTransform(-68,-68);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(1));

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-68,-68,136,136);


(lib.shape70 = function(mode,startPosition,loop) {
	this.initialize(mode,startPosition,loop,{});

	// Layer_1
	this.instance = new lib.Bitmap5copy();
	this.instance.parent = this;
	this.instance.setTransform(-73.5,-73.5);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(1));

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-73.5,-73.5,147,147);


(lib.shape69 = function(mode,startPosition,loop) {
	this.initialize(mode,startPosition,loop,{});

	// Layer_1
	this.instance = new lib.Bitmap2copy();
	this.instance.parent = this;
	this.instance.setTransform(-46.25,-46.25);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(1));

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-46.2,-46.2,93,93);


(lib.shape67 = function(mode,startPosition,loop) {
	this.initialize(mode,startPosition,loop,{});

	// Layer_1
	this.instance = new lib.Bitmap3copy();
	this.instance.parent = this;
	this.instance.setTransform(-87,-87);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(1));

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-87,-87,174,174);


(lib.Logo_line = function(mode,startPosition,loop) {
	this.initialize(mode,startPosition,loop,{});

	// Logo (mask)
	var mask = new cjs.Shape();
	mask._off = true;
	var mask_graphics_31 = new cjs.Graphics().p("AwdJaQh2h8AAitIAAjqQAAipB0h4QB0h5CmgDQCrgEB6B+QB6B9AACuIAACiIpBAAIAABDQAABHAwA0QAwA1BFACQAzABArgbQAqgbAWgtQANgdAbgRQAbgRAfAAICcAAIgBALQgJCkh1BxQh2ByiiAAQipAAh3h9gApVA0QgHhAgvgtQgxgshBAAQhBAAgwAsQgwAtgHBAIFQAAIAAAAgAk5KyIAAAAIAAAAIEIm9QARgcAAghQAAgigRgcIkIm8IDKAAQAnAAAiAUQAhATAUAiIBzDBIByjBQAKgRAOgNIAegXQAigUAnAAIDLAAIkMHCQgOAaAAAeQAAAhARAcIEJG9IjLAAQgnAAgigUQghgTgVgiIg4hfIACAEIg8hmIhzDBIgBACIgBABIgBACIgBABIgcAhQgKAIgLAHQgiATgnAAgAeGKyIAAsJIioAAIAAjrICoAAIAAkXIEFAAIAAEXICoAAIAADrIioAAIAAKaQAAAuggAgQggAhgtAAgAVLKyIAAsJIlOAAIAAKaQAAAuggAgQggAhgtAAIiYAAIAAsJIiKAAIAAjrICKAAIAAhAQAAiFBdhfQBdhfCDAAIDFAAIAADrIicAAQgoAAgdAdQgcAdAAApIAAA1IJTAAIAAOFQAAAuggAgQggAhgtAAgA5WKyIAA13IEEAAIAAUIQAAAuggAgQgfAhguAAgEgioAKyIAAsJIiKAAIAAjrICKAAIAAhAQAAiGBdheQBchfCEAAIDFAAIAADrIicAAQgoAAgdAdQgcAdAAApIAAA1IDGAAIAADrIjHAAIAAKaQAAAugfAgQggAhgtAAgAVwnwQgngnAAg4QAAg4AngnQAngoA3AAQA3AAAnAoQAmAnAAA4QAAA4gnAnQgmAog3AAQg3AAgngog");

	this.timeline.addTween(cjs.Tween.get(mask).to({graphics:null,x:0,y:0}).wait(31).to({graphics:mask_graphics_31,x:0.025,y:0.025}).wait(9));

	// Layer_17
	this.instance = new lib.Symbol1();
	this.instance.parent = this;
	this.instance.setTransform(-493.1,8);
	this.instance.alpha = 0.5898;
	this.instance._off = true;

	var maskedShapeInstanceList = [this.instance];

	for(var shapedInstanceItr = 0; shapedInstanceItr < maskedShapeInstanceList.length; shapedInstanceItr++) {
		maskedShapeInstanceList[shapedInstanceItr].mask = mask;
	}

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(31).to({_off:false},0).to({x:417.15},8).wait(1));

	// Layer_15
	this.instance_1 = new lib.Tween15("synched",0);
	this.instance_1.parent = this;
	this.instance_1.setTransform(205.25,4.05);
	this.instance_1.alpha = 0;
	this.instance_1._off = true;

	this.instance_2 = new lib.Tween16("synched",0);
	this.instance_2.parent = this;
	this.instance_2.setTransform(205.25,4.05);

	this.timeline.addTween(cjs.Tween.get({}).to({state:[]}).to({state:[{t:this.instance_1}]},23).to({state:[{t:this.instance_2}]},5).wait(12));
	this.timeline.addTween(cjs.Tween.get(this.instance_1).wait(23).to({_off:false},0).to({_off:true,alpha:1},5).wait(12));

	// T (mask)
	var mask_1 = new cjs.Shape();
	mask_1._off = true;
	var mask_1_graphics_17 = new cjs.Graphics().p("AiBKGIAAsIIioAAIAAjrICoAAIAAkYIEEAAIAAEYICnAAIAADqIinAAIAAKbQgBAtggAgQgfAhguAAgAkWiXICpAAIAAMJICCAAQAmAAAZgaQAagbAAglIAAqvICoAAIAAjCIioAAIAAkYIjbAAIAAEYIipAAg");

	this.timeline.addTween(cjs.Tween.get(mask_1).to({graphics:null,x:0,y:0}).wait(17).to({graphics:mask_1_graphics_17,x:205.25,y:4.3}).wait(23));

	// Layer_12
	this.instance_3 = new lib.Tween7("synched",0);
	this.instance_3.parent = this;
	this.instance_3.setTransform(243,80.05);
	this.instance_3._off = true;

	this.instance_4 = new lib.Tween8("synched",0);
	this.instance_4.parent = this;
	this.instance_4.setTransform(252.7,89.75,13.8462,13.8462,0,0,0,0.7,0.7);

	var maskedShapeInstanceList = [this.instance_3,this.instance_4];

	for(var shapedInstanceItr = 0; shapedInstanceItr < maskedShapeInstanceList.length; shapedInstanceItr++) {
		maskedShapeInstanceList[shapedInstanceItr].mask = mask_1;
	}

	this.timeline.addTween(cjs.Tween.get({}).to({state:[]}).to({state:[{t:this.instance_3}]},17).to({state:[{t:this.instance_4}]},7).to({state:[]},1).wait(15));
	this.timeline.addTween(cjs.Tween.get(this.instance_3).wait(17).to({_off:false},0).to({_off:true,regX:0.7,regY:0.7,scaleX:13.8462,scaleY:13.8462,x:252.7,y:89.75},7).wait(16));

	// Layer_14
	this.instance_5 = new lib.Tween14("synched",0);
	this.instance_5.parent = this;
	this.instance_5.setTransform(111.75,-2.2);
	this.instance_5.alpha = 0;
	this.instance_5._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_5).wait(20).to({_off:false},0).to({alpha:1},5).wait(15));

	// FI (mask)
	var mask_2 = new cjs.Shape();
	mask_2._off = true;
	var mask_2_graphics_15 = new cjs.Graphics().p("ADrLEIAAsIIlOAAIAAKaQAAAugfAgQggAggtAAIiZAAIAAsIIiJAAIAAjrICJAAIAAhAQABiGBchfQAUgUAWgQQBQg6BnAAIDEAAIAADqIibAAQgoAAgdAdQgcAegBApIAAA1IJTAAIAAOFQAAAtggAhQggAggtAAgAD/hYIAAMIICEAAQAlAAAagaQAagbAAglIAAtxIpSAAIAAhJQgBgyAjgjQAigjAxAAICHAAIAAjCIiwAAQhoAAhPA/QgNAMgOANQhYBZAAB+IAABUIiJAAIAADDICJAAIAAMIICFAAQAlAAAZgaQAbgaAAgmIAAqugAFOm5QgegHgZgWIgIgHQgmgoAAg4QAAg3AmgoIAIgHQAZgWAegHQAPgDARAAQA2AAAnAnQAnAoAAA3QAAA4gnAoQgmAng3AAQgRAAgPgDgAFOqrQgbAIgVAVIgHAHQgaAgAAAqQAAArAaAgIAHAHQAVAVAbAIQAPAEARAAQAuAAAhghQAhgiAAgwQAAgvghgiQghghguAAQgRAAgPAEg");

	this.timeline.addTween(cjs.Tween.get(mask_2).to({graphics:null,x:0,y:0}).wait(15).to({graphics:mask_2_graphics_15,x:111.75,y:-2.175}).wait(25));

	// Layer_11
	this.instance_6 = new lib.Tween5("synched",0);
	this.instance_6.parent = this;
	this.instance_6.setTransform(57.75,-61.2);
	this.instance_6._off = true;

	this.instance_7 = new lib.Tween6("synched",0);
	this.instance_7.parent = this;
	this.instance_7.setTransform(57.75,-61.2,16.907,16.907);

	var maskedShapeInstanceList = [this.instance_6,this.instance_7];

	for(var shapedInstanceItr = 0; shapedInstanceItr < maskedShapeInstanceList.length; shapedInstanceItr++) {
		maskedShapeInstanceList[shapedInstanceItr].mask = mask_2;
	}

	this.timeline.addTween(cjs.Tween.get({}).to({state:[]}).to({state:[{t:this.instance_6}]},15).to({state:[{t:this.instance_7}]},8).to({state:[]},1).wait(16));
	this.timeline.addTween(cjs.Tween.get(this.instance_6).wait(15).to({_off:false},0).to({_off:true,scaleX:16.907,scaleY:16.907},8).wait(17));

	// Layer_13
	this.instance_8 = new lib.Tween11("synched",0);
	this.instance_8.parent = this;
	this.instance_8.setTransform(12.7,18.2);
	this.instance_8.alpha = 0;
	this.instance_8._off = true;

	this.instance_9 = new lib.Tween12("synched",0);
	this.instance_9.parent = this;
	this.instance_9.setTransform(12.7,18.2);

	this.timeline.addTween(cjs.Tween.get({}).to({state:[]}).to({state:[{t:this.instance_8}]},17).to({state:[{t:this.instance_9}]},5).wait(18));
	this.timeline.addTween(cjs.Tween.get(this.instance_8).wait(17).to({_off:false},0).to({_off:true,alpha:1},5).wait(18));

	// X (mask)
	var mask_3 = new cjs.Shape();
	mask_3._off = true;
	var mask_3_graphics_12 = new cjs.Graphics().p("Am7H6IABAAIEsn6IA/hqIAAgBQAgg0AAg9IAAAAQAAg7gcgyIAAAAIgEgFIASgKIA5BfIAEAKIBzjCQAKgQAOgOIAAAAIAegXQAWgNAYgEIAAAAQAMgCAOAAIAAAAIDLAAIkLHCIgiA3Ig/BrIgBABIAAAAQgfA0AAA9IAAAAQAAA4AZAwIAAAAIADAEIAAAAIADAFIgRALIg+hoIhyDAIAAABQgVAhghAUIAAAAQgiATgnAAIAAAAgAmYHmICnAAQASAAAPgEIAAAAIAegNQAegRAQgdIAAAAIB/jTIAFgLIAeAzIAAAAQgCgQAAgPIAAAAQAAhDAig5IAAAAIAAAAIFbpGIioAAQgOAAgMACIAAAAQgTAEgRALIAAAAQgdAQgSAeIAAAAIiEDdIgdgyQACAQAAAPIAAAAQAABCgiA6IAAAAIgBAAIgEgCIAEADIAAAAIhjCnIjvGRIAAAAIgEAGIAAAAIgEAHgADyH6QgOAAgNgCIAAAAQgYgFgWgNIAAAAQgigTgUgiIAAAAIg1hYIARgLIgDgFIgDgEIAAAAIA7BiQARAdAeARIAAAAQARAKATAFIAAAAQANACAOAAIAAAAICnAAIj3meQgTghAAgnIAAAAIAig3QgOAagBAdIAAAAQABAhARAdIAAAAIEIG8gAm7H6IEIm8QARgdAAghIAAAAQAAgggRgdIAAAAIkIm8IDKAAQAnAAAiATIAAAAQAhATAVAiIAAAAIA1BZIgSAKIg0hYIgBAAQgQgdgegRIAAAAQgdgSgiABIAAAAIinAAID3GeQATAhAAAmIAAAAIksH6gAmYHmIAEgHIgDAHICmAAQASAAAPgEIAAAAQgPAEgSAAIAAAAgAAADJIgFALIAFgLIAeAzIAAAAgAgEj5IAEAJIBzjBQAKgRAOgNIAAAAQgOAOgKAQIAAAAIhzDCg");

	this.timeline.addTween(cjs.Tween.get(mask_3).to({graphics:null,x:0,y:0}).wait(12).to({graphics:mask_3_graphics_12,x:12.7498,y:18.3001}).wait(28));

	// Layer_10_copy
	this.instance_10 = new lib.Tween3("synched",0);
	this.instance_10.parent = this;
	this.instance_10.setTransform(12.75,15.5,0.2415,0.2415);
	this.instance_10._off = true;

	var maskedShapeInstanceList = [this.instance_10];

	for(var shapedInstanceItr = 0; shapedInstanceItr < maskedShapeInstanceList.length; shapedInstanceItr++) {
		maskedShapeInstanceList[shapedInstanceItr].mask = mask_3;
	}

	this.timeline.addTween(cjs.Tween.get(this.instance_10).wait(12).to({_off:false},0).to({regX:0.1,regY:1.4,scaleX:6.6449,scaleY:6.6449,x:13.05,y:24.45},6).to({_off:true},1).wait(21));

	// E (mask)
	var mask_4 = new cjs.Shape();
	mask_4._off = true;
	var mask_4_graphics_7 = new cjs.Graphics().p("AkfGbQh3h8ABitIAAjqQgBipB1h5QB0h5CmgDQCqgDB6B+QB6B8AACvIAAChIpAAAIAABDQAABIAwA0QAwA0BFACQAyACArgcQAqgaAWguQANgdAbgRQAbgRAfAAICcAAIgBALQgICkh2ByQh2BxiiAAQioAAh3h8gAgGoCQieADhvBzQhuBzAAChIAADqQAAClBwB2QByB2CfAAQCaABBwhtQBshoAMiWIiHAAQgaAAgWAOQgVAOgLAYIAAAAQgYAzgvAdQgwAfg5gCQhNgCg2g7Qg1g6AAhPIAAhXIJAAAIAAiNQgBimhzh3Qhyh1ibAAIgIAAgAi9h2IABgLQADhMA2g2QA4g2BLAAQBMAAA3A2QA3A2ADBMIABALgACoiKQgGg+gtgtQgxgwhEAAQhDAAgxAwQguAtgGA+IFQAAIAAAAg");

	this.timeline.addTween(cjs.Tween.get(mask_4).to({graphics:null,x:0,y:0}).wait(7).to({graphics:mask_4_graphics_7,x:-76.75,y:19.2953}).wait(33));

	// Layer_10_copy_copy
	this.instance_11 = new lib.Tween3("synched",0);
	this.instance_11.parent = this;
	this.instance_11.setTransform(-45.75,87.3);
	this.instance_11._off = true;

	this.instance_12 = new lib.Tween4("synched",0);
	this.instance_12.parent = this;
	this.instance_12.setTransform(-45.75,87.3,14.8113,14.8113);

	var maskedShapeInstanceList = [this.instance_11,this.instance_12];

	for(var shapedInstanceItr = 0; shapedInstanceItr < maskedShapeInstanceList.length; shapedInstanceItr++) {
		maskedShapeInstanceList[shapedInstanceItr].mask = mask_4;
	}

	this.timeline.addTween(cjs.Tween.get({}).to({state:[]}).to({state:[{t:this.instance_11}]},7).to({state:[{t:this.instance_12}]},4).wait(29));
	this.timeline.addTween(cjs.Tween.get(this.instance_11).wait(7).to({_off:false},0).to({_off:true,scaleX:14.8113,scaleY:14.8113},4).wait(29));

	// L (mask)
	var mask_5 = new cjs.Shape();
	mask_5._off = true;
	var mask_5_graphics_3 = new cjs.Graphics().p("AstK8IAA13IEFAAIAAUIQAAAuggAgQggAhgtAAgAsZKoICEAAQAlAAAagbQAagaAAgmIAAz0IjdAAg");

	this.timeline.addTween(cjs.Tween.get(mask_5).to({graphics:null,x:0,y:0}).wait(3).to({graphics:mask_5_graphics_3,x:-81.375,y:-1.175}).wait(37));

	// Layer_10
	this.instance_13 = new lib.Tween3("synched",0);
	this.instance_13.parent = this;
	this.instance_13.setTransform(-115.75,-92.7);
	this.instance_13._off = true;

	this.instance_14 = new lib.Tween4("synched",0);
	this.instance_14.parent = this;
	this.instance_14.setTransform(-115.75,-92.7,14.8113,14.8113);

	var maskedShapeInstanceList = [this.instance_13,this.instance_14];

	for(var shapedInstanceItr = 0; shapedInstanceItr < maskedShapeInstanceList.length; shapedInstanceItr++) {
		maskedShapeInstanceList[shapedInstanceItr].mask = mask_5;
	}

	this.timeline.addTween(cjs.Tween.get({}).to({state:[]}).to({state:[{t:this.instance_13}]},3).to({state:[{t:this.instance_14}]},6).wait(31));
	this.timeline.addTween(cjs.Tween.get(this.instance_13).wait(3).to({_off:false},0).to({_off:true,scaleX:14.8113,scaleY:14.8113},6).wait(31));

	// F (mask)
	var mask_6 = new cjs.Shape();
	mask_6._off = true;
	mask_6.graphics.p("Ai8K8IAAsJIiJAAIAAjrICJAAIAAhAQABiGBdheQBchfCCAAIDGAAIAADrIidAAQgoAAgcAdQgcAdgBApIAAA1IDHAAIAADrIjHAAIAAKaQAAAugfAgQggAhgtAAgAkxhhICJAAIAAMJICEAAQAlAAAZgbQAagaAAgmIAAquIDHAAIAAjDIjHAAIAAhJQAAgxAjgjQAigjAwAAICJAAIAAjDIiyAAQh6AAhXBZQhWBZgBB9IAABUIiJAAg");
	mask_6.setTransform(-203.25,-0.675);

	// Layer_9
	this.instance_15 = new lib.Tween1("synched",0);
	this.instance_15.parent = this;
	this.instance_15.setTransform(-182,78.75);

	this.instance_16 = new lib.Tween2("synched",0);
	this.instance_16.parent = this;
	this.instance_16.setTransform(-194.4,91.15,15.4894,15.4894,0,0,0,-0.8,0.8);

	var maskedShapeInstanceList = [this.instance_15,this.instance_16];

	for(var shapedInstanceItr = 0; shapedInstanceItr < maskedShapeInstanceList.length; shapedInstanceItr++) {
		maskedShapeInstanceList[shapedInstanceItr].mask = mask_6;
	}

	this.timeline.addTween(cjs.Tween.get({}).to({state:[{t:this.instance_15}]}).to({state:[{t:this.instance_16}]},5).wait(35));
	this.timeline.addTween(cjs.Tween.get(this.instance_15).to({_off:true,regX:-0.8,regY:0.8,scaleX:15.4894,scaleY:15.4894,x:-194.4,y:91.15},5).wait(35));

	// Layer_1_copy_copy
	this.instance_17 = new lib.LineDo();
	this.instance_17.parent = this;
	this.instance_17.setTransform(-0.4,346.1);

	this.timeline.addTween(cjs.Tween.get(this.instance_17).wait(40));

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-235.8,-73,471.3,491.8);


(lib.sprite68 = function(mode,startPosition,loop) {
	this.initialize(mode,startPosition,loop,{});

	// Layer_1
	this.instance = new lib.shape67("synched",0);
	this.instance.parent = this;

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(1));

}).prototype = getMCSymbolPrototype(lib.sprite68, new cjs.Rectangle(-87,-87,174,174), null);


(lib.sprite72 = function(mode,startPosition,loop) {
	this.initialize(mode,startPosition,loop,{});

	// timeline functions:
	this.frame_49 = function() {
		this.stop();
	}

	// actions tween:
	this.timeline.addTween(cjs.Tween.get(this).wait(49).call(this.frame_49).wait(1));

	// Layer_6
	this.instance = new lib.shape70("synched",0);
	this.instance.parent = this;
	this.instance.setTransform(-10.75,0,0.487,0.487);
	this.instance.alpha = 0;

	this.timeline.addTween(cjs.Tween.get(this.instance).to({scaleX:0.4961,scaleY:0.4961,x:-5.7,alpha:0.0391},1).to({scaleX:0.5686,scaleY:0.5686,x:-11.35,alpha:0.3594},2).to({scaleX:0.7135,scaleY:0.7135,x:-22.7,alpha:1},2).to({x:-39.7,alpha:0},27).to({_off:true},1).wait(17));

	// Layer_5
	this.instance_1 = new lib.shape69("synched",0);
	this.instance_1.parent = this;
	this.instance_1.setTransform(-3,0,0.4119,0.4119);
	this.instance_1.alpha = 0;

	this.timeline.addTween(cjs.Tween.get(this.instance_1).to({scaleX:0.424,scaleY:0.424,x:-2.55,alpha:0.0391},1).to({scaleX:0.4602,scaleY:0.4602,x:-1.2,alpha:0.1602},1).to({scaleX:0.605,scaleY:0.605,x:4.1,alpha:0.6406},2).to({scaleX:0.7135,scaleY:0.7135,x:8.1,alpha:1},1).to({scaleX:0.4108,scaleY:0.4108,x:14.1,alpha:0},29).to({_off:true},1).wait(15));

	// Layer_3
	this.instance_2 = new lib.sprite68();
	this.instance_2.parent = this;
	this.instance_2.setTransform(0,0,0.3125,0.3125);
	this.instance_2.alpha = 0;

	this.timeline.addTween(cjs.Tween.get(this.instance_2).to({scaleX:0.3286,scaleY:0.3286,alpha:0.0391},1).to({scaleX:0.3767,scaleY:0.3767,alpha:0.1602},1).to({scaleX:0.4569,scaleY:0.4569,alpha:0.3594},1).to({scaleX:0.5692,scaleY:0.5692,alpha:0.6406},1).to({scaleX:0.7135,scaleY:0.7135,alpha:1},1).to({scaleX:0.3229,scaleY:0.3229,alpha:0},31).to({_off:true},1).wait(13));

	// Layer_2
	this.instance_3 = new lib.shape71("synched",0);
	this.instance_3.parent = this;
	this.instance_3.setTransform(0,0,1.1365,1.1365,32.5708);
	this.instance_3.alpha = 0;
	this.instance_3._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_3).wait(3).to({_off:false},0).to({scaleX:1.4595,scaleY:1.4595,rotation:134.371,alpha:1},10).to({scaleX:1.0655,scaleY:1.0655,rotation:177.4834,alpha:0.1602},16).to({scaleX:1.041,scaleY:1.041,rotation:180.0302,alpha:0.1094},1).to({scaleX:0.9918,scaleY:0.9918,rotation:185.5064,alpha:0},2).to({_off:true},1).wait(17));

	// Layer_1
	this.instance_4 = new lib.shape71("synched",0);
	this.instance_4.parent = this;
	this.instance_4.setTransform(0,0,0.6602,0.6602,82.7387);
	this.instance_4.alpha = 0;
	this.instance_4._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_4).wait(3).to({_off:false},0).to({scaleX:0.6553,scaleY:0.6553,rotation:133.3961,alpha:0.4609},5).to({scaleX:0.6556,scaleY:0.6556,rotation:163.7187,alpha:0.7305},3).to({scaleX:0.6565,scaleY:0.6565,rotation:173.7075,alpha:0.8203},1).to({scaleX:0.6562,scaleY:0.6562,rotation:183.5473,alpha:0.9102},1).to({scaleX:0.656,scaleY:0.656,rotation:193.7809,alpha:1},1).to({scaleX:0.491,scaleY:0.491,rotation:278.9943,alpha:0},8).wait(28));

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-140.4,-140.3,280.70000000000005,280.70000000000005);


// stage content:
(lib.Flexfit = function(mode,startPosition,loop) {
	this.initialize(mode,startPosition,loop,{});

	// sao1
	this.instance = new lib.sprite72();
	this.instance.parent = this;
	this.instance.setTransform(454.5,62.55,0.9,0.8986);
	this.instance._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(50).to({_off:false},0).to({_off:true},21).wait(89));

	// Layer_12
	this.instance_1 = new lib.sprite14();
	this.instance_1.parent = this;
	this.instance_1.setTransform(-150.85,242.25,0.3655,0.2422,0.0072);
	this.instance_1.alpha = 0.0313;
	this.instance_1._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_1).wait(45).to({_off:false},0).to({scaleY:0.5508,rotation:0.0095,x:-7,alpha:1},5).to({scaleX:0.4029,scaleY:0.6072,rotation:0.0086,x:339.45},15).to({scaleX:0.3655,scaleY:0.2734,rotation:0.0064,x:422.85,alpha:0.0313},5).to({_off:true},1).wait(89));

	// text_nho
	this.instance_2 = new lib.ClipGroup();
	this.instance_2.parent = this;
	this.instance_2.setTransform(300,207.05,1,1,0,0,0,220.3,11.2);
	this.instance_2.alpha = 0;
	this.instance_2._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_2).wait(39).to({_off:false},0).to({y:229.05,alpha:1},3).wait(118));

	// flash0_ai
	this.instance_3 = new lib.Tween17("synched",0);
	this.instance_3.parent = this;
	this.instance_3.setTransform(300,129);
	this.instance_3.alpha = 0;
	this.instance_3._off = true;

	this.instance_4 = new lib.Tween18("synched",0);
	this.instance_4.parent = this;
	this.instance_4.setTransform(300,129);

	this.timeline.addTween(cjs.Tween.get({}).to({state:[]}).to({state:[{t:this.instance_3}]},39).to({state:[{t:this.instance_4}]},5).wait(116));
	this.timeline.addTween(cjs.Tween.get(this.instance_3).wait(39).to({_off:false},0).to({_off:true,alpha:1},5).wait(116));

	// Line
	this.instance_5 = new lib.Logo_line();
	this.instance_5.parent = this;
	this.instance_5.setTransform(300,129);

	this.timeline.addTween(cjs.Tween.get(this.instance_5).wait(160));

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(143.4,180.4,509.80000000000007,367.4);
// library properties:
lib.properties = {
	id: 'B16C40EBBD2C33479DC771A37441384E',
	width: 600,
	height: 300,
	fps: 24,
	color: "#000000",
	opacity: 0.00,
	manifest: [
		{src:"images/Bitmap1.png?1695634426264", id:"Bitmap1"},
		{src:"images/Bitmap1copy.png?1695634426264", id:"Bitmap1copy"},
		{src:"images/Bitmap11.png?1695634426264", id:"Bitmap11"},
		{src:"images/Bitmap2copy.png?1695634426264", id:"Bitmap2copy"},
		{src:"images/Bitmap3copy.png?1695634426264", id:"Bitmap3copy"},
		{src:"images/Bitmap5copy.png?1695634426264", id:"Bitmap5copy"}
	],
	preloads: []
};



// bootstrap callback support:

(lib.Stage = function(canvas) {
	createjs.Stage.call(this, canvas);
}).prototype = p = new createjs.Stage();

p.setAutoPlay = function(autoPlay) {
	this.tickEnabled = autoPlay;
}
p.play = function() { this.tickEnabled = true; this.getChildAt(0).gotoAndPlay(this.getTimelinePosition()) }
p.stop = function(ms) { if(ms) this.seek(ms); this.tickEnabled = false; }
p.seek = function(ms) { this.tickEnabled = true; this.getChildAt(0).gotoAndStop(lib.properties.fps * ms / 1000); }
p.getDuration = function() { return this.getChildAt(0).totalFrames / lib.properties.fps * 1000; }

p.getTimelinePosition = function() { return this.getChildAt(0).currentFrame / lib.properties.fps * 1000; }

an.bootcompsLoaded = an.bootcompsLoaded || [];
if(!an.bootstrapListeners) {
	an.bootstrapListeners=[];
}

an.bootstrapCallback=function(fnCallback) {
	an.bootstrapListeners.push(fnCallback);
	if(an.bootcompsLoaded.length > 0) {
		for(var i=0; i<an.bootcompsLoaded.length; ++i) {
			fnCallback(an.bootcompsLoaded[i]);
		}
	}
};

an.compositions = an.compositions || {};
an.compositions['B16C40EBBD2C33479DC771A37441384E'] = {
	getStage: function() { return exportRoot.getStage(); },
	getLibrary: function() { return lib; },
	getSpriteSheet: function() { return ss; },
	getImages: function() { return img; }
};

an.compositionLoaded = function(id) {
	an.bootcompsLoaded.push(id);
	for(var j=0; j<an.bootstrapListeners.length; j++) {
		an.bootstrapListeners[j](id);
	}
}

an.getComposition = function(id) {
	return an.compositions[id];
}


an.makeResponsive = function(isResp, respDim, isScale, scaleType, domContainers) {		
	var lastW, lastH, lastS=1;		
	window.addEventListener('resize', resizeCanvas);		
	resizeCanvas();		
	function resizeCanvas() {			
		var w = lib.properties.width, h = lib.properties.height;			
		var iw = window.innerWidth, ih=window.innerHeight;			
		var pRatio = window.devicePixelRatio || 1, xRatio=iw/w, yRatio=ih/h, sRatio=1;			
		if(isResp) {                
			if((respDim=='width'&&lastW==iw) || (respDim=='height'&&lastH==ih)) {                    
				sRatio = lastS;                
			}				
			else if(!isScale) {					
				if(iw<w || ih<h)						
					sRatio = Math.min(xRatio, yRatio);				
			}				
			else if(scaleType==1) {					
				sRatio = Math.min(xRatio, yRatio);				
			}				
			else if(scaleType==2) {					
				sRatio = Math.max(xRatio, yRatio);				
			}			
		}			
		domContainers[0].width = w * pRatio * sRatio;			
		domContainers[0].height = h * pRatio * sRatio;			
		domContainers.forEach(function(container) {				
			container.style.width = w * sRatio + 'px';				
			container.style.height = h * sRatio + 'px';			
		});			
		stage.scaleX = pRatio*sRatio;			
		stage.scaleY = pRatio*sRatio;			
		lastW = iw; lastH = ih; lastS = sRatio;            
		stage.tickOnUpdate = false;            
		stage.update();            
		stage.tickOnUpdate = true;		
	}
}


})(createjs = createjs||{}, AdobeAn = AdobeAn||{});
var createjs, AdobeAn;