function scrollbacktotop() {
    var st = $(this).scrollTop();
    if (st <= 80) {
        $('.back-top').hide();
    } else {
        $('.back-top').show();
    }
    lastScrollTop1 = st;
}
var lastScrollTop1 = 0;

const body = document.getElementsByTagName('header');
const main = document.getElementsByTagName('main');
const scrollDown = "fixed";
let lastScroll = 0;

function scrollmenu() {
    const currentScroll = $(this).scrollTop();
    let headerHeight = body[0].clientHeight;
    if (currentScroll <= headerHeight) {
        body[0].classList.remove(scrollDown);
        main[0].style.paddingTop = headerHeight + 'px';
        return;
    } else {
        body[0].classList.add(scrollDown);
        main[0].style.paddingTop = headerHeight + 'px';
    }
    lastScroll = currentScroll;
}

function checkHeader() {
    if (window.innerWidth <= 1199) {
        var header = $('header');
        var main = $('main');

        if (header.length > 0) {
            var headerH = header.outerHeight(true);
            document.documentElement.style.setProperty('--header', headerH + 'px');

            main.css({
                'padding-top': headerH + 'px'
            });
        }

    }
}
$(window).on('resize', checkHeader);
$(window).on('load', function() {
    /* Menu Moblie */

    $('nav#mmenu').mmenu({
        extensions: ['effect-slide-menu', 'pageshadow'],
        counters: true,
        navbar: {
            // title: i18n.site.danhmuc,
        },
        navbars: [{
                position: 'top',
                // content   : [ 'searchfield' ]
                // }, {
                content: ['prev', 'title', 'close'],
            },
            {
                position: 'bottom',
                content: [],
            },
        ],
    });
});


$(document).ready(function() {
    if ($('main').hasClass('page-home')) {
        $('main').parents('body').find('.header_menuall').addClass('header--home');
    } else {
        $('main').parents('body').find('.header_menuall').removeClass('header--home');
    }

    if ($('.news-result-slide').length > 0) {

        var partnerslide3 = new Swiper('.news-result-slide', {
            slidesPerView: 3,
            spaceBetween: 65,
            freeMode: false,

            navigation: {
                nextEl: ".swiper-button-next-news-result",
                prevEl: ".swiper-button-prev-news-result",
            },
            scrollbar: {
                el: ".swiper-scrollbar-news-result",
                hide: true,
            },
            breakpoints: {
                320: {
                    slidesPerView: 1.2,
                    spaceBetween: 14,
                },
                767: {
                    slidesPerView: 2.1,
                    spaceBetween: 20,
                },
                991: {
                    spaceBetween: 30,
                    slidesPerView: 3,
                },
                1200: {
                    spaceBetween: 65,
                },
            },
        });
    }
    if ($('.producthome-slide').length > 0) {

        var partnerslide3 = new Swiper('.producthome-slide', {
            slidesPerView: 6,
            spaceBetween: 25,
            freeMode: false,

            navigation: {
                nextEl: ".swiper-button-next-producthome",
                prevEl: ".swiper-button-prev-producthome",
            },
            pagination: {
                el: '.swiper-pagination-producthome',
                clickable: true,
            },
            breakpoints: {
                320: {
                    slidesPerView: 1.2,
                    spaceBetween: 14,
                },
                374: {
                    slidesPerView: 2,
                    spaceBetween: 14,
                },
                767: {
                    slidesPerView: 3,
                    spaceBetween: 20,
                },
                991: {
                    spaceBetween: 25,
                    slidesPerView: 4,
                },
                1200: {
                    slidesPerView: 6,
                    spaceBetween: 25,
                },
            },
        });
    }
    if ($('.store--slide').length > 0) {

        var partnerslide3 = new Swiper('.store--slide', {
            slidesPerView: 3,
            spaceBetween: 0,
            freeMode: false,

            navigation: {
                nextEl: ".swiper-button-next-store",
                prevEl: ".swiper-button-prev-store",
            },
            pagination: {
                el: '.swiper-pagination-store',
                clickable: true,
            },
            breakpoints: {
                320: {
                    slidesPerView: 2,
                },
                480: {
                    slidesPerView: 2.1,
                },
                992: {
                    slidesPerView: 3,
                }
            },
        });
    }
    if ($('.newshome--slide').length > 0) {

        var partnerslide3 = new Swiper('.newshome--slide', {
            slidesPerView: 3,
            spaceBetween: 0,
            freeMode: false,

            navigation: {
                nextEl: ".swiper-button-next-newshome",
                prevEl: ".swiper-button-prev-newshome",
            },
            pagination: {
                el: '.swiper-pagination-newshome',
                clickable: true,
            },
            breakpoints: {
                320: {
                    slidesPerView: 1,
                    spaceBetween: 10,
                },
                375: {
                    slidesPerView: 1.2,
                    spaceBetween: 12,
                },
                575: {
                    slidesPerView: 2,
                    spaceBetween: 20,
                },

                767: {
                    slidesPerView: 3,
                    spaceBetween: 25,
                },
                1200: {
                    spaceBetween: 55,
                }
            },
        });
    }
    if ($('.storesystem--slide').length > 0) {

        var partnerslide3 = new Swiper('.storesystem--slide', {
            slidesPerView: 3,
            spaceBetween: 20,
            freeMode: false,
            preloadImages: false,
            lazy: {
                loadPrevNext: true,
                loadOnTransitionStart: true,
            },

            navigation: {
                nextEl: ".swiper-button-next-storesystem",
                prevEl: ".swiper-button-prev-storesystem",
            },
            pagination: {
                el: '.swiper-pagination-storesystem',
                clickable: true,
            },
            breakpoints: {
                320: {
                    slidesPerView: 1,
                },
                575: {
                    slidesPerView: 2,
                },

                1200: {
                    slidesPerView: 3,
                },
            },
        });
    }
    if ($('.philosophy--slide').length > 0) {

        var partnerslide3 = new Swiper('.philosophy--slide', {
            slidesPerView: 3,
            spaceBetween: 69,
            freeMode: false,

            navigation: {
                nextEl: ".swiper-button-next-philosophy",
                prevEl: ".swiper-button-prev-philosophy",
            },
            pagination: {
                el: '.swiper-pagination-philosophy',
                clickable: true,
            },
            breakpoints: {
                320: {
                    slidesPerView: 1,
                    spaceBetween: 20,
                },
                575: {
                    slidesPerView: 2,
                    spaceBetween: 20,
                },
                767: {
                    spaceBetween: 20,
                },

                1200: {
                    slidesPerView: 3,
                    spaceBetween: 69,
                },
            },
        });
    }
    if ($('.prodsp--slide').length > 0) {

        var partnerslide3 = new Swiper('.prodsp--slide', {
            slidesPerView: 4,
            spaceBetween: 0,
            freeMode: false,

            navigation: {
                nextEl: ".swiper-button-next-prodsp",
                prevEl: ".swiper-button-prev-prodsp",
            },
            pagination: {
                el: '.swiper-pagination-prodsp',
                clickable: true,
            },
            breakpoints: {
                320: {
                    slidesPerView: 2,
                },
                767: {
                    slidesPerView: 3,
                },
                1200: {
                    slidesPerView: 4,
                },
            },
        });
    }
    if ($('.dowryjewelry--slide').length > 0) {

        var partnerslide3 = new Swiper('.dowryjewelry--slide', {
            slidesPerView: 1,
            spaceBetween: 0,
            freeMode: false,

            navigation: {
                nextEl: ".swiper-button-next-dowryjewelry",
                prevEl: ".swiper-button-prev-dowryjewelry",
            },
            pagination: {
                el: '.swiper-pagination-dowryjewelry',
                clickable: true,
            },
        });
    }
    if ($('.bannerslide--slide').length > 0) {

        var partnerslide3 = new Swiper('.bannerslide--slide', {
            slidesPerView: 1,
            spaceBetween: 0,
            freeMode: false,

            navigation: {
                nextEl: ".swiper-button-next-bannerslide",
                prevEl: ".swiper-button-prev-bannerslide",
            },
            pagination: {
                el: '.swiper-pagination-bannerslide',
                clickable: true,
            },
        });
    }
    if ($('.bannerslide--slide2').length > 0) {

        var partnerslide3 = new Swiper('.bannerslide--slide2', {
            slidesPerView: 1,
            spaceBetween: 0,
            freeMode: false,

            navigation: {
                nextEl: ".swiper-button-next-bannerslide2",
                prevEl: ".swiper-button-prev-bannerslide2",
            },
            pagination: {
                el: '.swiper-pagination-bannerslide2',
                clickable: true,
            },
        });
    }
    if ($('.collections--slide').length > 0) {

        var partnerslide3 = new Swiper('.collections--slide', {
            slidesPerView: 1,
            spaceBetween: 0,
            freeMode: false,
            loop: true,

            navigation: {
                nextEl: ".swiper-button-next-collections",
                prevEl: ".swiper-button-prev-collections",
            },
            pagination: {
                el: '.swiper-pagination-collections',
                clickable: true,
            },
        });
    }
    if ($('.pricegold--slide').length > 0) {
        var partnerslide3 = new Swiper('.pricegold--slide', {
            slidesPerView: 1,
            spaceBetween: 0,
            freeMode: false,

            navigation: {
                nextEl: ".swiper-button-next-pricegold",
                prevEl: ".swiper-button-prev-pricegold",
            },
            pagination: {
                el: '.swiper-pagination-pricegold',
                clickable: true,
            },
        });
    }
    if ($('.productintroduction--slide').length > 0) {

        var partnerslide3 = new Swiper('.productintroduction--slide', {
            slidesPerView: 1,
            spaceBetween: 0,
            freeMode: false,

            navigation: {
                nextEl: ".swiper-button-next-productintroduction",
                prevEl: ".swiper-button-prev-productintroduction",
            },
            pagination: {
                el: '.swiper-pagination-productintroduction',
                clickable: true,
            },
        });
    }
    if ($('.historyestablishment--list').length > 0) {

        var swiper = new Swiper(".historyestablishment-slidethumbs", {
            spaceBetween: 28,
            slidesPerView: 'auto',
            freeMode: true,
            watchSlidesProgress: true,
            breakpoints: {
                320: {
                    spaceBetween: 10,
                },
                767: {
                    spaceBetween: 28,
                },
            },
        });
        var swiper2 = new Swiper(".historyestablishment-slide", {
            spaceBetween: 0,
            slidesPerView: 1,
            navigation: {
                nextEl: ".swiper-button-next-historyestablishment",
                prevEl: ".swiper-button-prev-historyestablishment",
            },
            thumbs: {
                swiper: swiper,
            },
        });
    }
    // if ($('.product-tabs--menu').length > 0) {
    //     jQuery('.scrollbar-outer').scrollbar();
    // }
    if ($('.flexible_counter').length > 0) {
        $('.counter').countUp({
            'time': 1000,
            'delay': 5
        });
    }
    if ($('.btn-opfillter--mb').length > 0) {
        $('.btn-opfillter--mb').click(function() {
            $('.product-tabs--menu ').addClass('active');
        })
    }
    if ($('.btn-closefillter--mb').length > 0) {
        $('.btn-closefillter--mb').click(function() {
            $('.product-tabs--menu ').removeClass('active');
        })
    }
    if (window.innerWidth <= 991) {
        if ($('.product-tabs--title').length > 0) {
            $('.items--tabsbody').hide();
            $('.product-tabs--title').click(function() {
                $(this).next().toggle();
            })
        }

    }



    $('.hamburger-menu').on('click', function() {
        // $('.bar').toggleClass('animate');
        $('.mobile-menu').toggleClass('active');
        $('.bg_overlay').toggleClass('active');
        return false;
    })
    $('.has-children').on('click', function() {
        $(this).children('ul').slideToggle('slow', 'swing');
        $('.icon-arrow').toggleClass('open');
    });
    $('#closeButton').on('click', function(e) {
        $('.mobile-menu').removeClass('active');
        $('.bg_overlay').removeClass('active');
    });
    var menuTarget = $('.mobile-menu');

    $(document).mouseup(function(e) {
        if (!menuTarget.is(e.target) && !menuTarget.has(e.target).length) {
            $('.mobile-menu,.bg_overlay').removeClass('active');
        }
    });

    checkHeader();
    scrollbacktotop();
    scrollmenu();
    $(window).scroll(function() {
        scrollbacktotop();
        scrollmenu();
    });
    $('.back-top').click(function() {
        $("html, body").animate({ scrollTop: 0 }, "slow");
    });
    if ($('header').hasClass('header-static')) {
        var headerH = $('header').outerHeight();
        $('main').css({
            'margin-top': headerH + 'px'
        });
    }
    document.addEventListener('aos:in', ({ detail }) => {
        //  console.log(detail.classList.value.indexOf('count-box'), 'animation to display element starts');
        detail.classList.value.indexOf('count-box') == 0 ? countUp() : '';
    });

    document.addEventListener('aos:out', ({ detail }) => {
        //  console.log(detail.classList.value.indexOf('count-box'), 'animation to display element end');
        detail.classList.value.indexOf('count-box') == 0 ? countDown() : '';
    });

    $('.link-search').click(function() {
        $('.box-search').toggle('show');
    });

});
$(document).ready(function() {


    $('.search-head-ic').click(function() {
        if ($(this).hasClass('active')) {
            $(this).removeClass('active');
            $('.box-search').removeClass('active');
        } else {
            $(this).addClass('active');
            $('.box-search').addClass('active');
            $('.keyword-search').focus();

        }
    })

    if ($('.datepicker').length > 0) {
        $("#date_contact").datepicker({
            dateFormat: "dd-mm-yy",
            //            changeMonth: true,
            //            changeYear: true,
            yearRange: "c-0:c+10"
        });
    }
    if ($('.icon-mess').length > 0) {
        $('.icon-mess-other').hide();
        $('.mess-op').click(function() {
            $(this).next().toggle();
        })
    }

    $(window).on('load', function() {
        setTimeout(function() {
            ShowLichKham()
        }, 60000);
    });

    $('.minus').click(function() {
        var $input = $(this).parent().find('input');
        var count = parseInt($input.val()) - 1;
        if (!parseInt(count)) {
            count = 1;
        }
        count = count < 1 ? 1 : count;
        if (count <= 9 && count > 0) {
            count = '0' + count
        }
        $input.val(count);
        $input.change();
        return false;
    });
    $('.plus').click(function() {
        var $input = $(this).parent().find('input');
        var count = parseInt($input.val()) + 1;
        if (!parseInt(count)) {
            count = 1;
        }
        count = count < 1 ? 1 : count;
        if (count <= 9 && count > 0) {
            count = '0' + count
        }
        $input.val(count);
        $input.change();
        return false;
    });


});
$(document).ready(function() {
});



$('body').click(function(e) {
    var target = $(e.target);
    if (!target.is('.search-head-ic,.keyword-search')) {
        $('.search-head-ic').removeClass('active');
        $('.box-search').removeClass('active');
    }
});


function ShowMakeQuestion() {
    $("#MakeQuestion").modal("show");
}

function ShowLichKham() {
    $("#dat-lich-kham").modal("show");
}
// if ($(window).width() > 767) {
AOS.init({
    startEvent: 'load',
    duration: 500,
    easing: 'linear',
    speed: 10000,
});

function showModalBook() {
    $("#modal--book").modal("show");
}

function showModalBooksuccess() {
    $("#modal--book").modal("hide");
    $("#modal--book--success").modal("show");
}

function playVideo(embedLink, link = 'https://www.youtube.com/embed/RdYgHs0fGKM?autoplay=1&rel=0&showinfo=0&controls=0') {
    if (embedLink) {
        let container = event.currentTarget.closest('.video--other');
        container.removeAttribute('onclick');
        // if (embedLink.includes('youtube.com')) {
        container.innerHTML = `
                    <iframe width="100%" height="318" src="${link}" frameborder="0"
                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                        referrerpolicy="strict-origin-when-cross-origin" allowfullscreen>
                    </iframe>`;
        // } else {
        // container.innerHTML = `
        //                 <video width="100%" height="318" controls autoplay>
        //                     <source src="images/video.mp4" type="video/mp4">Your browser does not support HTML video.
        //                 </video>
        //             `;
        // }
    }
}

// function playVideo(embedLink) {
//     if (embedLink) {
//         let container = event.currentTarget.closest('.video--other');
//         container.removeAttribute('onclick');
//         if (embedLink.includes('youtube.com')) {
//             container.innerHTML = `
//                         <iframe width="100%" height="318" src="${embedLink}?autoplay=1&rel=0&showinfo=0&controls=0" frameborder="0"
//                             allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
//                             referrerpolicy="strict-origin-when-cross-origin" allowfullscreen>
//                         </iframe>`;
//         } else {
//             container.innerHTML = `
//                         <video width="100%" height="318" controls autoplay>
//                             <source src="${embedLink}" type="video/mp4">Your browser does not support HTML video.
//                         </video>
//                     `;
//         }
//     }
// }