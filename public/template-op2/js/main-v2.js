$(document).ready(function() {
    if ($('.projects_new_slide').length) {
        var swiper_1 = new Swiper('.projects_new_slide', {
            slidesPerView: 4,
            spaceBetween: 30,
            pagination: {
                el: '.swiper-pagination-other',
                clickable: true,
            },
            navigation: {
                nextEl: '.swiper-button-next-other',
                prevEl: '.swiper-button-prev-other',
            },
            breakpoints: {
                320: {
                    slidesPerView: 1.3,
                },
                480: {
                    slidesPerView: 2,
                },
                767: {
                    slidesPerView: 2,
                },
                992: {
                    slidesPerView: 3,
                }
            },
        });
    }
    if ($('.productsflex_slide').length) {
        var swiper_1 = new Swiper('.productsflex_slide', {
            slidesPerView: 3,
            spaceBetween: 30,
            pagination: {
                el: '.swiper-pagination-productsflex',
                clickable: true,
            },
            navigation: {
                nextEl: '.swiper-button-next-productsflex',
                prevEl: '.swiper-button-prev-productsflex',
            },
            breakpoints: {
                320: {
                    slidesPerView: 1,
                },
                480: {
                    slidesPerView: 2,
                },
                767: {
                    slidesPerView: 2,
                },
                992: {
                    slidesPerView: 2.4,
                }
            },
        });
    }
});