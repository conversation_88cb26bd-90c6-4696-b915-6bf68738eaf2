<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <link rel="shortcut icon" async href="images/common/favicon.png" />
  <title>Website Bảo Tín Mạnh Hải</title>
  <link rel="stylesheet" href="libs/bootstrap/bootstrap.min.css" />
  <link rel="stylesheet" href="libs/swiper/swiper-bundle.min.css" />
  <link rel="stylesheet" href="libs/fancybox/jquery.fancybox.min.css" />
  <link href="libs/flatpickr/flatpickr.min.css" rel="stylesheet" />
  <link rel="stylesheet" href="libs/aos/aos.css" />
  <link rel="stylesheet" href="libs/fancybox/jquery.fancybox.min.css" />
  <link rel="stylesheet" href="css/style.css" />
</head>

<body>
  <div id="app">
    <myheader></myheader>
    <main>
      <div class="master-wrapper">
        <div class="cart-page">
          <div class="container">
            <div class="cart-page-container">

              <div class="block-cart-title mb-4">
                <a href="javascript:;" class="btn-action">
                  <img src="images/ic-chevron-left.svg" alt="" class="mr-3">
                  <span>Tiếp tục mua hàng</span>
                </a>

                <h2 class="title">
                  Giỏ hàng
                </h2>
              </div>

              <div class="block-cart-content">
                <div class="incart-list-item">
                  <div class="incart-item">
                    
                    <div class="incart-item-wrap">
                      <a href="javascript:;" class="img">
                        <img src="images/product/trangsuc-daquy.jpg" alt="" class="img-fluid">
                      </a>
                      <div class="info">
                          <a href="javascript:;" class="product-name">
                              Nhẫn vàng phong cách Ý NY10K30
                          </a>
                          <div class="product-sku">
                            <span class="title">
                              Mã sản phẩm : NY10K30
                            </span> 
                          </div>
                          <div class="price-box">
                            <div class="product-price">
                              <span class="title">
                                Giá :
                              </span>
                              <span class="color-theme value">
                                2.500.000 VNĐ
                              </span>
                            </div>

                            <div class="cart-qty">
                              <span class="new-cart-quantity js-row-item">
                                  <a href="javascript:void(0);" class="minor quantity-change disallowed" data-value="-1">
                                    <img src="images/ic-minor.png" alt="" width="20" height="20">
                                  </a>
                                  <input class="buy-quantity quantity-change input-number" value="01" size="5" data-value="1"/>
                                  <a href="javascript:void(0);" class="add quantity-change" data-value="1">
                                    <img src="images/ic-plus.png" alt="" width="20" height="20">
                                  </a>
                              </span>
                            </div>
                          </div>
                          <div class="product-config">
                            <div class="box-title">
                              <span class="title">
                                Chọn kích cỡ
                              </span>
                            </div>
                            <div class="config-list">
                              <a href="javascript:;" class="config-item js-config-item disabled">
                                09
                              </a>
                              <a href="javascript:;" class="config-item js-config-item">
                                10
                              </a>
                              <a href="javascript:;" class="config-item js-config-item">
                                11
                              </a>
                              <a href="javascript:;" class="config-item js-config-item">
                                12
                              </a>
                              <a href="javascript:;" class="config-item js-config-item">
                                13
                              </a>
                            </div>
                          </div>
                          <div class="actions">
                              <a href="" class="del-item">
                                <img src="images/ic-trash.png" alt="" width="20" height="20">
                                <span class="txt">
                                  Xóa
                                </span>
                              </a>
                          </div>
                      </div>
                  </div>
                  </div>
                  <div class="incart-item">

                      <div class="incart-item-wrap">
                          <a href="javascript:;" class="img">
                            <img src="images/product/trangsuc-daquy.jpg" alt="" class="img-fluid">
                          </a>
                          <div class="info">
                              <a href="javascript:;" class="product-name">
                                  Nhẫn vàng phong cách Ý NY10K30
                              </a>
                              <div class="product-sku">
                                <span class="title">
                                  Mã sản phẩm : NY10K30
                                </span> 
                              </div>
                              <div class="price-box">
                                <div class="product-price">
                                  <span class="title">
                                    Giá :
                                  </span>
                                  <span class="color-theme value">
                                    2.500.000 VNĐ
                                  </span>
                                </div>

                                <div class="cart-qty">
                                  <span class="new-cart-quantity js-row-item">
                                      <a href="javascript:void(0);" class="minor quantity-change disallowed" data-value="-1">
                                        <img src="images/ic-minor.png" alt="" width="20" height="20">
                                      </a>
                                      <input class="buy-quantity quantity-change input-number" value="01" size="5" />
                                      <a href="javascript:void(0);" class="add quantity-change" data-value="1">
                                        <img src="images/ic-plus.png" alt="" width="20" height="20">
                                      </a>
                                  </span>
                                </div>
                              </div>
                              <div class="product-config">
                                <div class="box-title">
                                  <span class="title">
                                    Chọn kích cỡ
                                  </span>
                                </div>
                                <div class="config-list">
                                  <a href="javascript:;" class="config-item js-config-item disabled">
                                    09
                                  </a>
                                  <a href="javascript:;" class="config-item js-config-item">
                                    10
                                  </a>
                                  <a href="javascript:;" class="config-item js-config-item">
                                    11
                                  </a>
                                  <a href="javascript:;" class="config-item js-config-item">
                                    12
                                  </a>
                                  <a href="javascript:;" class="config-item js-config-item">
                                    13
                                  </a>
                                </div>
                              </div>
                              <div class="actions">
                                <a href="" class="del-item">
                                  <img src="images/ic-trash.png" alt="" width="20" height="20">
                                  <span class="txt">
                                    Xóa
                                  </span>
                                </a>
                            </div>
                          </div>
                      </div>
                      
                      <div class="special-offers special-offer-outside">
                        <div class="title">
                          Ưu đãi
                        </div>
                        <div class="special-offer-item">
                          <div class="item">
                            Giảm 30% từ 25/05/2022 đến 15/06/2022
                          </div>
                          <div class="item">
                            Nhận ngay phiếu mua hàng trị giá 200.000đ khi mua từ 2 sản phẩm trở lên từ 25/05/2022 đến
                            15/06/2022
                          </div>
                        </div>
                      </div>

                  </div>

                </div>
              </div>
              <div class="block-cart-actions">
                <div class="text-right">
                  <div class="cart-total-price">
                      Tổng tiền : 
                      <span id="total-amount" class="big-txt">
                        5.000.000 VNĐ
                      </span>
                  </div>
                </div>

                <div class="block-cart-actions-wrap">
                  <a href="javascript:;" class="btn-theme add-now ">
                    <span class="txt">
                      Tiến hành đặt hàng

                    </span>
                  </a>
                  <a href="javascript:;" class="btn-theme">
                    Chọn thêm sản phẩm khác
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
    <myfooter></myfooter>
  </div>

  <script type="text/javascript" src="js/vue.js"></script>
  <script type="text/javascript" src="js/httpvueloader.js"></script>
  <script type="text/javascript" src="libs/swiper/swiper-bundle.min.js"></script>
  <script type="text/javascript" src="libs/swiper/swiper-animation.min.js"></script>
  <script type="text/javascript" src="libs/bootstrap/jquery.min.js"></script>
  <script type="text/javascript" src="libs/bootstrap/popper.min.js"></script>
  <script type="text/javascript" src="libs/bootstrap/bootstrap.min.js"></script>
  <script type="text/javascript" src="libs/aos/aos.js"></script>
  <script type="text/javascript" src="libs/chartjs/chart.min.js"></script>
  <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.27.0/moment.min.js" integrity="sha512-rmZcZsyhe0/MAjquhTgiUcb4d9knaFc7b5xAfju483gbEXTkeJRUMIPk6s3ySZMYUHEcjKbjLjyddGWMrNEvZg==" crossorigin="anonymous"></script> -->
  <script src="libs/chartjs/chartjs-adapter-fns.min.js"></script>

  <script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@11.0.6/dist/lazyload.min.js"></script>
  <script type="text/javascript" src="libs/fancybox/jquery.fancybox.min.js"></script>
  <script type="text/javascript" src="js/main.js"></script>

  <script type="text/javascript">
    var app = new Vue({
      el: '#app',
      data() {
        return {
          flashSaleEndTime: 80000,
          activeItem: 'tab-1',
        };
      },
      components: {
        myheader: httpVueLoader('vue_components/header.vue'),
        myfooter: httpVueLoader('vue_components/footer.vue'),
        rednavi: httpVueLoader('vue_components/red-slide-navigation.vue'),
        yellownavi: httpVueLoader('vue_components/yellow-slide-navigation.vue'),
      },
      methods: {
        isActive(menuItem) {
          return this.activeItem === menuItem;
        },
        setActive(menuItem) {
          this.activeItem = menuItem;
        },
      },
      mounted() {
        this.$nextTick(() => {
          $(window).bind('load', function () {
            if (LazyLoad) {
              var lazyloader = new LazyLoad({
                elements: '.lazy',
              });
            }

            AOS.init({
              // startEvent: 'load',
              duration: 700,
              easing: 'ease',
            });

            qtyChange('.quantity-change')
          });
        });
      },
    });
  </script>
</body>

</html>