<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <link rel="shortcut icon" async href="images/common/favicon.png" />
  <title>Website Bảo Tín Mạnh Hải</title>
  <link rel="stylesheet" href="libs/bootstrap/bootstrap.min.css" />
  <link rel="stylesheet" href="libs/swiper/swiper-bundle.min.css" />
  <link rel="stylesheet" href="libs/fancybox/jquery.fancybox.min.css" />
  <link href="libs/flatpickr/flatpickr.min.css" rel="stylesheet" />
  <link rel="stylesheet" href="libs/aos/aos.css" />
  <link rel="stylesheet" href="libs/fancybox/jquery.fancybox.min.css" />
  <link rel="stylesheet" href="css/style.css" />
</head>

<body>
  <div id="app">
    <myheader></myheader>
    <main>
      <div class="master-wrapper">
        <div class="home-banner">
          <div class="swiper-container banner-slider">
            <div class="swiper-wrapper">
              <div class="swiper-slide">
                <img class="img-fluid swiper-lazy" src="images/home/<USER>" alt="" />
                <div class="swiper-lazy-preloader"></div>
              </div>
              <div class="swiper-slide">
                <img class="img-fluid swiper-lazy" src="images/home/<USER>" alt="" />
                <div class="swiper-lazy-preloader"></div>
              </div>
            </div>

            <div class="swiper-pagination"></div>

            <rednavi></rednavi>
          </div>
        </div>

        <div class="flashsale-row-container">
          <div class="container">
            <div class="flashsale-row d-flex align-items-center">
              <div class="flashsale-item flash-sale d-flex align-items-center">
                <img class="img-fluid flash-img lazy" data-src="images/common/flash-sale.png"
                  src="images/loading-spinner.gif" alt="" />

                <div class="sale-counter d-flex align-items-center">
                  <div class="counter-title d-flex align-items-center">
                    <img class="img-fluid lazy" data-src="images/common/sale-clock.png" src="images/loading-spinner.gif"
                      alt="" />
                    <p>KẾT THÚC TRONG</p>
                  </div>
                  <div class="counter-text d-flex align-items-center">
                    <span class="hours" id="flashsale-hours">01</span>
                    <span class="mins" id="flashsale-mins">01</span>
                    <span class="sec" id="flashsale-sec">01</span>
                  </div>
                </div>
              </div>
              <div class="flashsale-item flashsale-text highlight-b">
                <p>TRẢ GÓP <span>0%</span> LÃI SUẤT</p>
              </div>
              <div class="flashsale-item flashsale-text">
                <p>MUA ONLINE - <span>GIAO HÀNG 5H</span></p>
              </div>
              <div class="flashsale-item flashsale-text">
                <p>CAM KẾT Ý 10K - <span>RẺ NHẤT</span></p>
              </div>
            </div>
          </div>
        </div>

        <div class="home-chart">
          <div class="container">
            <div class="chart-container-row d-flex">
              <div class="chart-container container-l">
                <div class="chart-title title-w-deco">
                  <h4>THỊ TRƯỜNG VÀNG</h4>
                  <div class="title-deco"></div>
                </div>
                <div class="chart-body">
                  <div class="table-responsive gold-table">
                    <p class="unit">Đơn vị tính: Đồng/chỉ</p>
                    <table class="gold-table-content">
                      <thead>
                        <tr>
                          <th>LOẠI VÀNG</th>
                          <th>MUA VÀO</th>
                          <th>BÁN RA</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td>Kim Dần Niên</td>
                          <td class="price price-up">
                            5.456.000

                            <img class="img-fluid" src="images/common/price-up.png" alt="" />
                          </td>
                          <td class="price price-up">
                            5.621.000

                            <img class="img-fluid" src="images/common/price-up.png" alt="" />
                          </td>
                        </tr>
                        <tr>
                          <td>Vàng miến SJC</td>
                          <td class="price price-down">
                            6.911.000

                            <img class="img-fluid" src="images/common/price-down.png" alt="" />
                          </td>
                          <td class="price price-down">
                            6.984.000

                            <img class="img-fluid" src="images/common/price-down.png" alt="" />
                          </td>
                        </tr>
                        <tr>
                          <td>Nhẫn ép vỉ Vàng Rồng Thăng Long</td>
                          <td class="price">5.451.000</td>
                          <td class="price">5.526.000</td>
                        </tr>
                        <tr>
                          <td>Nhẫn ép vỉ Vàng Rồng Thăng Long</td>
                          <td class="price">5.451.000</td>
                          <td class="price">5.526.000</td>
                        </tr>
                        <tr>
                          <td>Nhẫn ép vỉ Vàng Rồng Thăng Long</td>
                          <td class="price">5.451.000</td>
                          <td class="price">5.526.000</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
                <div class="chart-footer">
                  <p class="note">(Cập nhật lúc 10:48 16/08/2022) (đơn vị tính: đồng/chỉ)</p>
                </div>
              </div>
              <div class="chart-container container-r">
                <div class="chart-title title-w-deco">
                  <h4>BIỂU ĐỒ GIÁ VÀNG</h4>
                  <div class="title-deco"></div>
                </div>
                <div class="chart-body">
                  <div class="js-chart-top d-flex">
                    <form action="">
                      <p>Mã vàng:</p>
                      <select>
                        <option value="KDN">Kim Dần Niên</option>
                        <option value="KGB">Nhẫn ép vỉ Kim Gia Bảo</option>
                        <option value="999">Vàng 99.9</option>
                        <option value="VRTL">Nhẫn ép vỉ Vàng Rồng Thăng Long</option>
                        <option value="SJC">Vàng miếng SJC</option>
                        <option value="9999">Vàng 999.9</option>
                      </select>
                    </form>
                    <div class="chart-note">
                      <div class="note note-buy">
                        <span></span>
                        <p>Mua</p>
                      </div>
                      <div class="note note-sell">
                        <span></span>
                        <p>Bán</p>
                      </div>
                    </div>
                  </div>
                  <canvas id="myChart"></canvas>
                </div>

                <div class="chart-footer">
                  <form action="">
                    <p>Xem theo:</p>
                    <div class="custom-radio custom-control-inline">
                      <input type="radio" id="customRadioInline1" name="customRadioInline1"
                        class="custom-control-input chart-radio" checked="checked" value="day" />
                      <label class="custom-label" for="customRadioInline1">24 giờ</label>
                    </div>
                    <div class="custom-radio custom-control-inline">
                      <input type="radio" id="customRadioInline2" name="customRadioInline1"
                        class="custom-control-input chart-radio" value="week" />
                      <label class="custom-label" for="customRadioInline2">Tuần</label>
                    </div>
                    <div class="custom-radio custom-control-inline">
                      <input type="radio" id="customRadioInline3" name="customRadioInline1"
                        class="custom-control-input chart-radio" value="month" />
                      <label class="custom-label" for="customRadioInline3">Tháng</label>
                    </div>
                    <div class="custom-radio custom-control-inline">
                      <input type="radio" id="customRadioInline4" name="customRadioInline1"
                        class="custom-control-input chart-radio" value="year" />
                      <label class="custom-label" for="customRadioInline4">Năm</label>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="home-section home-category">
          <div class="container">
            <div class="section-title title-w-deco">
              <h4>Danh mục sản phẩm</h4>
              <div class="title-deco"></div>
            </div>

            <div class="category-list d-flex">
              <div class="categogy-item" v-for="n in 12">
                <a href="javascript:;">
                  <img class="img-fluid cate-img lazy" data-src="images/home/<USER>" alt=""
                    src="images/loading-spinner.gif" />
                  <p class="cate-text">NHẪN</p>
                </a>
              </div>
            </div>
          </div>
        </div>

        <div class="home-section home-sanpham">
          <div class="sanpham-bl sanpham-noibat">
            <div class="container">
              <div class="section-title title-w-deco">
                <h4>sản phẩm nổi bật</h4>
                <div class="title-deco"></div>
              </div>

              <!-- <div class="sanpham-tabs d-flex" id="myTab" role="tablist">
                <a href="#tab-1" class="tabs-link" @click.prevent="setActive('tab-1')" :class="{ active: isActive('tab-1') }">
                  <div class="link-bg"></div>
                  <div class="link-text">Trang sức phong cách Ý</div>
                </a>
                <a href="#tab-2" class="tabs-link" @click.prevent="setActive('tab-2')" :class="{ active: isActive('tab-2') }">
                  <div class="link-bg"></div>
                  <div class="link-text">Trang sức phong cách Hàn quốc</div>
                </a>
                <a href="#tab-3" class="tabs-link" @click.prevent="setActive('tab-3')" :class="{ active: isActive('tab-3') }">
                  <div class="link-bg"></div>
                  <div class="link-text">Trang sức vàng tây đá màu</div>
                </a>
                <a href="#tab-4" class="tabs-link" @click.prevent="setActive('tab-4')" :class="{ active: isActive('tab-4') }">
                  <div class="link-bg"></div>
                  <div class="link-text">Nhẫn cưới</div>
                </a>
              </div> -->

              <ul class="sanpham-tabs nav nav-tabs" id="myTab" role="tablist">
                <li class="nav-item">
                  <a class="nav-link active" id="tab-1" data-toggle="tab" href="#tab-1-content" role="tab"
                    aria-controls="tab-1" aria-selected="true">
                    <div class="link-bg"></div>
                    <div class="link-text">Trang sức phong cách Ý</div>
                  </a>
                </li>
                <li class="nav-item">
                  <a class="nav-link" id="tab-2" data-toggle="tab" href="#tab-2-content" role="tab"
                    aria-controls="tab-2" aria-selected="false">
                    <div class="link-bg"></div>
                    <div class="link-text">Trang sức phong cách Hàn quốc</div>
                  </a>
                </li>
                <li class="nav-item">
                  <a class="nav-link" id="tab-3" data-toggle="tab" href="#tab-3-content" role="tab"
                    aria-controls="tab-3" aria-selected="false">
                    <div class="link-bg"></div>
                    <div class="link-text">Trang sức vàng tây đá màu</div>
                  </a>
                </li>
                <li class="nav-item">
                  <a class="nav-link" id="tab-4" data-toggle="tab" href="#tab-4-content" role="tab"
                    aria-controls="tab-4" aria-selected="false">
                    <div class="link-bg"></div>
                    <div class="link-text">Nhẫn cưới</div>
                  </a>
                </li>
              </ul>

              <div class="tab-content" id="myTabContent">
                <div class="tab-pane fade show active" id="tab-1-content" role="tabpanel" aria-labelledby="tab-1">
                  <div class="sanpham-slider sp-slider1">
                    <div class="swiper-container sanpham-slide">
                      <div class="swiper-wrapper">
                        <div class="swiper-slide" v-for="n in 6">
                          <div class="sp-item">
                            <div class="sp-item-img">
                              <a href="javascript:;">
                                <img class="img-fluid swiper-lazy" data-src="images/home/<USER>" alt="" />
                                <div class="swiper-lazy-preloader"></div>
                              </a>
                            </div>
                            <div class="sp-item-body">
                              <a href="javascript:;">Nhẫn gắn đá màu MS05</a>
                              <p class="price">500.000đ</p>
                              <span class="old-price">500.000đ</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <yellownavi></yellownavi>
                  </div>
                </div>
                <div class="tab-pane fade" id="tab-2-content" role="tabpanel" aria-labelledby="tab-2">Chuưa có nội
                  dung</div>
                <div class="tab-pane fade" id="tab-3-content" role="tabpanel" aria-labelledby="tab-3">Chuưa có nội
                  dung</div>
                <div class="tab-pane fade" id="tab-4-content" role="tabpanel" aria-labelledby="tab-4">Chuưa có nội
                  dung</div>
              </div>
            </div>
          </div>

          <div class="sanpham-bl sanpham-video">
            <div class="sp-video-container">
              <a class="video-thumb" href="https://youtu.be/jfKfPfyJRdk" data-fancybox="video">
                <img class="img-fluid img-base lazy" data-src="images/home/<USER>"
                  src="images/loading-spinner.gif" alt="" />

                <div class="play-btn">
                  <img class="img-fluid play-btn" src="images/common/play-btn.png" alt="" />
                </div>
              </a>
              <!-- <video controls>
                <source src="images/common/test-video.mp4" type="video/mp4">
              </video> -->
            </div>
            <div class="sp-video-content text-center">
              <h4 class="sp-title">bộ sưu tập <span>rose in love</span></h4>
              <a href="javascript:;" class="sp-link">
                Khám phá ngay
                <img class="img-fluid" src="images/common/arrow-right.png" alt="" />
              </a>
            </div>
          </div>

          <div class="sanpham-bl sanpham-banner-nolink">
            <div class="container">
              <div class="banner-nolink-container">
                <a href="javascript:;" class="img">
                  <img class="img-fluid lazy" data-src="images/home/<USER>" alt=""
                    src="images/loading-spinner.gif" />
                </a>
                <div class="banner-nolink-name">
                  <div class="nolink-name--inner">
                    <h4 class="sp-title">trang sức <span>italy</span></h4>
                    <p>Trang sức thể hiện phong cách của bạn</p>
                  </div>
                </div>
              </div>

              <div class="sanpham-slider sp-slider2">
                <div class="swiper-container sanpham-slide">
                  <div class="swiper-wrapper">
                    <div class="swiper-slide" v-for="n in 6">
                      <div class="sp-item">
                        <div class="sp-item-img">
                          <a href="javascript:;">
                            <img class="img-fluid swiper-lazy" data-src="images/home/<USER>" alt="" />
                            <div class="swiper-lazy-preloader"></div>
                          </a>
                        </div>
                        <div class="sp-item-body">
                          <a href="javascript:;">Nhẫn gắn đá màu MS05</a>
                          <p class="price">500.000đ</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <yellownavi></yellownavi>
              </div>
            </div>
          </div>

          <div class="sanpham-bl sanpham-banner-w-link">
            <div class="container">
              <div class="banner-w-link-container">
                <a href="javascript:;" class="img">
                  <img class="img-fluid lazy" data-src="images/home/<USER>" alt=""
                    src="images/loading-spinner.gif" />
                </a>
              </div>

              <div class="sanpham-slider sp-slider3">
                <div class="swiper-container sanpham-slide">
                  <div class="swiper-wrapper">
                    <div class="swiper-slide" v-for="n in 6">
                      <div class="sp-item">
                        <div class="sp-item-img">
                          <a href="javascript:;">
                            <img class="img-fluid swiper-lazy" data-src="images/home/<USER>" alt="" />
                            <div class="swiper-lazy-preloader"></div>
                          </a>
                        </div>
                        <div class="sp-item-body">
                          <a href="javascript:;">Nhẫn gắn đá màu MS05</a>
                          <p class="price">500.000đ</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <yellownavi></yellownavi>
              </div>
            </div>
          </div>
          <div class="sanpham-bl sanpham-banner-w-link">
            <div class="container">
              <div class="banner-w-link-container">
                <a href="javascript:;" class="img">
                  <img class="img-fluid lazy" data-src="images/home/<USER>" alt=""
                    src="images/loading-spinner.gif" />
                </a>
              </div>

              <div class="sanpham-slider sp-slider3">
                <div class="swiper-container sanpham-slide">
                  <div class="swiper-wrapper">
                    <div class="swiper-slide" v-for="n in 6">
                      <div class="sp-item">
                        <div class="sp-item-img">
                          <a href="javascript:;">
                            <img class="img-fluid swiper-lazy" data-src="images/home/<USER>" alt="" />
                            <div class="swiper-lazy-preloader"></div>
                          </a>
                        </div>
                        <div class="sp-item-body">
                          <a href="javascript:;">Nhẫn gắn đá màu MS05</a>
                          <p class="price">500.000đ</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <yellownavi></yellownavi>
              </div>
            </div>
          </div>
          <div class="sanpham-bl sanpham-banner-w-link side-banner">
            <div class="container">
              <div class="side-banner-wrapper d-flex">
                <div class="side-wrapper side-l">
                  <div class="banner-w-link-container">
                    <a href="javascript:;" class="img">
                      <img class="img-fluid" data-src="images/home/<USER>" alt=""
                        src="images/loading-spinner.gif" />
                    </a>
                  </div>
                </div>
                <div class="side-wrapper side-r">
                  <div class="side-sp-gr d-flex">
                    <div class="sp-item" v-for="n in 4">
                      <div class="sp-item-img">
                        <a href="javascript:;">
                          <img class="img-fluid lazy" data-src="images/home/<USER>" alt=""
                            src="images/loading-spinner.gif" />
                        </a>
                      </div>
                      <div class="sp-item-body">
                        <a href="javascript:;">Nhẫn gắn đá màu MS05</a>
                        <p class="price">500.000đ</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="home-section home-visao">
          <div class="container">
            <div class="section-title title-w-deco">
              <h4>tại sao nên chọn tại Bảo Tín Mạnh Hải?</h4>
              <div class="title-deco"></div>
            </div>

            <div class="visao-container d-flex">
              <div class="visao-item">
                <div class="visao-img">
                  <img class="img-fluid" src="images/home/<USER>" alt="" />
                </div>
                <div class="visao-content">
                  <h5>Về sản phẩm</h5>
                  <p>Bảo Tín Mạnh Hải cam kết chất liệu và kiểu dáng của sản phẩm giống 100% hình ảnh và mô tả (thậm chí
                    hàng thật còn đẹp hơn ảnh)</p>
                </div>
              </div>
              <div class="visao-item">
                <div class="visao-img">
                  <img class="img-fluid lazy" data-src="images/home/<USER>" alt=""
                    src="images/loading-spinner.gif" />
                </div>
                <div class="visao-content">
                  <h5>Về giá cả</h5>
                  <p>Bảo Tín Mạnh Hải sản xuất với số lượng lớn với hệ thống cửa hàng và trực tiếp nên giá thành cạnh
                    tranh nhất thị trường</p>
                </div>
              </div>
              <div class="visao-item">
                <div class="visao-img">
                  <img class="img-fluid" data-src="images/home/<USER>" alt=""
                    src="images/loading-spinner.gif" />
                </div>
                <div class="visao-content">
                  <h5>Về dịch vụ</h5>
                  <p>Đội ngũ tư vấn viên nhiệt tình, chu đáo, sẵn sàng giải đáp thắc mắc của khách hàng trong thời gian
                    nhanh nhất</p>
                </div>
              </div>
              <div class="visao-item">
                <div class="visao-img">
                  <img class="img-fluid" data-src="images/home/<USER>" alt=""
                    src="images/loading-spinner.gif" />
                </div>
                <div class="visao-content">
                  <h5>Về thời gian giao hàng</h5>
                  <p>Hàng có sẵn, thời gian chuẩn bị tối ưu nhất, giao hàng nhanh chóng đến tận tay khách hàng</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="home-section home-cuahang">
          <div class="home-cuahang--container">
            <div class="section-deco">
              <div class="deco-item item1"></div>
              <div class="deco-item item2"></div>
              <div class="deco-item item3"></div>
              <div class="deco-item item4"></div>
            </div>

            <div class="container">
              <div class="cuahang-wrapper">
                <div class="cuahang-head">
                  <img class="head-logo" src="images/common/logo.png" alt="" />

                  <h4>HỆ THỐNG 07 CỬA HÀNG TẠI HÀ NỘI</h4>
                </div>

                <div class="cuahang-slider">
                  <div class="swiper-container cuahang-slide">
                    <div class="swiper-wrapper">
                      <div class="swiper-slide" v-for="n in 6">
                        <div class="cuahang-item">
                          <div class="cuahang-img">
                            <img class="img-fluid swiper-lazy" data-src="images/home/<USER>" alt="" />
                            <div class="swiper-lazy-preloader"></div>
                            <div class="cuahang-img-overlay">
                              <p>Trụ sở chính</p>
                            </div>
                          </div>
                          <div class="cuahang-info">
                            <h4>Trụ sở chính:</h4>
                            <p>16 Trần Duy Hưng, Cầu Giấy, Hà Nội</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <rednavi></rednavi>
                </div>

                <div class="viewmore-cta">
                  <a href="javascript:;">
                    Xem tất cả Cửa hàng
                    <img class="img-fluid" src="images/common/arrow-right.png" alt="" />
                  </a>
                </div>
              </div>
            </div>
          </div>

          <div class="home-cuahang-online">
            <div class="container">
              <div class="cuahang-online-gr d-flex">
                <div class="cuahang-online-item">
                  <h5>Hotline mua hàng</h5>

                  <div class="online-item-body">
                    <p class="item-tele">
                      <img class="img-fluid" src="images/common/ic-tele.png" alt="" />

                      024 2233 9999
                    </p>
                  </div>
                </div>
                <div class="cuahang-online-item">
                  <h5>Đặt lịch hẹn</h5>

                  <div class="online-item-body item-w-link">
                    <a href="javascript:;" class="link link-offline">
                      <img class="img-fluid" src="images/common/calendar.png" alt="" />

                      Đặt lịch hẹn tại cửa hàng
                    </a>
                  </div>
                </div>
                <div class="cuahang-online-item">
                  <h5>Gian hàng trên Shopee</h5>

                  <div class="online-item-body item-w-link">
                    <a href="javascript:;" class="link link-online">
                      <img class="img-fluid" src="images/common/shoppe-ic.png" alt="" />

                      Theo dõi ngay
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="home-section home-hanhtrinh">
          <div class="container">
            <div class="hanhtrinh-wrapper">
              <div class="hanhtrinh-logo">
                <img class="img-fluid" data-src="images/common/logo-nobg.png" alt="" src="images/loading-spinner.gif" />
              </div>
              <div class="hanhtrinh-title d-flex">
                <p>HÀNH TRÌNH</p>
                <p>MỘT CHỮ “TÍN”</p>
                <img class="img-fluid" data-src="images/home/<USER>" alt=""
                  src="images/loading-spinner.gif" />
              </div>
              <div class="line"></div>
              <div class="hanhtrinh-body">
                <p>
                  Với phương châm <span>“giữ tín nhiệm hơn giữ vàng”</span> và luôn kiên trì với một mục tiêu <br />
                  <span>“lấy sự hài lòng của khách hàng là thước đo cho thành công”</span> <br />
                  <span>Bảo Tín Mạnh Hải</span> luôn nhận được sự tin tưởng của các cơ quan quản lý, <br />
                  sự ủng hộ của đối tác, bạn bè và đặc biệt là sự yêu mến của quý khách hàng Thủ đô Hà Nội
                </p>
              </div>
            </div>
          </div>
        </div>

        <div class="home-section home-tintuc">
          <div class="container">
            <div class="tintuc-grid">
              <div class="tintuc-item item-title">
                <h4>
                  CHUYÊN MỤC <br />
                  TIN TỨC
                </h4>
              </div>
              <div class="tintuc-item" v-for="n in 5">
                <a href="javascript:;">
                  <div class="item-base">
                    <img class="img-fluid" data-src="images/home/<USER>" alt=""
                      src="images/loading-spinner.gif" />
                  </div>
                  <div class="item-overlay">
                    <p>MIX & MATCH</p>
                  </div>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
    <myfooter></myfooter>
  </div>

  <script type="text/javascript" src="js/vue.js"></script>
  <script type="text/javascript" src="js/httpvueloader.js"></script>
  <script type="text/javascript" src="libs/swiper/swiper-bundle.min.js"></script>
  <script type="text/javascript" src="libs/swiper/swiper-animation.min.js"></script>
  <script type="text/javascript" src="libs/bootstrap/jquery.min.js"></script>
  <script type="text/javascript" src="libs/bootstrap/popper.min.js"></script>
  <script type="text/javascript" src="libs/bootstrap/bootstrap.min.js"></script>
  <script type="text/javascript" src="libs/aos/aos.js"></script>
  <script type="text/javascript" src="libs/chartjs/chart.min.js"></script>
  <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.27.0/moment.min.js" integrity="sha512-rmZcZsyhe0/MAjquhTgiUcb4d9knaFc7b5xAfju483gbEXTkeJRUMIPk6s3ySZMYUHEcjKbjLjyddGWMrNEvZg==" crossorigin="anonymous"></script> -->
  <script src="libs/chartjs/chartjs-adapter-fns.min.js"></script>

  <script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@11.0.6/dist/lazyload.min.js"></script>
  <script type="text/javascript" src="libs/fancybox/jquery.fancybox.min.js"></script>
  <script type="text/javascript" src="js/main.js"></script>

  <script type="text/javascript">
    var app = new Vue({
      el: '#app',
      data() {
        return {
          flashSaleEndTime: 80000,
          activeItem: 'tab-1',
        };
      },
      components: {
        myheader: httpVueLoader('vue_components/header.vue'),
        myfooter: httpVueLoader('vue_components/footer.vue'),
        rednavi: httpVueLoader('vue_components/red-slide-navigation.vue'),
        yellownavi: httpVueLoader('vue_components/yellow-slide-navigation.vue'),
      },
      methods: {
        isActive(menuItem) {
          return this.activeItem === menuItem;
        },
        setActive(menuItem) {
          this.activeItem = menuItem;
        },
      },
      mounted() {
        this.$nextTick(() => {
          $(window).bind('load', function () {
            if (LazyLoad) {
              var lazyloader = new LazyLoad({
                elements: '.lazy',
              });
            }

            // flashsale countdown
            demnguoc(app._data.flashSaleEndTime, '#flashsale-hours', '#flashsale-mins', '#flashsale-sec');

            // banner
            var bannerHome = new Swiper('.banner-slider', {
              // slidesPerView: 1,
              // spaceBetween: 20,
              speed: 1000,
              loop: false,
              preloadImages: false,
              observer: true,
              observeParents: true,
              lazy: {
                loadPrevNext: true,
              },
              zoom: true,
              watchOverflow: true,
              pagination: {
                el: '.banner-slider .swiper-pagination',
                clickable: true,
              },
              navigation: {
                nextEl: '.banner-slider .custom-button-next',
                prevEl: '.banner-slider .custom-button-prev',
              },
            });

            // san pham noi bat
            var bannernoibat = new Swiper('.sanpham-noibat .sanpham-slide', {
              slidesPerView: 2,
              spaceBetween: 15,
              speed: 1000,
              loop: false,
              observer: true,
              observeParents: true,
              preloadImages: false,
              lazy: { loadPrevNext: true },
              pagination: {
                el: '.sanpham-noibat .swiper-pagination',
              },
              navigation: {
                nextEl: '.sanpham-noibat .custom-button-next',
                prevEl: '.sanpham-noibat .custom-button-prev',
              },
              breakpoints: {
                576: {
                  slidesPerView: 3,
                  spaceBetween: 15,
                },
                992: {
                  slidesPerView: 4,
                  spaceBetween: 20,
                },
                1200: {
                  slidesPerView: 4,
                  spaceBetween: 29,
                },
              },
            });

            // san pham slider2
            var bannernolink = new Swiper('.sanpham-banner-nolink .sanpham-slide', {
              slidesPerView: 2,
              spaceBetween: 15,
              speed: 1000,
              loop: false,
              preloadImages: false,
              observer: true,
              observeParents: true,
              lazy: {
                loadPrevNext: true,
              },
              pagination: {
                el: '.sanpham-banner-nolink  .swiper-pagination',
              },
              navigation: {
                nextEl: '.sanpham-banner-nolink .custom-button-next',
                prevEl: '.sanpham-banner-nolink .custom-button-prev',
              },
              breakpoints: {
                576: {
                  slidesPerView: 3,
                  spaceBetween: 15,
                },
                992: {
                  slidesPerView: 4,
                  spaceBetween: 20,
                },
                1200: {
                  slidesPerView: 4,
                  spaceBetween: 29,
                },
              },
            });

            // san pham slider3
            // var bannerwlink = new Swiper('.sanpham-banner-w-link .sanpham-slide', {
            //   slidesPerView: 2,
            //   spaceBetween: 15,
            //   speed: 1000,
            //   loop: false,
            //   preloadImages: false,
            //   observer: true,
            //   observeParents: true,
            //   lazy: {
            //     loadPrevNext: true,
            //   },
            //   pagination: {
            //     el: '.sanpham-banner-w-link .swiper-pagination',
            //   },
            //   navigation: {
            //     nextEl: '.sanpham-banner-w-link .custom-button-next',
            //     prevEl: '.sanpham-banner-w-link .custom-button-prev',
            //   },
            //   breakpoints: {
            //     576: {
            //       slidesPerView: 3,
            //       spaceBetween: 15,
            //     },
            //     992: {
            //       slidesPerView: 4,
            //       spaceBetween: 20,
            //     },
            //     1200: {
            //       slidesPerView: 4,
            //       spaceBetween: 29,
            //     },
            //   },
            // });
            $('.sanpham-banner-w-link .sanpham-slide').each(function () {
              var mySwiper = new Swiper(this, {
                slidesPerView: 2,
                spaceBetween: 15,
                speed: 1000,
                loop: false,
                preloadImages: false,
                observer: true,
                observeParents: true,
                lazy: {
                  loadPrevNext: true,
                },
                breakpoints: {
                  576: {
                    slidesPerView: 3,
                    spaceBetween: 15,
                  },
                  992: {
                    slidesPerView: 4,
                    spaceBetween: 20,
                  },
                  1200: {
                    slidesPerView: 4,
                    spaceBetween: 29,
                  },
                },
              });
              $(this).parents('.sanpham-banner-w-link').find('.custom-button-prev').on('click', function (e) {
                e.preventDefault();
                mySwiper.slidePrev();
              });
              $(this).parents('.sanpham-banner-w-link').find('.custom-button-next').on('click', function (e) {
                e.preventDefault();
                mySwiper.slideNext();
              });
            });
            // cua hang
            var bannercuahang = new Swiper('.cuahang-slider .cuahang-slide', {
              slidesPerView: 1,
              spaceBetween: 1,
              speed: 1000,
              centeredSlides: true,
              roundLengths: true,
              loop: true,
              observer: true,
              observeParents: true,
              preloadImages: false,
              lazy: {
                loadPrevNext: true,
              },
              pagination: {
                el: '.cuahang-slider .swiper-pagination',
              },
              navigation: {
                nextEl: '.cuahang-slider .custom-button-next',
                prevEl: '.cuahang-slider .custom-button-prev',
              },
              breakpoints: {
                768: {
                  slidesPerView: 2,
                  spaceBetween: 1,
                },
                1024: {
                  slidesPerView: 3,
                  spaceBetween: 1,
                },
              },
            });

            AOS.init({
              // startEvent: 'load',
              duration: 700,
              easing: 'ease',
            });

            //check box height
            var height = $('.container-l .chart-body').outerHeight();
            if ($(window).width() > 992) {
              $('.container-r .chart-body').css('height', height + 'px');
            }
            // chart js
            var jsonHour = {
              "labels": [
                "2022-08-20 10:23:00",
                "2022-08-20 10:28:00",
                "2022-08-20 11:33:00",
                "2022-08-20 12:38:00",
                "2022-08-20 13:43:00",
                "2022-08-20 14:48:00",
                "2022-08-20 15:53:00",
                "2022-08-20 16:58:00",
                "2022-08-20 17:03:00",
                "2022-08-20 18:08:00",
              ],
              "data": {
                "rate": [
                  50,
                  50.022,
                  51.225,
                  53.1,
                  54,
                  54.6,
                  55.22,
                  57.5,
                  58.8,
                  61
                ],
                "sell": [
                  60,
                  61.022,
                  62.225,
                  63.1,
                  64,
                  64.6,
                  66.22,
                  67.6,
                  68.8,
                  69
                ],
              }
            }

            var jsonDay = {
              labels: [
                '2022-08-20 10:23:00',
                '2022-08-20 10:28:00',
                '2022-08-20 11:33:00',
                '2022-08-20 12:38:00',
                '2022-08-20 13:43:00',
                '2022-08-20 14:48:00',
                '2022-08-20 15:53:00',
                '2022-08-20 16:58:00',
                '2022-08-20 17:03:00',
                '2022-08-20 18:08:00',

                '2022-08-21 10:23:00',
                '2022-08-21 10:28:00',
                '2022-08-21 11:33:00',
                '2022-08-21 12:38:00',
                '2022-08-21 13:43:00',
                '2022-08-21 14:48:00',
                '2022-08-21 15:53:00',
                '2022-08-21 16:58:00',
                '2022-08-21 17:03:00',
                '2022-08-21 18:08:00',

                '2022-08-22 10:23:00',
                '2022-08-22 10:28:00',
                '2022-08-22 11:33:00',
                '2022-08-22 12:38:00',
                '2022-08-22 13:43:00',
                '2022-08-22 14:48:00',
                '2022-08-22 15:53:00',
                '2022-08-22 16:58:00',
                '2022-08-22 17:03:00',
                '2022-08-22 18:08:00',

                '2022-08-23 10:23:00',
                '2022-08-23 10:28:00',
                '2022-08-23 11:33:00',
                '2022-08-23 12:38:00',
                '2022-08-23 13:43:00',
                '2022-08-23 14:48:00',
                '2022-08-23 15:53:00',
                '2022-08-23 16:58:00',
                '2022-08-23 17:03:00',
                '2022-08-23 18:08:00',

                '2022-08-24 10:23:00',
                '2022-08-24 10:28:00',
                '2022-08-24 11:33:00',
                '2022-08-24 12:38:00',
                '2022-08-24 13:43:00',
                '2022-08-24 14:48:00',
                '2022-08-24 15:53:00',
                '2022-08-24 16:58:00',
                '2022-08-24 17:03:00',
                '2022-08-24 18:08:00',

                '2022-08-25 10:23:00',
                '2022-08-25 10:28:00',
                '2022-08-25 11:33:00',
                '2022-08-25 12:38:00',
                '2022-08-25 13:43:00',
                '2022-08-25 14:48:00',
                '2022-08-25 15:53:00',
                '2022-08-25 16:58:00',
                '2022-08-25 17:03:00',
                '2022-08-25 18:08:00',
              ],
              data: {
                rate: [
                  50, 50.022, 51.225, 53.1, 54, 54.6, 55.22, 57.5, 58.8, 61, 50, 50.022, 51.225, 53.1, 54, 54.6, 55.22, 57.5, 58.8, 61, 50, 50.022, 51.225,
                  53.1, 54, 54.6, 55.22, 57.5, 58.8, 61, 50, 50.022, 51.225, 53.1, 54, 54.6, 55.22, 57.5, 58.8, 61, 50, 50.022, 51.225, 53.1, 54, 54.6, 55.22,
                  57.5, 58.8, 61, 50, 50.022, 51.225, 53.1, 54, 54.6, 55.22, 57.5, 58.8, 61,
                ],
                sell: [
                  60, 61.022, 62.225, 63.1, 64, 64.6, 66.22, 67.6, 68.8, 69, 60, 61.022, 62.225, 63.1, 64, 64.6, 66.22, 67.6, 68.8, 69, 60, 61.022, 62.225,
                  63.1, 64, 64.6, 66.22, 67.6, 68.8, 69, 60, 61.022, 62.225, 63.1, 64, 64.6, 66.22, 67.6, 68.8, 69, 60, 61.022, 62.225, 63.1, 64, 64.6, 66.22,
                  67.6, 68.8, 69, 60, 61.022, 62.225, 63.1, 64, 64.6, 66.22, 67.6, 68.8, 69,
                ],
              },
            };

            // var config = {
            //   type: 'line',
            //   data: {
            //     labels: jsonDay.labels,
            //     datasets: [
            //       {
            //         label: 'Bán',
            //         data: jsonDay.data.rate,
            //         backgroundColor: 'transparent',
            //         borderColor: 'rgb(33 150 83)',
            //         lineTension: 0,
            //         pointRadius: 0,
            //         pointHitRadius: 4,
            //       },
            //       {
            //         label: 'Mua',
            //         data: jsonDay.data.sell,
            //         backgroundColor: 'transparent',
            //         borderColor: 'rgb(170 31 35)',
            //         lineTension: 0,
            //         pointRadius: 0,
            //         borderWidth: 1,
            //         pointHitRadius: 4,
            //       },
            //     ],
            //   },

            //   options: {
            //     legend: {
            //       display: false,
            //     },
            //     animation: false,
            //     scales: {
            //       y: {
            //         position: 'right',
            //         grid: {
            //           borderWidth: 0,
            //         },
            //         ticks: {
            //           color: '#BABABA',
            //           font: {
            //             size: 14,
            //             family: 'SVN-Megante',
            //           },
            //           callback: function (value) {
            //             let minValue = jsonDay.data.rate[9];
            //             let maxValue = jsonDay.data.sell[9];
            //             return value;
            //           },
            //         },
            //       },
            //       x: {
            //         grid: {
            //           display: false,
            //         },
            //         // offset : false ,
            //         type: 'time',
            //         beginAtZero: 'false',
            //         time: {
            //           // stepSize: 1,
            //           // parser: 'm:s.SSS',
            //           min: min,
            //           unit: 'day',
            //           displayFormats: { week: 'd/M' },
            //         },
            //         ticks: {
            //           color: 'black',
            //           font: {
            //             size: 14,
            //             family: 'SVN-Megante',
            //           },
            //         },
            //       },
            //     },
            //   },
            // };
            // var options = {
            //     legend: {
            //       display: false,
            //     },
            //     animation: false,
            //     scales: {
            //       y: {
            //         position: 'right',
            //         grid: {
            //           borderWidth: 0,
            //         },
            //         ticks: {
            //           color: '#BABABA',
            //           font: {
            //             size: 14,
            //             family: 'SVN-Megante',
            //           },
            //           callback: function (value) {
            //             let minValue = jsonDay.data.rate[9];
            //             let maxValue = jsonDay.data.sell[9];
            //             return value;
            //           },
            //         },
            //       },
            //       x: {
            //         grid: {
            //           display: false,
            //         },
            //         // offset : false ,
            //         type: 'time',
            //         beginAtZero: 'false',
            //         time: {
            //           // stepSize: 1,
            //           // parser: 'm:s.SSS',
            //           min: min,
            //           unit: 'day',
            //           displayFormats: { week: 'd/M' },
            //         },
            //         ticks: {
            //           color: 'black',
            //           font: {
            //             size: 14,
            //             family: 'SVN-Megante',
            //           },
            //         },
            //       },
            //     },
            // }
            var xAsisOptions = {
              stepSize: 2,
              min: 0,
              max: 0,
              unit: 'hour',
              displayFormats: { hour: 'H:mm' }
            };


            initChart(jsonHour, xAsisOptions)


            // chart radio
            $('.chart-radio').on('change', function () {

              let chartType = $(this).val();

              updateChart(chartType)
            });
          });
        });
      },
    });
  </script>
</body>

</html>