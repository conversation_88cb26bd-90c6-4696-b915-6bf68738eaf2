<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="shortcut icon" async href="images/common/favicon.png" />
    <title>Website Bảo Tín Mạnh Hải</title>
    <link rel="stylesheet" href="libs/bootstrap/bootstrap.min.css" />
    <link rel="stylesheet" href="libs/swiper/swiper-bundle.min.css" />
    <link rel="stylesheet" href="libs/fancybox/jquery.fancybox.min.css" />
    <link rel="stylesheet" href="libs/flatpickr/flatpickr.min.css" />
    <link rel="stylesheet" href="libs/aos/aos.css" />
    <link rel="stylesheet" href="libs/fancybox/jquery.fancybox.min.css" />
    <link rel="stylesheet" href="css/style.css" />
  </head>

  <body>
    <div id="app">
      <myheader></myheader>
      <main>
        <div class="customer-page pt-header">
          <section class="customer-banner bg-section" style="background-image: url(images/banner-customer.png)">
            <div class="col-md-10 mx-auto">
              <h1>
                Các sản phẩm quà tặng <br />
                dành cho khách hàng doanh nghiệp
              </h1>
            </div>
          </section>

          <section class="gold">
            <div class="container">
              <h3 class="title-section text-uppercase text-center mb-4 mb-xl-5">vàng 9999</h3>
              <a href="" class="d-block"><img src="images/img-gold.png" alt="" /></a>
            </div>
          </section>

          <section class="product-gift">
            <div class="container">
              <h3 class="title-section text-uppercase text-center mb-4 mb-xl-5">sản phẩm quà tặng</h3>

              <div class="wrapper">
                <div class="left">
                  <h2>Quý khách hàng doanh nghiệp quan tâm đến các sản phẩm của Bảo Tính Mạnh Hải làm quà tặng, thưởng hoặc kỷ niệm chương.</h2>
                  <p>Vui lòng liên hệ theo địa chỉ hoặc điền vào form dưới đây để nhận được những ưu đãi hấp dẫn.</p>
                  <form action="">
                    <div class="form-group">
                      <input type="text" name="" id="" class="form-control" placeholder="Họ và tên" aria-describedby="helpId" />
                      <small id="helpId" class="text-danger">Help text</small>
                    </div>
                    <div class="form-group">
                      <input type="text" name="" id="" class="form-control" placeholder="Số điện thoại" aria-describedby="helpId" />
                      <small id="helpId" class="text-danger">Help text</small>
                    </div>
                    <div class="form-group">
                      <input type="text" name="" id="" class="form-control" placeholder="Tên doanh nghiệp, tổ chức" aria-describedby="helpId" />
                      <small id="helpId" class="text-danger">Help text</small>
                    </div>
                    <div class="form-group">
                      <textarea name="" id="" cols="30" rows="3" class="form-control" placeholder="Ghi chú"></textarea>
                      <small id="helpId" class="text-danger">Help text</small>
                    </div>

                    <button class="btn-submit d-block border-0 w-100"><span>Gửi thông tin</span></button>
                  </form>
                </div>
                <div class="right bg-section text-white" style="background-image: url(images/gift-img.png)">
                  <div class="address">
                    <div class="address-item">
                      <div class="icon"><img src="images/icon-ad-1.png" alt="" /></div>
                      <div class="txt">
                        Trụ sở chính
                        <span>T19 Center Building, Số 1 Nguyễn Huy Tưởng, Thanh Xuân, Hà Nội</span>
                      </div>
                    </div>
                    <div class="address-item">
                      <div class="icon"><img src="images/icon-ad-2.png" alt="" /></div>
                      <div class="txt">
                        Điện thoại chăm sóc
                        <span>024.22339999 </span>
                      </div>
                    </div>
                    <div class="address-item">
                      <div class="icon"><img src="images/icon-ad-1.png" alt="" /></div>
                      <div class="txt">
                        Email hỗ trợ
                        <span><a href=""><EMAIL></a></span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          <section class="slogan bg-section text-center" style="background-image: url(images/jounery-bg.png)">
            <div class="container">
              <div class="row">
                <div class="col-md-10 mx-auto">
                  <img src="images/jounery-slogan.png" alt="" />
                  <div class="line my-4 text-center"><img src="images/line.png" alt="" /></div>
                  <div class="desc">
                    Với phương châm <span>“giữ tín nhiệm hơn giữ vàng”</span> và luôn kiên trì với một mục tiêu <br />
                    <span>“lấy sự hài lòng của khách hàng là thước đo cho thành công”</span> <br />
                    Bảo Tín Mạnh Hải luôn nhận được sự tin tưởng của các cơ quan quản lý, <br />
                    sự ủng hộ của đối tác, bạn bè và đặc biệt là sự yêu mến của quý khách hàng Thủ đô Hà Nội
                  </div>
                </div>
              </div>
            </div>
          </section>

          <section class="home-section home-visao mb-0">
            <div class="container">
              <div class="section-title title-w-deco">
                <h4>tại sao nên chọn tại Bảo Tín Mạnh Hải?</h4>
                <div class="title-deco"></div>
              </div>

              <div class="visao-container d-flex">
                <div class="visao-item">
                  <div class="visao-img">
                    <img class="img-fluid" src="images/home/<USER>" alt="" />
                  </div>
                  <div class="visao-content">
                    <h5>Về sản phẩm</h5>
                    <p>Bảo Tín Mạnh Hải cam kết chất liệu và kiểu dáng của sản phẩm giống 100% hình ảnh và mô tả (thậm chí hàng thật còn đẹp hơn ảnh)</p>
                  </div>
                </div>
                <div class="visao-item">
                  <div class="visao-img">
                    <img class="img-fluid lazy" alt="" src="images/home/<USER>" />
                  </div>
                  <div class="visao-content">
                    <h5>Về giá cả</h5>
                    <p>Bảo Tín Mạnh Hải sản xuất với số lượng lớn với hệ thống cửa hàng và trực tiếp nên giá thành cạnh tranh nhất thị trường</p>
                  </div>
                </div>
                <div class="visao-item">
                  <div class="visao-img">
                    <img class="img-fluid" alt="" src="images/home/<USER>" />
                  </div>
                  <div class="visao-content">
                    <h5>Về dịch vụ</h5>
                    <p>Đội ngũ tư vấn viên nhiệt tình, chu đáo, sẵn sàng giải đáp thắc mắc của khách hàng trong thời gian nhanh nhất</p>
                  </div>
                </div>
                <div class="visao-item">
                  <div class="visao-img">
                    <img class="img-fluid" alt="" src="images/home/<USER>" />
                  </div>
                  <div class="visao-content">
                    <h5>Về thời gian giao hàng</h5>
                    <p>Hàng có sẵn, thời gian chuẩn bị tối ưu nhất, giao hàng nhanh chóng đến tận tay khách hàng</p>
                  </div>
                </div>
              </div>
            </div>
          </section>

          <section class="home-section home-cuahang m-0">
            <div class="home-cuahang--container">
              <div class="section-deco">
                <div class="deco-item item1"></div>
                <div class="deco-item item2"></div>
                <div class="deco-item item3"></div>
                <div class="deco-item item4"></div>
              </div>

              <div class="cuahang-wrapper">
                <div class="cuahang-head">
                  <img class="head-logo" src="images/common/logo.png" alt="" />
                  <h4>DANH SÁCH ĐẠI LÝ</h4>
                </div>

                <div class="cuahang-slider">
                  <div class="swiper-container cuahang-slide swiper">
                    <div class="swiper-wrapper">
                      <div class="swiper-slide" v-for="n in 9">
                        <div class="cuahang-item">
                          <div class="cuahang-img">
                            <img class="img-fluid swiper-lazy" data-src="images/home/<USER>" alt="" />
                            <div class="swiper-lazy-preloader"></div>
                            <div class="cuahang-img-overlay">
                              <p>Trụ sở chính</p>
                            </div>
                          </div>
                          <div class="cuahang-info">
                            <h4>Trụ sở chính:</h4>
                            <p>16 Trần Duy Hưng, Cầu Giấy, Hà Nội</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="custom-slide-button red-btn">
                    <div class="custom-button-prev">
                      <img class="img-fluid" src="images/common/red-nav-prev.png" alt="" />
                    </div>
                    <div class="custom-button-next">
                      <img class="img-fluid" src="images/common/red-nav-next.png" alt="" />
                    </div>
                  </div>
                </div>

                <div class="viewmore-cta">
                  <a href="javascript:;">
                    Xem tất cả Cửa hàng
                    <img class="img-fluid" src="images/common/arrow-right.png" alt="" />
                  </a>
                </div>
              </div>
            </div>
          </section>

          <section class="policy-section">
            <div class="container">
              <h3 class="title-section text-center text-uppercase">chính sách bán buôn</h3>

              <div class="list">
                <div class="item" v-for="n in 3">
                  <h4>Chính sách đại lý mới</h4>
                  <div class="desc">
                    Chúng tôi luôn nỗ lực đảm bảo duy trì hệ thống kỹ thuật của máy chế tác trang sức trong tình trạng chạy ổn định, an toàn và tuân thủ quy định pháp luật liên
                    quan đến việc cung cứng các dịch vụ thương mại điện tử và thực hiện các giao dịch trực tuyến
                  </div>
                </div>
              </div>
            </div>
          </section>

          <section class="home-chart">
            <div class="container">
              <div class="chart-container-row">
                <div class="chart-container container-r w-100 mw-100">
                  <div class="block-title">
                    <h2 class="title-section text-center text-uppercase">BIỂU ĐỒ GIÁ VÀNG</h2>
                  </div>
                  <div class="chart-body chart-entry">
                    <div class="js-chart-top d-flex">
                      <form action="">
                        <p>Mã vàng:</p>
                        <select>
                          <option value="KDN">Kim Dần Niên</option>
                          <option value="KGB">Nhẫn ép vỉ Kim Gia Bảo</option>
                          <option value="999">Vàng 99.9</option>
                          <option value="VRTL">Nhẫn ép vỉ Vàng Rồng Thăng Long</option>
                          <option value="SJC">Vàng miếng SJC</option>
                          <option value="9999">Vàng 999.9</option>
                        </select>
                      </form>
                      <div class="chart-footer">
                        <form action="">
                          <p>Xem theo:</p>
                          <div class="custom-radio custom-control-inline">
                            <input type="radio" id="customRadioInline1" name="customRadioInline1" class="custom-control-input chart-radio" checked="checked" value="day" />
                            <label class="custom-label" for="customRadioInline1">24 giờ</label>
                          </div>
                          <div class="custom-radio custom-control-inline">
                            <input type="radio" id="customRadioInline2" name="customRadioInline1" class="custom-control-input chart-radio" value="week" />
                            <label class="custom-label" for="customRadioInline2">Tuần</label>
                          </div>
                          <div class="custom-radio custom-control-inline">
                            <input type="radio" id="customRadioInline3" name="customRadioInline1" class="custom-control-input chart-radio" value="month" />
                            <label class="custom-label" for="customRadioInline3">Tháng</label>
                          </div>
                          <div class="custom-radio custom-control-inline">
                            <input type="radio" id="customRadioInline4" name="customRadioInline1" class="custom-control-input chart-radio" value="year" />
                            <label class="custom-label" for="customRadioInline4">Năm</label>
                          </div>
                        </form>
                      </div>
                    </div>
                    <canvas id="myChart"></canvas>
                    <div class="chart-note d-flex align-items-center justify-content-center">
                      <div class="note note-buy">
                        <span></span>
                        <p>Mua vào</p>
                      </div>
                      <div class="note note-sell">
                        <span></span>
                        <p>Bán ra</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          <section class="partners">
            <div class="container">
              <h3 class="title-section text-center text-uppercase mb-4">ĐỐI TÁC</h3>
              <div class="partner-slides position-relative">
                <div class="swiper overflow-hidden">
                  <div class="swiper-wrapper">
                    <div class="swiper-slide" v-for="n in 6">
                      <div class="partner-item">
                        <img src="images/partner-3.png" alt="" />
                      </div>
                    </div>
                  </div>
                </div>
                <div class="swiper-button-prev"></div>
                <div class="swiper-button-next"></div>
              </div>
            </div>
          </section>
        </div>
      </main>
      <myfooter></myfooter>
    </div>

    <script type="text/javascript" src="js/vue.js"></script>
    <script type="text/javascript" src="js/httpvueloader.js"></script>
    <script type="text/javascript" src="libs/swiper/swiper-bundle.min.js"></script>
    <script type="text/javascript" src="libs/bootstrap/jquery.min.js"></script>
    <script type="text/javascript" src="libs/bootstrap/popper.min.js"></script>
    <script type="text/javascript" src="libs/bootstrap/bootstrap.min.js"></script>
    <script type="text/javascript" src="libs/aos/aos.js"></script>
    <script type="text/javascript" src="libs/chartjs/chart.min.js"></script>
    <script src="libs/chartjs/chartjs-adapter-fns.min.js"></script>
    <script type="text/javascript" src="libs/fancybox/jquery.fancybox.min.js"></script>
    <script type="text/javascript" src="js/main.js"></script>

    <script type="text/javascript">
      var app = new Vue({
        el: '#app',
        data() {
          return {};
        },
        components: {
          myheader: httpVueLoader('vue_components/header.vue'),
          myfooter: httpVueLoader('vue_components/footer.vue'),
          rednavi: httpVueLoader('vue_components/red-slide-navigation.vue'),
          yellownavi: httpVueLoader('vue_components/yellow-slide-navigation.vue'),
        },
        mounted() {
          this.$nextTick(() => {
            $(document).ready(function () {
              // cua hang
              var bannercuahang = new Swiper('.cuahang-slider .cuahang-slide', {
                slidesPerView: 1,
                spaceBetween: 1,
                speed: 1000,
                centeredSlides: true,
                roundLengths: true,
                observer: true,
                observeParents: true,
                loop: true,
                preloadImages: false,
                effect: 'coverflow',
                coverflowEffect: {
                  rotate: 0,
                  depth: 0,
                  scale: 0.9,
                  stretch: 0,
                  slideShadows: false,
                  modifier: 1,
                },
                lazy: {
                  loadPrevNext: true,
                },
                pagination: {
                  el: '.cuahang-slider .swiper-pagination',
                },
                navigation: {
                  nextEl: '.cuahang-slider .custom-button-next',
                  prevEl: '.cuahang-slider .custom-button-prev',
                },
                breakpoints: {
                  768: {
                    slidesPerView: 2,
                  },
                  1024: {
                    slidesPerView: 4,
                  },
                },
              });

              var swiper = new Swiper('.partner-slides .swiper', {
                slidesPerView: 2,
                loop: true,
                speed: 1000,
                autoplay: {
                  delay: 5000,
                },
                spaceBetween: 17,
                navigation: {
                  nextEl: '.partner-slides .swiper-button-next',
                  prevEl: '.partner-slides .swiper-button-prev',
                },
                breakpoints: {
                  480: {
                    slidesPerView: 3,
                  },
                  768: {
                    slidesPerView: 4,
                  },
                  992: {
                    slidesPerView: 5,
                  },
                },
              });
            });

            $(window).bind('load', function () {
              //   SLIDER DOI TAC

              // chart js
              var jsonHour = {
                labels: [
                  '2022-08-20 10:23:00',
                  '2022-08-20 10:28:00',
                  '2022-08-20 11:33:00',
                  '2022-08-20 12:38:00',
                  '2022-08-20 13:43:00',
                  '2022-08-20 14:48:00',
                  '2022-08-20 15:53:00',
                  '2022-08-20 16:58:00',
                  '2022-08-20 17:03:00',
                  '2022-08-20 18:08:00',
                ],
                data: {
                  rate: [50, 50.022, 51.225, 53.1, 54, 54.6, 55.22, 57.5, 58.8, 61],
                  sell: [60, 61.022, 62.225, 63.1, 64, 64.6, 66.22, 67.6, 68.8, 69],
                },
              };

              var jsonDay = {
                labels: [
                  '2022-08-20 10:23:00',
                  '2022-08-20 10:28:00',
                  '2022-08-20 11:33:00',
                  '2022-08-20 12:38:00',
                  '2022-08-20 13:43:00',
                  '2022-08-20 14:48:00',
                  '2022-08-20 15:53:00',
                  '2022-08-20 16:58:00',
                  '2022-08-20 17:03:00',
                  '2022-08-20 18:08:00',

                  '2022-08-21 10:23:00',
                  '2022-08-21 10:28:00',
                  '2022-08-21 11:33:00',
                  '2022-08-21 12:38:00',
                  '2022-08-21 13:43:00',
                  '2022-08-21 14:48:00',
                  '2022-08-21 15:53:00',
                  '2022-08-21 16:58:00',
                  '2022-08-21 17:03:00',
                  '2022-08-21 18:08:00',

                  '2022-08-22 10:23:00',
                  '2022-08-22 10:28:00',
                  '2022-08-22 11:33:00',
                  '2022-08-22 12:38:00',
                  '2022-08-22 13:43:00',
                  '2022-08-22 14:48:00',
                  '2022-08-22 15:53:00',
                  '2022-08-22 16:58:00',
                  '2022-08-22 17:03:00',
                  '2022-08-22 18:08:00',

                  '2022-08-23 10:23:00',
                  '2022-08-23 10:28:00',
                  '2022-08-23 11:33:00',
                  '2022-08-23 12:38:00',
                  '2022-08-23 13:43:00',
                  '2022-08-23 14:48:00',
                  '2022-08-23 15:53:00',
                  '2022-08-23 16:58:00',
                  '2022-08-23 17:03:00',
                  '2022-08-23 18:08:00',

                  '2022-08-24 10:23:00',
                  '2022-08-24 10:28:00',
                  '2022-08-24 11:33:00',
                  '2022-08-24 12:38:00',
                  '2022-08-24 13:43:00',
                  '2022-08-24 14:48:00',
                  '2022-08-24 15:53:00',
                  '2022-08-24 16:58:00',
                  '2022-08-24 17:03:00',
                  '2022-08-24 18:08:00',

                  '2022-08-25 10:23:00',
                  '2022-08-25 10:28:00',
                  '2022-08-25 11:33:00',
                  '2022-08-25 12:38:00',
                  '2022-08-25 13:43:00',
                  '2022-08-25 14:48:00',
                  '2022-08-25 15:53:00',
                  '2022-08-25 16:58:00',
                  '2022-08-25 17:03:00',
                  '2022-08-25 18:08:00',
                ],
                data: {
                  rate: [
                    50, 50.022, 51.225, 53.1, 54, 54.6, 55.22, 57.5, 58.8, 61, 50, 50.022, 51.225, 53.1, 54, 54.6, 55.22, 57.5, 58.8, 61, 50, 50.022, 51.225, 53.1, 54, 54.6, 55.22,
                    57.5, 58.8, 61, 50, 50.022, 51.225, 53.1, 54, 54.6, 55.22, 57.5, 58.8, 61, 50, 50.022, 51.225, 53.1, 54, 54.6, 55.22, 57.5, 58.8, 61, 50, 50.022, 51.225, 53.1,
                    54, 54.6, 55.22, 57.5, 58.8, 61,
                  ],
                  sell: [
                    60, 61.022, 62.225, 63.1, 64, 64.6, 66.22, 67.6, 68.8, 69, 60, 61.022, 62.225, 63.1, 64, 64.6, 66.22, 67.6, 68.8, 69, 60, 61.022, 62.225, 63.1, 64, 64.6, 66.22,
                    67.6, 68.8, 69, 60, 61.022, 62.225, 63.1, 64, 64.6, 66.22, 67.6, 68.8, 69, 60, 61.022, 62.225, 63.1, 64, 64.6, 66.22, 67.6, 68.8, 69, 60, 61.022, 62.225, 63.1,
                    64, 64.6, 66.22, 67.6, 68.8, 69,
                  ],
                },
              };

              var xAsisOptions = {
                stepSize: 2,
                min: 0,
                max: 0,
                unit: 'hour',
                displayFormats: { hour: 'H:mm' },
              };

              initChart(jsonHour, xAsisOptions);
              // chart radio
              $('.chart-radio').on('change', function () {
                let chartType = $(this).val();
                updateChart(chartType);
              });
            });
          });
        },
      });
    </script>
  </body>
</html>
