<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="shortcut icon" async href="images/common/favicon.png" />
    <title>Website Bảo Tín Mạnh Hải</title>
    <link rel="stylesheet" href="libs/bootstrap/bootstrap.min.css" />
    <link rel="stylesheet" href="libs/swiper/swiper-bundle.min.css" />
    <link rel="stylesheet" href="libs/fancybox/jquery.fancybox.min.css" />
    <link rel="stylesheet" href="libs/flatpickr/flatpickr.min.css" />
    <link rel="stylesheet" href="libs/aos/aos.css" />
    <link rel="stylesheet" href="libs/fancybox/jquery.fancybox.min.css" />
    <link rel="stylesheet" href="css/style.css" />
  </head>

  <body>
    <div id="app">
      <myheader></myheader>
      <main>
        <div class="orders-page">
          <div class="container">
            <div class="row">
                <div class=" col-lg-4">
                  <div class="details-order-status">
                    <h3 class="title">
                      Cập nhật trạng thái đơn hàng 
                    </h3>
                    <ul class="list-unstyled p-0 m-0">
                      <li class="status-order-item">
                          <span class="time">
                            08:35 29/4 :
                          </span>
                          <span class="value success">
                            Đã giao thành công
                          </span>
                      </li>
                      <li class="status-order-item">
                          <span class="time">
                            08:35 29/4 :
                          </span>
                          <span class="value ">
                            Đã xuất kho
                          </span>
                      </li>
                      <li class="status-order-item">
                          <span class="time">
                            08:35 29/4 :
                          </span>
                          <span class="value error ">
                            Đã hủy
                          </span>
                      </li>
                    </ul>
                  </div>
                </div>
                <div class="col-lg-8">
                  <div class="details-order">
                    <div class="head">
                      <div class="left">
                        <h3>Đơn hàng #123456</h3>
                        <div class="date">Thời gian đặt hàng: <span>17/06/2022 - 16:20</span></div>
                      </div>
                      <div class="status">
                        <div class="status-done">Đã nhận hàng</div>
                        <div class="status-processing">Đã nhận hàng</div>
                        <div class="status-cancle">Đã nhận hàng</div>
                      </div>
                    </div>
      
                    <div class="wrapper">
                      <div class="item" v-for="n in 2">
                        <div class="image"><img src="images/product-img.jpg" alt="" /></div>
                        <div class="content">
                          <div class="name">
                            <div class="txt">Nhẫn vàng phong cách Ý NY10K30</div>
                            <div class="size">Size 12</div>
                          </div>
                          <div class="price">
                            <strong>2.500.000 VNĐ</strong>
                            <div class="sl">Số lượng: 01</div>
                          </div>
                        </div>
                      </div>
                    </div>
      
                    <div class="total">
                      <div class="total-item tamtinh">
                        <div>Tạm tính:</div>
                        <strong>5.000.000 VNĐ</strong>
                      </div>
                      <div class="total-item">
                        <div>Khuyến mại:</div>
                        <strong>(30%) - 1.500.000 VNĐ</strong>
                      </div>
                      <div class="total-item">
                        <div>Vận chuyển:</div>
                        <strong>0 VNĐ</strong>
                      </div>
                      <div class="total-item total-money">
                        <div>Tổng cộng:</div>
                        <div>3.550.000 VNĐ</div>
                      </div>
                    </div>
      
                    <div class="info">
                      <h3>Thông tin nhận hàng</h3>
                      <div class="content">
                        <div>
                          <span>Họ và tên:</span>
                          <span>Hoàng Văn Nhất</span>
                        </div>
                        <div><span>Số điện thoại:</span> <span>0987654321</span></div>
                        <div><span>Địa chỉ nhận hàng:</span> <span>Số 20 Đội Cấn, quận Ba Đình, Hà Nội</span></div>
                      </div>
                    </div>
                  </div>
                </div>
            </div>
          
          
            <div class="text-center">
              <a href="" class="btn-back-link">Về danh sách đơn hàng</a>
            </div>
          </div>
          <supports></supports>
        </div>
      </main>
      <myfooter></myfooter>
    </div>

    <script type="text/javascript" src="js/vue.js"></script>
    <script type="text/javascript" src="js/httpvueloader.js"></script>
    <script type="text/javascript" src="libs/swiper/swiper-bundle.min.js"></script>
    <script type="text/javascript" src="libs/bootstrap/jquery.min.js"></script>
    <script type="text/javascript" src="libs/bootstrap/popper.min.js"></script>
    <script type="text/javascript" src="libs/bootstrap/bootstrap.min.js"></script>
    <script type="text/javascript" src="libs/aos/aos.js"></script>
    <script type="text/javascript" src="libs/chartjs/chart.min.js"></script>
    <script type="text/javascript" src="libs/fancybox/jquery.fancybox.min.js"></script>
    <script type="text/javascript" src="js/main.js"></script>

    <script type="text/javascript">
      var app = new Vue({
        el: '#app',
        data() {
          return {};
        },
        components: {
          myheader: httpVueLoader('vue_components/header.vue'),
          myfooter: httpVueLoader('vue_components/footer.vue'),
          supports: httpVueLoader('vue_components/supports.vue'),
          rednavi: httpVueLoader('vue_components/red-slide-navigation.vue'),
          yellownavi: httpVueLoader('vue_components/yellow-slide-navigation.vue'),
        },
      });
    </script>
  </body>
</html>
