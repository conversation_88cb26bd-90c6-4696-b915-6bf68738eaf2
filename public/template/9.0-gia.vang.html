<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <link rel="shortcut icon" async href="images/common/favicon.png" />
  <title>Website Bảo Tín Mạnh Hải</title>
  <link rel="stylesheet" href="libs/bootstrap/bootstrap.min.css" />
  <link rel="stylesheet" href="libs/swiper/swiper-bundle.min.css" />
  <link rel="stylesheet" href="libs/fancybox/jquery.fancybox.min.css" />
  <link rel="stylesheet" href="libs/flatpickr/flatpickr.min.css" />
  <link rel="stylesheet" href="libs/aos/aos.css" />
  <link rel="stylesheet" href="libs/fancybox/jquery.fancybox.min.css" />
  <link rel="stylesheet" href="css/style.css" />
</head>

<body>
  <div id="app">
    <myheader></myheader>
    <main>
      <div class="master-wrapper">
        <div class="golden-timeprice-page">

          <div class="box-elementor-chart py-lg-5 py-4">
            <div class="container">
              <div class="home-chart">
                <div class="chart-container-row">
                  <div class="chart-container mb-lg-5 mb-4">
                    <div class="block-title">
                      <h2 class="title">
                        GÍA VÀNG HÔM NAY
                      </h2>
                    </div>
                    <div class="chart-body chart-entry">
                      <div class="table-responsive gold-table">
                        <p class="unit">Đơn vị tính: Đồng/chỉ</p>
                        <table class="gold-table-content">
                          <thead>
                            <tr>
                              <th>LOẠI VÀNG</th>
                              <th>MUA VÀO</th>
                              <th>BÁN RA</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr>
                              <td>Kim Dần Niên</td>
                              <td class="price price-up">
                                5.456.000

                                <img class="img-fluid" src="images/common/price-up.png" alt="" />
                              </td>
                              <td class="price price-up">
                                5.621.000

                                <img class="img-fluid" src="images/common/price-up.png" alt="" />
                              </td>
                            </tr>
                            <tr>
                              <td>Vàng miến SJC</td>
                              <td class="price price-down">
                                6.911.000

                                <img class="img-fluid" src="images/common/price-down.png" alt="" />
                              </td>
                              <td class="price price-down">
                                6.984.000

                                <img class="img-fluid" src="images/common/price-down.png" alt="" />
                              </td>
                            </tr>
                            <tr>
                              <td>Nhẫn ép vỉ Vàng Rồng Thăng Long</td>
                              <td class="price">5.451.000</td>
                              <td class="price">5.526.000</td>
                            </tr>
                            <tr>
                              <td>Nhẫn ép vỉ Vàng Rồng Thăng Long</td>
                              <td class="price">5.451.000</td>
                              <td class="price">5.526.000</td>
                            </tr>
                            <tr>
                              <td>Nhẫn ép vỉ Vàng Rồng Thăng Long</td>
                              <td class="price">5.451.000</td>
                              <td class="price">5.526.000</td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>
                    <div class="chart-footer">
                      <p class="note">(Cập nhật lúc 10:48 16/08/2022) (đơn vị tính: đồng/chỉ)</p>
                    </div>
                  </div>
                  <div class="chart-container container-r w-100 mw-100">
                    <div class="block-title">
                      <h2 class="title">
                        BIỂU ĐỒ GIÁ VÀNG
                      </h2>
                    </div>
                    <div class="chart-body chart-entry">
                      <div class="js-chart-top d-flex">
                        <form action="">
                          <p>Mã vàng:</p>
                          <select>
                            <option value="KDN">Kim Dần Niên</option>
                            <option value="KGB">Nhẫn ép vỉ Kim Gia Bảo</option>
                            <option value="999">Vàng 99.9</option>
                            <option value="VRTL">Nhẫn ép vỉ Vàng Rồng Thăng Long</option>
                            <option value="SJC">Vàng miếng SJC</option>
                            <option value="9999">Vàng 999.9</option>
                          </select>
                        </form>
                        <div class="chart-footer">
                          <form action="">
                            <p>Xem theo:</p>
                            <div class="custom-radio custom-control-inline">
                              <input type="radio" id="customRadioInline1" name="customRadioInline1"
                                class="custom-control-input chart-radio" checked="checked" value="day" />
                              <label class="custom-label" for="customRadioInline1">24 giờ</label>
                            </div>
                            <div class="custom-radio custom-control-inline">
                              <input type="radio" id="customRadioInline2" name="customRadioInline1"
                                class="custom-control-input chart-radio" value="week" />
                              <label class="custom-label" for="customRadioInline2">Tuần</label>
                            </div>
                            <div class="custom-radio custom-control-inline">
                              <input type="radio" id="customRadioInline3" name="customRadioInline1"
                                class="custom-control-input chart-radio" value="month" />
                              <label class="custom-label" for="customRadioInline3">Tháng</label>
                            </div>
                            <div class="custom-radio custom-control-inline">
                              <input type="radio" id="customRadioInline4" name="customRadioInline1"
                                class="custom-control-input chart-radio" value="year" />
                              <label class="custom-label" for="customRadioInline4">Năm</label>
                            </div>
                          </form>
                        </div>

                      </div>
                      <canvas id="myChart"></canvas>
                      <div class="chart-note">
                        <div class="note note-buy">
                          <span></span>
                          <p>Mua vào</p>
                        </div>
                        <div class="note note-sell">
                          <span></span>
                          <p>Bán ra</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="box-elementor-support">
            <div class="container">
              <div class="elementor-flex elementor-background elementor-padding">

                <div class="elementor-title">
                  <h2 class="title text-lg-left text-center">
                    BẠN CẦN ĐƯỢC TƯ VẤN?
                  </h2>

                  <span class="sub text-lg-left text-center">
                    Đặt lịch hẹn để được tư vấn online miễn phí hoặc tư vấn trực tiếp tại các cửa hàng của Bảo Tín Mạnh
                    Hải
                  </span>
                </div>

                <div class="elementor-button">
                  <div class="d-block text-lg-right text-center">
                    <a href="javascript:;" class="btn">
                      Đặt lịch hẹn ngay
                    </a>
                  </div>

                  <div class="d-block text-lg-right text-center">
                    <span class="notice">
                      Hoặc liên hệ trực tiếp qua Hotline <b>024 2233 9999</b>
                    </span>
                  </div>
                </div>

              </div>
            </div>
          </div>

          <div class="box-elementor-blog">
            <div class="container">

              <div class="box-title">
                <h2 class="title">
                  Tin tức giá vàng
                </h2>
              </div>

              <div id="blogPriceGolden" class="blog-slide mb-lg-5 mb-4">
                <!-- Swiper -->
                <div id class="swiper mySwiper overflow-hidden">
                  <div class="swiper-wrapper">
                    <div class="swiper-slide" v-for="item in 9">
                      <article class="article-vertical-base">
                        <div class="image">
                          <a href="javascript:;" title="">
                            <img src="images/c.png" alt="">
                          </a>
                        </div>

                        <div class="content">
                          <a href="javascript:;" class="title">
                            Bản tin giá vàng hôm nay ngày 07/06/2022
                          </a>

                          <a href="javascript:;" class="btn">
                            <span>xem thêm</span>
                            <img src="images/arrow-right.svg" alt="">
                          </a>
                        </div>
                      </article>
                    </div>
                  </div>
                </div>
              </div>

              <div class="d-block text-center">
                <a href="javascript:;" class="btn-bg-red btn-text-golden-gradient p-2 btn-w-275">
                  <span>
                    Xem tất cả tin tức giá vàng
                  </span>
                </a>
              </div>

            </div>
          </div>
        </div>
      </div>
    </main>
    <myfooter></myfooter>
  </div>

  <script type="text/javascript" src="js/vue.js"></script>
  <script type="text/javascript" src="js/httpvueloader.js"></script>
  <script type="text/javascript" src="libs/swiper/swiper-bundle.min.js"></script>
  <script type="text/javascript" src="libs/bootstrap/jquery.min.js"></script>
  <script type="text/javascript" src="libs/bootstrap/popper.min.js"></script>
  <script type="text/javascript" src="libs/bootstrap/bootstrap.min.js"></script>
  <script type="text/javascript" src="libs/aos/aos.js"></script>
  <script type="text/javascript" src="libs/chartjs/chart.min.js"></script>
  <script src="libs/chartjs/chartjs-adapter-fns.min.js"></script>
  <script type="text/javascript" src="libs/fancybox/jquery.fancybox.min.js"></script>
  <script type="text/javascript" src="js/main.js"></script>

  <script type="text/javascript">
    var app = new Vue({
      el: '#app',
      data() {
        return {};
      },
      components: {
        myheader: httpVueLoader('vue_components/header.vue'),
        myfooter: httpVueLoader('vue_components/footer.vue'),
        rednavi: httpVueLoader('vue_components/red-slide-navigation.vue'),
        yellownavi: httpVueLoader('vue_components/yellow-slide-navigation.vue'),
      },
      mounted() {
        $(window).bind('load', function () {
          var blogPriceGolden = new Swiper("#blogPriceGolden .mySwiper", {
            autoplay: true,
            breakpoints: {
              320: {
                slidesPerView: 1,
                spaceBetween: 15
              },
              768: {
                slidesPerView: 2,
                spaceBetween: 15
              },
              992: {
                slidesPerView: 3,
                spaceBetween: 15
              },
              1200: {
                slidesPerView: 3,
                spaceBetween: 30
              },
            }
          });

          // chart js
          var jsonHour = {
            "labels": [
              "2022-08-20 10:23:00",
              "2022-08-20 10:28:00",
              "2022-08-20 11:33:00",
              "2022-08-20 12:38:00",
              "2022-08-20 13:43:00",
              "2022-08-20 14:48:00",
              "2022-08-20 15:53:00",
              "2022-08-20 16:58:00",
              "2022-08-20 17:03:00",
              "2022-08-20 18:08:00",
            ],
            "data": {
              "rate": [
                50,
                50.022,
                51.225,
                53.1,
                54,
                54.6,
                55.22,
                57.5,
                58.8,
                61
              ],
              "sell": [
                60,
                61.022,
                62.225,
                63.1,
                64,
                64.6,
                66.22,
                67.6,
                68.8,
                69
              ],
            }
          }

          var jsonDay = {
            labels: [
              '2022-08-20 10:23:00',
              '2022-08-20 10:28:00',
              '2022-08-20 11:33:00',
              '2022-08-20 12:38:00',
              '2022-08-20 13:43:00',
              '2022-08-20 14:48:00',
              '2022-08-20 15:53:00',
              '2022-08-20 16:58:00',
              '2022-08-20 17:03:00',
              '2022-08-20 18:08:00',

              '2022-08-21 10:23:00',
              '2022-08-21 10:28:00',
              '2022-08-21 11:33:00',
              '2022-08-21 12:38:00',
              '2022-08-21 13:43:00',
              '2022-08-21 14:48:00',
              '2022-08-21 15:53:00',
              '2022-08-21 16:58:00',
              '2022-08-21 17:03:00',
              '2022-08-21 18:08:00',

              '2022-08-22 10:23:00',
              '2022-08-22 10:28:00',
              '2022-08-22 11:33:00',
              '2022-08-22 12:38:00',
              '2022-08-22 13:43:00',
              '2022-08-22 14:48:00',
              '2022-08-22 15:53:00',
              '2022-08-22 16:58:00',
              '2022-08-22 17:03:00',
              '2022-08-22 18:08:00',

              '2022-08-23 10:23:00',
              '2022-08-23 10:28:00',
              '2022-08-23 11:33:00',
              '2022-08-23 12:38:00',
              '2022-08-23 13:43:00',
              '2022-08-23 14:48:00',
              '2022-08-23 15:53:00',
              '2022-08-23 16:58:00',
              '2022-08-23 17:03:00',
              '2022-08-23 18:08:00',

              '2022-08-24 10:23:00',
              '2022-08-24 10:28:00',
              '2022-08-24 11:33:00',
              '2022-08-24 12:38:00',
              '2022-08-24 13:43:00',
              '2022-08-24 14:48:00',
              '2022-08-24 15:53:00',
              '2022-08-24 16:58:00',
              '2022-08-24 17:03:00',
              '2022-08-24 18:08:00',

              '2022-08-25 10:23:00',
              '2022-08-25 10:28:00',
              '2022-08-25 11:33:00',
              '2022-08-25 12:38:00',
              '2022-08-25 13:43:00',
              '2022-08-25 14:48:00',
              '2022-08-25 15:53:00',
              '2022-08-25 16:58:00',
              '2022-08-25 17:03:00',
              '2022-08-25 18:08:00',
            ],
            data: {
              rate: [
                50, 50.022, 51.225, 53.1, 54, 54.6, 55.22, 57.5, 58.8, 61, 50, 50.022, 51.225, 53.1, 54, 54.6, 55.22, 57.5, 58.8, 61, 50, 50.022, 51.225,
                53.1, 54, 54.6, 55.22, 57.5, 58.8, 61, 50, 50.022, 51.225, 53.1, 54, 54.6, 55.22, 57.5, 58.8, 61, 50, 50.022, 51.225, 53.1, 54, 54.6, 55.22,
                57.5, 58.8, 61, 50, 50.022, 51.225, 53.1, 54, 54.6, 55.22, 57.5, 58.8, 61,
              ],
              sell: [
                60, 61.022, 62.225, 63.1, 64, 64.6, 66.22, 67.6, 68.8, 69, 60, 61.022, 62.225, 63.1, 64, 64.6, 66.22, 67.6, 68.8, 69, 60, 61.022, 62.225,
                63.1, 64, 64.6, 66.22, 67.6, 68.8, 69, 60, 61.022, 62.225, 63.1, 64, 64.6, 66.22, 67.6, 68.8, 69, 60, 61.022, 62.225, 63.1, 64, 64.6, 66.22,
                67.6, 68.8, 69, 60, 61.022, 62.225, 63.1, 64, 64.6, 66.22, 67.6, 68.8, 69,
              ],
            },
          };

          var xAsisOptions = {
            stepSize: 2,
            min: 0,
            max: 0,
            unit: 'hour',
            displayFormats: { hour: 'H:mm' }
          };


          initChart(jsonHour, xAsisOptions)


          // chart radio
          $('.chart-radio').on('change', function () {

            let chartType = $(this).val();

            updateChart(chartType)
          });

          // var config = {
          //   type: 'line',
          //   data: {
          //     labels: jsonDay.labels,
          //     datasets: [
          //       {
          //         label: 'Bán',
          //         data: jsonDay.data.rate,
          //         backgroundColor: 'transparent',
          //         borderColor: 'rgb(33 150 83)',
          //         lineTension: 0,
          //         pointRadius: 0,
          //         pointHitRadius: 4,
          //       },
          //       {
          //         label: 'Mua',
          //         data: jsonDay.data.sell,
          //         backgroundColor: 'transparent',
          //         borderColor: 'rgb(170 31 35)',
          //         lineTension: 0,
          //         pointRadius: 0,
          //         borderWidth: 1,
          //         pointHitRadius: 4,
          //       },
          //     ],
          //   },

          //   options: {
          //     legend: {
          //       display: false,
          //     },
          //     animation: false,
          //     scales: {
          //       y: {
          //         position: 'right',
          //         grid: {
          //           borderWidth: 0,
          //         },
          //         ticks: {
          //           color: '#BABABA',
          //           font: {
          //             size: 14,
          //             family: 'SVN-Megante',
          //           },
          //           callback: function (value) {
          //             let minValue = jsonDay.data.rate[9];
          //             let maxValue = jsonDay.data.sell[9];
          //             return value;
          //           },
          //         },
          //       },
          //       x: {
          //         grid: {
          //           display: false,
          //         },
          //         // offset : false ,
          //         type: 'time',
          //         beginAtZero: 'false',
          //         time: {
          //           // stepSize: 1,
          //           // parser: 'm:s.SSS',
          //           min: min,
          //           unit: 'day',
          //           displayFormats: { week: 'd/M' },
          //         },
          //         ticks: {
          //           color: 'black',
          //           font: {
          //             size: 14,
          //             family: 'SVN-Megante',
          //           },
          //         },
          //       },
          //     },
          //   },
          // };
          // var options = {
          //     legend: {
          //       display: false,
          //     },
          //     animation: false,
          //     scales: {
          //       y: {
          //         position: 'right',
          //         grid: {
          //           borderWidth: 0,
          //         },
          //         ticks: {
          //           color: '#BABABA',
          //           font: {
          //             size: 14,
          //             family: 'SVN-Megante',
          //           },
          //           callback: function (value) {
          //             let minValue = jsonDay.data.rate[9];
          //             let maxValue = jsonDay.data.sell[9];
          //             return value;
          //           },
          //         },
          //       },
          //       x: {
          //         grid: {
          //           display: false,
          //         },
          //         // offset : false ,
          //         type: 'time',
          //         beginAtZero: 'false',
          //         time: {
          //           // stepSize: 1,
          //           // parser: 'm:s.SSS',
          //           min: min,
          //           unit: 'day',
          //           displayFormats: { week: 'd/M' },
          //         },
          //         ticks: {
          //           color: 'black',
          //           font: {
          //             size: 14,
          //             family: 'SVN-Megante',
          //           },
          //         },
          //       },
          //     },
          // }


        })
      },


    });
  </script>
</body>

</html>