<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <link rel="shortcut icon" async href="images/common/favicon.png" />
  <title>Website Bảo Tín Mạnh Hải</title>
  <link rel="stylesheet" href="libs/bootstrap/bootstrap.min.css" />
  <link rel="stylesheet" href="libs/swiper/swiper-bundle.min.css" />
  <link rel="stylesheet" href="libs/fancybox/jquery.fancybox.min.css" />
  <link href="libs/flatpickr/flatpickr.min.css" rel="stylesheet" />
  <link rel="stylesheet" href="libs/aos/aos.css" />
  <link rel="stylesheet" href="libs/fancybox/jquery.fancybox.min.css" />
  <link rel="stylesheet" href="css/style.css" />
</head>

<body>
  <div id="app">
    <myheader></myheader>
    <main>
      <div class="master-wrapper">
        <div class="cart-page">
          <div class="container">
            <div class="cart-page-container cart-page-container-full">
              
              <div class="row">
                <div class="col-lg-6 col-12">
                    <div class="block-cart-title mb-4">
                        <h2 class="title text-left">
                            Thông tin giỏ hàng
                        </h2>
                    </div>
                    <div class="block-cart-content">
                        <div class="incart-list-item">
                          <div class="incart-item">
                            
                            <div class="incart-item-wrap">
                              <a href="javascript:;" class="img">
                                <img src="images/product/trangsuc-daquy.jpg" alt="" class="img-fluid">
                              </a>
                              <div class="info">
                                  <a href="javascript:;" class="product-name">
                                      Nhẫn vàng phong cách Ý NY10K30
                                  </a>
                                  <div class="product-sku">
                                    <span class="title">
                                      Mã sản phẩm : NY10K30
                                    </span> 
                                  </div>
                                  <div class="price-box">
                                    <div class="product-price">
                                      <span class="title">
                                        Giá :
                                      </span>
                                      <span class="color-theme value">
                                        2.500.000 VNĐ
                                      </span>
                                    </div>
    
                                    <div class="cart-qty">
                                      <span class="new-cart-quantity js-row-item">
                                          <a href="javascript:void(0);" class="minor quantity-change disallowed" data-value="-1">
                                            <img src="images/ic-minor.png" alt="" width="20" height="20">
                                          </a>
                                          <input class="buy-quantity quantity-change input-number" value="01" size="5" data-value="1"/>
                                          <a href="javascript:void(0);" class="add quantity-change" data-value="1">
                                            <img src="images/ic-plus.png" alt="" width="20" height="20">
                                          </a>
                                      </span>
                                    </div>
                                  </div>
                                  <div class="product-config">
                                    <div class="box-title">
                                      <span class="title">
                                        Chọn kích cỡ
                                      </span>
                                    </div>
                                    <div class="config-list">
                                      <a href="javascript:;" class="config-item js-config-item disabled">
                                        09
                                      </a>
                                      <a href="javascript:;" class="config-item js-config-item">
                                        10
                                      </a>
                                      <a href="javascript:;" class="config-item js-config-item">
                                        11
                                      </a>
                                      <a href="javascript:;" class="config-item js-config-item">
                                        12
                                      </a>
                                      <a href="javascript:;" class="config-item js-config-item">
                                        13
                                      </a>
                                    </div>
                                  </div>
                                  <div class="actions">
                                      <a href="" class="del-item">
                                        <img src="images/ic-trash.png" alt="" width="20" height="20">
                                        <span class="txt">
                                          Xóa
                                        </span>
                                      </a>
                                  </div>
                              </div>
                          </div>
                          </div>
                          <div class="incart-item">

                              <div class="incart-item-wrap">
                                  <a href="javascript:;" class="img">
                                    <img src="images/product/trangsuc-daquy.jpg" alt="" class="img-fluid">
                                  </a>
                                  <div class="info">
                                      <a href="javascript:;" class="product-name">
                                          Nhẫn vàng phong cách Ý NY10K30
                                      </a>
                                      <div class="product-sku">
                                        <span class="title">
                                          Mã sản phẩm : NY10K30
                                        </span> 
                                      </div>
                                      <div class="price-box">
                                        <div class="product-price">
                                          <span class="title">
                                            Giá :
                                          </span>
                                          <span class="color-theme value">
                                            2.500.000 VNĐ
                                          </span>
                                        </div>
        
                                        <div class="cart-qty">
                                          <span class="new-cart-quantity js-row-item">
                                              <a href="javascript:void(0);" class="minor quantity-change disallowed" data-value="-1">
                                                <img src="images/ic-minor.png" alt="" width="20" height="20">
                                              </a>
                                              <input class="buy-quantity quantity-change input-number" value="01" size="5" />
                                              <a href="javascript:void(0);" class="add quantity-change" data-value="1">
                                                <img src="images/ic-plus.png" alt="" width="20" height="20">
                                              </a>
                                          </span>
                                        </div>
                                      </div>
                                      <div class="product-config">
                                        <div class="box-title">
                                          <span class="title">
                                            Chọn kích cỡ
                                          </span>
                                        </div>
                                        <div class="config-list">
                                          <a href="javascript:;" class="config-item js-config-item disabled">
                                            09
                                          </a>
                                          <a href="javascript:;" class="config-item js-config-item">
                                            10
                                          </a>
                                          <a href="javascript:;" class="config-item js-config-item">
                                            11
                                          </a>
                                          <a href="javascript:;" class="config-item js-config-item">
                                            12
                                          </a>
                                          <a href="javascript:;" class="config-item js-config-item">
                                            13
                                          </a>
                                        </div>
                                      </div>
                                      <div class="actions">
                                        <a href="" class="del-item">
                                          <img src="images/ic-trash.png" alt="" width="20" height="20">
                                          <span class="txt">
                                            Xóa
                                          </span>
                                        </a>
                                    </div>
                                  </div>
                              </div>
                              
                              <div class="special-offers special-offer-outside">
                                <div class="title">
                                  Ưu đãi
                                </div>
                                <div class="special-offer-item">
                                  <div class="item">
                                    Giảm 30% từ 25/05/2022 đến 15/06/2022
                                  </div>
                                  <div class="item">
                                    Nhận ngay phiếu mua hàng trị giá 200.000đ khi mua từ 2 sản phẩm trở lên từ 25/05/2022 đến
                                    15/06/2022
                                  </div>
                                </div>
                              </div>

                          </div>

                        </div>

                        

                        <div class="block-cart-voucher">
                          <div class="title">
                            Mã giảm giá
                          </div>
                          <div class="voucher-apply">
                              <div class="cart-voucher">
                                <input id="js_voucher_input" class="form-control" placeholder="Mã giảm giá " size="20" />
                                <button class="btn-theme js-voucher-submit">
                                  <span class="txt">
                                    Áp dụng
                                  </span>
                                </button>
                              </div>
                              <!-- <span id="js-voucher-message" class="error">
                                Mã không hợp lệ
                              </span> -->
                              
                              <div id="js-voucher-message">
                                  <span class="error">
                                    Mã không hợp lệ
                                  </span>
                                  <div class="js-voucher-apply voucher-code-applied" id="voucher-code-1">
                                      <span class="txt">
                                        Mã giảm giá: OHFQOHFOQBB
                                      </span>
                                      <a href="javascript:;" class="ic-close" onclick="removeCode(1)">
                                          <img src="images/ic-close-black.png" alt="" class="img-fluid" width="24" height="24">
                                      </a>
                                  </div>
                                  <div class="js-voucher-apply voucher-code-applied" id="voucher-code-2">
                                      <span class="txt">
                                        Mã giảm giá: OHFQOHFOQBB
                                      </span>
                                      <a href="javascript:;" class="ic-close" onclick="removeCode(2)">
                                          <img src="images/ic-close-black.png" alt="" class="img-fluid" width="24" height="24">
                                      </a>
                                  </div>
                              </div>
                          </div>
                        </div>

                        <div class="block-cart-total">
                            <div class="item price">
                              <div class="name">
                                Tạm tính:
                              </div>
                              <div class="value">
                                5.000.000 VNĐ
                              </div>
                            </div>
                            <div class="item discount">
                              <div class="name">
                                Khuyến mại:
                              </div>
                              <div class="value">
                                (30%) - 1.500.000 VNĐ
                              </div>
                            </div>
                            <div class="item shipping">
                              <div class="name">
                                Vận chuyển:
                              </div>
                              <div class="value">
                                0 VNĐ
                              </div>
                            </div>
                            <div class="item cart-total">
                              <div class="name">
                                Tổng cộng:
                              </div>
                              <div class="value">
                                3.550.000 VNĐ
                              </div>
                            </div>
                        </div>
                        
                    </div>
                  
                        
                </div>
                <div class="col-lg-6 col-12">
                    <div class="customer-card">
                      <div class="customer-card-main">
                        <div class="customer-card-info">
                          <div class="title">
                            Thông tin khách hàng
                          </div>
                          <div class="form-group">
                            <input type="text" class="form-control" placeholder="Họ và tên (*)">
                            <small class="error text-danger font-italic">Vui lòng nhập họ và tên !</small>
                          </div>
                          <div class="form-group">
                            <input type="text" class="form-control" placeholder="Số điện thoại (*)">
                            <small class="error text-danger font-italic">Vui lòng nhập số điện thoại !</small>
                          </div>
                          <div class="form-group">
                            <input type="text" class="form-control" placeholder="Email (*)">
                            <small class="error text-danger font-italic">Vui lòng nhập email !</small>
                          </div>
                        </div>

                        <div class="customer-card-info">
                          <div class="title">
                            Hình thức nhận hàng
                          </div>
                          <div class="payment-tabs">
                            <a href="javascript:;" class="item ship-tab-name js-show-ship active" data-target="#ship-tab-1">
                              <span class="name">
                                Giao hàng tận nơi
                              </span>
                              <span class="desc">
                                (miễn phí giao hàng toàn quốc)
                              </span>
                            </a>
                            <a href="javascript:;" class="item ship-tab-name js-show-ship" data-target="#ship-tab-2">
                              <span class="name">
                                Nhận tại cửa hàng
                              </span>
                              <span class="desc">
                                (Giữ hàng lấy ngay)
                              </span>
                            </a>
                          </div>


                          <div class="payment-tabs-content">
                            <div id="ship-tab-1" class="ship-type-tab tab-content active">
                                <div class="customer-card-info">
                                  <div class="row">
                                      <div class="col-6">
                                        <div class="form-group">
                                          <select name="" id="">
                                            <option value="0" selected disabled> Chọn Tỉnh/Thành phố (*) </option>
                                            <option value="1"> Hà Nội </option>
                                            <option value="2"> Tp.HCM </option>
                                            <option value="3"> Đà Nẵng </option>
                                          </select>
                                        </div>
                                      </div>
                                      <div class="col-6">
                                        <div class="form-group">
                                          <select name="" id="">
                                            <option value="0" selected disabled> Chọn Quận/Huyện (*) </option>
                                            <option value="1"> Hà Nội </option>
                                            <option value="2"> Tp.HCM </option>
                                            <option value="3"> Đà Nẵng </option>
                                          </select>
                                        </div>
                                      </div>
                                      <div class="col-6">
                                        <div class="form-group">
                                          <select name="" id="">
                                            <option value="0" selected disabled> Chọn Phường/Xã (*) </option>
                                            <option value="1"> Hà Nội </option>
                                            <option value="2"> Tp.HCM </option>
                                            <option value="3"> Đà Nẵng </option>
                                          </select>
                                        </div>
                                      </div>
                                      <div class="col-6">
                                        <div class="form-group">
                                          <input type="text" class="form-control" placeholder="Nhập địa chỉ ">
                                        </div>
                                      </div>
                                      <div class="col-12">
                                        <div class="form-group">
                                          <input type="text" class="form-control" placeholder="Lời nhắn cho người bán">
                                        </div>
                                      </div>
                                  </div>
                                </div>
                                <div class="customer-card-info">
                                  <div class="title">
                                    Hình thức vận chuyển
                                  </div>
                                  <div class="shipping-note">
                                    <div class="custom-radio">
                                        <input type="radio" class="" id="shipping_note_1" name="shipping_note" />
                                        <label class="custom-radio-label" for="shipping_note_1" >
                                          Miễn phí giao hàng toàn quốc &nbsp;
                                          Dự kiến giao hàng: <b>1-3 ngày</b>
                                        </label>
                                    </div>
                                  </div>
                                  <div class="shipping-note">
                                    <div class="custom-radio">
                                        <input type="radio" class="" id="shipping_note_2" name="shipping_note" />
                                        <label class="custom-radio-label" for="shipping_note_2" >
                                          Miễn phí giao hàng siêu tốc: &nbsp;
                                          Dự kiến giao hàng:  <b>08/08/2022</b>
                                        </label>
                                    </div>
                                  </div>
                                </div>
                            </div>
                            <div id="ship-tab-2" class="ship-type-tab tab-content">
                              <div class="customer-card-info">
                                <div class="row">
                                    <div class="col-12">
                                      <div class="form-group">
                                        <select name="" id="">
                                          <option value="0" selected disabled> Chọn cửa hàng </option>
                                          <option value="1"> Hà Nội </option>
                                          <option value="2"> Tp.HCM </option>
                                          <option value="3"> Đà Nẵng </option>
                                        </select>
                                      </div>
                                    </div>
                                    <div class="col-12">
                                      <div class="form-group">
                                        <input type="text" class="form-control" placeholder="Lời nhắn cho người bán">
                                      </div>
                                    </div>
                                </div>
                              </div>
                            </div>
                          </div>

                          <div class="other-noticed mb-2">
                            <div class="custom-checkbox">
                                <input type="checkbox" class="" id="vat" name="" onchange="$('#vat-form').toggle()"/>
                                <label class="custom-checkbox-label" for="vat" >
                                  Xuất hóa đơn công ty
                                </label>
                            </div>
                            <div id="vat-form" style="display: none;">
                                <p>
                                  Tich chọn và điền thông tin nếu bạn muốn xuất hóa đơn diện tử
                                </p>
                                <div class="form-group">
                                  <label for="vat-company">Tên công ty / Đơn vị</label>
                                  <input type="text" class="form-control" placeholder="">
                                </div>
                                <div class="form-group">
                                  <label for="vat-mst">Mã số thuế</label>
                                  <input type="text" class="form-control" placeholder="">
                                </div>
                                <div class="form-group">
                                  <label for="vat-address">Địa chỉ</label>
                                  <input type="text" class="form-control" placeholder="">
                                </div>
                                <div class="form-group">
                                  <label for="vat-email">Email nhận hóa đơn</label>
                                  <input type="text" class="form-control" placeholder="">
                                </div>
                            </div>
                          </div>
                          <div class="other-noticed mb-2">
                            <div class="custom-checkbox">
                                <input type="checkbox" class="" id="privacy" name="" />
                                <label class="custom-checkbox-label" for="privacy" >
                                  Tôi đồng ý nhận các thông tin và chương trình khuyến mãi của Bảo Tín Mạnh Hải.
                                </label>
                            </div>
                          </div>

                          <div class="submit-cart">
                            <button class="btn-theme" type="submit">
                              <span class="txt">
                                Tiếp theo

                              </span>
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                </div>
              </div>

             
            </div>
          </div>
        </div>
        <support></support>
      </div>
    </main>
    <myfooter></myfooter>
  </div>

  <script type="text/javascript" src="js/vue.js"></script>
  <script type="text/javascript" src="js/httpvueloader.js"></script>
  <script type="text/javascript" src="libs/swiper/swiper-bundle.min.js"></script>
  <script type="text/javascript" src="libs/swiper/swiper-animation.min.js"></script>
  <script type="text/javascript" src="libs/bootstrap/jquery.min.js"></script>
  <script type="text/javascript" src="libs/bootstrap/popper.min.js"></script>
  <script type="text/javascript" src="libs/bootstrap/bootstrap.min.js"></script>
  <script type="text/javascript" src="libs/aos/aos.js"></script>
  <script type="text/javascript" src="libs/chartjs/chart.min.js"></script>
  <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.27.0/moment.min.js" integrity="sha512-rmZcZsyhe0/MAjquhTgiUcb4d9knaFc7b5xAfju483gbEXTkeJRUMIPk6s3ySZMYUHEcjKbjLjyddGWMrNEvZg==" crossorigin="anonymous"></script> -->
  <script src="libs/chartjs/chartjs-adapter-fns.min.js"></script>

  <script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@11.0.6/dist/lazyload.min.js"></script>
  <script type="text/javascript" src="libs/fancybox/jquery.fancybox.min.js"></script>
  <script type="text/javascript" src="js/main.js"></script>

  <script type="text/javascript">
    var app = new Vue({
      el: '#app',
      data() {
        return {
          flashSaleEndTime: 80000,
          activeItem: 'tab-1',
        };
      },
      components: {
        myheader: httpVueLoader('vue_components/header.vue'),
        myfooter: httpVueLoader('vue_components/footer.vue'),
        rednavi: httpVueLoader('vue_components/red-slide-navigation.vue'),
        yellownavi: httpVueLoader('vue_components/yellow-slide-navigation.vue'),
        support: httpVueLoader('vue_components/supports.vue'),
      },
      methods: {
        isActive(menuItem) {
          return this.activeItem === menuItem;
        },
        setActive(menuItem) {
          this.activeItem = menuItem;
        },
      },
      mounted() {
        this.$nextTick(() => {
         
          $(window).bind('load', function () {
            if (LazyLoad) {
              var lazyloader = new LazyLoad({
                elements: '.lazy',
              });
            }

            AOS.init({
              // startEvent: 'load',
              duration: 700,
              easing: 'ease',
            });

            qtyChange('.quantity-change');

            $('.js-show-ship').on('click' , function(e) {
              e.preventDefault;
              let target = $(this).data('target');

              $('.ship-tab-name.active').removeClass('active');
              $(this).addClass('active');
              $('.ship-type-tab.active').removeClass('active');
              if(!target) return ; 

              // hide other tab

              $(target).addClass('active');

            })

            
          });
        });
      },
    });
  </script>
</body>

</html>