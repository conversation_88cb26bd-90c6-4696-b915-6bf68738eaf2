<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <link rel="shortcut icon" async href="images/common/favicon.png" />
  <title>Website Bảo Tín Mạnh Hải</title>
  <link rel="stylesheet" href="libs/bootstrap/bootstrap.min.css" />
  <link rel="stylesheet" href="libs/swiper/swiper-bundle.min.css" />
  <link rel="stylesheet" href="libs/fancybox/jquery.fancybox.min.css" />
  <link href="libs/flatpickr/flatpickr.min.css" rel="stylesheet" />
  <link rel="stylesheet" href="libs/aos/aos.css" />
  <link rel="stylesheet" href="libs/fancybox/jquery.fancybox.min.css" />
  <link rel="stylesheet" href="css/style.css" />
</head>

<body>
  <div id="app">
    <myheader></myheader>
    <main>
      <div class="master-wrapper">
        <div class="cart-page">
          <div class="container">
            <div class="cart-page-success">

              <div class="cart-success-wrap">

                <div class="cart-order">
                  <div class="wellcome-txt">
                    <div class="img">
                      <img src="images/ic-success-order.png" alt="" class="img-flui">
                    </div>
                    <div class="content">
                      <div class="bigtxt color-theme">
                        Đặt hàng thành công
                      </div>
                      <div class="desc">
                        Cám ơn quý khách đã mua hàng tại <span class="color-theme">
                          Bảo Tín Mạnh Hải
                        </span>
                        <br>
                        Đơn hàng <span class="color-theme">#123456</span> đã được tiếp nhận và đang trong quá trình xử
                        lý <br>
                        Chúng tôi sẽ liên hệ với quý khách để xử lý đơn hàng ngay lập tức.
                      </div>
                    </div>
                  </div>
                  <div class="cart-order-info">
                    <div class="title">
                      Đơn hàng #123456
                    </div>
                    <div class="item">
                      <div class="icon">
                        <img src="images/ic-clock-red.png" alt="" class="img-fluid" width="24" height="24">
                      </div>
                      <div class="value">
                        Thời gian đặt hàng: <span class="color-black">17/06/2022 - 16:20</span>
                      </div>
                    </div>
                    <div class="item">
                      <div class="icon">
                        <img src="images/ic-truck-red.png" alt="" class="img-fluid" width="24" height="24">
                      </div>
                      <div class="value">
                        Dự kiến giao hàng: <span class="color-black">19/06/2022</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="cart-order">
                  <div class="order-cart-item">
                    <div class="left">
                      <a href="javascript:;" class="img">
                        <img src="images/product/trangsuc-daquy.jpg" alt="" class="img-fluid">
                      </a>
                      <div class="info">
                        <a href="javascript:;" class="name">
                          Nhẫn vàng phong cách Ý NY10K30
                        </a>
                        <div class="config">
                          Size 12
                        </div>
                      </div>
                    </div>
                    <div class="right">
                      <div class="price">
                        2.500.000 VNĐ
                      </div>
                      <div class="qty">
                        Số lượng: 01
                      </div>
                    </div>
                  </div>
                  <div class="order-cart-item">
                    <div class="left">
                      <a href="javascript:;" class="img">
                        <img src="images/product/trangsuc-daquy.jpg" alt="" class="img-fluid">
                      </a>
                      <div class="info">
                        <a href="javascript:;" class="name">
                          Nhẫn vàng phong cách Ý NY10K30
                        </a>
                        <div class="config">
                          Size 12
                        </div>
                      </div>
                    </div>
                    <div class="right">
                      <div class="price">
                        2.500.000 VNĐ
                      </div>
                      <div class="qty">
                        Số lượng: 01
                      </div>
                    </div>
                  </div>
                </div>
                <div class="cart-order">
                  <div class="block-cart-total">
                    <div class="item price">
                      <div class="name">
                        Tạm tính:
                      </div>
                      <div class="value">
                        5.000.000 VNĐ
                      </div>
                    </div>
                    <div class="item discount">
                      <div class="name">
                        Khuyến mại:
                      </div>
                      <div class="value">
                        (30%) - 1.500.000 VNĐ
                      </div>
                    </div>
                    <div class="item shipping">
                      <div class="name">
                        Vận chuyển:
                      </div>
                      <div class="value">
                        0 VNĐ
                      </div>
                    </div>
                    <div class="item cart-total">
                      <div class="name">
                        Tổng cộng:
                      </div>
                      <div class="value">
                        3.550.000 VNĐ
                      </div>
                    </div>
                  </div>
                </div>

                <div class="cart-order">
                    <div class="cart-order-note">
                        <div class="title">
                          Thanh toán
                        </div>
                        <div class="d-flex align-items-center info ">
                          <div class="name">
                            Hình thức thanh toán:
                          </div>
                          <div class="value">
                            Thẻ tín dụng/thẻ ghi nợ
                          </div>
                        </div>
                    </div>
                    <div class="cart-order-note">
                        <div class="title">
                          Thông tin nhận hàng
                        </div>
                        <div class="d-flex align-items-center info ">
                          <div class="name">
                            Họ và tên:
                          </div>
                          <div class="value">
                            Hoàng Văn Nhất
                          </div>
                        </div>
                        <div class="d-flex align-items-center info ">
                          <div class="name">
                            Số điện thoại:
                          </div>
                          <div class="value">
                            0987654321
                          </div>
                        </div>
                        <div class="d-flex align-items-center info ">
                          <div class="name">
                            Địa chỉ nhận hàng:
                          </div>
                          <div class="value">
                            Số 20 Đội Cấn, quận Ba Đình, Hà Nội Số 20 Đội Cấn, quận Ba Đình, Hà Nội Số 20 Đội Cấn, quận Ba Đình, Hà Nội Số 20 Đội Cấn, quận Ba Đình, Hà Nội
                          </div>
                        </div>
                    </div>
                </div>
              </div>


            </div>
            <div class="submit-cart last-step">
                <a class="btn-theme" href="javascript:;">
                  Về Trang chủ
                </a>
            </div>
          </div>
        </div>
      </div>
    </main>
    <myfooter></myfooter>
  </div>

  <script type="text/javascript" src="js/vue.js"></script>
  <script type="text/javascript" src="js/httpvueloader.js"></script>
  <script type="text/javascript" src="libs/swiper/swiper-bundle.min.js"></script>
  <script type="text/javascript" src="libs/swiper/swiper-animation.min.js"></script>
  <script type="text/javascript" src="libs/bootstrap/jquery.min.js"></script>
  <script type="text/javascript" src="libs/bootstrap/popper.min.js"></script>
  <script type="text/javascript" src="libs/bootstrap/bootstrap.min.js"></script>
  <script type="text/javascript" src="libs/aos/aos.js"></script>
  <script type="text/javascript" src="libs/chartjs/chart.min.js"></script>
  <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.27.0/moment.min.js" integrity="sha512-rmZcZsyhe0/MAjquhTgiUcb4d9knaFc7b5xAfju483gbEXTkeJRUMIPk6s3ySZMYUHEcjKbjLjyddGWMrNEvZg==" crossorigin="anonymous"></script> -->
  <script src="libs/chartjs/chartjs-adapter-fns.min.js"></script>

  <script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@11.0.6/dist/lazyload.min.js"></script>
  <script type="text/javascript" src="libs/fancybox/jquery.fancybox.min.js"></script>
  <script type="text/javascript" src="js/main.js"></script>

  <script type="text/javascript">
    var app = new Vue({
      el: '#app',
      data() {
        return {
          flashSaleEndTime: 80000,
          activeItem: 'tab-1',
        };
      },
      components: {
        myheader: httpVueLoader('vue_components/header.vue'),
        myfooter: httpVueLoader('vue_components/footer.vue'),
        rednavi: httpVueLoader('vue_components/red-slide-navigation.vue'),
        yellownavi: httpVueLoader('vue_components/yellow-slide-navigation.vue'),
      },
      methods: {
        isActive(menuItem) {
          return this.activeItem === menuItem;
        },
        setActive(menuItem) {
          this.activeItem = menuItem;
        },
      },
      mounted() {
        this.$nextTick(() => {
          $(window).bind('load', function () {
            if (LazyLoad) {
              var lazyloader = new LazyLoad({
                elements: '.lazy',
              });
            }

            AOS.init({
              // startEvent: 'load',
              duration: 700,
              easing: 'ease',
            });

            qtyChange('.quantity-change')
          });
        });
      },
    });
  </script>
</body>

</html>