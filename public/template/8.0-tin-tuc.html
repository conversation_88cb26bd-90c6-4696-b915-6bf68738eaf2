<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <link rel="shortcut icon" async href="images/common/favicon.png" />
  <title>Website Bảo Tín Mạnh Hải</title>
  <link rel="stylesheet" href="libs/bootstrap/bootstrap.min.css" />
  <link rel="stylesheet" href="libs/swiper/swiper-bundle.min.css" />
  <link rel="stylesheet" href="libs/fancybox/jquery.fancybox.min.css" />
  <link rel="stylesheet" href="libs/flatpickr/flatpickr.min.css" />
  <link rel="stylesheet" href="libs/aos/aos.css" />
  <link rel="stylesheet" href="libs/fancybox/jquery.fancybox.min.css" />
  <link rel="stylesheet" href="css/style.css" />
</head>

<body>
  <div id="app">
    <myheader></myheader>
    <main>
      <div class="news-site">
        <section class="articles">
          <div class="container">
            <div class="article-main">
              <div class="article-item" v-for="n in 3">
                <a href="" class="link-item"></a>
                <div class="image image-hover">
                  <img src="images/test.png" alt="" />
                </div>
                <h3>
                  <span>Bộ sưu tập Diamond Born To Shine – Kim Cương sinh ra để tỏa sáng</span>
                </h3>
              </div>
            </div>
          </div>
        </section>

        <section class="mix-match">
          <div class="container">
            <h3 class="title title-section">Mix & Match</h3>
            <div class="post">
              <div class="post-item post-include" v-for="n in 6">
                <a href="" class="image d-block"><img src="images/test.png" alt="" /></a>
                <h3><a href="" class="hover-title hover-title-black font-monsterat">Lorem ipsum dolor sit amet,
                    consectetur adipisicing elit.</a></h3>
              </div>
            </div>
          </div>
          <!--                <div class="col-md-4">
                  <aside class="categories-right">
                    <h3 class="text-uppercase">Danh mục tin tức</h3>
                    <ul>
                      <li><a href="" class="hover-title hover-title-black">Mix & match</a></li>
                      <li><a href="" class="hover-title hover-title-black">Cẩm nang cưới</a></li>
                      <li><a href="" class="hover-title hover-title-black">Kiến thức trang sức</a></li>
                      <li><a href="" class="hover-title hover-title-black">Câu chuyện về trang sức</a></li>
                      <li><a href="" class="hover-title hover-title-black">Trang sức & phong thủy</a></li>
                    </ul>

                    <h3 class="text-uppercase">Tin tức nổi bật</h3>
                  </aside>
                </div> -->
        </section>

        <section class="handbook-wedding">
          <div class="container">
            <h3 class="title-section mb-4">Cẩm nang cưới</h3>
            <div class="list">
              <div class="item position-relative" v-for="n in 3">
                <a href="" class="link-item"></a>
                <div class="image image-hover">
                  <img src="images/test.png" class="rounded-8" alt="" />
                </div>
                <h3 class="mb-0 name">
                  <span>Gợi ý chọn trang sức làm món quà cầu hôn thật ý nghĩa cho nàng</span>
                </h3>
              </div>
            </div>
          </div>
        </section>

        <section class="knowledge knowledge-jewels">
          <div class="container">
            <h3 class="title-section">Kiến thức trang sức</h3>

            <div class="knowledge__list">
              <div class="knowledge__item" v-for="n in 3">
                <div class="image image-hover">
                  <a href=""><img src="images/img-test.jpg" alt="" /></a>
                </div>
                <div class="content">
                  <a class="title d-inline-block" href="">
                    <span class="name"> Hành trình đi tìm phong cách cho bản thân không hề dễ dàng. BTMH sẽ giúp bạn
                      định hình và tìm cho mình phong cách phù hợp nhất! </span>
                    <span class="mt-3 d-inline-flex align-items-center link">Tìm hiểu thêm <img class="ml-2"
                        src="images/common/arrow-right.png" alt="" /> </span>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </section>

        <section class="story story-jewelry">
          <div class="container">
            <h3 class="title-section">Câu chuyện về trang sức</h3>

            <div class="story__list post">
              <div class="story__item post-include" v-for="n in 4">
                <div class="image image-hover">
                  <a href=""><img src="images/img-test.jpg" alt="" /></a>
                </div>
                <h3><a href="" class="name">Các mẫu nhẫn cưới phong cách ý mà bạn nhất định không thể bỏ qua trong tháng
                    8 này.</a></h3>
              </div>
              <div class="story__item post-include">
                <div class="image image-hover">
                  <a href=""><img src="images/img-test.jpg" alt="" /></a>
                </div>
                <h3><a href="" class="name">Lorem ipsum dolor sit amet.</a></h3>
              </div>
            </div>
          </div>
        </section>

        <section class="jewelry-articles">
          <div class="container">
            <h3 class="title-section">Trang sức & phong thủy</h3>
            <div class="articles-list">
              <div class="article-item" v-for="n in 3">
                <div class="image image-hover">
                  <a href=""><img src="images/img-test.jpg" alt="" /></a>
                </div>
                <div class="content">
                  <a href="" class="name"> Hành trình đi tìm phong cách cho bản thân không hề dễ dàng. BTMH sẽ giúp bạn
                    định hình và tìm cho mình phong cách phù hợp nhất!</a>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </main>
    <myfooter></myfooter>
  </div>

  <script type="text/javascript" src="js/vue.js"></script>
  <script type="text/javascript" src="js/httpvueloader.js"></script>
  <script type="text/javascript" src="libs/swiper/swiper-bundle.min.js"></script>
  <script type="text/javascript" src="libs/bootstrap/jquery.min.js"></script>
  <script type="text/javascript" src="libs/bootstrap/popper.min.js"></script>
  <script type="text/javascript" src="libs/bootstrap/bootstrap.min.js"></script>
  <script type="text/javascript" src="libs/aos/aos.js"></script>
  <script type="text/javascript" src="libs/chartjs/chart.min.js"></script>
  <script type="text/javascript" src="libs/fancybox/jquery.fancybox.min.js"></script>
  <script type="text/javascript" src="js/main.js"></script>

  <script type="text/javascript">
    var app = new Vue({
      el: '#app',
      data() {
        return {};
      },
      components: {
        myheader: httpVueLoader('vue_components/header.vue'),
        myfooter: httpVueLoader('vue_components/footer.vue'),
        rednavi: httpVueLoader('vue_components/red-slide-navigation.vue'),
        yellownavi: httpVueLoader('vue_components/yellow-slide-navigation.vue'),
      },
    });
  </script>
</body>

</html>