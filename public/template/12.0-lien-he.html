<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <link rel="shortcut icon" async href="images/common/favicon.png" />
  <title>Website Bảo Tín Mạnh Hải</title>
  <link rel="stylesheet" href="libs/bootstrap/bootstrap.min.css" />
  <link rel="stylesheet" href="libs/swiper/swiper-bundle.min.css" />
  <link rel="stylesheet" href="libs/fancybox/jquery.fancybox.min.css" />
  <link rel="stylesheet" href="libs/flatpickr/flatpickr.min.css" />
  <link rel="stylesheet" href="libs/aos/aos.css" />
  <link rel="stylesheet" href="libs/fancybox/jquery.fancybox.min.css" />
  <link rel="stylesheet" href="css/style.css" />
</head>

<body>
  <div id="app">
    <myheader></myheader>
    <main>
      <div class="master-wrapper">
        <div class="contact-page">
          <div class="container">

            <h2 class="title-page d-block text-center">
              Liên hệ
            </h2>

            <div class="row align-items-center">
              <div class="col-lg-5 col-12 mb-lg-0 mb-4">
                <div class="box-form-contact">

                  <div class="box-title">
                    <h2 class="title">
                      Liên hệ với chúng tôi
                    </h2>

                    <span class="sub">
                      Quý khách vui lòng để lại yêu cầu hoặc liên hệ với chúng tôi theo thông tin dưới đây.
                    </span>
                  </div>

                  <form action="" id="form-contact">
                    <input type="text" name="" id="" placeholder="Họ và tên">
                    <input type="text" name="" id="" placeholder="Số điện thoại">
                    <input type="text" name="" id="" placeholder="Tên doanh nghiệp, tổ chức">
                    <textarea name="" placeholder="Ghi chú"></textarea>

                    <button class="submit">
                      <span>
                        Gửi thông tin
                      </span>
                    </button>
                  </form>

                </div>
              </div>
              <div class="col-lg-7 col-12">
                <div class="box-list-contact" style="background-image: url(../images/bg-contact-info.png)">
                  <ul>
                    <li v-for="(item,index) in listContact" :key="index">
                      <div class="icon">
                        <img :src="item.icon" alt="">
                      </div>

                      <div class="info">
                        <h3 class="title" v-html="item.title"></h3>
                        <span class="sub" v-html="item.sub"></>
                      </div>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
    <myfooter></myfooter>
  </div>

  <script type="text/javascript" src="js/vue.js"></script>
  <script type="text/javascript" src="js/httpvueloader.js"></script>
  <script type="text/javascript" src="libs/swiper/swiper-bundle.min.js"></script>
  <script type="text/javascript" src="libs/bootstrap/jquery.min.js"></script>
  <script type="text/javascript" src="libs/bootstrap/popper.min.js"></script>
  <script type="text/javascript" src="libs/bootstrap/bootstrap.min.js"></script>
  <script type="text/javascript" src="libs/aos/aos.js"></script>
  <script type="text/javascript" src="libs/chartjs/chart.min.js"></script>
  <script type="text/javascript" src="libs/fancybox/jquery.fancybox.min.js"></script>
  <script type="text/javascript" src="js/main.js"></script>

  <script type="text/javascript">
    var app = new Vue({
      el: '#app',
      data() {
        return {
          listContact: [
            {
              icon: '../images/map.svg',
              title: 'Trụ sở chính',
              sub: 'T19 Center Building, Số 1 Nguyễn Huy Tưởng, Thanh Xuân, Hà Nội'
            },
            {
              icon: '../images/call.svg',
              title: 'Điện thoại chăm sóc',
              sub: '024.22339999 '
            },
            {
              icon: '../images/sms.svg',
              title: 'Email hỗ trợ',
              sub: '<EMAIL>'
            },
          ],
        };
      },
      components: {
        myheader: httpVueLoader('vue_components/header.vue'),
        myfooter: httpVueLoader('vue_components/footer.vue'),
        rednavi: httpVueLoader('vue_components/red-slide-navigation.vue'),
        yellownavi: httpVueLoader('vue_components/yellow-slide-navigation.vue'),
      },
    });
  </script>
</body>

</html>