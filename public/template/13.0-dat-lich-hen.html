<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <link rel="shortcut icon" async href="images/common/favicon.png" />
  <title>Website Bảo Tín Mạnh Hải</title>
  <link rel="stylesheet" href="libs/bootstrap/bootstrap.min.css" />
  <link rel="stylesheet" href="libs/swiper/swiper-bundle.min.css" />
  <link rel="stylesheet" href="libs/fancybox/jquery.fancybox.min.css" />
  <link rel="stylesheet" href="libs/flatpickr/flatpickr.min.css" />
  <link rel="stylesheet" href="libs/aos/aos.css" />
  <link rel="stylesheet" href="libs/fancybox/jquery.fancybox.min.css" />
  <link rel="stylesheet" href="css/style.css" />
</head>

<body>
  <div id="app">
    <myheader></myheader>
    <main>
      <div class="master-wrapper">
        <div class="container">

          <div class="appointment-page">

            <div class="d-block text-center mb-xl-5 mb-4">
              <h2 class="title-appointment">
                Đặt lịch hẹn
              </h2>
            </div>

            <form action="" class="form-appointment">
              <h3 class="title-form mb-lg-4 mb-3">
                Thông tin khách hàng
              </h3>

              <input type="text" name="" id="" placeholder="Họ và tên *">
              <span class="form-error-message mb-lg-4 mb-3">
                Error !!!
              </span>
              <input type="text" name="" id="" placeholder="Số điện thoại *">
              <input type="text" name="" id="" placeholder="Tên doanh nghiệp, tổ chức">

              <h3 class="title-form mb-lg-4 mb-3">
                Chọn lịch hẹn
              </h3>

              <select name="" id="">
                <option value="">Chọn cửa hàng</option>
                <option value="">Chọn cửa hàng</option>
                <option value="">Chọn cửa hàng</option>
              </select>

              <div class="row align-items-stretch">
                <div class="col-xl-6 col-12">
                  <label for="pickDate" class="mb-0">
                    <input type="text" id="pickDate" placeholder="Chọn ngày">
                  </label>
                </div>
                <div class="col-xl-6 col-12">
                  <label for="pickTime" class="mb-0">
                    <input type="text" id="pickTime" placeholder="Chọn giờ">
                  </label>
                </div>
              </div>

              <textarea name="" id="" placeholder="Yêu cầu của quý khách"></textarea>

              <button class="submit">
                <span>
                  Đặt lịch hẹn
                </span>
              </button>
            </form>

          </div>

        </div>
      </div>
    </main>
    <myfooter></myfooter>
  </div>

  <script type="text/javascript" src="js/vue.js"></script>
  <script type="text/javascript" src="js/httpvueloader.js"></script>
  <script type="text/javascript" src="libs/swiper/swiper-bundle.min.js"></script>
  <script type="text/javascript" src="libs/bootstrap/jquery.min.js"></script>
  <script type="text/javascript" src="libs/bootstrap/popper.min.js"></script>
  <script type="text/javascript" src="libs/bootstrap/bootstrap.min.js"></script>
  <script type="text/javascript" src="libs/aos/aos.js"></script>
  <script type="text/javascript" src="libs/chartjs/chart.min.js"></script>
  <script type="text/javascript" src="libs/flatpickr/flatpickr.js"></script>
  <script type="text/javascript" src="libs/fancybox/jquery.fancybox.min.js"></script>
  <script type="text/javascript" src="js/main.js"></script>

  <script type="text/javascript">
    var app = new Vue({
      el: '#app',
      data() {
        return {};
      },
      components: {
        myheader: httpVueLoader('vue_components/header.vue'),
        myfooter: httpVueLoader('vue_components/footer.vue'),
        rednavi: httpVueLoader('vue_components/red-slide-navigation.vue'),
        yellownavi: httpVueLoader('vue_components/yellow-slide-navigation.vue'),
      },
      mounted() {
        $(window).bind('load', function () {
          $("#pickDate").flatpickr({
            altInput: true,
            altFormat: "F j, Y",
            dateFormat: "d-m-Y",
          });

          $("#pickTime").flatpickr({
            enableTime: true,
            noCalendar: true,
            dateFormat: "H:i",
            time_24hr: true
          });
        })
      },
    });
  </script>
</body>

</html>