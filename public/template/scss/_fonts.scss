@import '../fonts/awesome/scss/variables';
@import '../fonts/awesome/scss/mixins';
@import '../fonts/awesome/scss/path';
@import '../fonts/awesome/scss/core';
@import '../fonts/awesome/scss/larger';
@import '../fonts/awesome/scss/fixed-width';
@import '../fonts/awesome/scss/list';
@import '../fonts/awesome/scss/bordered-pulled';
@import '../fonts/awesome/scss/animated';
@import '../fonts/awesome/scss/rotated-flipped';
@import '../fonts/awesome/scss/stacked';
@import '../fonts/awesome/scss/icons';
@import '../fonts/awesome/scss/screen-reader';

// SVN-Megante
@font-face {
   font-family: 'SVN-Megante';
   src: url('../fonts/SVN-Megante/SVN-Megante.woff2') format('woff2'),
      url('../fonts/SVN-Megante/SVN-Megante.woff') format('woff');
   font-weight: normal;
   font-style: normal;
   font-display: swap;
}

// montserrat
@font-face {
   font-family: 'Montserrat';
   src: url('../fonts/montserrat/Montserrat-Black.woff2') format('woff2'),
      url('../fonts/montserrat/Montserrat-Black.woff') format('woff');
   font-weight: 900;
   font-style: normal;
   font-display: swap;
}

@font-face {
   font-family: 'Montserrat';
   src: url('../fonts/montserrat/Montserrat-Bold.woff2') format('woff2'),
      url('../fonts/montserrat/Montserrat-Bold.woff') format('woff');
   font-weight: bold;
   font-style: normal;
   font-display: swap;
}

@font-face {
   font-family: 'Montserrat';
   src: url('../fonts/montserrat/Montserrat-BlackItalic.woff2') format('woff2'),
      url('../fonts/montserrat/Montserrat-BlackItalic.woff') format('woff');
   font-weight: 900;
   font-style: italic;
   font-display: swap;
}

@font-face {
   font-family: 'Montserrat';
   src: url('../fonts/montserrat/Montserrat-BoldItalic.woff2') format('woff2'),
      url('../fonts/montserrat/Montserrat-BoldItalic.woff') format('woff');
   font-weight: bold;
   font-style: italic;
   font-display: swap;
}

@font-face {
   font-family: 'Montserrat';
   src: url('../fonts/montserrat/Montserrat-ExtraLight.woff2') format('woff2'),
      url('../fonts/montserrat/Montserrat-ExtraLight.woff') format('woff');
   font-weight: 200;
   font-style: normal;
   font-display: swap;
}

@font-face {
   font-family: 'Montserrat';
   src: url('../fonts/montserrat/Montserrat-ExtraBoldItalic.woff2') format('woff2'),
      url('../fonts/montserrat/Montserrat-ExtraBoldItalic.woff') format('woff');
   font-weight: bold;
   font-style: italic;
   font-display: swap;
}

@font-face {
   font-family: 'Montserrat';
   src: url('../fonts/montserrat/Montserrat-ExtraLightItalic.woff2') format('woff2'),
      url('../fonts/montserrat/Montserrat-ExtraLightItalic.woff') format('woff');
   font-weight: 200;
   font-style: italic;
   font-display: swap;
}

@font-face {
   font-family: 'Montserrat';
   src: url('../fonts/montserrat/Montserrat-ExtraBold.woff2') format('woff2'),
      url('../fonts/montserrat/Montserrat-ExtraBold.woff') format('woff');
   font-weight: bold;
   font-style: normal;
   font-display: swap;
}

@font-face {
   font-family: 'Montserrat';
   src: url('../fonts/montserrat/Montserrat-LightItalic.woff2') format('woff2'),
      url('../fonts/montserrat/Montserrat-LightItalic.woff') format('woff');
   font-weight: 300;
   font-style: italic;
   font-display: swap;
}

@font-face {
   font-family: 'Montserrat';
   src: url('../fonts/montserrat/Montserrat-Medium.woff2') format('woff2'),
      url('../fonts/montserrat/Montserrat-Medium.woff') format('woff');
   font-weight: 500;
   font-style: normal;
   font-display: swap;
}

@font-face {
   font-family: 'Montserrat';
   src: url('../fonts/montserrat/Montserrat-Italic.woff2') format('woff2'),
      url('../fonts/montserrat/Montserrat-Italic.woff') format('woff');
   font-weight: normal;
   font-style: italic;
   font-display: swap;
}

@font-face {
   font-family: 'Montserrat';
   src: url('../fonts/montserrat/Montserrat-Light.woff2') format('woff2'),
      url('../fonts/montserrat/Montserrat-Light.woff') format('woff');
   font-weight: 300;
   font-style: normal;
   font-display: swap;
}

@font-face {
   font-family: 'Montserrat';
   src: url('../fonts/montserrat/Montserrat-MediumItalic.woff2') format('woff2'),
      url('../fonts/montserrat/Montserrat-MediumItalic.woff') format('woff');
   font-weight: 500;
   font-style: italic;
   font-display: swap;
}

@font-face {
   font-family: 'Montserrat';
   src: url('../fonts/montserrat/Montserrat-Regular.woff2') format('woff2'),
      url('../fonts/montserrat/Montserrat-Regular.woff') format('woff');
   font-weight: normal;
   font-style: normal;
   font-display: swap;
}

@font-face {
   font-family: 'Montserrat';
   src: url('../fonts/montserrat/Montserrat-SemiBoldItalic.woff2') format('woff2'),
      url('../fonts/montserrat/Montserrat-SemiBoldItalic.woff') format('woff');
   font-weight: 600;
   font-style: italic;
   font-display: swap;
}

@font-face {
   font-family: 'Montserrat';
   src: url('../fonts/montserrat/Montserrat-SemiBold.woff2') format('woff2'),
      url('../fonts/montserrat/Montserrat-SemiBold.woff') format('woff');
   font-weight: 600;
   font-style: normal;
   font-display: swap;
}

@font-face {
   font-family: 'Montserrat';
   src: url('../fonts/montserrat/Montserrat-ThinItalic.woff2') format('woff2'),
      url('../fonts/montserrat/Montserrat-ThinItalic.woff') format('woff');
   font-weight: 100;
   font-style: italic;
   font-display: swap;
}

@font-face {
   font-family: 'Montserrat';
   src: url('../fonts/montserrat/Montserrat-Thin.woff2') format('woff2'),
      url('../fonts/montserrat/Montserrat-Thin.woff') format('woff');
   font-weight: 100;
   font-style: normal;
   font-display: swap;
}
