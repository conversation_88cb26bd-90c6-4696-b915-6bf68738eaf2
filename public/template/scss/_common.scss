// color
$red: #aa1f23;
$yellow: #d0b46c;
$black: #000000;
$secondBlack: #222222;
$white: #ffffff;
$gray: #606368;
$mon: 'Montserrat';
$mega: 'SVN-Megante';

.gardient-color {
  background: linear-gradient(90deg, #d0b46c 0%, #ffe9af 51.56%, #d0b46c 99.48%);
}
.color-theme {
  color: $red;
}
.color-black {
  color: $black;
}
.color-606368 {
  color: $gray;
}
.font-weight-semibold {
  font-weight: 600;
}
.clip-text {
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

.fit-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.format-title {
  font-size: 25px;
  line-height: 40px;
  color: $red;
}

.flashsale-row-text {
  @include font($mon, 600, 13px, 22px);
  color: #3d4043;
  @media (min-width: 1200px) {
    @include font($mon, 600, 15px, 28px);
  }
}

.master-wrapper {
  padding-top: 72px;
  @media (min-width: 1200px) {
    padding-top: 120px;
  }
}

.title-w-deco {
  position: relative;
  display: flex;

  h4 {
    @include font($mega, normal, 22px, 29px);
    color: $red;
    margin-bottom: 0;
    background: $white;
    display: inline-block;
    text-transform: uppercase;
    text-align: center;
  }
  h3 {
    @include font($mega, normal, 32px, 40px);
    color: $red;
    margin-bottom: 0;
    background: $white;
    display: inline-block;
    text-transform: uppercase;
    text-align: center;
  }

  .title-deco {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url(../images/common/title-deco.png);
    background-repeat: repeat-x;
    background-size: auto auto;
    background-position: right;
    z-index: -1;
  }
}

.custom-slide-button {
  .custom-button-prev,
  .custom-button-prev2,
  .custom-button-next,
  .custom-button-next2 {
    position: absolute;
    top: calc(50% - 24px);
    max-width: 48px;
    z-index: 1;
    margin-top: -24px;
    cursor: pointer;
    img {
      display: block;
    }

    &.swiper-button-disabled {
      opacity: 0.5;
    }
  }
}

.gold-table {
  position: relative;

  .unit {
    @include font($mon, 400, 16px, 22px);
    color: $black;
    text-align: right;
  }
  .gold-table-content {
    background: $white;
    border: 0;
    max-width: 100%;
    width: 100%;
    margin-bottom: 0;
    border-collapse: separate !important;
    border-spacing: 0;
    thead {
      background-color: #aa1f23;
      color: #d0b46c;
      tr,
      th {
        color: #d0b46c;
        font-weight: 700;
      }
    }
    td,
    th {
      padding: 10px 16px;
      border: 1px solid $black;
      border-bottom: 0;
    }

    th {
      &:not(:first-child) {
        text-align: center;
      }
      border-right: 0;
      &:last-child {
        border-right: 1px solid $black;
      }
      @include font($mon, 400, 14px, 18px);
      color: $black;
      @media (min-width: 1200px) {
        @include font($mon, 400, 16px, 22px);
      }
    }

    td {
      @include font($mon, 400, 14px, 18px);

      &:first-child {
        font-weight: 600;
        line-height: 28px;
      }

      &.price {
        text-align: center;
        img {
          margin-left: 5px;
        }
      }

      &.price-up {
        color: #219653;
      }

      &.price-down {
        color: $red;
      }
      @media (min-width: 1200px) {
        @include font($mon, 400, 16px, 22px);
      }
    }

    tr {
      &:first-child {
        th {
          &:first-child {
            border-top-left-radius: 8px;
          }

          &:last-child {
            border-top-right-radius: 8px;
          }
        }
      }

      &:last-child {
        td {
          &:first-child {
            border-bottom-left-radius: 8px;
          }

          &:last-child {
            border-bottom-right-radius: 8px;
          }
          border-bottom: 1px solid $black;
        }
      }
      td {
        border-right: 0;
        &:last-child {
          border-right: 1px solid $black;
        }
      }
    }
  }
  @media (max-width: 768px) {
    .gold-table-content {
      td,
      th {
        padding: 10px;
        font-size: 13px;
        line-height: 18px;
      }
      td:first-child {
        line-height: 20px;
      }
      td.price {
        white-space: nowrap;
      }
    }
  }
}

.gold-table-footer {
  display: flex;
  justify-content: space-between;
  .gold-page-detail-link {
    margin-left: 10px;
    a {
      color: #aa1f23;
      display: flex;
      align-items: center;
      img {
        width: 24px;
        height: 24px;
        margin-left: 10px;
      }
    }
  }
}

.sp-item {
  position: relative;
  background: #ffffff;
  border-radius: 10px;
  border: 1px solid transparent;
  overflow: hidden;
  transition: 0.3s all ease-in-out;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  &:hover {
    border: 1px solid $red;
    box-shadow: 5px 5px 5px rgba(0, 0, 0, 0.05);
    .sp-item-img {
      img {
        transform: scale3d(1.1, 1.1, 1.1);
      }
    }
  }

  .sp-item-img {
    width: 100%;
    @include ratio-img(100%);
    margin: 0 auto 3px;
    img {
      transform: scale(1);
    }
  }

  .sp-item-body {
    position: relative;
    text-align: center;
    padding: 16px;
    flex: 1;
    display: flex;
    flex-direction: column;
    a {
      display: block;
      @include font($mega, normal, 18px, 24px);
      color: #333333;
      margin-bottom: 12px;
      &:hover {
        color: $red;
      }
    }

    p {
      @include font($mon, 600, 22px, 32px);
      color: $red;
      margin-bottom: 0;
    }
    .price {
      margin-top: auto;
    }
    .old-price {
      font-weight: 400;
      font-size: 18px;
      line-height: 15px;
      /* identical to box height, or 83% */
      display: block;
      margin-top: 6px;
      text-align: center;
      text-decoration-line: line-through;

      color: #333333;
    }
  }
  .discount {
    @include abs(13px, 13px, unset, unset);
    background: $red;
    border-radius: 4px;
    padding: 4px 16px;
    z-index: 10;
    @include font($mon, 500, 14px, 20px);
    span {
      @extend .gardient-color, .clip-text;
    }
    .txt {
      font-weight: 500;
    }
    .value {
      font-weight: 700;
    }
  }
  &.sp-item-border {
    border-color: #f2f2f2;
    &:hover {
      border: 1px solid $red;
      box-shadow: 5px 5px 5px rgba(0, 0, 0, 0.05);
    }
  }
  &:hover {
    .sp-item-body {
      a {
        color: $red;
      }
    }
  }
  @media (max-width: 1200px) {
    .sp-item-body {
      a {
        @include font($mega, normal, 16px, calc(24 / 18));
        margin-bottom: 8px;
      }

      p {
        @include font($mon, 600, 20px, calc(32 / 20));
      }
    }
  }

  @media (max-width: 768px) {
    .sp-item-body {
      padding: 8px;
      a {
        @include font($mega, normal, 14px, calc(24 / 18));
        margin-bottom: 8px;
      }

      p {
        @include font($mon, 600, 16px, calc(32 / 20));
      }
    }
  }
}

.sp-title {
  @include font($mega, normal, 25px, 40px);
  text-transform: uppercase;
  color: $black;

  span {
    color: $red;
  }
}

.pt-header {
  padding-top: 100px;
  @media screen and (min-width: 1200px) {
    padding-top: 120px;
  }
}
