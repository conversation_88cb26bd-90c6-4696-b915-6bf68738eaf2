@charset "UTF-8";
@import '../scss/_fonts.scss';
@import '../scss/bootstrap';

@import '../scss/reset', '../scss/variables', '../scss/body',
  // common
  '../scss/common',
  //components
  '../scss/components/header',
  '../scss/components/footer', '../scss/components/filterblock', '../scss/components/paginate',
  // pages
  '../scss/pages/home',
  '../scss/pages/customer',
  // ------------------- <PERSON><PERSON> STLYE -------------------
  '../scss/pages/product',
  '../scss/pages/vertify',
  // ------------------- NEWS DETAILS FOLDER -------------------
  '../scss/pages/news-details/index',
  '../scss/pages/promotion', '../scss/pages/news-details/news-related',
  // ------------------- <PERSON>WS FOLDER -------------------
  '../scss/pages/introduce',
  '../scss/pages/news/news-site/story-section', '../scss/pages/news/news-site/news', '../scss/pages/news/news-site/jewelry', '../scss/pages/news/news-site/mix-match',
  '../scss/pages/news/news-site/knowledge-section',
  // ------------------- CART -------------------
  '../scss/pages/cart/cart',
  '../scss/pages/cart/payment', '../scss/pages/cart/success',
  // ------------------- CART -------------------
  '../scss/pages/orders/order';

@import '../scss/pages/dat-lich-hen', '../scss/pages/gia-vang', '../scss/pages/quan-he-co-dong', '../scss/pages/cua-hang', '../scss/pages/line-he';
