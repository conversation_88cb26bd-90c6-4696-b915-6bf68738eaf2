// set width cho cột cho flex
@mixin col-w($width) {
   flex: 0 0 $width;
   max-width: $width;
}

@mixin col-img($padding) {
   position: relative;
   width: 100%;
   padding-bottom: $padding;
   overflow: hidden;

   img,
   iframe {
      position: absolute;
      width: 100%;
      height: 100%;
      object-fit: cover;
      object-position: center;
   }
}

@mixin line-clamp($line) {
   display: -webkit-box;
   -webkit-line-clamp: $line;
   -webkit-box-orient: vertical;
   overflow: hidden;
}

@mixin ratio-img($pad) {
   position: relative;
   display: block;
   overflow: hidden;

   img {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: all 0.3s;
   }

   &::before {
      content: '';
      display: block;
      padding-bottom: $pad;
   }
}

@mixin no-line {
   &:focus,
   &:active,
   &:focus-visible,
   &:visited {
      box-shadow: none;
      outline: 0;
   }
}

@mixin font($name, $weight, $size, $height) {
   font-family: $name;
   font-weight: $weight;
   font-size: $size;
   line-height: $height;
}

@mixin abs($top, $right, $bottom, $left) {
   position: absolute;
   top: $top;
   right: $right;
   bottom: $bottom;
   left: $left;
}
