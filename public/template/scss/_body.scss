@charset "UTF-8";

$red: #aa1f23;
$svn: 'SVN-Megante';

body {
  @include font('Montserrat', normal, 16px, 24px);
  word-break: break-word;
  &.menu-opened {
    overflow-y: hidden;
    #fb-root * {
      z-index: -1 !important;
    }
  }
}
a,
a:hover {
  text-decoration: none;
  transition: 0.1s linear;
}
a:hover {
  color: $red;
}
.container {
  max-width: 1246px !important;
}

img {
  max-width: 100%;
  vertical-align: middle;
}

.btn-12 {
  border-radius: 12px;
}
.rounded-8 {
  border-radius: 8px;
}

/* .hover-title {
   padding-bottom: 0;
   background-size: 0 1px;
   background-repeat: no-repeat;
   background-position: left 100%;
   transition: all 0.7s;
   background-image: linear-gradient(to bottom, currentColor 0%, currentColor 98%);
   &:hover {
      background-size: 100% 1px;
   }
} */

.image-hover {
  overflow: hidden;
  img {
    transform: scale(1);
    transition: 0.5s all ease;
  }
  &:hover {
    img {
      transform: scale3d(1.1, 1.1, 1.1);
    }
  }
}

.title-section {
  margin-bottom: 24px;
  font-weight: 400;
  font-family: 'SVN-Megante';
  font-size: 24px;
  line-height: 40px;
  color: #aa1f23;
  &.fz-24 {
    font-size: 24px;
    line-height: calc(32 / 24);
  }
  @media screen and (min-width: 768px) {
    font-size: 32px;
  }
}

.font-monsterat {
  font-family: 'montserrat';
}

img.lazy:not(.loaded) {
  object-fit: scale-down !important ;
}

.fancybox-slide {
  padding: 1rem;
}

.socials-fixed {
  position: fixed;
  right: 24px;
  bottom: 10%;
  display: flex;
  flex-direction: column;
  gap: 6px;
  z-index: 99999;
  img {
    width: 30px;
    height: 30px;
    object-fit: contain;
  }

  .navigation {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .menuToggle {
    position: relative;
    width: 44px;
    height: 44px;
    background-color: #fff;
    border-radius: 70px;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 15px 25px rgba(0, 0, 0, 0.15);
    @media screen and (min-width: 1200px) {
      width: 60px;
      height: 60px;
    }
  }

  .menuToggle::before {
    content: '+';
    position: absolute;
    font-size: 40px;
    color: #aa1f23;
    transition: 1s;
  }

  .menuToggle.active::before {
    transform: rotate(315deg);
  }

  .menu {
    position: absolute;
    width: 30px;
    height: 30px;
    background: #fff;
    border-radius: 70px;
    z-index: -1;
    transition: transform 0.5s, width 0.5s, height 0.5s;
    // transition-delay: 1s, 0.5s, 0.5s;
    // transition-timing-function: cubic-bezier(0.075, 0.82, 0.165, 1);
    opacity: 0;
    visibility: hidden;
  }

  .menuToggle.active ~ .menu {
    width: fit-content;
    height: auto;
    z-index: 1;
    transform: translateY(-80px);
    transition-delay: 0s, 0.5s, 0.5s;
    box-shadow: 0 15px 25px rgba(0, 0, 0, 0.1);
    padding: 20px 10px;
    top: -100%;
    opacity: 1;
    visibility: visible;
  }

  .menu::before {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    background: #fff;
    left: calc(50% - 8px);
    bottom: 4px;
    transform: rotate(45deg);
    border-radius: 2px;
    transition: 0.5s;
  }

  .menuToggle.active ~ .menu::before {
    transition-delay: 0.5s;
    bottom: -6px;
  }

  .menu ul {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    height: fit-content;
    flex-direction: column;
    gap: 16px;
    margin: 0;
    padding: 0;
  }

  .menu ul li {
    list-style: none;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-30px);
    transition: 0.25s;
    transition-delay: calc(0s + var(--i));
  }

  .menuToggle.active ~ .menu ul li {
    opacity: 1;
    visibility: visible;
    transform: translateY(0px);
    transition-delay: calc(0.75s + var(--i));
  }

  .menu ul li a {
    display: block;
    font-size: 2.2em;
  }
}


img:not(.loaded) {
  object-fit: scale-down !important;
  height: auto;
  // margin: 0 auto;
  // display: block;
}
/*



  .coccoc-alo-ph-circle {
    width: 90px;
    height: 90px;
    position: absolute;
    background-color: rgba(0, 0, 0, 0);
    border-radius: 100%;
    border: 2px solid rgba(30, 30, 30, 0.4);
    opacity: 0.1;
    animation: coccoc-alo-circle-anim 1.2s infinite ease-in-out;
    transition: all 0.5s;
    top: 0px;
    left: -20px;
  }

  .coccoc-alo-phone {
    background-color: transparent;
    width: 80px;
    height: 80px;
    cursor: pointer;
    z-index: 200000 !important;
    transition: visibility 0.5s;
    position: relative;
  }

  .coccoc-alo-phone.coccoc-alo-green .coccoc-alo-ph-circle-fill {
    background-color: rgba(0, 175, 242, 0.5);
    opacity: 0.75 !important;
  }

  .coccoc-alo-ph-circle-fill {
    width: 60px;
    height: 60px;
    top: 15px;
    left: -5px;
    position: absolute;
    background-color: #000;
    border-radius: 100%;
    border: 2px solid rgba(0, 0, 0, 0);
    opacity: 0.1;
    animation: coccoc-alo-circle-fill-anim 2.3s infinite ease-in-out;
    transition: all 0.5s;
  }

  .coccoc-alo-ph-img-circle {
    width: 40px;
    height: 40px;
    top: 25px;
    left: 5px;
    position: absolute;
    background: rgba(30, 30, 30, 0.1)
      url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAyCAYAAAAeP4ixAAAACXBIWXMAAAsTAAALEwEAmpwYAAABNmlDQ1BQaG90b3Nob3AgSUNDIHByb2ZpbGUAAHjarY6xSsNQFEDPi6LiUCsEcXB4kygotupgxqQtRRCs1SHJ1qShSmkSXl7VfoSjWwcXd7/AyVFwUPwC/0Bx6uAQIYODCJ7p3MPlcsGo2HWnYZRhEGvVbjrS9Xw5+8QMUwDQCbPUbrUOAOIkjvjB5ysC4HnTrjsN/sZ8mCoNTIDtbpSFICpA/0KnGsQYMIN+qkHcAaY6addAPAClXu4vQCnI/Q0oKdfzQXwAZs/1fDDmADPIfQUwdXSpAWpJOlJnvVMtq5ZlSbubBJE8HmU6GmRyPw4TlSaqo6MukP8HwGK+2G46cq1qWXvr/DOu58vc3o8QgFh6LFpBOFTn3yqMnd/n4sZ4GQ5vYXpStN0ruNmAheuirVahvAX34y/Axk/96FpPYgAAACBjSFJNAAB6JQAAgIMAAPn/AACA6AAAUggAARVYAAA6lwAAF2/XWh+QAAAB/ElEQVR42uya7W3CMBCG31QM4A1aNggTlG6QbpBMkHYC1AloJ4BOABuEDcgGtBOETnD9c1ERCH/lwxeaV8oPFGP86Hy+DxMREW5Bd7gRjSDSNGn4/RiAOvm8C0ZCRD5PSkQVXSr1nK/xE3mcWimA1ZV3JYBZCIO4giQANoYxMwYS6+xKY4lT5dJPreWZY+uspqSCKPYN27GJVBDXheVSQe494ksiEWTuMXcu1dld9SARxDX1OAJ4lgjy4zDnFsC076A4adEiRwAZg4hOUSpNoCsBPDGM+HqkNGynYBCuILuWj+dgWysGsNe8nwL4GsrW0m2fxZBq9rW0rNcX5MOQ9eZD8JFahcG5g/iKT671alGAYQggpYWvpEPYWrU/HDTOfeRIX0q2SL3QN4tGhZJukVobQyXYWw7WtLDKDIuM+ZSzscyCE9PCy5IttCvnZNaeiGLNHKuz8ZVh/MXTVu/1xQKmIqLEAuJ0fNo3iG5B51oSkeKnsBi/4bG9gYB/lCytU5G9DryFW+3Gm+JLwU7ehbJrwTjq4DJU8bHcVbEV9dXXqqP6uqO5e2/QZRYJpqu2IUAA4B3tXvx8hgKp05QZW6dJqrLTNkB6vrRURLRwPHqtYgkC3cLWQAcDQGGKH13FER/NATzi786+BPDNjm1dMkfjn2pGkBHkf4D8DgBJDuDHx9BN+gAAAABJRU5ErkJggg==)
      no-repeat center center;
    border-radius: 100%;
    border: 2px solid rgba(0, 0, 0, 0);
    opacity: 0.7;
    animation: coccoc-alo-circle-img-anim 1s infinite ease-in-out;
    background-size: 30px;
  }

  .coccoc-alo-phone.coccoc-alo-green .coccoc-alo-ph-img-circle {
    background-color: #00aff2;
  }

  .coccoc-alo-phone.coccoc-alo-green .coccoc-alo-ph-circle {
    border-color: #00aff2;
    opacity: 0.5;
  }

  .coccoc-alo-phone.coccoc-alo-green.coccoc-alo-hover .coccoc-alo-ph-circle,
  .coccoc-alo-phone.coccoc-alo-green:hover .coccoc-alo-ph-circle {
    border-color: #aa1f23;
    opacity: 0.5;
  }

  .coccoc-alo-phone.coccoc-alo-green.coccoc-alo-hover .coccoc-alo-ph-circle-fill,
  .coccoc-alo-phone.coccoc-alo-green:hover .coccoc-alo-ph-circle-fill {
    background-color: rgba($color: #aa1f23, $alpha: 0.5);
    opacity: 0.75 !important;
  }

  .coccoc-alo-phone.coccoc-alo-green.coccoc-alo-hover .coccoc-alo-ph-img-circle,
  .coccoc-alo-phone.coccoc-alo-green:hover .coccoc-alo-ph-img-circle {
    background-color: #aa1f23;
  }

  @-moz-keyframes coccoc-alo-circle-anim {
    0% {
      transform: rotate(0) scale(0.5) skew(1deg);
      opacity: 0.1;
    }
    30% {
      transform: rotate(0) scale(0.7) skew(1deg);
      opacity: 0.5;
    }
    100% {
      transform: rotate(0) scale(1) skew(1deg);
      opacity: 0.1;
    }
  }

  @-webkit-keyframes coccoc-alo-circle-anim {
    0% {
      transform: rotate(0) scale(0.5) skew(1deg);
      opacity: 0.1;
    }
    30% {
      transform: rotate(0) scale(0.7) skew(1deg);
      opacity: 0.5;
    }
    100% {
      transform: rotate(0) scale(1) skew(1deg);
      opacity: 0.1;
    }
  }

  @-o-keyframes coccoc-alo-circle-anim {
    0% {
      transform: rotate(0) scale(0.5) skew(1deg);
      opacity: 0.1;
    }
    30% {
      transform: rotate(0) scale(0.7) skew(1deg);
      opacity: 0.5;
    }
    100% {
      transform: rotate(0) scale(1) skew(1deg);
      opacity: 0.1;
    }
  }

  @keyframes coccoc-alo-circle-anim {
    0% {
      transform: rotate(0) scale(0.5) skew(1deg);
      opacity: 0.1;
    }
    30% {
      transform: rotate(0) scale(0.7) skew(1deg);
      opacity: 0.5;
    }
    100% {
      transform: rotate(0) scale(1) skew(1deg);
      opacity: 0.1;
    }
  }

  @-moz-keyframes coccoc-alo-circle-fill-anim {
    0% {
      transform: rotate(0) scale(0.7) skew(1deg);
      opacity: 0.2;
    }
    50% {
      transform: rotate(0) scale(1) skew(1deg);
      opacity: 0.2;
    }
    100% {
      transform: rotate(0) scale(0.7) skew(1deg);
      opacity: 0.2;
    }
  }

  @-webkit-keyframes coccoc-alo-circle-fill-anim {
    0% {
      transform: rotate(0) scale(0.7) skew(1deg);
      opacity: 0.2;
    }
    50% {
      transform: rotate(0) scale(1) skew(1deg);
      opacity: 0.2;
    }
    100% {
      transform: rotate(0) scale(0.7) skew(1deg);
      opacity: 0.2;
    }
  }

  @-o-keyframes coccoc-alo-circle-fill-anim {
    0% {
      transform: rotate(0) scale(0.7) skew(1deg);
      opacity: 0.2;
    }
    50% {
      transform: rotate(0) scale(1) skew(1deg);
      opacity: 0.2;
    }
    100% {
      transform: rotate(0) scale(0.7) skew(1deg);
      opacity: 0.2;
    }
  }

  @keyframes coccoc-alo-circle-fill-anim {
    0% {
      transform: rotate(0) scale(0.7) skew(1deg);
      opacity: 0.2;
    }
    50% {
      transform: rotate(0) scale(1) skew(1deg);
      opacity: 0.2;
    }
    100% {
      transform: rotate(0) scale(0.7) skew(1deg);
      opacity: 0.2;
    }
  }

  @-moz-keyframes coccoc-alo-circle-img-anim {
    0% {
      transform: rotate(0) scale(1) skew(1deg);
    }
    10% {
      transform: rotate(-25deg) scale(1) skew(1deg);
    }
    20% {
      transform: rotate(25deg) scale(1) skew(1deg);
    }
    30% {
      transform: rotate(-25deg) scale(1) skew(1deg);
    }
    40% {
      transform: rotate(25deg) scale(1) skew(1deg);
    }
    50% {
      transform: rotate(0) scale(1) skew(1deg);
    }
    100% {
      transform: rotate(0) scale(1) skew(1deg);
    }
  }

  @-webkit-keyframes coccoc-alo-circle-img-anim {
    0% {
      transform: rotate(0) scale(1) skew(1deg);
    }
    10% {
      transform: rotate(-25deg) scale(1) skew(1deg);
    }
    20% {
      transform: rotate(25deg) scale(1) skew(1deg);
    }
    30% {
      transform: rotate(-25deg) scale(1) skew(1deg);
    }
    40% {
      transform: rotate(25deg) scale(1) skew(1deg);
    }
    50% {
      transform: rotate(0) scale(1) skew(1deg);
    }
    100% {
      transform: rotate(0) scale(1) skew(1deg);
    }
  }

  @-o-keyframes coccoc-alo-circle-img-anim {
    0% {
      transform: rotate(0) scale(1) skew(1deg);
    }
    10% {
      transform: rotate(-25deg) scale(1) skew(1deg);
    }
    20% {
      transform: rotate(25deg) scale(1) skew(1deg);
    }
    30% {
      transform: rotate(-25deg) scale(1) skew(1deg);
    }
    40% {
      transform: rotate(25deg) scale(1) skew(1deg);
    }
    50% {
      transform: rotate(0) scale(1) skew(1deg);
    }
    100% {
      transform: rotate(0) scale(1) skew(1deg);
    }
  }

  @keyframes coccoc-alo-circle-img-anim {
    0% {
      transform: rotate(0) scale(1) skew(1deg);
    }
    10% {
      transform: rotate(-25deg) scale(1) skew(1deg);
    }
    20% {
      transform: rotate(25deg) scale(1) skew(1deg);
    }
    30% {
      transform: rotate(-25deg) scale(1) skew(1deg);
    }
    40% {
      transform: rotate(25deg) scale(1) skew(1deg);
    }
    50% {
      transform: rotate(0) scale(1) skew(1deg);
    }
    100% {
      transform: rotate(0) scale(1) skew(1deg);
    }
  }
*/
