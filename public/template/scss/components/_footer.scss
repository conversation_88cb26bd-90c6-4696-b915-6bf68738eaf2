.footer {
    position: relative;
    background: $white;

    .footer-content {
        position: relative;
        padding: 60px 0 70px;
        border-top: 2px solid #F4F4F4;
        justify-content: space-between;

        .footer-gr {
            flex: 1;
            max-width: 100%;

            &.gr-left {
                max-width: 280px;
            }

            &.gr-right {
                max-width: 864px;
            }

            .footer-info {
                position: relative;

                .footer-logo {
                    max-width: 154px;
                    margin: 0 auto 20px;
                    display: block;

                    img {
                        display: block;
                    }
                }

                h4 {
                    @include font($mon, 400, 14px, 24px);
                    color: $black;
                    margin-bottom: 16px;
                }

                ul {
                    padding-left: 0;
                    margin-bottom: 0;

                    li {
                        @include font($mon, 400, 14px, 24px);
                        margin-bottom: 16px;
                        color: $black;

                        span {
                            font-weight: 700;
                        }
                    }
                }
            }

            .footer-row {
                margin-bottom: 28px;
                justify-content: space-between;
            }

            .footer-item-bl {
                position: relative;
                flex: 0 0 auto;
                width: 100%;

                .bl-title {
                    @include font($mega, 400, 18px, 24px);
                    color: $black;
                    padding-bottom: 12px;
                    margin-bottom: 12px;
                    position: relative;
                    text-transform: uppercase;

                    &::before {
                        content: '';
                        position: absolute;
                        bottom: 0;
                        left: 0;
                        width: 36px;
                        height: 1px;
                        background: $black;
                    }

                    &.title-low {
                        text-transform: none;
                    }
                }

                ul {
                    padding-left: 0;
                    margin-bottom: 0;

                    li {
                        margin-bottom: 24px;

                        .footer-link {
                            @include font($mon, 400, 14px, 24px);
                            color: $black;

                            &:hover {
                                text-decoration: none;
                            }
                        }
                    }
                }

                .tuvan-form {
                    position: relative;

                    input,
                    textarea {
                        background: #F5F5F5;
                        border: 1px solid $red;
                        border-radius: 8px;
                        @include font($mon, 400, 14px, 22px);
                        padding: 6px 16px;
                        width: 100%;
                        margin-bottom: 12px;

                        &:focus {
                            outline: none;
                        }
                    }

                    textarea {
                        resize: none;
                        height: 74px;
                    }

                    .submit-btn {
                        @include font($mega, 400, 16px, 22px);
                        padding: 7px;
                        background: $red;
                        border-radius: 4px;
                        border: 0;
                        outline: none;
                        display: block;
                        width: 100%;

                        span {
                            display: block;
                            @extend .gardient-color;
                            @extend .clip-text;
                        }

                    }
                }

                .chungnhan-gr {
                    position: relative;

                    img {
                        display: block;
                    }
                }

                .payment-gr {
                    position: relative;
                    gap: 11px;

                    img {
                        display: block;
                        max-width: 51px;
                    }
                }

                .social-gr {
                    position: relative;
                    gap: 8px;

                    img {
                        display: block;
                        max-width: 29px;
                    }
                }

                &:nth-child(1) {
                    max-width: 137px;
                }

                &:nth-child(2) {
                    max-width: 237px;
                }

                &:nth-child(3) {
                    max-width: 280px;
                }
            }
        }
    }

    .footer-credit {
        padding: 18px 0;
        border-top: 1px solid #AA1F23;

        h5 {
            text-align: center;
            @include font($mon, 400, 14px, 24px);
            color: $black;
            margin-bottom: 0;
        }
    }

    @media (max-width : 1200px) {
        .footer-content {
            padding: 40px 0 ;
            .footer-gr.gr-right {
                padding : 0 15px ; 
            }
        }
    }
    @media (max-width : 992px) {
        .footer-content {
            padding: 40px 0 ;
            flex-wrap:  wrap;
            .footer-gr {
                .footer-info  {
                    ul li {
                        margin-bottom: 10px;
                    }
                }
                .footer-item-bl ul li {
                    margin-bottom: 10px;
                }
                &.gr-right {
                    padding : 0  ;
                }
                &.gr-left {
                    text-align: center;
                    padding-bottom: 16px;
                    margin-bottom: 16px;
                    border-bottom: 1px solid #C5C5C5;
                }
                &.gr-right,
                &.gr-left {
                    flex : 0 0 100% ;
                    max-width: 100%;
                }

                .footer-row {
                    padding-bottom: 16px;
                    margin-bottom: 16px;
                    border-bottom: 1px solid #C5C5C5;
                    &.row-bot {
                        border : 0 ;
                    }
                }
            }
        }
    }
    @media (max-width : 768px) {
        .footer-content {
            padding: 25px 0 0;
            .footer-gr {
                .footer-item-bl {
                    flex : 0 0 100% ;
                    max-width: 100%;
                    margin-bottom: 10px;
                    &:nth-child(1),
                    &:nth-child(2),
                    &:nth-child(3){
                        max-width: 100%;
                    }
                    
                    
                    ul li {
                        display: inline-block;
                        width: 100%;
                        padding-right: 0px;
                    }
                    .bl-title{
                        font-size: 14px;
                        line-height: 24px;
                        padding-bottom: 8px;
                        margin-bottom: 8px;
                    }
                    .chungnhan-gr {
                        img {
                            display: inline;
                        }
                    }
                }
                &.gr-right {
                    padding : 0  ;
                }
                &.gr-left {
                    text-align: center;
                    padding-bottom: 16px;
                    margin-bottom: 16px;
                    border-bottom: 1px solid #C5C5C5;
                }
                &.gr-right,
                &.gr-left {
                    flex : 0 0 100% ;
                    max-width: 100%;
                }

                .footer-row {
                    flex-wrap: wrap;
                    &.row-top {
                        .footer-item-bl:nth-child(3)  {
                            display: none;
                        }
                    }
                    &.row-bot {
                        .footer-item-bl:nth-child(1) {
                            order : 3 ;
                        }
                        .footer-item-bl:nth-child(3) {
                            order : -1 ;
                        }
                    }
                }
            }
        }
    }
    @media (max-width : 414px) {
        .footer-content {
            .footer-gr {
                .footer-item-bl {
                  
                    .bl-title{
                        font-size: 14px;
                        line-height: 24px;
                        padding-bottom: 8px;
                        margin-bottom: 8px;
                    }
                    ul li .footer-link {
                        font-size : 12px;
                    }
                }
            }
        }
    }
}

// floating icons

.floating-social{
    position: fixed;
    right: 15px;
    bottom: 80px;
    z-index: 999;
    animation: 3s ease 0s normal forwards 1 fadeindelay;
    .icon-main {
        width: 54px;
        height: 54px;
        text-align: center;
        display: inline-block;
        cursor: pointer;
        img {
            max-width: 100%;
            height: auto;
            vertical-align: middle;
            font-size: 0;
        }
    }
    .list-socials {
        position: absolute;
        min-height: 150px;
        // bottom: -500px;
        // bottom: 55px;
        transition: .3s all ease;
        display: flex;
        flex-flow: row wrap;
        .item {
            flex: 0 0 100%;
            height: 54px;
            cursor: pointer;
            margin-bottom: 10px;
            position: relative;
        }
    }

    @media (max-width : 992px) {
        bottom: 10%;
        .icon-main {
            width: 40px;
            height: 40px;
        }
        .list-socials {
            .item {
                height: 40px;
            }
        }
    }
}



@keyframes fadeindelay {
    0% {
        opacity: 0;
    }

    66% {
        opacity: 0;
    }

    100% {
        opacity: 1;
    }
}

@-webkit-keyframes fadeindelay {
    0% {
        opacity: 0;
    }

    66% {
        opacity: 0;
    }

    100% {
        opacity: 1;
    }
}