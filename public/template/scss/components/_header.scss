.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1040;
  background: $white;
  @media (min-width: 1200px) {
    box-shadow: 2px 2px 2px rgba(0, 0, 0, 0.1);
  }
}

.header--inner {
  // position: relative;
  padding: 13px 0 12px;
  // align-items: center;
  justify-content: space-between;

  .header-gr {
    .nav-link {
      @include font($mega, normal, 14px, 20px);
      display: flex;
      align-items: center;
      padding: 0;
      color: $black;
      .icon-hot {
        position: relative;
        top: -2px;
        img {
          filter: brightness(1);
          max-width: 12px;
          margin-right: 0;
          margin-left: 4px;
        }
      }
      img {
        max-width: 16px;
        filter: brightness(0);
        display: block;
        margin-right: 8px;
      }

      &.link-no-ic {
        text-transform: uppercase;
      }

      &.active,
      &:hover {
        color: $red;

        img {
          filter: brightness(1);
        }
      }
    }

    .header-search {
      position: relative;
      max-width: 228px;

      img {
        position: absolute;
        top: 13px;
        left: 15px;
        max-width: 16px;
        pointer-events: none;
      }

      input {
        width: 100%;
        padding: 9px 20px 9px 40px;
        border: 1px solid $black;
        border-radius: 4px;
        @include font($mega, normal, 14px, 20px);
        color: $black;

        &:focus {
          outline: none;
        }
      }
    }

    .header-gr-top {
      margin-bottom: 36px;
      gap: 24px;
    }

    .header-gr-bot {
      gap: 40px;
    }

    &.left-gr {
      margin-top: 11px;
    }

    &.right-gr {
      .header-gr-top {
        margin-bottom: 26px;
      }
    }
  }

  .header-logo {
    position: relative;
    max-width: 108px;

    a {
      display: block;
    }
    img {
      max-width: 108px;
      max-height: 94px;
    }
  }
  .cart-qty {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 11px;
    height: 11px;
    background: $red;
    border-radius: 50%;
    color: $white;
    @include font($mon, 600, 8px, 20px);
    @include abs(-5px, 0px, auto, auto);
  }
  .user-box {
    img {
      filter: none !important;
      border-radius: 50%;
      width: 16px;
      height: 16px;
    }
  }
  .has-submenu {
    &::after {
      content: '';
      height: 20px;
      width: 100%;
      background: rgba(255, 255, 255, 0.95);
      position: absolute;
      display: inline-block;
      bottom: 0;
      left: 0;
      display: none;
    }
  }
  .has-submenu:hover {
    &::after {
      display: block;
    }
    .sanpham-subheader {
      opacity: 1;
      visibility: visible;
      margin-top: 0;
      pointer-events: auto;
    }
  }
}

.sanpham-subheader {
  background: rgba(255, 255, 255, 0.95);
  position: absolute;
  max-height: calc(100vh - 70px);
  overflow-y: auto;
  left: 0;
  top: 100%;
  width: 100%;
  opacity: 0;
  margin-top: 30px;
  pointer-events: none;
  visibility: hidden;
  transition: all 0.2s ease;

  .sanpham-category {
    padding: 20px 0;
    @extend .d-flex, .flex-wrap;
    margin: 0 -17px;
    .sanpham-category-item {
      @include col-w(25%);
      padding: 0 17px;
    }
  }
  .category-name {
    @extend .d-inline-block, .w-100;
    @include font($mega, 600, 18px, 24px);
    margin-bottom: 13px;
  }
  .category-link {
    @extend .d-inline-block, .w-100;
    @include font($mega, normal, 16px, 22px);
    .icon-hot {
      position: relative;
      top: -3px;
    }
    margin-bottom: 13px;
    color: $secondBlack;
    &:hover,
    &.active {
      color: $red;
    }
  }
}

// responsive header
.header-pc {
  display: none;
  @media (min-width: 1200px) {
    display: block;
  }
}

.header-mb {
  display: block;
  @media (min-width: 1200px) {
    display: none;
  }
  .header-mb-wrap {
    @extend .d-flex, .align-items-center, .justify-content-between;
    padding: 10px 0;
  }
  .mb-ham {
    display: inline-block;
    width: 21px;
    height: 16px;
    span {
      float: left;
      width: 21px;
      height: 2px;
      background: $red;
      &:not(:last-child) {
        margin-bottom: 5px;
      }
      &:nth-child(2) {
        width: 14px;
      }
    }
  }
  .mb-logo {
    width: 60px;
    height: 52px;
    margin-left: 21px;
  }
  .mb-header-right {
    display: flex;
    align-items: center;
    a {
      display: flex;
      align-items: center;
      &:not(:first-child) {
        margin-left: 16px;
      }
    }
  }
  .mb-cart {
    position: relative;
    .cart-qty {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 11px;
      height: 11px;
      background: $red;
      border-radius: 50%;
      color: $white;
      @include font($mon, 600, 8px, 20px);
      @include abs(-5px, -5px, auto, auto);
    }
    img {
      filter: brightness(0);
    }
  }
  .user-box {
    img {
      filter: none !important;
    }
  }
}

// menu mb
.menu-mb {
  .menu-mb-overlay {
    background: #000000;
    opacity: 0.25;
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 1050;
    display: none;
  }
  .menu-mb-wrap {
    position: fixed;
    top: 0;
    left: 0;
    width: 20rem;
    max-width: 80vw;
    height: 100%;
    display: flex;
    flex-direction: column;
    z-index: 1099;
    overflow: hidden;
    background: #fafafa;
    transform: translate(-100%);
    transition: all 0.5s ease-in-out;
  }
  .menu-mb-header {
    @extend .d-flex, .align-items-center, .justify-content-between;
    padding: 8px 24px;
    position: relative;
    position: sticky;
    top: 0;
    height: 52px;
    z-index: 501;
    background: #fafafa;
    .txt {
      display: inline-block;
      margin-left: 8px;
      @include font($mon, normal, 14px, 20px);
      color: #000000;
    }
  }
  .menu-mb-body {
    // padding-top: 42px;
    padding: 16px 16px 0;
    flex: 1;
    //height: calc(100% - 52px);
    overflow-y: auto;
    overflow-x: hidden;
    .menu-mb-section {
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        display: block;
        line-height: 1;
        margin: 0;
        &:not(:last-child) {
          a {
            border-bottom: 1px solid #e0e0e0;
          }
        }
        a {
          display: inline-block;
          width: 100%;
          position: relative;
          font-family: 'SVN-Megante';
          font-weight: 400;
          font-size: 16px;
          line-height: 22px;
          color: $black;
          text-transform: uppercase;
          padding: 8px;
          margin-bottom: 8px;
          border-bottom: 1px solid #e0e0e0;
        }
        &.has-sub {
          a {
            padding-right: 32px;
            position: relative;
          }
        }
      }
      .menu-subs {
        position: absolute;
        display: none;
        top: 0;
        left: 0;
        max-width: none;
        min-width: auto;
        width: 100%;
        height: 100%;
        margin: 0;
        padding: 1rem;
        padding-top: 4rem;
        opacity: 1;
        overflow-y: auto;
        visibility: visible;
        transform: translateX(0%);
        z-index: 500;
        background: #fafafa;
        //animation: 0.5s ease 0s 1 normal forwards running slideLeft;
        &.active {
          display: block;
        }
      }
      .mega-subs-title {
        display: flex;
        align-items: center;
        i {
          margin-right: 8px;
        }
      }
      .list-item {
        padding: 8px 0;
        border-bottom: 1px solid #e0e0e0;
        ul {
          list-style: none;
          padding: 0;
          margin: 0;
          height: 100%;
          overflow-y: auto;
          overflow-x: hidden;
          li {
            display: block;
            line-height: 1;
            margin: 0;
            a {
              padding-right: 0;
              color: #4f4f4f;
              text-transform: capitalize;
              line-height: 22px;
              border: none;
              .icon-hot {
                position: relative;
                top: -4px;
              }
            }
          }
        }
        .title {
          font-weight: 400;
          font-size: 18px;
          line-height: 24px;
          color: #000000;
          border: none;
        }
      }
    }
    .icon-right {
      position: absolute;
      top: 0;
      right: 0;
      width: 24px;
      height: 24px;
      color: #828282;
    }
    .icon-left {
      img {
        transform: rotate(-180deg);
      }
    }
  }
  &.active {
    .menu-mb-wrap {
      transform: translate(0%);
    }
    .menu-mb-overlay {
      display: block;
    }
  }
}

.search-mobile {
  position: absolute;
  width: 100%;
  left: 0;
  top: 0;
  background: $white;
  transform: translateX(100%);
  transition: 0.5s all;
  opacity: 1;
  visibility: hidden;
  .search-mobile-wrap {
    display: flex;
    align-items: center;
    padding: 16px;
  }
  .close-search-mb {
    margin: 0 8px;
    img {
      transform: rotate(-180deg);
    }
  }
  .input-wrap {
    flex: 1;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 7px 12px;
    display: flex;
    align-items: center;

    input {
      flex: 1;
      border: 0;
      outline: 0;
      appearance: none;
      padding: 0 8px;
    }
    button {
      border: 0;
      outline: 0;
      padding: 0;
      line-height: 1;
      appearance: none;
      background: transparent;
    }
  }
  &.active {
    transform: translateX(0);
    visibility: visible;
  }
}

@-webkit-keyframes slideLeft {
  0% {
    opacity: 0;
    transform: translateX(100%);
  }
  100% {
    opacity: 1;
    transform: translateX(0%);
  }
}

@keyframes slideLeft {
  0% {
    opacity: 0;
    transform: translateX(100%);
  }
  100% {
    opacity: 1;
    transform: translateX(0%);
  }
}

@-webkit-keyframes slideRight {
  0% {
    opacity: 1;
    transform: translateX(0%);
  }
  100% {
    opacity: 0;
    transform: translateX(100%);
  }
}

@keyframes slideRight {
  0% {
    opacity: 1;
    transform: translateX(0%);
  }
  100% {
    opacity: 0;
    transform: translateX(100%);
  }
}

button,
.form-control,
input,
textarea {
  outline: 0;
  &:focus {
    box-shadow: unset;
    border-color: initial;
  }
}
#modalResigter {
  position: fixed;
  top: 0;
  right: -100%;
  background: #fff;
  height: 100%;
  width: 100%;
  // padding: 1rem;
  max-width: 603px;
  z-index: 9;
  transition: 0.3s all ease-in-out;
  opacity: 0;
  visibility: hidden;
  overflow-x: hidden;
  overflow-y: auto;
  &.active {
    opacity: 1;
    visibility: visible;
    right: 0;
  }
  .closed {
    position: absolute;
    top: 1rem;
    right: 1rem;
    cursor: pointer;
    @media screen and (min-width: 1200px) {
      top: 25px;
      right: 25px;
    }
  }
  .wrapper {
    display: flex;
    flex-direction: column;
    justify-content: center;
    min-height: 100%;
    max-width: 342px;
    margin: 0 auto;
    padding: 3rem 0;
    h2 {
      font-family: 'SVN-Megante';
      font-style: normal;
      font-weight: 400;
      font-size: 24px;
      line-height: 32px;
      color: #333333;
      margin-bottom: 8px;
    }
    .not-yet-acc {
      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
      color: #808b96;
      margin-bottom: 24px;
      a {
        color: #aa1f23;
        font-weight: 600;
      }
    }
    .form-control {
      padding: 10px 0px 10px 12px;
      background: #f2f2f2;
      border-radius: 8px;
      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
      border: 0;
      min-height: 40px;
      &::placeholder {
        color: #828282;
      }
    }

    .forgot {
      font-weight: 600;
      font-size: 14px;
      line-height: 20px;
      color: #aa1f23;
    }
    .btn-login {
      border: 0;
      padding: 10px 16px;
      min-height: 42px;
      background: #aa1f23;
      border-radius: 8px;
      text-align: center;
      font-weight: 600;
      font-size: 16px;
      line-height: 22px;
      margin-bottom: 24px;
      outline: 0;
    }
    .or {
      position: relative;
      text-align: center;
      &::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        height: 1px;
        width: 100%;
        background: #eaecee;
      }
      span {
        display: inline-block;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: #aeb6bf;
        background: #fff;
        padding: 0px 10px;
        position: relative;
        z-index: 2;
      }
    }
    .soicals {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1rem;
      margin: 24px 0;
      a {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 10px 16px;
        min-height: 40px;
        border: 1px solid #eaecee;
        border-radius: 8px;
      }
    }
    .faq {
      font-weight: 400;
      font-size: 16px;
      line-height: 22px;
      color: #333333;
      display: grid;
      gap: 9px;
      .item {
        display: flex;
        align-items: center;
        gap: 10px;
      }
      strong {
        color: #aa1f23;
        font-weight: 600;
      }
    }
  }
}

.header__overlay {
  position: fixed;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  z-index: 1041;
  background: rgba(0, 0, 0, 0.5);
  opacity: 0;
  visibility: hidden;
  transition: 0.3s all ease-in-out;
  &.active {
    opacity: 1;
    visibility: visible;
  }
}
