.paging-block{
    display: block;
    margin-top: 48px;
    .paging-block-wrap {
        display: flex;
        align-items: center;
        padding: 0;
        margin: 0;
        justify-content: center;
        flex-wrap: wrap;
        .page-item {
            margin : 0 8px 8px ; 
            &.active, &:hover {
                .page-link {
                    background: $red;
                    color : $white ; 
                    svg {
                        path {
                            fill:  $white ;;
                        }
                    }
                }
            }
            @media (max-width : 768px) {
                margin : 0 4px 8px ; 
            }
        }
        .page-link{
            display: flex;
            align-items: center;
            justify-content: center;
            @include font($mon,400,16px,calc(24/16));
            color : $red ; 
            padding: 0 ;
            width: 32px;
            height: 32px;
            background: $white;
            border-radius: 4px;
            border: 0;
            &.page-control {
                background: #F2F2F2;
            }
        }
        .paging-dots {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            background: #fff;
            border-radius: 4px;
            border: 1px solid #E0E0E0; 
        }
    }
}