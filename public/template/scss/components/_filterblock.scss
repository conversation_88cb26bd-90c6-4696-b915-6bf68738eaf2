.block-filter {
  .box-title {
    .box-title-wrap {
      @extend .d-flex, .align-items-center;
    }
    h3 {
      margin: 0;
    }
    .title {
      @include font($mega, normal, 24px, calc(32 / 24));
      color: #000000;
      .icon {
        position: relative;
        top: -4px;
      }
    }
    .remove-filter {
      // margin-left: 24px;
      a {
        display: inline-block;
        @include font($mega, normal, 16px, calc(28 / 16));
        color: #4f4f4f;
        background: #f2f2f2;
        border-radius: 4px;
        padding: 2px 16px 2px 5px;
      }
      .icon {
        position: relative;
        top: -3px;
      }
    }
  }
  .box-content {
    padding: 33px 0 16px 0;
  }
  .filter-options-wrap {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -6px;
    .item {
      flex: 0 0 calc(100% / 6);
      max-width: calc(100% / 6);
      padding: 0 6px;
      margin-bottom: 16px;
      .dropdown {
        border: 1px solid #000000;
        border-radius: 5px;

        &-menu {
          width: 100%;
          border: 1px solid #000000;
          border-radius: 0;
          top: 100% !important;
          transform: unset !important;
          max-height: 40vh;
          overflow-y: auto;
        }

        a {
          display: flex;
          align-items: center;
          @include font($mega, normal, 13px, calc(20 / 13));
          padding: 8px 11px;
          color: #333333;
        }
        span.txt {
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 1;
          overflow: hidden;
          white-space: normal;
          text-overflow: ellipsis;
          margin-right: 10px;
        }
        .dropdown-item {
          @include font($mega, normal, 13px, calc(21 / 13));
          padding: 3px 11px;
          width: 100%;
          white-space: normal;
        }
        .dropdown-item:hover,
        .dropdown-item:focus,
        .dropdown-item.active {
          color: $red;
          background: none;
        }
        &.choosen {
          background: #aa1f23;
          border-color: transparent;
          .dropdown-toggle {
            @extend .gardient-color;
            @extend .clip-text;
            color: #d0b46c;
            font-weight: 600;
          }
          .dropdown-toggle::after {
            filter: none;
          }
        }
      }
      .dropdown-toggle::after {
        margin-left: auto;
        content: '';
        width: 20px;
        height: 20px;
        border: 0;
        filter: brightness(0%);
        background: url('../images/product/ic-arrow-down-color.png');
      }
    }
  }
  @media (max-width: 1200px) {
    .filter-options-wrap {
      .item {
        flex: 0 0 25%;
        max-width: 25%;
      }
    }
  }
  @media (max-width: 768px) {
    .filter-options-wrap {
      .item {
        flex: 0 0 33.333%;
        max-width: 33.333%;
      }
    }
  }
  @media (max-width: 576px) {
    .filter-options-wrap {
      .item {
        flex: 0 0 50%;
        max-width: 50%;
      }
    }
  }
}

#price-range {
  .medium-counter {
    &--item {
      display: block;
      width: 100%;
      border: 1px solid #bcbcbc;
      border-radius: 5px;
      padding: 5px;
      font-weight: 400;
      font-size: 13px;
      line-height: 1;
      text-align: center;
      color: #000000;
    }

    .text {
      display: block;
      width: 100%;
      font-family: 'SVN-Megante';
      font-style: normal;
      font-weight: 400;
      font-size: 13px;
      line-height: 1;
      text-align: center;
      color: #696969;
      margin: 5px 0;
    }

    input {
      &:focus {
        outline: unset;
      }

      &::-webkit-outer-spin-button,
      &::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
      }

      &[type='number'] {
        -moz-appearance: textfield;
      }
    }
  }

  .before-amount {
    display: inline-block;
    font-weight: 700;
    font-size: 10px;
    line-height: 2;
    text-align: center;
    color: #000000;
  }

  .ui-slider-horizontal {
    height: 4px;
  }

  .ui-widget-header {
    background: #d9d9d9;
    border: unset;
  }

  .ui-state-default,
  .ui-widget-content .ui-state-default {
    border: unset;
    background: #8b8b8b;
    font-weight: normal;
    color: #454545;
    border-radius: 50%;
    width: 8px;
    height: 8px;
    top: -3px;
  }
}
