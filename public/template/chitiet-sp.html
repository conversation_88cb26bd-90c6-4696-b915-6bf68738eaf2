<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <link rel="shortcut icon" async href="images/common/favicon.png" />
  <title>Website Bảo Tín Mạnh Hải</title>
  <link rel="stylesheet" href="libs/bootstrap/bootstrap.min.css" />
  <link rel="stylesheet" href="libs/swiper/swiper-bundle.min.css" />
  <link rel="stylesheet" href="libs/fancybox/jquery.fancybox.min.css" />
  <link href="libs/flatpickr/flatpickr.min.css" rel="stylesheet" />
  <link rel="stylesheet" href="libs/aos/aos.css" />

  <link rel="stylesheet" href="libs/dropzone/dropzone.min.css" />


  <link rel="stylesheet" href="libs/fancybox/jquery.fancybox.min.css" />
  <link rel="stylesheet" href="css/style.css" />
</head>

<body>
  <div id="app">
    <myheader></myheader>
    <main>
      <div class="master-wrapper">

        <div class="productdetail-page">

          <div class="productdetail-top">
            <div class="container">
              <div class="productdetail-top-wrap">
                <div class="gallery">
                  <div class="gallery-wrap">
                    <div class="thumb-gallery">
                      <div class="swiper-container gallery-slider-thumb">
                        <div class="swiper-wrapper">
                          <!-- Slides -->
                          <div class="swiper-slide">
                            <div class="img">
                              <img data-src="images/product/product-thumb.jpg" alt="" class="img-fluid swiper-lazy">
                              <div class="swiper-lazy-preloader"></div>
                            </div>
                          </div>
                          <div class="swiper-slide">
                            <div class="img">
                              <img data-src="images/product/product-thumb.jpg" alt="" class="img-fluid swiper-lazy">
                              <div class="swiper-lazy-preloader"></div>
                            </div>
                          </div>
                          <div class="swiper-slide">
                            <div class="img">
                              <img data-src="images/product/product-thumb.jpg" alt="" class="img-fluid swiper-lazy">
                              <div class="swiper-lazy-preloader"></div>
                            </div>
                          </div>
                          <div class="swiper-slide">
                            <div class="img">
                              <img data-src="images/product/product-thumb.jpg" alt="" class="img-fluid swiper-lazy">
                              <div class="swiper-lazy-preloader"></div>
                            </div>
                          </div>
                          <div class="swiper-slide">
                            <div class="img">
                              <img data-src="images/product/product-thumb.jpg" alt="" class="img-fluid swiper-lazy">
                              <div class="swiper-lazy-preloader"></div>
                            </div>
                          </div>
                          <div class="swiper-slide">
                            <div class="img">
                              <img data-src="images/product/product-thumb.jpg" alt="" class="img-fluid swiper-lazy">
                              <div class="swiper-lazy-preloader"></div>
                            </div>
                          </div>
                        </div>
                        <div class="swiper-button-next thumb-swiper-button-next">
                          <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path d="M18 9.5L12 15.5L6 9.5" stroke="#AA1F23" stroke-width="2" stroke-linecap="round"
                              stroke-linejoin="round" />
                          </svg>
                        </div>
                        <div class="swiper-button-prev thumb-swiper-button-prev ">
                          <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path d="M6 15.5L12 9.5L18 15.5" stroke="#AA1F23" stroke-width="2" stroke-linecap="round"
                              stroke-linejoin="round" />
                          </svg>

                        </div>
                      </div>
                    </div>
                    <div class="main-gallery">
                      <div class="swiper-container gallery-slider-big">
                        <!-- Additional required wrapper -->
                        <div class="swiper-wrapper">
                          <!-- Slides -->
                          <div class="swiper-slide">
                            <div class="img">
                              <img data-src="images/product/product-img.jpg" alt="" class="img-fluid swiper-lazy">
                              <div class="swiper-lazy-preloader"></div>
                            </div>
                          </div>
                          <div class="swiper-slide">
                            <div class="img">
                              <img data-src="images/product/product-img.jpg" alt="" class="img-fluid swiper-lazy">
                              <div class="swiper-lazy-preloader"></div>
                            </div>
                          </div>
                          <div class="swiper-slide">
                            <div class="img">
                              <img data-src="images/product/product-img.jpg" alt="" class="img-fluid swiper-lazy">
                              <div class="swiper-lazy-preloader"></div>
                            </div>
                          </div>
                          <div class="swiper-slide">
                            <div class="img">
                              <img data-src="images/product/product-img.jpg" alt="" class="img-fluid swiper-lazy">
                              <div class="swiper-lazy-preloader"></div>
                            </div>
                          </div>
                          <div class="swiper-slide">
                            <div class="img">
                              <img data-src="images/product/product-img.jpg" alt="" class="img-fluid swiper-lazy">
                              <div class="swiper-lazy-preloader"></div>
                            </div>
                          </div>
                          <div class="swiper-slide">
                            <div class="img">
                              <img data-src="images/product/product-img.jpg" alt="" class="img-fluid swiper-lazy">
                              <div class="swiper-lazy-preloader"></div>
                            </div>
                          </div>
                        </div>
                        <!-- Add Arrows -->
                        <div class="swiper-button-next big-swiper-button-next">
                          <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path d="M8.5 6L14.5 12L8.5 18" stroke="#AA1F23" stroke-width="2" stroke-linecap="round"
                              stroke-linejoin="round" />
                          </svg>
                        </div>
                        <div class="swiper-button-prev big-swiper-button-prev">
                          <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path d="M15.5 18L9.5 12L15.5 6" stroke="#AA1F23" stroke-width="2" stroke-linecap="round"
                              stroke-linejoin="round" />
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="social-share">
                    <span>Chia sẻ:</span>
                    <a href="javascript:;" v-for="(item,index) in 4">
                      <img :src="'images/ic-social-share-' + (index + 1) + '.svg'" alt="">
                    </a>
                  </div>
                </div>
                <div class="top-info">
                  <h1 class="name">
                    Nhẫn vàng phong cách Ý NY10K30
                  </h1>
                  <div class="price-box">
                    <div class="price-normal">
                      2.487.000đ - 2.503.000đ
                      <div class="discount">
                        <span class="txt">Giảm</span>
                        <span class="value">30%</span>
                      </div>
                    </div>
                    <div class="price-old">
                      2.507.000đ - 2.603.000đ
                    </div>


                  </div>
                  <div class="product-summary">
                    <div class="product-sku">
                      <span class="title">
                        Mã sản phẩm :
                      </span>
                      <span class="color-theme value">
                        NY10K30
                      </span>
                    </div>
                    <div class="product-stocks">
                      <span class="color-theme value">
                        Còn hàng
                      </span>
                      <!-- <span class="color-theme value font-weight-bold">
                                    Hết hàng
                                  </span> -->
                    </div>

                    <div class="product-config">
                      <div class="box-title">
                        <span class="title">
                          Chọn kích cỡ
                        </span>
                        <a href="javascript:;" class="value">
                          Hướng dẫn chọn kích cỡ
                        </a>
                      </div>
                      <div class="config-list">
                        <a href="javascript:;" class="config-item js-config-item disabled">
                          09
                        </a>
                        <a href="javascript:;" class="config-item js-config-item">
                          10
                        </a>
                        <a href="javascript:;" class="config-item js-config-item">
                          11
                        </a>
                        <a href="javascript:;" class="config-item js-config-item">
                          12
                        </a>
                        <a href="javascript:;" class="config-item js-config-item">
                          13
                        </a>
                      </div>
                    </div>

                    <div class="special-offers">

                      <div class="title">
                        Ưu đãi
                      </div>
                      <div class="special-offer-item">
                        <div class="item">
                          Giảm 30% từ 25/05/2022 đến 15/06/2022
                        </div>
                        <div class="item">
                          Nhận ngay phiếu mua hàng trị giá 200.000đ khi mua từ 2 sản phẩm trở lên từ 25/05/2022 đến
                          15/06/2022
                        </div>
                      </div>
                    </div>

                    <div class="product-actions">
                      <div class="actions-wrap">
                        <a class="action-btn add-now" href="javascript:;">
                          Mua ngay
                        </a>
                        <a class="action-btn add-to-cart" href="javascript:;">
                          <span class="icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                              xmlns="http://www.w3.org/2000/svg">
                              <g clip-path="url(#clip0_1141_10672)">
                                <path
                                  d="M23.79 5.4555C23.6119 5.2365 23.3447 5.10938 23.0625 5.10938H6.08137L6.01181 4.47038L6.00933 4.44984C5.67652 1.91306 3.49608 0 0.9375 0C0.419719 0 0 0.419719 0 0.9375C0 1.45528 0.419719 1.875 0.9375 1.875C2.55497 1.875 3.93389 3.08198 4.149 4.68412L5.26336 14.9217C4.28883 15.364 3.60938 16.3463 3.60938 17.4844C3.60938 17.4922 3.60947 17.5 3.60966 17.5078C3.60947 17.5156 3.60938 17.5234 3.60938 17.5312C3.60938 19.0821 4.87106 20.3438 6.42188 20.3438H6.80334C6.708 20.6233 6.65625 20.9229 6.65625 21.2344C6.65625 22.7593 7.89689 24 9.42188 24C10.9469 24 12.1875 22.7593 12.1875 21.2344C12.1875 20.9229 12.1357 20.6233 12.0404 20.3438H16.0846C15.9893 20.6233 15.9375 20.9229 15.9375 21.2344C15.9375 22.7593 17.1782 24 18.7032 24C20.2282 24 21.4688 22.7593 21.4688 21.2344C21.4688 19.7094 20.2282 18.4688 18.7032 18.4688H6.42188C5.90494 18.4688 5.48438 18.0482 5.48438 17.5312C5.48438 17.5234 5.48428 17.5156 5.48409 17.5078C5.48428 17.5 5.48438 17.4922 5.48438 17.4844C5.48438 16.9674 5.90494 16.5469 6.42188 16.5469H18.3905C20.0547 16.5469 21.582 15.5172 22.2814 13.9237C22.4895 13.4496 22.2739 12.8965 21.7998 12.6884C21.3257 12.4803 20.7727 12.696 20.5645 13.17C20.1703 14.0684 19.2966 14.6719 18.3905 14.6719H7.12223L6.28547 6.98438H21.9093L21.4506 9.18356C21.3449 9.69042 21.67 10.187 22.1769 10.2927C22.2414 10.3062 22.3058 10.3127 22.3693 10.3127C22.8039 10.3127 23.1938 10.0087 23.286 9.56644L23.9802 6.23831C24.0379 5.96203 23.968 5.6745 23.79 5.4555ZM18.7031 20.3438C19.1942 20.3438 19.5938 20.7433 19.5938 21.2344C19.5938 21.7255 19.1942 22.125 18.7031 22.125C18.212 22.125 17.8125 21.7255 17.8125 21.2344C17.8125 20.7433 18.212 20.3438 18.7031 20.3438ZM9.42188 20.3438C9.91298 20.3438 10.3125 20.7433 10.3125 21.2344C10.3125 21.7255 9.91298 22.125 9.42188 22.125C8.93077 22.125 8.53125 21.7255 8.53125 21.2344C8.53125 20.7433 8.93077 20.3438 9.42188 20.3438Z"
                                  fill="#D0B46C" />
                              </g>
                              <defs>
                                <clipPath id="clip0_1141_10672">
                                  <rect width="24" height="24" fill="white" />
                                </clipPath>
                              </defs>
                            </svg>
                          </span>
                        </a>
                      </div>
                      <div class="actions-wrap">
                        <a href="javascript:;" class="action-btn add-schedule">
                          Đặt lịch hẹn tại cửa hàng
                        </a>
                      </div>
                      <div class="actions-wrap">
                        <div class="hotline">
                          Hoặc liên hệ tư vấn qua hotline: <a href="tel:02498765432" class="color-theme">024 9876
                            5432</a> hoặc qua
                          <a href="javascript:;" class="icon">
                            <img src="images/common/ic-zalo.jpg" alt="" class="img-fluid" width="20" height="20">
                          </a>
                          <a href="javascript:;" class="icon">
                            <img src="images/common/ic-messenger.jpg" alt="" class="img-fluid" width="20" height="20">
                          </a>
                        </div>
                      </div>
                    </div>

                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="productdetail-main">
            <div class="container">
              <div class="accordion-group">
                <div class="accordion-heading">
                  <a class="accordion-toggle collapsed" data-toggle="collapse" data-parent="#faq-wrap" href="#faq1">
                    Thông số sản phẩm
                    <div class="icon">
                      <div class="horizontal"></div>
                      <div class="vertical"></div>
                    </div>
                  </a>
                </div>
                <div id="faq1" class="accordion-body collapse">
                  <div class="accordion-inner">
                    <div class="table-responsive tskt-table">
                      <table class="tskt-table-content">
                        <tbody>
                          <tr>
                            <td>Thương hiệu</td>
                            <td>
                              <span class="color-theme">
                                Bảo Tín Mạnh Hải
                              </span>
                            </td>
                          </tr>
                          <tr>
                            <td>Nhóm sản phẩm</td>
                            <td>
                              <span class="color-theme">
                                Phong cách Ý
                              </span>
                            </td>
                          </tr>
                          <tr>
                            <td>Chủng loại</td>
                            <td>
                              <span class="color-theme">
                                Nhẫn
                              </span>
                            </td>
                          </tr>
                          <tr>
                            <td>Màu vàng</td>
                            <td>
                              <span class="color-theme">
                                Trắng
                              </span>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
              <div class="accordion-group">
                <div class="accordion-heading">
                  <a class="accordion-toggle collapsed" data-toggle="collapse" data-parent="#faq-wrap" href="#faq2">
                    Mô tả sản phẩm
                    <div class="icon">
                      <div class="horizontal"></div>
                      <div class="vertical"></div>
                    </div>
                  </a>
                </div>
                <div id="faq2" class="accordion-body collapse">
                  <div class="accordion-inner">
                    <div class="nd-content">
                      <p>
                        Lorem ipsum dolor sit amet consectetur adipisicing elit. Adipisci eveniet ab fugit aperiam
                        temporibus nemo earum. Voluptatibus veritatis recusandae sequi magni doloremque vitae! Atque
                        eius suscipit qui molestias nisi maxime.
                      </p>
                      <p>
                        Lorem ipsum dolor sit amet consectetur adipisicing elit. Adipisci eveniet ab fugit aperiam
                        temporibus nemo earum. Voluptatibus veritatis recusandae sequi magni doloremque vitae! Atque
                        eius suscipit qui molestias nisi maxime.
                      </p>
                      <p>
                        Lorem ipsum dolor sit amet consectetur adipisicing elit. Adipisci eveniet ab fugit aperiam
                        temporibus nemo earum. Voluptatibus veritatis recusandae sequi magni doloremque vitae! Atque
                        eius suscipit qui molestias nisi maxime.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div class="accordion-group">
                <div class="accordion-heading">
                  <a class="accordion-toggle collapsed" data-toggle="collapse" data-parent="#faq-wrap" href="#faq3">
                    Hướng dẫn sử dụng
                    <div class="icon">
                      <div class="horizontal"></div>
                      <div class="vertical"></div>
                    </div>
                  </a>
                </div>
                <div id="faq3" class="accordion-body collapse">
                  <div class="accordion-inner">
                    <div class="nd-content">
                      <p>
                        Lorem ipsum dolor sit amet consectetur adipisicing elit. Adipisci eveniet ab fugit aperiam
                        temporibus nemo earum. Voluptatibus veritatis recusandae sequi magni doloremque vitae! Atque
                        eius suscipit qui molestias nisi maxime.
                      </p>
                      <p>
                        Lorem ipsum dolor sit amet consectetur adipisicing elit. Adipisci eveniet ab fugit aperiam
                        temporibus nemo earum. Voluptatibus veritatis recusandae sequi magni doloremque vitae! Atque
                        eius suscipit qui molestias nisi maxime.
                      </p>
                      <p>
                        Lorem ipsum dolor sit amet consectetur adipisicing elit. Adipisci eveniet ab fugit aperiam
                        temporibus nemo earum. Voluptatibus veritatis recusandae sequi magni doloremque vitae! Atque
                        eius suscipit qui molestias nisi maxime.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div class="accordion-group">
                <div class="accordion-heading">
                  <a class="accordion-toggle collapsed" data-toggle="collapse" data-parent="#faq-wrap" href="#faq4">
                    Phụ kiện đi kèm
                    <div class="icon">
                      <div class="horizontal"></div>
                      <div class="vertical"></div>
                    </div>
                  </a>
                </div>
                <div id="faq4" class="accordion-body collapse">
                  <div class="accordion-inner">
                    <div class="accessory-list">
                      <div class="list-img">
                        <a href="javascript:;" class="img">
                          <img src="images/loading.gif" data-src="images/product/accessory.jpg" alt=""
                            class="img-fluid lazy">
                        </a>
                        <a href="javascript:;" class="img">
                          <img src="images/loading.gif" data-src="images/product/accessory.jpg" alt=""
                            class="img-fluid lazy">
                        </a>
                        <a href="javascript:;" class="img">
                          <img src="images/loading.gif" data-src="images/product/accessory.jpg" alt=""
                            class="img-fluid lazy">
                        </a>
                      </div>
                      <div class="list-txt">
                        <ul>
                          <li>
                            1 Hộp đựng trang sức bàng nhung
                          </li>
                          <li>
                            1 Khăn vệ sinh trang sức
                          </li>
                          <li>
                            1 Phiếu bảo hành ghi rõ ngày tháng
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>  

          <div class="block-review">
            <div class="container">
              <form action="">   
                <div class="box-stars">
                  Đánh giá
                  <div class="rating-comment" id="select-rate-pro">
                      <input type="radio" class="rating-input" id="rating-input-review-0-5" value="5" data-title="Rất hài lòng" name="user_post[rate]" checked="" />
                      <label for="rating-input-review-0-5" class="rating-star" data-title="Rất hài lòng"></label>
                      <input type="radio" class="rating-input" id="rating-input-review-0-4" value="4" data-title="Hài lòng" name="user_post[rate]" />
                      <label for="rating-input-review-0-4" class="rating-star" data-title="Hài lòng"></label>
                      <input type="radio" class="rating-input" id="rating-input-review-0-3" value="3" data-title="Bình thường" name="user_post[rate]" />
                      <label for="rating-input-review-0-3" class="rating-star" data-title="Bình thường"></label>
                      <input type="radio" class="rating-input" id="rating-input-review-0-2" value="2" data-title="Tạm được" name="user_post[rate]" />
                      <label for="rating-input-review-0-2" class="rating-star" data-title="Tạm được"></label>
                      <input type="radio" class="rating-input" id="rating-input-review-0-1" value="1" data-title="Không thích" name="user_post[rate]" />
                      <label for="rating-input-review-0-1" class="rating-star" data-title="Không thích"></label>
                  </div>
                </div>
                <div class="comment-note">
                  <textarea name="" id="aasdads" cols="30" rows="2" placeholder="  Viết đánh giá "></textarea>
                </div>

                <div class="img-upload-box d-flex flex-wrap" id="js-preview-file-upload-1">
                  <div class="img-uploaded img-uploaded-small float-left" v-for="item in 5" :key="item">
                    <img src="images/partner-1.png" alt="" />
                    <div class="img-uploaded-actions">
                      <a href="javascript:;" class="action-del img-actions">
                        <img src="images/ic-close.png" alt="" class="lazy img-fluid" />
                      </a>
                    </div>
                  </div>
                </div>


                <div class="actions">

                  <a class="upload-img js-trigger-uploadimg" href="javascript:;" data-id="1">
                      <span class="icon">
                        <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M7.8125 6L9.375 3H15.625L17.1875 6H7.8125Z" fill="black" fill-opacity="0.85" stroke="black" stroke-opacity="0.85" stroke-width="2" stroke-linejoin="round"/>
                          <path d="M21.3529 6H3.64453C2.78159 6 2.08203 6.67157 2.08203 7.5V19.5C2.08203 20.3284 2.78159 21 3.64453 21H21.3529C22.2158 21 22.9154 20.3284 22.9154 19.5V7.5C22.9154 6.67157 22.2158 6 21.3529 6Z" fill="black" fill-opacity="0.85" stroke="black" stroke-opacity="0.85" stroke-width="2" stroke-linejoin="round"/>
                          <path d="M12.4987 17.5C14.7999 17.5 16.6654 15.7092 16.6654 13.5C16.6654 11.2908 14.7999 9.5 12.4987 9.5C10.1975 9.5 8.33203 11.2908 8.33203 13.5C8.33203 15.7092 10.1975 17.5 12.4987 17.5Z" fill="white" stroke="white" stroke-width="2" stroke-linejoin="round"/>
                        </svg>                          
                      </span>
                      Tải ảnh lên
                  </a>
                  <input type="file" id="" multiple="" accept="image/x-png, image/jpg, image/jpeg" name="file" style="display: none" class="js-input-upload-imgs" />
                  <button type="submit" class="btn-submit">
                      Gửi đánh giá
                  </button>

                </div>
              </form>

              <div class="comment-list">

                <div class="item" v-for="t in 2" :key="t">
                    <div class="comment-user">
                        <div class="ava">
                            <img src="https://randomuser.me/api/portraits/men/86.jpg" alt="">
                        </div>
                        <div class="comment-info">
                            <div class="rating">
                                <i class="rating-active" style="width  : 50%"></i>
                                <i class="rating-inactive" ></i>
                            </div>
                            <div class="user-info">
                                <b class="name"> Van Anh Nguyen</b>
                                <span class="created">19/11, 4:20</span>
                            </div>
                        </div>
                    </div>
                    <div class="comment-content">
                        <div class="nd-content">
                            <p>
                              Sản phẩm nội thất phòng khách cao cấp không thể thiểu cho một không gian sang trọng có thể kể đến như bộ sofa, bàn trà, bàn trang trí và hệ thống kệ tủ, kệ tivi. 
                            </p>
                        </div>
                        <div class="list-img">
                          <a href="images/promotion-store-1.png" data-fancybox="album" v-for="n in 10">
                            <img src="images/promotion-store-1.png" alt="" class="img-fluid">
                          </a>
                        </div>
                    </div>
                </div>

              </div>

              <div class="text-center">
                <a href="javascript:;" class="view-more-comment">
                  Xem thêm đánh giá
                </a>
              </div>
            </div>
          </div>

          <div class="home-section home-sanpham">
            <div class="sanpham-bl sanpham-noibat">
              <div class="container">
                <div class="section-title title-w-deco">
                  <h4>Sản phẩm bạn có thể quan tâm</h4>
                  <div class="title-deco"></div>
                </div>
                <div class="list-products">
                  <div class="sp-item sp-item-border" v-for="n in 4">
                    <div class="sp-item-img">
                      <a href="javascript:;">
                        <img class="img-fluid" src="images/home/<USER>" alt="">
                      </a>
                    </div>
                    <div class="sp-item-body">
                      <a href="javascript:;">Nhẫn gắn đá màu MS05</a>
                      <p class="price">500.000đ</p>
                    </div>
                  </div>
                </div>
                <div class="text-center">
                  <a href="javascript:;" class="view-bst">
                    Xem tất cả
                  </a>
                </div>
              </div>
            </div>
          </div>

          <div class="home-section home-sanpham">
            <div class="sanpham-bl sanpham-noibat">
              <div class="container">
                <div class="section-title title-w-deco">
                  <h4>Sản phẩm bán chạy</h4>
                  <div class="title-deco"></div>
                </div>
                <div class="list-products">
                  <div class="sp-item sp-item-border" v-for="n in 4">
                    <div class="sp-item-img">
                      <a href="javascript:;">
                        <img class="img-fluid" src="images/home/<USER>" alt="">
                      </a>
                    </div>
                    <div class="sp-item-body">
                      <a href="javascript:;">Nhẫn gắn đá màu MS05</a>
                      <p class="price">500.000đ</p>
                    </div>
                  </div>
                </div>
                <div class="text-center">
                  <a href="javascript:;" class="view-bst">
                    Xem tất cả
                  </a>
                </div>
              </div>
            </div>
          </div>

          <div class="home-section home-sanpham">
            <div class="sanpham-bl sanpham-noibat">
              <div class="container">
                <div class="section-title title-w-deco">
                  <h4>Sản phẩm bạn đã xem</h4>
                  <div class="title-deco"></div>
                </div>
                <div class="list-products">
                  <div class="sp-item sp-item-border" v-for="n in 4">
                    <div class="sp-item-img">
                      <a href="javascript:;">
                        <img class="img-fluid" src="images/home/<USER>" alt="">
                      </a>
                    </div>
                    <div class="sp-item-body">
                      <a href="javascript:;">Nhẫn gắn đá màu MS05</a>
                      <p class="price">500.000đ</p>
                    </div>
                  </div>
                </div>
                <div class="text-center">
                  <a href="javascript:;" class="view-bst">
                    Xem tất cả
                  </a>
                </div>
              </div>
            </div>
          </div>

        </div>

      </div>
    </main>
    <myfooter></myfooter>
  </div>

  <script type="text/javascript" src="js/vue.js"></script>
  <script type="text/javascript" src="js/httpvueloader.js"></script>
  <script type="text/javascript" src="libs/swiper/swiper-bundle.min.js"></script>
  <script type="text/javascript" src="libs/swiper/swiper-animation.min.js"></script>
  <script type="text/javascript" src="libs/bootstrap/jquery.min.js"></script>

  <script type="text/javascript" src="libs/bootstrap/popper.min.js"></script>
  <script type="text/javascript" src="libs/bootstrap/bootstrap.min.js"></script>

  <script src="libs/dropzone/dropzone.min.js"></script>


  <script type="text/javascript" src="libs/aos/aos.js"></script>
  <script type="text/javascript" src="libs/chartjs/chart.min.js"></script>
  <script type="text/javascript" src="libs/fancybox/jquery.fancybox.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@11.0.6/dist/lazyload.min.js"></script>
  <script type="text/javascript" src="js/main.js"></script>

  <script type="text/javascript">
    var app = new Vue({
      el: "#app",
      data() {
        return {
          flashSaleEndTime: 80000,
          activeItem: 'tab-1',
        };
      },
      components: {
        myheader: httpVueLoader("vue_components/header.vue"),
        myfooter: httpVueLoader("vue_components/footer.vue"),
        rednavi: httpVueLoader("vue_components/red-slide-navigation.vue"),
        yellownavi: httpVueLoader("vue_components/yellow-slide-navigation.vue"),
        filterbox: httpVueLoader("vue_components/filterbox.vue"),
        pagination: httpVueLoader("vue_components/pagination.vue"),
      },
      methods: {
        isActive(menuItem) {
          return this.activeItem === menuItem
        },
        setActive(menuItem) {
          this.activeItem = menuItem
        },
      },
      mounted() {
        this.$nextTick(() => {
          $(window).bind("load", function () {

            if (LazyLoad) {
              var lazyloader = new LazyLoad({
                elements: ".lazy"
              });
            }

            AOS.init({
              // startEvent: 'load',
              duration: 700,
              easing: "ease",
            });

            productConfigChange();


            var swiper11 = new Swiper(".gallery-slider-thumb", {
              loop: false,
              spaceBetween: 16,
              slidesPerView: 3,
              direction: 'horizontal',
              freeMode: true,
              watchSlidesProgress: true,
              preloadImages: false,
              lazy: {
                loadPrevNext: true,
              },
              navigation: {
                nextEl: ".gallery-slider-thumb .thumb-swiper-button-next",
                prevEl: ".gallery-slider-thumb .thumb-swiper-button-prev",
              },
              breakpoints: {
                480: {
                  slidesPerView: 4,
                },
                768: {
                  slidesPerView: 5,
                },
                992: {
                  slidesPerView: 5,
                  // direction: "vertical",
                },
                1200: {
                  slidesPerView: 5,
                  direction: "vertical",
                  // navigation: {
                  //     nextEl: ".gallery-slider-thumb .thumb-swiper-button-next",
                  //     prevEl: ".gallery-slider-thumb .thumb-swiper-button-prev",
                  // },
                },
              },
            });
            var swiper22 = new Swiper(".gallery-slider-big", {
              loop: false,
              spaceBetween: 10,
              preloadImages: false,
              lazy: {
                loadPrevNext: true,
              },
              navigation: {
                nextEl: ".big-swiper-button-next",
                prevEl: ".big-swiper-button-prev",
              },
              thumbs: {
                swiper: swiper11,
              },
            });


            $('.js-trigger-uploadimg').each(function(){
                let id= $(this).data('id');
                let previewContainer = "#js-preview-file-upload-" + id ;
                $(this).dropzone({
                    url: "http://127.0.0.1:5501/",
                    paramName : "uploadedFiles",
                    autoDiscover : false, 
                    uploadMultiple :false,
                    acceptedFiles : "image/*,video/*,audio/*",
                    forceFallback: false,
                    maxFilesize:1000,
                    parallelUploads: 100,
                    previewsContainer: previewContainer,
                    previewTemplate: `
                    <div class="img-uploaded img-uploaded-small float-left">
                        <img data-dz-thumbnail class="img-fluid lazy"/>
                        <div class="img-uploaded-actions">
                            <a href="javascript:;" class="action-del img-actions" data-dz-remove><img src="images/ic-close.png" alt="" class="lazy img-fluid" /></a>
                        </div>
                    </div>
                    ` ,
                    // sending : function(file,response){
                    //     console.log("File đang được upload");
                    // },
                    // success : function(file,response){
                    //     // push img vao [] de gui sau khi cap nhat
                    // },
                    // complete : function(file,response){
                    //     // show anh tu template 
                    // },
                    // removedfile : function(file,response) {
                    //     // remove file tren serve
                    // }
                });

                // success

                // complete

                //remove files

                //
            })

          });
        });
      },
    });
  </script>
</body>

</html>