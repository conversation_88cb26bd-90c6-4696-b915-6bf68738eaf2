<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="shortcut icon" async href="images/common/favicon.png" />
    <title>Website Bảo Tín Mạnh Hải</title>
    <link rel="stylesheet" href="libs/bootstrap/bootstrap.min.css" />
    <link rel="stylesheet" href="libs/swiper/swiper-bundle.min.css" />
    <link rel="stylesheet" href="libs/fancybox/jquery.fancybox.min.css" />
    <link href="libs/flatpickr/flatpickr.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="libs/aos/aos.css" />
    <link rel="stylesheet" href="libs/fancybox/jquery.fancybox.min.css" />
    <link rel="stylesheet" href="css/style.css" />
  </head>

  <body>
    <div id="app">
      <myheader></myheader>
      <main>
        <div class="promotion-page pt-header">
          <section class="sections-banner-promotion">
            <img src="images/banner-promotion.png" alt="" />
          </section>

          <section class="sections-product-online home-sanpham">
            <div class="sanpham-bl sanpham-noibat mb-0">
              <div class="container">
                <div class="section-title title-w-deco">
                  <h4>Ưu đãi Mua ONLINE tháng 8</h4>
                  <div class="title-deco"></div>
                </div>

                <div class="list-products product-online-swiper position-relative">
                  <div class="swiper overflow-hidden">
                    <div class="swiper-wrapper">
                      <div class="swiper-slide" v-for="n in 3">
                        <div class="sp-item sp-item-border">
                          <div class="sp-item-img">
                            <a href="">
                              <img class="img-fluid lazy" data-src="images/home/<USER>" src="images/gift-img.png" alt="" />
                            </a>
                          </div>
                          <div class="sp-item-body">
                            <a class="name" href="">Lorem, ipsum dolor sit amet consectetur adipisicing elit. Veniam, optio quae.</a>
                            <p class="price">500.000đ <del>1.000.000đ</del></p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="swiper-pagination"></div>
                </div>
              </div>
            </div>
          </section>

          <section class="home-category mb-0">
            <div class="container">
              <div class="section-title title-w-deco mb-3 justify-content-center">
                <h4 class="px-3">Các sản phẩm ưu đãi</h4>
                <div class="title-deco"></div>
              </div>
              <div class="text-center desc">Giảm giá lên đến <strong>30%</strong> các sản phẩm dưới đây</div>

              <div class="category-list">
                <div class="categogy-item" v-for="n in 6">
                  <a href="javascript:;">
                    <img class="img-fluid cate-img lazy" data-src="images/home/<USER>" alt="" src="images/loading-spinner.gif" />
                    <p class="cate-text">NHẪN</p>
                  </a>
                </div>
              </div>
            </div>
          </section>

          <section class="promotion-store">
            <div class="container">
              <div class="head text-center">
                <h3 class="title-section mb-1 text-uppercase">Ưu đãi Từ cửa hàng Bảo tín mạnh hải</h3>
                <div>Xem ngay sản phẩm & Ưu đãi theo cửa hàng</div>
              </div>
              <div class="promotion-store-swiper position-relative">
                <div class="swiper overflow-hidden">
                  <div class="swiper-wrapper">
                    <div class="swiper-slide">
                      <a href=""><img src="images/promotion-store-1.png" alt="" /></a>
                    </div>
                    <div class="swiper-slide">
                      <a href=""><img src="images/promotion-store-2.png" alt="" /></a>
                    </div>
                  </div>
                </div>
                <div class="swiper-button-prev"></div>
                <div class="swiper-button-next"></div>
                <div class="swiper-pagination"></div>
              </div>
            </div>
          </section>

          <section class="promotion-other">
            <div class="container">
              <h3 class="title-section text-center text-uppercase mb-4">Các ưu đãi khác</h3>
              <div class="list">
                <div class="item" v-for="n in 3">
                  <a href="" class="d-block">
                    <div class="image"><img src="images/img-test.jpg" alt="" /></div>
                    <div class="name">Ưu đãi dành cho khách hàng trẻ</div>
                  </a>
                </div>
              </div>
            </div>
          </section>
        </div>
      </main>
      <myfooter></myfooter>
    </div>
    <script type="text/javascript" src="js/vue.js"></script>
    <script type="text/javascript" src="js/httpvueloader.js"></script>
    <script type="text/javascript" src="libs/swiper/swiper-bundle.min.js"></script>
    <script type="text/javascript" src="libs/swiper/swiper-animation.min.js"></script>
    <script type="text/javascript" src="libs/bootstrap/jquery.min.js"></script>
    <script type="text/javascript" src="libs/bootstrap/popper.min.js"></script>
    <script type="text/javascript" src="libs/bootstrap/bootstrap.min.js"></script>
    <script type="text/javascript" src="libs/aos/aos.js"></script>
    <script type="text/javascript" src="libs/chartjs/chart.min.js"></script>
    <script src="libs/chartjs/chartjs-adapter-fns.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@11.0.6/dist/lazyload.min.js"></script>
    <script type="text/javascript" src="libs/fancybox/jquery.fancybox.min.js"></script>
    <script type="text/javascript" src="js/main.js"></script>

    <script type="text/javascript">
      var app = new Vue({
        el: '#app',
        data() {
          return {};
        },
        components: {
          myheader: httpVueLoader('vue_components/header.vue'),
          myfooter: httpVueLoader('vue_components/footer.vue'),
          rednavi: httpVueLoader('vue_components/red-slide-navigation.vue'),
          yellownavi: httpVueLoader('vue_components/yellow-slide-navigation.vue'),
        },
      });

      var swiper = new Swiper('.product-online-swiper .swiper', {
        slidesPerView: 2,
        spaceBetween: 16,
        loop: false,
        watchOverflow : true , 
        pagination: {
          el: '.product-online-swiper .swiper-pagination',
          type: 'progressbar',
        },
        breakpoints: {
          576: {
            slidesPerView: 3,
          },
          768: {
            slidesPerView: 4,
          },
          1024: {
            slidesPerView: 5,
          },
          1200: {
            slidesPerView: 6,
          },
        },
      });
      var swiper = new Swiper('.promotion-store-swiper .swiper', {
        slidesPerView: 2,
        spaceBetween: 16,
        loop: true,
        speed: 1000,
        autoplay: {
          delay: 5000,
        },
        navigation: {
          nextEl: '.promotion-store-swiper .swiper-button-next',
          prevEl: '.promotion-store-swiper .swiper-button-prev',
        },
        pagination: {
          el: '.promotion-store-swiper .swiper-pagination',
          clickable: true,
        },
        breakpoints: {},
      });
    </script>
  </body>
</html>
