<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <link rel="shortcut icon" async href="images/common/favicon.png" />
  <title>Website Bảo Tín Mạnh Hải</title>
  <link rel="stylesheet" href="libs/bootstrap/bootstrap.min.css" />
  <link rel="stylesheet" href="libs/swiper/swiper-bundle.min.css" />
  <link rel="stylesheet" href="libs/fancybox/jquery.fancybox.min.css" />
  <link href="libs/flatpickr/flatpickr.min.css" rel="stylesheet" />
  <link rel="stylesheet" href="libs/aos/aos.css" />
  <link rel="stylesheet" href="libs/fancybox/jquery.fancybox.min.css" />
  <link rel="stylesheet" href="css/style.css" />
</head>

<body>
  <div id="app">
    <myheader></myheader>
    <main>
      <div class="master-wrapper">
        <div class="cart-page">
          <div class="container">
            <div class="cart-page-container cart-page-container-full">

              <div class="block-cart-title mb-4">
                <h2 class="title text-left">
                  Thông tin giỏ hàng
                </h2>
              </div>
              <div class="row">
                <div class="col-lg-6 col-12">
                  <div class="block-cart-content">
                    <div class="incart-list-item">
                      <div class="incart-item">

                        <div class="incart-item-wrap">
                          <a href="javascript:;" class="img">
                            <img src="images/product/trangsuc-daquy.jpg" alt="" class="img-fluid">
                          </a>
                          <div class="info">
                            <a href="javascript:;" class="product-name">
                              Nhẫn vàng phong cách Ý NY10K30
                            </a>
                            <div class="product-sku">
                              <span class="title">
                                Mã sản phẩm : NY10K30
                              </span>
                            </div>
                            <div class="price-box">
                              <div class="product-price">
                                <span class="title">
                                  Giá :
                                </span>
                                <span class="color-theme value">
                                  2.500.000 VNĐ
                                </span>
                              </div>

                              <div class="cart-qty">
                                <span class="new-cart-quantity js-row-item">
                                  <a href="javascript:void(0);" class="minor quantity-change disallowed"
                                    data-value="-1">
                                    <img src="images/ic-minor.png" alt="" width="20" height="20">
                                  </a>
                                  <input class="buy-quantity quantity-change input-number" value="01" size="5"
                                    data-value="1" />
                                  <a href="javascript:void(0);" class="add quantity-change" data-value="1">
                                    <img src="images/ic-plus.png" alt="" width="20" height="20">
                                  </a>
                                </span>
                              </div>
                            </div>
                            <div class="product-config">
                              <div class="box-title">
                                <span class="title">
                                  Chọn kích cỡ
                                </span>
                              </div>
                              <div class="config-list">
                                <a href="javascript:;" class="config-item js-config-item disabled">
                                  09
                                </a>
                                <a href="javascript:;" class="config-item js-config-item">
                                  10
                                </a>
                                <a href="javascript:;" class="config-item js-config-item">
                                  11
                                </a>
                                <a href="javascript:;" class="config-item js-config-item">
                                  12
                                </a>
                                <a href="javascript:;" class="config-item js-config-item">
                                  13
                                </a>
                              </div>
                            </div>
                            <div class="actions">
                              <a href="" class="del-item">
                                <img src="images/ic-trash.png" alt="" width="20" height="20">
                                <span class="txt">
                                  Xóa
                                </span>
                              </a>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="incart-item">

                        <div class="incart-item-wrap">
                          <a href="javascript:;" class="img">
                            <img src="images/product/trangsuc-daquy.jpg" alt="" class="img-fluid">
                          </a>
                          <div class="info">
                            <a href="javascript:;" class="product-name">
                              Nhẫn vàng phong cách Ý NY10K30
                            </a>
                            <div class="product-sku">
                              <span class="title">
                                Mã sản phẩm : NY10K30
                              </span>
                            </div>
                            <div class="price-box">
                              <div class="product-price">
                                <span class="title">
                                  Giá :
                                </span>
                                <span class="color-theme value">
                                  2.500.000 VNĐ
                                </span>
                              </div>

                              <div class="cart-qty">
                                <span class="new-cart-quantity js-row-item">
                                  <a href="javascript:void(0);" class="minor quantity-change disallowed"
                                    data-value="-1">
                                    <img src="images/ic-minor.png" alt="" width="20" height="20">
                                  </a>
                                  <input class="buy-quantity quantity-change input-number" value="01" size="5" />
                                  <a href="javascript:void(0);" class="add quantity-change" data-value="1">
                                    <img src="images/ic-plus.png" alt="" width="20" height="20">
                                  </a>
                                </span>
                              </div>
                            </div>
                            <div class="product-config">
                              <div class="box-title">
                                <span class="title">
                                  Chọn kích cỡ
                                </span>
                              </div>
                              <div class="config-list">
                                <a href="javascript:;" class="config-item js-config-item disabled">
                                  09
                                </a>
                                <a href="javascript:;" class="config-item js-config-item">
                                  10
                                </a>
                                <a href="javascript:;" class="config-item js-config-item">
                                  11
                                </a>
                                <a href="javascript:;" class="config-item js-config-item">
                                  12
                                </a>
                                <a href="javascript:;" class="config-item js-config-item">
                                  13
                                </a>
                              </div>
                            </div>
                            <div class="actions">
                              <a href="" class="del-item">
                                <img src="images/ic-trash.png" alt="" width="20" height="20">
                                <span class="txt">
                                  Xóa
                                </span>
                              </a>
                            </div>
                          </div>
                        </div>

                        <div class="special-offers special-offer-outside">
                          <div class="title">
                            Ưu đãi
                          </div>
                          <div class="special-offer-item">
                            <div class="item">
                              Giảm 30% từ 25/05/2022 đến 15/06/2022
                            </div>
                            <div class="item">
                              Nhận ngay phiếu mua hàng trị giá 200.000đ khi mua từ 2 sản phẩm trở lên từ 25/05/2022 đến
                              15/06/2022
                            </div>
                          </div>
                        </div>

                      </div>

                    </div>



                    <div class="block-cart-voucher">
                      <div class="title">
                        Mã giảm giá
                      </div>
                      <div class="voucher-apply">
                        <div class="cart-voucher">
                          <input id="js_voucher_input" class="form-control" placeholder="Mã giảm giá " size="20" />
                          <button class="btn-theme js-voucher-submit">Áp dụng</button>
                        </div>
                        <span id="js-voucher-message" class="error">
                          Mã không hợp lệ
                        </span>

                      </div>
                    </div>

                    <div class="block-cart-total">
                      <div class="item price">
                        <div class="name">
                          Tạm tính:
                        </div>
                        <div class="value">
                          5.000.000 VNĐ
                        </div>
                      </div>
                      <div class="item discount">
                        <div class="name">
                          Khuyến mại:
                        </div>
                        <div class="value">
                          (30%) - 1.500.000 VNĐ
                        </div>
                      </div>
                      <div class="item shipping">
                        <div class="name">
                          Vận chuyển:
                        </div>
                        <div class="value">
                          0 VNĐ
                        </div>
                      </div>
                      <div class="item cart-total">
                        <div class="name">
                          Tổng cộng:
                        </div>
                        <div class="value">
                          3.550.000 VNĐ
                        </div>
                      </div>
                    </div>

                  </div>


                </div>
                <div class="col-lg-6 col-12">
                  <div class="customer-card">
                    <div class="customer-card-main">
                      <div class="customer-card-info">
                        <div class="title">
                          Thông tin khách hàng
                        </div>
                        <div class="cart-order-note">
                          <div class="d-flex align-items-center info ">
                            <div class="name">
                              Họ và tên:
                            </div>
                            <div class="value">
                              Hoàng Văn Nhất
                            </div>
                          </div>
                          <div class="d-flex align-items-center info ">
                            <div class="name">
                              Số điện thoại:
                            </div>
                            <div class="value">
                              0987654321
                            </div>
                          </div>
                          <div class="d-flex align-items-center info ">
                            <div class="name">
                              Địa chỉ nhận hàng:
                            </div>
                            <div class="value">
                              Số 20 Đội Cấn, quận Ba Đình, Hà Nội Số 20 Đội Cấn, quận Ba Đình, Hà Nội Số 20 Đội Cấn,
                              quận Ba Đình, Hà Nội Số 20 Đội Cấn, quận Ba Đình, Hà Nội
                            </div>
                          </div>
                          <div class="d-flex align-items-center info ">
                            <div class="name">
                              Dự kiến nhận hàng:
                            </div>
                            <div class="value">
                              08/08/2022 - 09/08/2022
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="customer-card-info">
                        <div class="title">
                          Hình thức thanh toán
                        </div>
                        <div class="payment-list">
                          <a class="payment-item js-show-payment active" href="javascript:;">
                            <div class="wrap">
                              <div class="name">
                                Thanh toán khi nhận hàng
                              </div>
                            </div>
                          </a>
                          <a class="payment-item js-show-payment" href="javascript:;">
                            <div class="wrap">
                              <div class="name">
                                Thanh toán bằng thẻ quốc tế
                              </div>
                              <div class="desc">
                                <img src="images/visa.png" alt="" class="img-fluid">
                              </div>
                            </div>
                          </a>
                          <a class="payment-item js-show-payment" href="javascript:;" data-target="#bank-list">
                            <div class="wrap">
                              <div class="name">
                                Thanh toán chuyển tiền
                              </div>
                            </div>
                          </a>
                          <a class="payment-item js-show-payment" href="javascript:;">
                            <div class="wrap">
                              <div class="name">
                                Thanh toán bằng thẻ nội địa
                              </div>
                            </div>
                          </a>
                          <a class="payment-item js-show-payment" href="javascript:;" data-target="#installment-list">
                            <div class="wrap">
                              <div class="name">
                                Trả góp qua thẻ
                              </div>
                              <span class="small text-center">
                                Trả góp 0% qua thẻ tín dụng</span>
                            </div>
                          </a>
                        </div>

                        <div id="installment-list" style="display: none;">
                          <ul class="installment-desc list-unstyled p-0 m-0 ">
                            <li>
                              Theo yêu cầu từ ngân hàng, quý khách cần kiểm tra kỹ hạn mức còn lại của thẻ tín dụng, hạn
                              mức của thẻ phải lớn hơn giá trị hóa đơn.
                            </li>
                            <li>
                              Trường hợp giao dịch trả góp không thành công do vượt quá hạn mức, Bảo Tín Mạnh Hải sẽ
                              không hoàn tiền phí chuyển đổi trả góp.
                            </li>
                            <li>
                              Trường hợp giao dịch trả góp ghi nhận thành công trên website, Bảo Tín Mạnh Hải sẽ không
                              hỗ trợ hủy giao dịch.
                            </li>
                          </ul>
                          <div class="installment-step">
                            <div class="installment-step-title">
                              Bước 1: Chọn ngân hàng
                            </div>
                            <div class="installment-step-bank-list">
                              <label for="installment-bank-1">
                                <input type="radio" name="installment-bank" value="bank1" id="installment-bank-1">
                                <div class="item">
                                  <img src="images/bank/tpbank.jpg" alt="">
                                </div>
                              </label>
                              <label for="installment-bank-2">
                                <input type="radio" name="installment-bank" value="bank2" id="installment-bank-2">
                                <div class="item">
                                  <img src="images/bank/vcbank.jpg" alt="">
                                </div>
                              </label>
                              <label for="installment-bank-3">
                                <input type="radio" name="installment-bank" value="bank3" id="installment-bank-3">
                                <div class="item">
                                  <img src="images/bank/vpbank.jpg" alt="">
                                </div>
                              </label>


                            </div>
                          </div>
                          <div class="installment-step">
                            <div class="installment-step-title">
                              Bước 2: Chọn kỳ hạn (VNĐ)
                            </div>
                            <div class="mb-table">
                              <div class="table-main table__credit">
                                <ul class="table-main-line">
                                  <li>Số tháng trả góp</li>
                                  <li>3 tháng</li>
                                  <li>6 tháng</li>
                                  <li>9 tháng</li>
                                  <li>12 tháng</li>
                                </ul>
                                <ul class="table-main-line">
                                  <li>Giá mua trả góp</li>
                                  <li>3.550.000 đ</li>
                                  <li>3.550.000 đ</li>
                                  <li>3.550.000 đ</li>
                                  <li>3.550.000 đ</li>
                                </ul>
                                <ul class="table-main-line">
                                  <li>Góp mỗi tháng</li>
                                  <li>1.200.000 đ</li>
                                  <li>1.200.000 đ</li>
                                  <li>1.200.000 đ</li>
                                  <li>1.200.000 đ</li>
                                </ul>
                                <ul class="table-main-line">
                                  <li>Tổng tiền phải trả</li>
                                  <li>3.700.000 đ</li>
                                  <li>3.700.000 đ</li>
                                  <li>3.700.000 đ</li>
                                  <li>3.700.000 đ</li>
                                </ul>
                                <ul class="table-main-line">
                                  <li>Chênh lệch so với trả thẳng</li>
                                  <li>279.000 đ</li>
                                  <li>279.000 đ</li>
                                  <li>279.000 đ</li>
                                  <li>279.000 đ</li>
                                </ul>
                                <ul class="table-main-line">
                                  <li>Chọn kỳ hạn trả góp</li>
                                  <li>
                                    <input id="installment-plan-1" type="radio" name="installment-plan" value="3 thang">
                                    <label for="installment-plan-1" class="installment-step-button">
                                      <span>
                                        Chọn
                                      </span>
                                    </label>
                                    <!-- <a href="javascript:;"
                                      class="js-checkecd-plan-installment installment-step-button checked">
                                      <span>
                                        Chọn
                                      </span>
                                    </a> -->
                                  </li>
                                  <li>
                                    <input id="installment-plan-2" type="radio" name="installment-plan" value="6 thang">
                                    <label for="installment-plan-2" class="installment-step-button">
                                      <span>
                                        Chọn
                                      </span>
                                    </label>
                                    <!-- <a href="javascript:;" class="js-checkecd-plan-installment installment-step-button">
                                      <span>
                                        Chọn
                                      </span>
                                    </a> -->
                                  </li>
                                  <li>
                                    <input id="installment-plan-3" type="radio" name="installment-plan" value="9 thang">
                                    <label for="installment-plan-3" class="installment-step-button">
                                      <span>
                                        Chọn
                                      </span>
                                    </label>
                                    <!-- <a href="javascript:;" class="js-checkecd-plan-installment installment-step-button">
                                      <span>
                                        Chọn
                                      </span>
                                    </a> -->
                                  </li>
                                  <li>
                                    <input id="installment-plan-4" type="radio" name="installment-plan"
                                      value="12 thang">
                                    <label for="installment-plan-4" class="installment-step-button">
                                      <span>
                                        Chọn
                                      </span>
                                    </label>
                                    <!-- <a href="javascript:;" class="js-checkecd-plan-installment installment-step-button">
                                      <span>
                                        Chọn
                                      </span>
                                    </a> -->
                                  </li>
                                </ul>
                              </div>
                            </div>

                          </div>

                          <p class="mb-0 text-left">
                            <button class="btn-theme" type="button" onclick="goBackInstallment()">
                              <span class="txt">
                                Quay lại
                              </span>
                            </button>
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>


                  <div class="payment-type-content" id="bank-list">
                    <div class="wrap">
                      <div class="title">
                        Hướng dẫn chuyển khoản
                      </div>
                      <p class="desc">
                        Chuyển khoản theo thông tin tài khoản dưới đây với nội dung: “BTMH<dấu cách>Tên người mua<dấu
                            cách>Tên sản phẩm” và nhấn <strong class="color-theme font-weight-semibold">
                              “Đặt hàng”
                            </strong> để hoàn tất đặt hàng
                      </p>
                      <div class="list-bank-info">
                        <div class="item">
                          <div class="img">
                            <img src="images/tcb.png" alt="" class="img-fluid" width="48" height="48">
                          </div>
                          <div class="info">
                            <div class="name">
                              Ngân hàng Techcombank
                            </div>
                            <div class="d-flex align-items-center mb-2 color-606368">
                              Chủ tài khoản:
                              <span class="font-weight-semibold">
                                Bảo Tín Mạnh Hải
                              </span>
                            </div>
                            <div class="d-flex align-items-center mb-2 color-606368">
                              Số tài khoản:
                              <span class="font-weight-semibold">
                                0123 456 789000
                              </span>
                            </div>
                          </div>
                        </div>
                        <div class="item">
                          <div class="img">
                            <img src="images/vpb.png" alt="" class="img-fluid" width="48" height="48">
                          </div>
                          <div class="info">
                            <div class="name">
                              Ngân hàng Vpbank
                            </div>
                            <div class="d-flex align-items-center mb-2 color-606368">
                              Chủ tài khoản:
                              <span class="font-weight-semibold">
                                Bảo Tín Mạnh Hải
                              </span>
                            </div>
                            <div class="d-flex align-items-center mb-2 color-606368">
                              Số tài khoản:
                              <span class="font-weight-semibold">
                                0123 456 789000
                              </span>
                            </div>
                          </div>
                        </div>
                        <div class="item">
                          <div class="img">
                            <img src="images/vietinbank.png" alt="" class="img-fluid" width="48" height="48">
                          </div>
                          <div class="info">
                            <div class="name">
                              Ngân hàng Vietinbank
                            </div>
                            <div class="d-flex align-items-center mb-2 color-606368">
                              Chủ tài khoản:
                              <span class="font-weight-semibold">
                                Bảo Tín Mạnh Hải
                              </span>
                            </div>
                            <div class="d-flex align-items-center mb-2 color-606368">
                              Số tài khoản:
                              <span class="font-weight-semibold">
                                0123 456 789000
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>


                  <div class="submit-cart">

                    <button class="btn-theme" type="submit">
                      <span class="txt">
                        Tiếp theo
                      </span>
                    </button>
                  </div>
                </div>
              </div>


            </div>
          </div>
        </div>
      </div>
    </main>
    <myfooter></myfooter>
  </div>

  <script type="text/javascript" src="js/vue.js"></script>
  <script type="text/javascript" src="js/httpvueloader.js"></script>
  <script type="text/javascript" src="libs/swiper/swiper-bundle.min.js"></script>
  <script type="text/javascript" src="libs/swiper/swiper-animation.min.js"></script>
  <script type="text/javascript" src="libs/bootstrap/jquery.min.js"></script>
  <script type="text/javascript" src="libs/bootstrap/popper.min.js"></script>
  <script type="text/javascript" src="libs/bootstrap/bootstrap.min.js"></script>
  <script type="text/javascript" src="libs/aos/aos.js"></script>
  <script type="text/javascript" src="libs/chartjs/chart.min.js"></script>
  <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.27.0/moment.min.js" integrity="sha512-rmZcZsyhe0/MAjquhTgiUcb4d9knaFc7b5xAfju483gbEXTkeJRUMIPk6s3ySZMYUHEcjKbjLjyddGWMrNEvZg==" crossorigin="anonymous"></script> -->
  <script src="libs/chartjs/chartjs-adapter-fns.min.js"></script>

  <script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@11.0.6/dist/lazyload.min.js"></script>
  <script type="text/javascript" src="libs/fancybox/jquery.fancybox.min.js"></script>
  <script type="text/javascript" src="js/main.js"></script>

  <script type="text/javascript">
    var app = new Vue({
      el: '#app',
      data() {
        return {
          flashSaleEndTime: 80000,
          activeItem: 'tab-1',
        };
      },
      components: {
        myheader: httpVueLoader('vue_components/header.vue'),
        myfooter: httpVueLoader('vue_components/footer.vue'),
        rednavi: httpVueLoader('vue_components/red-slide-navigation.vue'),
        yellownavi: httpVueLoader('vue_components/yellow-slide-navigation.vue'),
      },
      methods: {
        isActive(menuItem) {
          return this.activeItem === menuItem;
        },
        setActive(menuItem) {
          this.activeItem = menuItem;
        },
      },
      mounted() {
        this.$nextTick(() => {
          $(window).bind('load', function () {
            if (LazyLoad) {
              var lazyloader = new LazyLoad({
                elements: '.lazy',
              });
            }

            AOS.init({
              // startEvent: 'load',
              duration: 700,
              easing: 'ease',
            });

            qtyChange('.quantity-change');

            // click show payment
            $('.js-show-payment').on('click', function (e) {
              e.preventDefault;
              let target = $(this).data('target');

              if (target === '#installment-list') {
                $('.payment-list').hide();
                $(target).show();
              }
              $('.payment-item.active').removeClass('active');
              $(this).addClass('active');
              $('.payment-type-content.active').removeClass('active');
              if (!target) return;

              // hide other tab

              $(target).addClass('active');


            })
          });
        });
      },
    });
  </script>
</body>

</html>