<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <link rel="shortcut icon" async href="images/common/favicon.png" />
  <title>Website Bảo Tín Mạnh Hải</title>
  <link rel="stylesheet" href="libs/bootstrap/bootstrap.min.css" />
  <link rel="stylesheet" href="libs/swiper/swiper-bundle.min.css" />
  <link rel="stylesheet" href="libs/fancybox/jquery.fancybox.min.css" />
  <link rel="stylesheet" href="libs/flatpickr/flatpickr.min.css" />
  <link rel="stylesheet" href="libs/aos/aos.css" />
  <link rel="stylesheet" href="libs/fancybox/jquery.fancybox.min.css" />
  <link rel="stylesheet" href="css/style.css" />
</head>

<body>
  <div id="app">
    <myheader></myheader>
    <main>
      <div class="master-wrapper">
        <div class="market-page">

          <div class="d-block text-center">
            <div class="container">
              <h2 class="market-title mb-lg-5 mb-4">
                HỆ THỐNG 07 CỬA HÀNG TẠI HÀ NỘI
              </h2>
            </div>
          </div>

          <div class="market-wrapper">

            <div class="market-address">
              <ul>
                <li v-for="(item,index) in markets" :key="index">
                  <a :href="'#' + item.name.replace(/\s/g, '').toLowerCase()" :title="item.name">
                    <span v-html="item.name"></span>
                    <span v-html="item.address"></span>
                  </a>
                </li>
              </ul>
            </div>

            <div class="market-this-address">
              <ul :id="item.name.replace(/\s/g, '').toLowerCase() + '-info'" v-for="(item,index) in markets"
                :key="index">
                <li v-for="(item,index) in markets.slice(0,3)" :key="index">
                  <div class="icon">
                    <img :src="'images/ic-market-' + (index + 1) + '.png'" alt="">
                  </div>

                  <div class="info">
                    <h3 class="title" v-html="item.name"></h3>
                    <span class="adress" v-html="item.address"></span>
                  </div>
                </li>
              </ul>
            </div>

            <div class="market-maps">
              <div :id="item.name.replace(/\s/g, '').toLowerCase() + '-map'" class="map" v-for="(item,index) in markets"
                :key="index">
                <iframe
                  src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3724.8247664869377!2d105.80549261540193!3d20.99966029414589!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3135ac9788960205%3A0xe4f831d0c56eb989!2sVCCORP!5e0!3m2!1svi!2s!4v1662721004930!5m2!1svi!2s"
                  width="100%" height="715" style="border:0;" allowfullscreen="" loading="lazy"
                  referrerpolicy="no-referrer-when-downgrade"></iframe>
              </div>
            </div>

          </div>

        </div>
      </div>
    </main>
    <myfooter></myfooter>
  </div>

  <script type="text/javascript" src="js/vue.js"></script>
  <script type="text/javascript" src="js/httpvueloader.js"></script>
  <script type="text/javascript" src="libs/swiper/swiper-bundle.min.js"></script>
  <script type="text/javascript" src="libs/bootstrap/jquery.min.js"></script>
  <script type="text/javascript" src="libs/bootstrap/popper.min.js"></script>
  <script type="text/javascript" src="libs/bootstrap/bootstrap.min.js"></script>
  <script type="text/javascript" src="libs/aos/aos.js"></script>
  <script type="text/javascript" src="libs/chartjs/chart.min.js"></script>
  <script type="text/javascript" src="libs/fancybox/jquery.fancybox.min.js"></script>
  <script type="text/javascript" src="js/main.js"></script>

  <script type="text/javascript">
    var app = new Vue({
      el: '#app',
      data() {
        return {
          markets: [
            {
              name: 'Trụ sở chính',
              address: '16 Trần Duy Hưng, Cầu Giấy - Hà Nội.'
            },
            {
              name: 'Cơ sở 1',
              address: '39 Nguyễn Trãi, Thanh Xuân - Hà Nội.'
            },
            {
              name: 'Cơ sở 2',
              address: '15 Quang Trung, Hà Đông - Hà Nội.'
            },
            {
              name: 'Cơ sở 3',
              address: '16 Trần Duy Hưng, Cầu Giấy - Hà Nội.'
            },
            {
              name: 'Cơ sở 4',
              address: '08 Hoàng Cầu, Đống Đa - Hà Nội.08 Hoàng Cầu, Đống Đa - Hà Nội.08 Hoàng Cầu, Đống Đa - Hà Nội.'
            },
            {
              name: 'Cơ sở 2',
              address: '15 Quang Trung, Hà Đông - Hà Nội.08 Hoàng Cầu, Đống Đa - Hà Nội.08 Hoàng Cầu, Đống Đa - Hà Nội.'
            },
            {
              name: 'Cơ sở 3',
              address: '16 Trần Duy Hưng, Cầu Giấy - Hà Nội.08 Hoàng Cầu, Đống Đa - Hà Nội.08 Hoàng Cầu, Đống Đa - Hà Nội.'
            },
            {
              name: 'Cơ sở 4',
              address: '08 Hoàng Cầu, Đống Đa - Hà Nội.08 Hoàng Cầu, Đống Đa - Hà Nội.08 Hoàng Cầu, Đống Đa - Hà Nội.'
            },
          ]
        };
      },
      components: {
        myheader: httpVueLoader('vue_components/header.vue'),
        myfooter: httpVueLoader('vue_components/footer.vue'),
        rednavi: httpVueLoader('vue_components/red-slide-navigation.vue'),
        yellownavi: httpVueLoader('vue_components/yellow-slide-navigation.vue'),
      },
      mounted() {
        window.onload = () => {
          let itemClick = document.querySelectorAll('.market-address a');
          let itemMaps = document.querySelectorAll('.market-maps .map');
          let itemInfo = document.querySelectorAll('.market-this-address ul');

          for (i = 0; i < itemClick.length; i++) {
            itemClick[0].classList.add('active');
            itemMaps[0].classList.add('active');
            itemInfo[0].classList.add('active');

            itemClick[i].addEventListener('click', (e) => {
              e.preventDefault();
              let targetClicked = e.currentTarget;


              for (click of itemClick) {
                click.classList.remove('active');
              }

              targetClicked.classList.add('active');

              let hrefID = targetClicked.getAttribute('href');
              let mapsID = document.querySelector(hrefID + '-map');
              let infoID = document.querySelector(hrefID + '-info');

              for (info of itemInfo) {
                info.classList.remove('active');
              }

              for (map of itemMaps) {
                map.classList.remove('active');
              }

              mapsID.classList.add('active');
              infoID.classList.add('active');
            });
          }
        }
      },
    });
  </script>
</body>

</html>