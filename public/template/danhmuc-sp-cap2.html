<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <link rel="shortcut icon" async href="images/common/favicon.png" />
  <title>Website Bảo Tín Mạnh Hải</title>
  <link rel="stylesheet" href="libs/bootstrap/bootstrap.min.css" />
  <link rel="stylesheet" href="libs/swiper/swiper-bundle.min.css" />
  <link rel="stylesheet" href="libs/fancybox/jquery.fancybox.min.css" />
  <link href="libs/flatpickr/flatpickr.min.css" rel="stylesheet" />
  <link rel="stylesheet" href="libs/aos/aos.css" />
  <link rel="stylesheet" href="libs/fancybox/jquery.fancybox.min.css" />
  <link rel="stylesheet" href="libs/jqueryui/jquery-ui.css" />
  <link rel="stylesheet" href="css/style.css" />
</head>

<body>
  <div id="app">
    <myheader></myheader>
    <main>
      <div class="master-wrapper">

        <div class="productlist-page">

          <div class="banner-product-list">
            <div class="container">
              <a href="javascript:;">
                <img src="images/product/banner-product-list.jpg" alt="" class="img-fluid">
              </a>
            </div>
          </div>

          <!-- bộ lọc -->
          <filterbox></filterbox>
          <!-- end bộ lọc-->



          <div class="productlist-page-wrap">
            <div class="container">
              <div class="list-products">
                <div class="sp-item sp-item-border" v-for="n in 20">
                  <div class="sp-item-img">
                    <a href="javascript:;">
                      <img class="img-fluid" src="images/home/<USER>" alt="">
                    </a>
                  </div>
                  <div class="sp-item-body">
                    <a href="javascript:;">Nhẫn gắn đá màu MS05</a>
                    <p class="price">500.000đ</p>
                  </div>
                </div>
              </div>

              <!-- pagination -->

              <pagination></pagination>

              <!-- end pagination -->
            </div>
          </div>

        </div>

      </div>
    </main>
    <myfooter></myfooter>
  </div>

  <script type="text/javascript" src="js/vue.js"></script>
  <script type="text/javascript" src="js/httpvueloader.js"></script>
  <script type="text/javascript" src="libs/swiper/swiper-bundle.min.js"></script>
  <script type="text/javascript" src="libs/swiper/swiper-animation.min.js"></script>
  <script type="text/javascript" src="libs/bootstrap/jquery.min.js"></script>
  <script type="text/javascript" src="libs/bootstrap/popper.min.js"></script>
  <script type="text/javascript" src="libs/bootstrap/bootstrap.min.js"></script>
  <script type="text/javascript" src="libs/aos/aos.js"></script>
  <script type="text/javascript" src="libs/chartjs/chart.min.js"></script>
  <script type="text/javascript" src="libs/fancybox/jquery.fancybox.min.js"></script>
  <script type="text/javascript" src="libs/jqueryui/jquery-ui.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@11.0.6/dist/lazyload.min.js"></script>
  <script type="text/javascript" src="js/main.js"></script>

  <script type="text/javascript">
    var app = new Vue({
      el: "#app",
      data() {
        return {
          flashSaleEndTime: 80000,
          activeItem: 'tab-1',
        };
      },
      components: {
        myheader: httpVueLoader("vue_components/header.vue"),
        myfooter: httpVueLoader("vue_components/footer.vue"),
        rednavi: httpVueLoader("vue_components/red-slide-navigation.vue"),
        yellownavi: httpVueLoader("vue_components/yellow-slide-navigation.vue"),
        filterbox: httpVueLoader("vue_components/filterbox.vue"),
        pagination: httpVueLoader("vue_components/pagination.vue"),
      },
      methods: {
        isActive(menuItem) {
          return this.activeItem === menuItem
        },
        setActive(menuItem) {
          this.activeItem = menuItem
        },
      },
      mounted() {
        this.$nextTick(() => {
          $(window).bind("load", function () {

            if (LazyLoad) {
              var lazyloader = new LazyLoad({
                elements: ".lazy"
              });
            }

            AOS.init({
              // startEvent: 'load',
              duration: 700,
              easing: "ease",
            });

            var bannernoibat = new Swiper(".sanpham-slide", {
              slidesPerView: 1,
              spaceBetween: 15,
              speed: 1000,
              loop: false,
              preloadImages: false,
              observer: true,
              observeParents: true,
              lazy: {
                loadPrevNext: true,
              },
              pagination: {
                el: ".swiper-pagination",
              },
              navigation: {
                nextEl: ".custom-button-next",
                prevEl: ".custom-button-prev",
              },
              breakpoints: {
                576: {
                  slidesPerView: 2,
                  spaceBetween: 15,
                },
                992: {
                  slidesPerView: 3,
                  spaceBetween: 20,
                },
                1200: {
                  slidesPerView: 4,
                  spaceBetween: 29,
                },
              },
            });

            // slider range
            $('#slider-range').slider({
              range: true,
              step: 100000,
              min: 0,
              max: 100000000,
              values: [0, 100000000],
              slide: function (event, ui) {
                $('#amount-from').text(formatNumber(ui.values[0]) + ' Đ');
                $('#amount-to').text(formatNumber(ui.values[1]) + ' Đ');
                $('#counter-from').val(formatNumber(ui.values[0]) + ' Đ');
                $('#counter-to').val(formatNumber(ui.values[1]) + ' Đ');
              }
            });
            $('#amount-from').text(formatNumber($('#slider-range').slider('values', 0)) + ' Đ');
            $('#amount-to').text(formatNumber($('#slider-range').slider('values', 1)) + ' Đ');
            $('#counter-from').val(formatNumber($('#slider-range').slider('values', 0)) + ' Đ');
            $('#counter-to').val(formatNumber($('#slider-range').slider('values', 1)) + ' Đ');

          });
        });
      },
    });
  </script>
</body>

</html>