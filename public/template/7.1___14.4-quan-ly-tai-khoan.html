<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="shortcut icon" async href="images/common/favicon.png" />
    <title>Website Bảo Tín Mạnh Hải</title>
    <link rel="stylesheet" href="libs/bootstrap/bootstrap.min.css" />
    <link rel="stylesheet" href="libs/swiper/swiper-bundle.min.css" />
    <link rel="stylesheet" href="libs/fancybox/jquery.fancybox.min.css" />
    <link rel="stylesheet" href="libs/flatpickr/flatpickr.min.css" />
    <link rel="stylesheet" href="libs/aos/aos.css" />
    <link rel="stylesheet" href="libs/fancybox/jquery.fancybox.min.css" />
    <link rel="stylesheet" href="css/style.css" />
  </head>

  <body>
    <div id="app">
      <myheader></myheader>
      <main>
        <div class="orders-page">
          <div class="container">
            <div class="account-management">
              <h2 class="title-section text-center fz-24">Quản lý tài khoản</h2>
              <!-- Nav tabs -->
              <ul class="nav nav-tabs border-0">
                <li class="nav-item m-0">
                  <a class="nav-link active" data-toggle="tab" href="#profile">Thông tin cá nhân và địa chỉ</a>
                </li>
                <li class="nav-item m-0">
                  <a class="nav-link" data-toggle="tab" href="#listOrder">Danh sách đơn hàng đã mua</a>
                </li>
              </ul>

              <!-- Tab panes -->
              <div class="tab-content">
                <div class="tab-pane" id="profile">
                  <h4>Thông tin cá nhân</h4>
                  <form action="" class="form-profile">
                    <div class="form-group">
                      <input type="text" name="" id="" class="form-control" placeholder="Nhập họ và tên" aria-describedby="helpId" />
                      <small id="helpId" class="text-danger">Help text</small>
                    </div>
                    <div class="form-group">
                      <input type="text" name="" id="" class="form-control" placeholder="Nhập số điện thoại" aria-describedby="helpId" />
                      <small id="helpId" class="text-danger">Help text</small>
                    </div>
                    <div class="form-group">
                      <input type="text" name="" id="" class="form-control" placeholder="Nhập email" aria-describedby="helpId" />
                      <small id="helpId" class="text-danger">Help text</small>
                    </div>

                    <h4 class="mt-4">Thông tin nhận hàng</h4>
                    <div class="form-group-address">
                      <select class="form-control" name="" id="">
                        <option selected disabled style="color: gray">Thành phố</option>
                        <option>1</option>
                        <option>2</option>
                      </select>
                      <select class="form-control" name="" id="">
                        <option selected disabled>Quận/Huyện</option>
                        <option>1</option>
                        <option>2</option>
                      </select>
                      <select class="form-control" name="" id="">
                        <option selected disabled>Phường/Xã</option>
                        <option>1</option>
                        <option>2</option>
                      </select>
                      <input type="text" class="form-control" placeholder="Địa chỉ" />
                    </div>

                    <div class="text-right mt-3">
                      <a href="" class="btn-update">Cập nhật</a>
                    </div>
                  </form>

                  <div class="bottom">
                    <a href="">Đổi mật khẩu</a>
                    <a href="">Thoát tài khoản</a>
                  </div>
                </div>
                <div class="tab-pane active" id="listOrder">
                  <div class="name-user">Xin chào anh <strong>Hoàng Văn Nhất - 0987654321</strong></div>
                  <h4 class="title-section">Đơn hàng đã mua gần đây</h4>

                  <div class="table-orders">
                    <div class="table-responsive">
                      <table class="table">
                        <thead>
                          <tr>
                            <th>Mã đơn hàng</th>
                            <th>Sản phẩm</th>
                            <th>Giá</th>
                            <th>Ngày đặt mua</th>
                            <th>Trạng thái</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr v-for="n in 4">
                            <td>
                              <div class="code">#12345678</div>
                            </td>
                            <td>
                              <div class="product mb-2" v-for="n in 3">
                                <div class="image">
                                  <a href=""><img src="images/product-img.jpg" alt="" /></a>
                                </div>
                                <div class="content">
                                  <div><a href="" class="name">Nhẫn vàng phong cách Ý</a></div>
                                  <div><a href="" class="view-link">Xem chi tiết</a></div>
                                </div>
                              </div>

                              <a href="" class="view-product">Xem thêm 2 sản phẩm</a>
                            </td>
                            <td>
                              <div class="price">30.750.000 VNĐ</div>
                            </td>
                            <td>
                              <div class="date">07/05/2022</div>
                            </td>
                            <td>
                              <div class="status">
                                <div class="status-done">Đã nhận hàng</div>
                                <div class="status-processing">Đã nhận hàng</div>
                                <div class="status-cancle">Đã nhận hàng</div>
                              </div>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <supports></supports>
        </div>
      </main>
      <myfooter></myfooter>
    </div>

    <script type="text/javascript" src="js/vue.js"></script>
    <script type="text/javascript" src="js/httpvueloader.js"></script>
    <script type="text/javascript" src="libs/swiper/swiper-bundle.min.js"></script>
    <script type="text/javascript" src="libs/bootstrap/jquery.min.js"></script>
    <script type="text/javascript" src="libs/bootstrap/popper.min.js"></script>
    <script type="text/javascript" src="libs/bootstrap/bootstrap.min.js"></script>
    <script type="text/javascript" src="libs/aos/aos.js"></script>
    <script type="text/javascript" src="libs/chartjs/chart.min.js"></script>
    <script type="text/javascript" src="libs/fancybox/jquery.fancybox.min.js"></script>
    <script type="text/javascript" src="js/main.js"></script>

    <script type="text/javascript">
      var app = new Vue({
        el: '#app',
        data() {
          return {};
        },
        components: {
          myheader: httpVueLoader('vue_components/header.vue'),
          myfooter: httpVueLoader('vue_components/footer.vue'),
          supports: httpVueLoader('vue_components/supports.vue'),
          rednavi: httpVueLoader('vue_components/red-slide-navigation.vue'),
          yellownavi: httpVueLoader('vue_components/yellow-slide-navigation.vue'),
        },
      });
    </script>
  </body>
</html>
