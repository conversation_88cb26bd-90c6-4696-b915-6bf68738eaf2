<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <link rel="shortcut icon" async href="images/common/favicon.png" />
  <title>Website Bảo Tín Mạnh Hải</title>
  <link rel="stylesheet" href="libs/bootstrap/bootstrap.min.css" />
  <link rel="stylesheet" href="libs/swiper/swiper-bundle.min.css" />
  <link rel="stylesheet" href="libs/fancybox/jquery.fancybox.min.css" />
  <link rel="stylesheet" href="libs/flatpickr/flatpickr.min.css" />
  <link rel="stylesheet" href="libs/aos/aos.css" />
  <link rel="stylesheet" href="libs/fancybox/jquery.fancybox.min.css" />
  <link rel="stylesheet" href="css/style.css" />
</head>

<body>
  <div id="app">
    <myheader></myheader>
    <main>
      <div class="master-wrapper">
        <div class="shareholder-relation--page">
          <div class="container">
            <div class="d-block text-center mb-lg-5 mb-4">
              <h2 class="shareholder-relation--title">
                Quan hệ cổ đông
              </h2>
            </div>

            <div class="shareholder-relation--tabs">
              <ul class="category-link">
                <li v-for="(item,index) in tabList" :key="index">
                  <a href="javascript:;" :title="item" :class="{ 'active' : index === 0 }" v-html="item"></a>
                </li>
              </ul>

              <div class="category-content">
                <div class="row">
                  <div class="col-lg-4 col-12 mb-4 mb-lg-0">

                    <ul id="articleTabItem" class="link-article-item">
                      <li v-for="(item,index) in tabList.slice(0, 4)" :key="index">
                        <a href="javascript:;" :title="item" :class="{ 'active' : index === 0 }">
                          <span v-html="item"></span>
                        </a>
                      </li>
                    </ul>

                  </div>

                  <div class="col-lg-8 col-12">

                        <div class="shareholder-content-detail">
                            <div class="back-link">
                                <a href="javascript:;">
                                    <img src="images/arrow-left.png" alt="" class="img-fluid" width="16" height="16">
                                    Trở lại
                                </a>
                            </div>
                            <div class="info">
                                <h3 class="title">
                                    ĐHĐCĐ thường niên năm 2022 và dự thảo tài liệu đại hội
                                </h3>
                                <span class="created-at">
                                    26/04/2022 16:24
                                </span>
                            </div>
                            <div class="nd-content">
                                <p>
                                    Công ty Cổ phần Bảo Tín Mạnh Hải kính gửi quý cổ đông chương trình và tài liệu họp ĐHĐCĐ thường niên năm 2022
                                </p>
                                <p>
                                    <a href="">
                                        Chương trình cuộc họp
                                    </a>
                                </p>
                                <p>
                                    <a href="">
                                        Quy chế đại hội
                                    </a>
                                </p>
                                <p>
                                    <a href="">
                                        Nguyên tắc biểu quyết
                                    </a>
                                </p>
                                <p>
                                    <a href="">
                                        Quy chế bầu HĐQT, BKS
                                    </a>
                                </p>
                            </div>

                            <div class="navigator">
                                <a class=" item">
                                    <img src="images/common/ic-prev.png" alt="" class="img-fluid">
                                    Trang trước
                                </a>
                                <a class=" item">
                                    Trang sau
                                    <img src="images/common/ic-next.png" alt="" class="img-fluid">
                                </a>
                            </div>
                        </div>

                  </div>
                </div>
              </div>

            </div>

          </div>
        </div>
      </div>
    </main>
    <myfooter></myfooter>
  </div>

  <script type="text/javascript" src="js/vue.js"></script>
  <script type="text/javascript" src="js/httpvueloader.js"></script>
  <script type="text/javascript" src="libs/swiper/swiper-bundle.min.js"></script>
  <script type="text/javascript" src="libs/bootstrap/jquery.min.js"></script>
  <script type="text/javascript" src="libs/bootstrap/popper.min.js"></script>
  <script type="text/javascript" src="libs/bootstrap/bootstrap.min.js"></script>
  <script type="text/javascript" src="libs/aos/aos.js"></script>
  <script type="text/javascript" src="libs/chartjs/chart.min.js"></script>
  <script type="text/javascript" src="libs/fancybox/jquery.fancybox.min.js"></script>
  <script type="text/javascript" src="js/main.js"></script>

  <script type="text/javascript">
    var app = new Vue({
      el: '#app',
      data() {
        return {
          tabList: ['Báo cáo tài chính', 'Báo cáo thường niên', 'Nghị quyết HĐQT', 'Thông báo', 'Điều lệ và quy chế', 'Bản tin', 'Báo cáo tháng'],
        };
      },
      components: {
        myheader: httpVueLoader('vue_components/header.vue'),
        myfooter: httpVueLoader('vue_components/footer.vue'),
        rednavi: httpVueLoader('vue_components/red-slide-navigation.vue'),
        yellownavi: httpVueLoader('vue_components/yellow-slide-navigation.vue'),
      },
    });
  </script>
</body>

</html>