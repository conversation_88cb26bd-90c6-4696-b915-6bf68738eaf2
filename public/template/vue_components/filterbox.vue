<template>
  <div class="block-filter">
    <div class="container">
      <div class="box-title">
        <div class="box-title-wrap">
          <h3 class="title">
            Bộ lọc
            <span class="icon">
              <img src="images/loading.gif" data-src="images/product/ic-filter.jpg" alt="" width="24" height="24" class="lazy img-fluid" />
            </span>
          </h3>
          <!-- selected filter -->
          <div class="remove-filter">
            <a href="javascript:;" class="remove">
              <span class="icon">
                <img src="images/loading.gif" data-src="images/common/ic-remove-filter.png" alt="" class="lazy img-fluid" width="24" height="24" />
              </span>
              <span class="txt"> Bỏ chọn </span>
            </a>
          </div>
        </div>
      </div>

      <div class="box-content">
        <div class="filter-options-wrap">
          <div class="item" v-for="n in 11" :key="n">
            <div class="dropdown show">
              <a class="drodown-name dropdown-toggle" href="javascript:;" role="button" :id="'filter-type-' + n" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <span class="txt"> Nhóm sản phẩm </span>
              </a>

              <div class="dropdown-menu" :aria-labelledby="'filter-type-' + n">
                <a class="dropdown-item" href="javascript:;">Phong cách Ý</a>
                <a class="dropdown-item" href="javascript:;">Phong cách Hàn Quốc</a>
                <a class="dropdown-item" href="javascript:;">Trang sức Kim cương</a>
                <a class="dropdown-item" href="javascript:;">Trang sức Vàng tây đá màu</a>
                <a class="dropdown-item" href="javascript:;">Trang sức Nhẫn cưới</a>
                <a class="dropdown-item" href="javascript:;">Bộ sưu tập</a>
                <a class="dropdown-item" href="javascript:;">Trang sức phong thủy</a>
                <a class="dropdown-item" href="javascript:;">Trang sức Bạc</a>
                <a class="dropdown-item" href="javascript:;">Quà tặng</a>
              </div>
            </div>
          </div>

          <div class="item">
            <div class="dropdown show">
              <a class="drodown-name dropdown-toggle" href="javascript:;" role="button" id="filter-type-12" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <span class="txt"> Giá </span>
              </a>

              <div class="dropdown-menu" aria-labelledby="filter-type-12">
                <div class="dropdown-item">
                  <div id="price-range" class="pt-2">
                    <div class="medium-counter mb-lg-4 mb-3">
                      <input type="text" id="counter-from" class="medium-counter--item" />
                      <span class="text"> đến </span>
                      <input type="text" id="counter-to" class="medium-counter--item" />
                    </div>

                    <div id="slider-range"></div>

                    <div class="d-flex align-items-center justify-content-between">
                      <span id="amount-from" class="before-amount"></span>
                      <span id="amount-to" class="before-amount"></span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script></script>
