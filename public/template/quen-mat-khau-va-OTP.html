<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="shortcut icon" async href="images/common/favicon.png" />
    <title>Website Bảo Tín Mạnh Hải</title>
    <link rel="stylesheet" href="libs/bootstrap/bootstrap.min.css" />
    <link rel="stylesheet" href="libs/swiper/swiper-bundle.min.css" />
    <link rel="stylesheet" href="libs/fancybox/jquery.fancybox.min.css" />
    <link rel="stylesheet" href="libs/flatpickr/flatpickr.min.css" />
    <link rel="stylesheet" href="libs/aos/aos.css" />
    <link rel="stylesheet" href="libs/fancybox/jquery.fancybox.min.css" />
    <link rel="stylesheet" href="css/style.css" />
  </head>

  <body>
    <div id="app">
      <myheader></myheader>
      <main>
        <section class="vertify-page">
          <div class="container">
            <div class="retrieve">
              <h2>Lấy lại mật khẩu của bạn</h2>
              <p>Nhập số điện thoại đăng ký, chúng tôi sẽ gửi mã lấy lại mật khẩu qua email để bạn nhập mật khẩu mới.</p>
              <form class="form" action="">
                <div class="form-group my-4">
                  <input type="text" class="form-control" placeholder="Email của bạn" />
                </div>
                <button class="d-flex align-items-center justify-content-center w-100 border-0 text-white btn-section">Gửi mã OTP</button>
              </form>
            </div>

            <!--    <div class="otp">
              <h2>Nhập mã lấy lại mật khẩu</h2>
              <p>
                Chúng tôi đã gửi mã lấy lại mật khẩu qua email: <br />
                ng***.com
              </p>
              <div class="form">
                <div class="form-group my-4">
                  <span v-for="n in 6">1</span>
                </div>
                <div class="not-yet mb-4">Chưa nhận được mã? <a href="">Gửi lại</a></div>
                <a href="" class="d-flex align-items-center justify-content-center w-100 border-0 text-white btn-section">Xác nhận</a>
              </div>
            </div> -->
          </div>
        </section>

        <section class="supports">
          <div class="container">
            <h2>Hỗ trợ</h2>
            <ul>
              <li>
                <div class="icon"><img src="images/icon-faq1.png " alt="" /></div>
                <div class="txt">Câu hỏi thường gặp</div>
              </li>
              <li>
                <div class="icon"><img src="images/icon-faq2.png " alt="" /></div>
                <div class="txt">Hotline hỗ trợ: <strong>1900 8765</strong></div>
              </li>
              <li>
                <div class="icon"><img src="images/icon-oto.png " alt="" /></div>
                <div class="txt">Chính sách vận chuyển</div>
              </li>
              <li>
                <div class="icon"><img src="images/icon-doitra.png " alt="" /></div>
                <div class="txt">Chính sách đổi trả</div>
              </li>
            </ul>
          </div>
        </section>
      </main>
      <myfooter></myfooter>
    </div>

    <script type="text/javascript" src="js/vue.js"></script>
    <script type="text/javascript" src="js/httpvueloader.js"></script>
    <script type="text/javascript" src="libs/swiper/swiper-bundle.min.js"></script>
    <script type="text/javascript" src="libs/bootstrap/jquery.min.js"></script>
    <script type="text/javascript" src="libs/bootstrap/popper.min.js"></script>
    <script type="text/javascript" src="libs/bootstrap/bootstrap.min.js"></script>
    <script type="text/javascript" src="libs/aos/aos.js"></script>
    <script type="text/javascript" src="libs/chartjs/chart.min.js"></script>
    <script type="text/javascript" src="libs/fancybox/jquery.fancybox.min.js"></script>
    <script type="text/javascript" src="js/main.js"></script>

    <script type="text/javascript">
      var app = new Vue({
        el: '#app',
        data() {
          return {};
        },
        components: {
          myheader: httpVueLoader('vue_components/header.vue'),
          myfooter: httpVueLoader('vue_components/footer.vue'),
          rednavi: httpVueLoader('vue_components/red-slide-navigation.vue'),
          yellownavi: httpVueLoader('vue_components/yellow-slide-navigation.vue'),
        },
      });
    </script>
  </body>
</html>
