{"name": "apiato/apiato", "description": "A flawless framework for building scalable and testable API-Centric Apps with PHP and Laravel.", "homepage": "http://apiato.io/", "support": {"issues": "https://github.com/apiato/apiato/issues", "source": "https://github.com/apiato/apiato"}, "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "keywords": ["Apiato", "API", "Framework", "API framework", "API Starter", "PHP API Starter", "Laravel API", "PHP", "Hello API", "Porto", "Porto SAP"], "license": "MIT", "type": "project", "require": {"php": "7.4.*", "ext-mbstring": "*", "ext-openssl": "*", "ext-pdo": "*", "ext-tokenizer": "*", "artesaos/seotools": "^0", "baokim/baokim-sdk": "^1.3", "barryvdh/laravel-translation-manager": "^0.5.10", "fideloper/proxy": "^4.2", "firebase/php-jwt": "v5.5.0", "fruitcake/laravel-cors": "^1.0", "guzzlehttp/guzzle": "^6.3", "htmlmin/htmlmin": "^8.0", "intervention/image": "^2.5", "laravel/framework": "^8.0", "laravel/passport": "v10.3.3", "laravel/tinker": "^2.0", "laravelcollective/html": "^6.2", "doctrine/dbal": "2.5.*", "lcobucci/jwt": "*", "league/fractal": "*", "rap2hpoutre/laravel-log-viewer": "^1.7", "staudenmeir/eloquent-eager-limit": "^1.0", "unisharp/laravel-filemanager": "^2.2", "wikimedia/composer-merge-plugin": "^2.0"}, "require-dev": {"facade/ignition": "^2.0", "fzaninotto/faker": "^1.9.1", "mockery/mockery": "^1.3.1", "nunomaduro/collision": "^4.1", "phpunit/phpunit": "^8.5", "ext-json": "*"}, "autoload": {"classmap": ["database/seeds", "database/factories"], "psr-4": {"App\\": "app/"}}, "autoload-dev": {"psr-4": {}}, "extra": {"laravel": {"dont-discover": ["*"]}, "merge-plugin": {"include": ["app/Ship/composer.json", "app/Ship/core/composer.json", "app/Containers/*/composer.json", "app/Containers/composer.json"], "recurse": true, "replace": false, "merge-dev": true, "merge-extra": false, "merge-extra-deep": false, "merge-scripts": true}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"wikimedia/composer-merge-plugin": true}}, "minimum-stability": "dev", "prefer-stable": true}