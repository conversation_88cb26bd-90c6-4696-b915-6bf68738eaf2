<?php

/**
 * @ Created by: VSCode
 * @ Author: Oops!Memory - OopsMemory.com
 * @ Create Time: 2021-07-12 10:07:07
 * @ Modified by: Oops!Memory - OopsMemory.com
 * @ Modified time: 2021-12-31 14:04:22
 * @ Description: Happy Coding!
 */

namespace App\Containers\ShoppingCart\Bussiness\Traits;

use App\Containers\BaseContainer\Enums\BaseEnum;
use Closure;
use Illuminate\Support\Collection;
use App\Containers\ShoppingCart\Facades\Cart;
use App\Containers\Product\Enums\ProductStatus;
use App\Containers\ShoppingCart\Bussiness\CartItem;
use App\Containers\SpecialOffer\Models\SpecialOffer;
use App\Containers\SpecialOffer\Enums\SpecialOfferType;
use App\Containers\Product\Actions\FrontEnd\ProductListingAction;
use App\Containers\ShoppingCart\Exceptions\InvalidRowIDException;
use App\Containers\ShoppingCart\Actions\FrontEnd\UpdateCartItemAction;
use App\Containers\Pos\Tasks\Products\GetDataVariantBySkuPosProductSkuTask;
use App\Containers\Product\Actions\FrontEnd\PreCheckingToAddCartItemAction;
use App\Containers\PromotionCampaign\Actions\SubActions\CalcProductPriceByCampaignSubAction;
use App\Containers\PromotionCampaign\Actions\FrontEnd\SubActions\GetMostRecentlyCampaignOnProductListSubAction;

trait GettersTrait
{
    /**
     * Get a cart item from the cart by its rowId.
     *
     * @param string $rowId
     * @return \Gloudemans\Shoppingcart\CartItem
     */
    public function get($rowId)
    {
        $content = $this->getContent();
        foreach($content as $item){
            if($item->rowId == $rowId){
                return $content->get($rowId);
            }
        }
        Cart::remove($rowId);
        return null;
        // throw new InvalidRowIDException("The cart does not contain rowId {$rowId}.");
    }

    /**
     * Get the content of the cart.
     *
     * @return \Illuminate\Support\Collection
     */
    public function content(
        bool $selectedItem = false,
        bool $isRefresh = false,
        bool $pureContent = false,
        ?string $identify = null,
        ?int $selectedDelivery = null,
        ?bool $isCartView = false
    ) {
        $this->identify = !empty($this->identify) ? $this->identify : $identify;
        // if (is_null($this->session->get($this->instance))) {
        //     return new Collection([]);
        // }
        // return $this->session->get($this->instance);
        $content = $this->getContent();
        // $couponContent = $this->getCouponContent();=
        // if($isCartView){
            $this->refreshCart($isRefresh, $content);
        // }

        if ($selectedItem) {
            $content = $content->filter(function ($item) {
                return $item->selected == true;
            });

            $content = $content->map(function ($item) {
                if (isset($item->attachProducts) && !empty($item->attachProducts)) {
                    $item->attachProducts = array_values(array_filter($item->attachProducts, function ($attach) {
                        return $attach->selected == true;
                    }));
                }

                return $item;
            });

            // foreach($content as $item) {
            //     if ($item->selected == true) {
            //         if (isset($item->attachProducts) && !empty($item->attachProducts)) {

            //         }
            //         return true;
            //     }
            // }
        }
        $total = $this->total();
        $arr = [
            'items' => $content->toArray(),
            'count' => $this->count(),
            'count_selected' => $this->countSelected(),
            'delivery_selected' => $selectedDelivery,
            'tax' => $this->tax(),
            'sub_total' => $this->subtotal(),
            'total' => $total,
            'delivery' => $pureContent ? false : $this->calcDeliveryFee($total, $selectedDelivery),
        ];
    
        $arr['coupon'] = $pureContent ? false : $this->calcCouponValue($arr['sub_total'], (float)$arr['delivery']['value'], $content);
        // $arr['coupon']['value'] = 0;
        if(!empty($arr['coupon']['value'])){
          $arr['total'] = (float)$total - (float)@$arr['coupon']['value'];

        }else{
          $arr['total'] = (float)$total;
        }

        return $arr;
    }

    /**
     * Get the carts content, if there is no cart content set yet, return a new empty Collection
     *
     * @return \Illuminate\Support\Collection
     */
    protected function getContent()
    {
        if (!$this->session->has('cart_session_started')) {
            $this->session->regenerate(true);
            $this->session->put('cart_session_started', true);
        }
        $content = $this->session->has($this->instance)
            ? $this->session->get($this->instance)
            : new Collection();

        return $content;
    }

    /**
     * Search the cart content for a cart item matching the given search closure.
     *
     * @param \Closure $search
     * @return \Illuminate\Support\Collection
     */
    public function search(Closure $search)
    {
        $content = $this->getContent();

        return $content->filter($search);
    }

    public function refreshCart($isRefresh, $content): void
    {
        if ($isRefresh) {
            /**
             *  variantId => productId
             */
            $productIds = $content->pluck('id', 'variantId')->toArray();

            // dd($productIds);

            $products = !empty($productIds) ? app(ProductListingAction::class)->skipCache()->run(
                [
                    'equalIds' => $productIds,'status' => BaseEnum::ACTIVE
                ], // filters
                [], // orderBy
                0, // limit
                true, // Skip pagination
                $this->currentLang, // curent lang
            ) : collect([]);
            // $this->updateFromCampaign($products, $productIds, $content);
            $this->updateFromPos($products, $productIds, $content);
            // xoá những sản phẩm không tồn tại trong giỏ hàng
            $this->removeProductNotExist($products, $content);
            $this->session->put($this->instance, $content);
        }
    }
    public function updateFromPos($products, $productIds, &$content)
    {
        foreach ($products as $product) {
            $filterItems = $content->filter(function ($item) use ($product) {
                return $item->id == $product->id;
            });
            /**
             * 1 sản phẩm có thể có nhiều variant khác nhau nên phải loop để ốp hết
             */
            foreach ($filterItems as $rowId => $itemCart) {
                // $rowId = CartItem::generateRowId($product->id, $variantIds[$product->id]);
                $currentCartItem = $content->get($rowId);
                $checkSkuDataPos =app(GetDataVariantBySkuPosProductSkuTask::class)->run($itemCart->sku);
                $dataGet = $checkSkuDataPos->getResponseData();
                if(!empty($dataGet['giam_gia']) && $itemCart->price != $dataGet['giam_gia']){
                    app(UpdateCartItemAction::class)->currentLang($this->currentLang)
                    ->setIdentify(!empty($this->user->id) ? $this->user->id : null)
                    ->instance()
                    ->rowId($rowId)
                    ->quantity($itemCart->quantity)
                    ->price($dataGet['giam_gia'])
                    ->variantId($itemCart->variantId)
                    ->run();
                }else if(empty( $dataGet['giam_gia']) && $itemCart->price != $dataGet['giaBan']){
                    app(UpdateCartItemAction::class)->currentLang($this->currentLang)
                    ->setIdentify(!empty($this->user->id) ? $this->user->id : null)
                    ->instance()
                    ->rowId($rowId)
                    ->quantity($itemCart->quantity)
                    ->price($dataGet['giaBan'])
                    ->variantId($itemCart->variantId)
                    ->run();
                }
                if(empty( $dataGet['giaBan'])){
                    app(UpdateCartItemAction::class)->currentLang($this->currentLang)
                    ->setIdentify(!empty($this->user->id) ? $this->user->id : null)
                    ->instance()
                    ->rowId($rowId)
                    ->variantId($itemCart->variantId)
                    ->isRemove()
                    ->run();
                }
                $content->put($rowId, $currentCartItem);
            }
        }
    }
    public function updateFromCampaign($products, $productIds, &$content)
    {
        $campaigns = app(GetMostRecentlyCampaignOnProductListSubAction::class)->run($productIds);

        // Flip chơi chơi :"))))~
        $variantIds = array_flip($productIds);

        $preCheckingToAddCartItemAction = app(PreCheckingToAddCartItemAction::class);
        foreach ($products as $product) {
            $filterItems = $content->filter(function ($item) use ($product) {
                return $item->id == $product->id;
            });

            /**
             * 1 sản phẩm có thể có nhiều variant khác nhau nên phải loop để ốp hết
             */
            foreach ($filterItems as $rowId => $itemCart) {
                // $rowId = CartItem::generateRowId($product->id, $variantIds[$product->id]);
                $currentCartItem = $content->get($rowId);

                $itemUpdate = $preCheckingToAddCartItemAction
                    ->currentCampaign(isset($campaigns[$product->id]) ? $campaigns[$product->id] : null)
                    ->currentProduct($product)
                    ->run($product->id, @$itemCart->variantId ?? null, @$itemCart->quantity ?? null);

                $currentCartItem->updateFromArray($itemUpdate);

                $this->updateFromSpecialOffer($product, $currentCartItem);

                $content->put($rowId, $currentCartItem);
            }
        }
    }

    public function updateFromSpecialOffer($product, &$currentItem): void
    {
        $currentItem->attachProducts = [];

        $specs = $product->specialOffers->map(function ($item) use ($currentItem) {

            $this->calcSpecialOffer($item, $currentItem);

            return [
                'id' => $item->id,
                'name' => $item->desc->name,
                'special_offer_id' => $item->desc->special_offer_id,
                'type' => $item->offer_discount_type,
                'value' => $item->offer_discount_value
            ];
        });

        $currentItem->setSpecialOffers($specs->toArray());
    }

    public function calcSpecialOffer(SpecialOffer $specialOffer, &$currentCartItem)
    {
        if ($specialOffer->offer_discount_type == SpecialOfferType::PRICE) {
            $currentCartItem->price = $currentCartItem->price > $specialOffer->offer_discount_value ? ($currentCartItem->price - $specialOffer->offer_discount_value) : 0;
        } else if ($specialOffer->offer_discount_type == SpecialOfferType::GIFT) {
            $this->attachProducts($currentCartItem, $specialOffer);
        }
    }

    /**
     * Hàm add sản phẩm tặng kèm từ Special Offers thuộc mỗi item trong giỏ hàng
     */
    public function attachProducts(&$currentCartItem, $specialOffer)
    {
        $preCheckingToAddCartItemAction = app(PreCheckingToAddCartItemAction::class);

        $specialOffer->load([
            'special_offer_product_gift' => function ($q) {
                $q->selectRaw('products.*, special_offer_product_gift.product_id, special_offer_product_gift.special_offer_id, special_offer_product_gift.quantity');
                $q->whereRaw("product_id in  (select id from products where status=" . ProductStatus::ACTIVE . ")");
            },
            'special_offer_product_gift.desc' => function ($q) {
                $q->select(['id', 'product_id', 'language_id', 'name', 'slug']);
                $q->activeLang($this->currentLang->language_id);
            }
        ]);

        // dd($specialOffer);

        if (isset($specialOffer->special_offer_product_gift) && $specialOffer->special_offer_product_gift->isNotEmpty()) {

            foreach ($specialOffer->special_offer_product_gift as $product) {

                $quantityNeedle = $this->getProductQuantityNeedle($product->id, $currentCartItem->quantity, $product->quantity);

                $itemData = $preCheckingToAddCartItemAction
                    ->currentProduct($product)
                    ->run($product->id, 0, $quantityNeedle);

                if (!isset($itemData['error_code'])) {
                    $itemData['selected'] = true;
                }

                $currentCartItem->attachProducts[] = new CartItem($itemData);
            }
        }
    }

    /**
     * Để tạm đây, sử dụng cho việc check tổng số lượng sản phẩm trong giỏ nếu vừa mua lẻ vừa được tặng kèm
     * Another case: Mua bao nhiêu sản phẩm gốc (cha) thì được tặng kèm bấy nhiêu sản phẩm
     * Mục đích là lấy ra tổng số cuối cùng so sánh với tồn kho
     */
    public function getProductQuantityNeedle($productId, $parentQuantity, $attachQuantity)
    {
        /**
         * Return tổng số lượng sản phẩm đã có trong giỏ + số lượng được tặng kèm
         */

        return $attachQuantity > 0 ? ($parentQuantity * $attachQuantity) : 1;
    }

    /**
     * xoá những sản phẩm không tồn tại trong giỏ hàng
     */
    public function removeProductNotExist($products, &$content)
    {
        $productNotExist = $content->whereNotIn('id', $products->pluck('id'));
        if(count($productNotExist) > 0){
            foreach ($productNotExist as $rowId => $item) {
                Cart::remove($rowId);
            }
        }
    }
}
