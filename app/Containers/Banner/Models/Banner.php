<?php

/**
 * @ Created by: VSCode
 * @ Author: Oops!Memory - OopsMemory.com
 * @ Create Time: 2020-10-04 13:58:15
 * @ Modified by: Oops!Memory - OopsMemory.com
 * @ Modified time: 2022-09-09 13:54:29
 * @ Description:
 */

namespace App\Containers\Banner\Models;

use App\Containers\Banner\Enums\BannerStatus;
use App\Ship\Parents\Models\Model;
use Carbon\Carbon;

class Banner extends Model
{
    protected $table = 'banner';

    protected $fillable = [
        'status',
        'sort_order',
        'position',
        'is_mobile',
        'publish_at',
        'end_at',
        'category_id',
        'collection_id'
    ];

    const SIZE = [
        'big_home' => ['width' => 1920, 'height' => 0],
        'big_news' => ['width' => 1920, 'height' => 0],
        'big_product' => ['width' => 1920, 'height' => 0],
        // 'fae_home' => ['width' => 600, 'height' => 398],
    ];

    public static function getSize($type = 'big_home')
    {
        if (isset(self::SIZE[$type])) {
            return (object)self::SIZE[$type];
        }
        return false;
    }

    public function positions()
    {

        $configPositions = config('banner-container.positions');
        $positions = [];

        if (!empty($configPositions)) {
            foreach ($configPositions as $k => $v) {
                $positions = array_merge($positions, $v['pos']);
            }

            $all = explode(',', $this->position);
            if (!empty($all)) {
                $tmp = [];
                foreach ($all as $a) {
                    if (isset($positions[$a])) {
                        $tmp[$a] = $positions[$a];
                    }
                }
                return $tmp;
            }
        }
        return false;
    }

    public function desc()
    {
        return $this->hasOne(BannerDesc::class, 'banner_id', 'id');
    }

    public function all_desc()
    {
        return $this->hasManyKeyBy('language_id', BannerDesc::class, 'banner_id', 'id');
    }

    public function scopeAvailable($query, array $positions = [])
    {
        $now = Carbon::now();
        return $query->where('status', BannerStatus::ACTIVE)
            ->where('publish_at', '<', $now)
            ->where('end_at', '>', $now)
            ->where(function ($query) use ($positions) {
                foreach ($positions as $position) {
                    $query->orWhereRaw("LOCATE(',{$position},', position) > 0");
                }
            });
    }
}
