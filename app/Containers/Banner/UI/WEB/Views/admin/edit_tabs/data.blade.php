<div class="tab-pane" id="data">
    <div class="tabbable">
        <div class="row form-group align-items-center">
            <label class="col-sm-2 control-label text-right mb-0" for="publish_at"><PERSON><PERSON><PERSON> hi<PERSON><PERSON> lực <span class="d-block small text-danger">(<PERSON><PERSON><PERSON> mà banner chính thức được đăng)</span></label>
            <div class="col-sm-6">
                <input type="text" name="publish_at" readonly value="{{ old('publish_at',@$data['publish_at'] ? Carbon\Carbon::parse($data['publish_at'])->format('d/m/Y H:i') : '') }}" placeholder="dd/mm/YY H:i"
                       id="publish_at" class="form-control">
            </div>
        </div>
        <div class="row form-group align-items-center">
            <label class="col-sm-2 control-label text-right mb-0" for="end_at"><PERSON><PERSON><PERSON> k<PERSON><PERSON> thúc <span class="d-block small text-danger">(<PERSON><PERSON><PERSON> <PERSON><PERSON> banner ch<PERSON>h thức đư<PERSON> h<PERSON>)</span></label>
            <div class="col-sm-6">
                <input type="text" name="end_at" readonly value="{{ old('end_at',@$data['end_at'] ? Carbon\Carbon::parse($data['end_at'])->format('d/m/Y H:i') : '') }}" placeholder="dd/mm/YY H:i"
                       id="end_at" class="form-control">
            </div>
        </div>
        <div class="row form-group align-items-center">
            <label class="col-sm-2 control-label text-right mb-0" for="status">Trạng thái <span class="d-block small text-danger">(Hiển thị hay không)</span></label>
            <div class="col-sm-3">
                <select class="form-control{{ $errors->has('status') ? ' is-invalid' : '' }}" name="status" id="status">
                    <option value="1" {{@$data['status'] == 1 ? 'selected' : ''}}>Ẩn</option>
                    <option value="2" {{@$data['status'] == 2 ? 'selected' : ''}}>Hiển thị</option>
                </select>
            </div>
        </div>

        {{-- <div class="row form-group">
            <label class="col-sm-2 control-label text-right mb-0 align-text-top">Dành cho mobile <span class="d-block small text-danger">(Chọn để hiển thị riêng trên mobile)</span></label>
            <div class="col-sm-10">
                <div class="row">
                    <div class="col-sm-1">
                        <label class="c-switch c-switch-label c-switch-outline-primary">
                            {!! Form::checkbox('is_mobile', 1, old('is_mobile', @$data['is_mobile']) == 1, ['class' => 'c-switch-input']) !!}
                            <span class="c-switch-slider" data-checked="On" data-unchecked="Off"></span>
                        </label>
                    </div>
                    <div class="col-sm-11 mb-2"></div>
                </div>
            </div>
        </div> --}}

        <div class="row form-group align-items-center">
            <label class="col-sm-2 control-label text-right mb-0" for="sort_order">Sắp xếp <span class="d-block small text-danger">(Vị trí, càng nhỏ thì càng lên đầu)</span></label>
            <div class="col-sm-3">
                <input type="text" name="sort_order" value="{{ old('sort_order', @$data['sort_order']) }}" placeholder="Vị trí sắp xếp"
                       id="sort_order" class="form-control"
                       onkeypress="return shop.numberOnly()" onfocus="this.select()">
            </div>
        </div>

        <div class="row form-group align-items-center">
            <label class="col-sm-2 control-label text-right mb-0" for="category_id">Thuộc Danh mục/Ngành hàng <span class="d-block small text-danger">(Danh mục/Ngành hàng top trên cùng)</span></label>
            <div class="col-sm-3">
                <select class="form-control{{ $errors->has('category_id') ? ' is-invalid' : '' }}" name="category_id" id="category_id">
                    <option value="0" >-- Chọn Danh mục/Ngành hàng</option>
                    @foreach($categories as $item)
                    <option {{@$data['category_id'] == $item->category_id ? 'selected' : ''}} value="{{$item->category_id}}">{{$item->self_name}}</option>
                     @endforeach
                </select>
            </div>
        </div>

        <div class="row form-group align-items-center">
            <label class="col-sm-2 control-label text-right mb-0" for="collection_id">Thuộc Bộ sưu tập <span class="d-block small text-danger">(Bộ sưu tập top trên cùng)</span></label>
            <div class="col-sm-3">
                <select class="form-control{{ $errors->has('collection_id') ? ' is-invalid' : '' }}" name="collection_id" id="collection_id">
                    <option value="" >-- Chọn Bộ sưu tập</option>
                    @foreach($collections ?? [] as $item)
                    <option {{@$data['collection_id'] == $item->id ? 'selected' : ''}} value="{{$item->id}}">{{$item->desc->name}}</option>
                     @endforeach
                </select>
            </div>
        </div>
    </div>
</div>

@push('js_bot_all')
    <script>
        $('#publish_at').datetimepicker({ format:'d/m/Y H:i', });
        $('#end_at').datetimepicker({ format:'d/m/Y H:i', });
        // $(".chosen-select").chosen({disable_search_threshold: 10});
    </script>
@endpush
