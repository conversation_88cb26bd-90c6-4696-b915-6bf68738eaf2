<?php

namespace App\Containers\Collection\Tasks\Admin;

use App\Containers\Collection\Data\Repositories\CollectionRepository;
use App\Ship\Criterias\Eloquent\ThisEqualThatCriteria;
use App\Ship\Criterias\Eloquent\ThisOperationThatCriteria;
use App\Ship\Parents\Tasks\Task;
use Illuminate\Database\Eloquent\Builder;

class GetAllCollectionTask extends Task
{
    protected $repository;

    public function __construct(CollectionRepository $repository)
    {
        $this->repository = $repository;
    }

    public function run($filters, $perPage, $currentLang = null)
    {
        if (isset($filters['id'])) {
            $this->repository->pushCriteria(new ThisOperationThatCriteria('id', '%' . $filters['id'] . '%', 'like'));
        }
        if (isset($filters['status'])) {
            $this->repository->pushCriteria(new ThisEqualThatCriteria('status', $filters['status']));
        }
        $this->repository->with([
            'desc' => function ($query) use ($currentLang) {
                $query->activeLang($currentLang->language_id);
            }
        ]);

        if (!empty($filters['in_category_ids'])) {
            $this->repository
                ->leftJoin('collection_details', 'collection_details.collection_id', '=', 'collections.id')
                ->whereIn('collection_details.object_id', $filters['in_category_ids'])
                ->where('collection_details.type', 1);
        }

        $this->repository->orderByRaw($filters['order_by'] ?? 'created desc');

        $this->repository->whereHas('desc', function (Builder $q) use ($filters) {
            if (!empty($filters['title'])) {
                $q->where(\DB::raw('lower(name)'), 'like', '%' . strtolower($filters['title']) . '%');
            }
        });

        if (isset($filters['skip_pagination']) && $filters['skip_pagination']) {
            return $this->repository->limit($perPage);
        }
        return $this->repository->paginate($perPage);
    }
}
