<?php

namespace App\Containers\Collection\Tasks\Admin;

use App\Containers\Collection\Data\Repositories\CollectionRepository;
use App\Ship\Parents\Tasks\Task;

class SaveCollectionTask extends Task
{
    protected $repository;

    public function __construct(CollectionRepository $repository)
    {
        $this->repository = $repository;
    }

    public function run($collection_data, $collection_id)
    {
        if (!empty($collection_data)) {
            $this->repository->where('id', $collection_id)->update([
                'status'     => isset($collection_data['status']) ? (int)$collection_data['status'] : 0,
                'type'       => isset($collection_data['type']) ? (int)$collection_data['type'] : 0,
                'sort_order' => (int)$collection_data['sort_order'] ?? null,
                'is_home'    => $collection_data['is_home'] ?? null,
                'link'       => $collection_data['link'] ?? null,
                // 'category_id' => $collection_data['category_id'],
                // 'manufacturer_id' => $collection_data['partner'],
                // 'sku' => $collection_data['sku'],
            ]);
        }
        if (isset($collection_data['delete_image']) && $collection_data['delete_image'] === 1) {
            $this->repository->where('id', $collection_id)->update([
                'image' => $collection_data['image_avatar'] ?? null,
            ]);
        } else {
            if (!empty($collection_data['image_avatar'])) {
                $this->repository->where('id', $collection_id)->update([
                    'image' => $collection_data['image_avatar'],
                ]);
            }
        }
        return $collection_id;
    }
}
