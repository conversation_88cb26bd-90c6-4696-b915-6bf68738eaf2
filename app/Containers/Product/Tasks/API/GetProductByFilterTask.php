<?php

namespace App\Containers\Product\Tasks\API;

use App\Containers\Product\Data\Repositories\ProductRepository;
use App\Ship\Parents\Tasks\Task;

class GetProductByFilterTask extends Task
{
    protected $repository;

    public function __construct(ProductRepository $repository)
    {
        $this->repository = $repository;
    }

    public function run(array $filters=[])
    {
        $this->repository->pushCriteria(app('Prettus\Repository\Criteria\RequestCriteria'));
        $repository = $this->repository;

        if (!empty($filters['category_id'])) {
            $repository = $repository->whereHas('categories', function ($query) use ($filters) {
                return $query->where('category.category_id', $filters['category_id']);
            });
        }

        // Always eager load desc for listing
        $repository = $repository->with([
            'desc' => function ($query) {
                $query->select('product_id', 'name', 'slug', 'short_description', 'meta_title', 'meta_description', 'meta_keyword');
            }
        ]);

        // Apply filters by AND-ing each selected filter_id
        if (!empty($filters['filter_ids'])) {
            $filterIds = array_filter((array) $filters['filter_ids']);
            foreach ($filterIds as $filterId) {
                $repository = $repository->whereHas('filters', function ($query) use ($filterId) {
                    $query->where('filter.filter_id', $filterId);
                });
            }
        }

        if ( !empty($filters['orderField']) && !empty($filters['sortValue']) ) {
            if ($filters['orderField'] == 'price_asc') {
                $repository = $repository->orderBy('price', 'asc');
            } else if($filters['orderField'] == 'price_desc') {
                $repository = $repository->orderBy('price', 'desc');
            } else if($filters['orderField'] == 'newest') {
                $repository = $repository->orderBy('product_id', 'desc');
            } else if($filters['orderField'] == 'oldest') {
                $repository = $repository->orderBy('product_id', 'asc');
            }else{
                $repository = $repository->orderBy('product_id', 'desc');
            }
        }
        return $repository->paginate($filters['limit'] ?? 10);
    }
} // End class
