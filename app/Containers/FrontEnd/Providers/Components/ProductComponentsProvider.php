<?php

/**
 * @ Created by: VSCode
 * @ Author: Oops!Memory - OopsMemory.com
 * @ Create Time: 2021-07-31 22:43:25
 * @ Modified by: Oops!Memory - OopsMemory.com
 * @ Modified time: 2022-08-30 11:50:16
 * @ Description: Happy Coding!
 */

namespace App\Containers\FrontEnd\Providers\Components;

use Illuminate\Support\Facades\Blade;
use App\Ship\Parents\Providers\MainProvider;
use App\Containers\FrontEnd\UI\WEB\Components\Product\Collection\TagComponent;
use App\Containers\FrontEnd\UI\WEB\Components\Product\Sale\SaleHeaderComponent;
use App\Containers\FrontEnd\UI\WEB\Components\Product\Index\HotCategoryComponent;
use App\Containers\FrontEnd\UI\WEB\Components\Product\Commons\FiltersBoxComponent;
use App\Containers\FrontEnd\UI\WEB\Components\Product\Collection\ColectionComponent;
use App\Containers\FrontEnd\UI\WEB\Components\Product\Detail\DetailProductComponent;
use App\Containers\FrontEnd\UI\WEB\Components\Product\Detail\ProductBannerComponent;
use App\Containers\FrontEnd\UI\WEB\Components\Product\Detail\ProductRatingComponent;
use App\Containers\FrontEnd\UI\WEB\Components\Product\Sale\SaleProductListComponent;
use App\Containers\FrontEnd\UI\WEB\Components\Product\Detail\ProductSimilarComponent;
use App\Containers\FrontEnd\UI\WEB\Components\Product\HotTrend\CategoryListComponent;
use App\Containers\FrontEnd\UI\WEB\Components\Product\Promotion\CampaignListComponent;
use App\Containers\FrontEnd\UI\WEB\Components\Product\Category\CategoryBannerComponent;
use App\Containers\FrontEnd\UI\WEB\Components\Product\Delivery\DeliveryBannerComponent;
use App\Containers\FrontEnd\UI\WEB\Components\Product\Detail\ProductTopDetailComponent;
use App\Containers\FrontEnd\UI\WEB\Components\Product\FreeShip\FreeShipBannerComponent;
use App\Containers\FrontEnd\UI\WEB\Components\Product\Category\CategoryProductComponent;
use App\Containers\FrontEnd\UI\WEB\Components\Product\Category\CategorySidebarComponent;
use App\Containers\FrontEnd\UI\WEB\Components\Product\Delivery\DeliveryProductComponent;
use App\Containers\FrontEnd\UI\WEB\Components\Product\Detail\DetailProductMiniComponent;
use App\Containers\FrontEnd\UI\WEB\Components\Product\FreeShip\FreeShipProductComponent;
use App\Containers\FrontEnd\UI\WEB\Components\Product\FlashSale\FlashSaleHeaderComponent;
use App\Containers\FrontEnd\UI\WEB\Components\Product\FreeShip\FreeShipCategoryComponent;
use App\Containers\FrontEnd\UI\WEB\Components\Product\Promotion\ProductEachCateComponent;
use App\Containers\FrontEnd\UI\WEB\Components\Product\PromotionDetail\TopBannerComponent;
use App\Containers\FrontEnd\UI\WEB\Components\Product\Promotion\PromotionCategoryComponent;
use App\Containers\FrontEnd\UI\WEB\Components\Product\FreeShip\FreeShipBottomBannerComponent;
use App\Containers\FrontEnd\UI\WEB\Components\Product\FlashSale\FlashSaleProductListComponent;
use App\Containers\FrontEnd\UI\WEB\Components\Product\Promotion\PromotionIndexBannerComponent;
use App\Containers\FrontEnd\UI\WEB\Components\Product\Collection\AnotherCollectionBoxComponent;
use App\Containers\FrontEnd\UI\WEB\Components\Product\PromotionDetail\HotPromotionProductComponent;
use App\Containers\FrontEnd\UI\WEB\Components\Product\HotTrend\TopBannerComponent as HotTrendTopBannerComponent;

class ProductComponentsProvider extends MainProvider
{
    public function boot()
    {
        //Category product listing
        Blade::component('category-banner', CategoryBannerComponent::class);
        Blade::component('category-sidebar', CategorySidebarComponent::class);
        Blade::component('category-product', CategoryProductComponent::class);

        // Detail product
        Blade::component('product-banner', ProductBannerComponent::class);
        Blade::component('product-top-detail', ProductTopDetailComponent::class);
        Blade::component('detail-product', DetailProductComponent::class);
        Blade::component('detail-product-mini', DetailProductMiniComponent::class);
        Blade::component('product-rating', ProductRatingComponent::class);
        Blade::component('product-similar', ProductSimilarComponent::class);

        // Delivery Page
        Blade::component('delivery-banner', DeliveryBannerComponent::class);
        Blade::component('product-list', DeliveryProductComponent::class);

        // FreeShip page
        Blade::component('freeship-banner', FreeShipBannerComponent::class);
        Blade::component('freeship-category-list', FreeShipCategoryComponent::class);
        Blade::component('freeship-product-list', FreeShipProductComponent::class);
        Blade::component('freeship-bottom-banner', FreeShipBottomBannerComponent::class);

        //Sale Page
        Blade::component('promotion-index-banner', PromotionIndexBannerComponent::class);
        Blade::component('promotion-category-list', PromotionCategoryComponent::class);
        Blade::component('promotion-product-each-cate', ProductEachCateComponent::class);
        Blade::component('promotion-campaign-list', CampaignListComponent::class);

        //Sale Detail Page
        Blade::component('promotion-detail-top-banner', TopBannerComponent::class);
        Blade::component('promotion-hot-product', HotPromotionProductComponent::class);

        // Hot trend Page
        Blade::component('hottrend-top-banner', HotTrendTopBannerComponent::class);
        Blade::component('hottrend-category-list', CategoryListComponent::class);

        // Flashsale
        Blade::component('flashsale-header', FlashSaleHeaderComponent::class);
        Blade::component('flashsale-product-list', FlashSaleProductListComponent::class);

        // Sale
        Blade::component('sale-header', SaleHeaderComponent::class);
        Blade::component('sale-product-list', SaleProductListComponent::class);

        Blade::component('collection-product-list', ColectionComponent::class);
        Blade::component('another-collection-box-v2', AnotherCollectionBoxComponent::class);

        Blade::component('hot-category-product-page', HotCategoryComponent::class);


        // Prd component version 2;

        Blade::component('filters-box-v2', FiltersBoxComponent::class);
    }
}
