<?php

/**
 * @ Created by: VSCode
 * @ Author: Oops!Memory - OopsMemory.com
 * @ Create Time: 2021-10-02 09:35:24
 * @ Modified by: Oops!Memory - OopsMemory.com
 * @ Modified time: 2022-09-09 13:42:06
 * @ Description: Happy Coding!
 */

use App\Containers\Localization\Actions\CheckSegmentLanguageAction;

Route::group(
    [
        'middleware' => [
            // 'htmloptimized',
            'Maintenance',
            'WebLocaleRedirect'
        ],
        'prefix' => app(CheckSegmentLanguageAction::class)->run(),
    ],
    function ($router) {

        // Routes cho Sản phẩm
        $router->get('/{slug}-p{id}', [
            'as'   => 'web_product_detail_page',
            'uses' => 'ProductController@detailProduct',
        ])->where([
            'slug' => '[a-zA-Z0-9_\-]+',
            'id' => '[0-9]+'
        ]);
        
        $router->get('/san-pham', [
            'as'   => 'web_product_index_page',
            'uses' => 'ProductController@index',
        ]);

        $router->post('/san-pham/bo-loc', [
            'as'   => 'web_product_filter_page',
            'uses' => 'ProductController@filterWithoutCategory',
        ]);
        

        // Routes cho Danh mục con
        $router->get('/{slug}-cp{id}', [
            'as'   => 'web_product_category_page',
            'uses' => 'ProductController@categoryWithinProducts',
        ])->where([
            'slug' => '[a-zA-Z0-9_\-]+',
            'id' => '[0-9]+'
        ]);
        // Routes cho Danh mục cha
        $router->get('/{slug}-c{id}', [
            'as'   => 'web_category_index',
            'uses' => 'ProductController@categoryIndex',
            'where' => [
                'slug' => '[a-zA-Z0-9_\-]+',
                'id' => '[0-9]+',
            ],
        ]);
        

        // Routes cho tìm kiếm sản phẩm
        $router->get('/search/bo-loc', [
            'as'   => 'web_product_search_page',
            'uses' => 'ProductController@searchProduct',
        ]);

        //Routes cho trang giao hàng
        $router->get('/delivery', [
            'as'   => 'web_product_delivery_page',
            'uses' => 'ProductController@deliveryProduct',
        ]);

        //Routes cho trang freeship
        $router->get('/freeship', [
            'as'   => 'web_product_freeship_page',
            'uses' => 'ProductController@freeshipProduct',
        ]);

        //Routes cho trang flashsale
        $router->get('/flashsale', [
            'as'   => 'web_product_flashsale_page',
            'uses' => 'ProductController@freeshipProduct',
        ]);

        //Routes cho chi tiết từng chiến dịch Khuyến mãi
        $router->get('/sale/{slug}-{id}', [
            'as'   => 'web_product_sale_campaign',
            'uses' => 'ProductController@saleDetail',
        ])->where([
            'slug' => '[a-zA-Z0-9_\-]+',
            'id' => '[0-9]+'
        ]);

        //Routes cho trang Promotion
        $router->get('/promotion', [
            'as'   => 'web_product_promotion_page',
            'uses' => 'ProductController@promotionIndexProduct',
        ]);
        // Route Promotion đi theo từng danh mục sản phẩm
        $router->get('/promotion/{slug}-cp{id}', [
            'as'   => 'web_product_promotion_detail_cate',
            'uses' => 'ProductController@promotionDetail',
        ])->where([
            'slug' => '[a-zA-Z0-9_\-]+',
            'id' => '[0-9]+'
        ]);

        // Routes cho trang Hot Trending
        $router->get('/hottrend', [
            'as'   => 'web_product_hottrend_page',
            'uses' => 'ProductController@hotTrendProduct',
        ]);

        // Routes cho trang flashsale
        $router->get('/flashsale', [
            'as'   => 'web_product_flashsale_page',
            'uses' => 'ProductController@flashSaleProduct',
        ]);

        // Routes cho Tag
        $router->get('/tag/{tag}', [
            'as'   => 'web_tag_product_page',
            'uses' => 'ProductController@searchProductByTag',
        ])->where([
            'tag' => '[a-zA-Z0-9_\-]+',
        ]);

        //Route cho collection

        $router->get('/{slug}-col{collection_id}', [
            'as'   => 'web_collection_page',
            'uses' => 'ProductController@collectionIndex',
        ])->where([
            'slug' => '[a-zA-Z0-9_\-]+',
            'collection_id' => '[0-9]+'
        ]);


        $router->get('/{slug?}-brand{manufacturer_id?}', [
            'as'   => 'web_manufacturer_page',
            'uses' => 'ProductController@manufacturerIndex',
        ])->where([
            'slug' => '[a-zA-Z0-9_\-]+',
            'manufacturer_id' => '[0-9]+'
        ]);

        $router->get('/topsearch', [
            'as'   => 'web_top_search_index_page',
            'uses' => 'ProductController@topSearchProduct',
        ]);
    }
);
