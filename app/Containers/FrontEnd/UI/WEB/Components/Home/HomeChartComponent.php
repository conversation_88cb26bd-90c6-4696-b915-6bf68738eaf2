<?php

namespace App\Containers\FrontEnd\UI\WEB\Components\Home;

use App\Containers\Pos\Actions\ExchangeRate\GetGeneralRateAction;
use App\Containers\Pos\Actions\ExchangeRate\SortGeneralRateAction;
use App\Containers\Product\Tasks\ProductCategory\FindProductByIdCateTask;
use App\Containers\BaseContainer\UI\WEB\Components\FrontEnd\BaseComponent;

class HomeChartComponent extends BaseComponent
{
    public $generalGoldRates;
    public $products;
    public function render()
    {
        $generalRates = app(GetGeneralRateAction::class)->run();
        $sortGoldRates = app(SortGeneralRateAction::class)->run($generalRates);
        $this->generalGoldRates = $sortGoldRates;

        $products = app(FindProductByIdCateTask::class)->run(1733	, $this->currentLang, 10, 0);
        $this->products = $products;
        return $this->returnView('frontend', 'version2.home.components.home-chart-v2');
    }
} 