<?php

namespace App\Containers\FrontEnd\UI\WEB\Components\Home;

use App\Containers\Distributor\Enums\DistributorStatus;
use App\Containers\Distributor\Actions\FrontEnd\GetDistributorListAction;
use App\Containers\BaseContainer\UI\WEB\Components\FrontEnd\BaseComponent;

class HomeStoreComponent extends BaseComponent
{
    public $getDistributorListAction;
    public $distributors;

    public function __construct(GetDistributorListAction $getDistributorListAction)
    {
        parent::__construct();
        $this->getDistributorListAction = $getDistributorListAction;
    }
    public function render()
    {
        $distributors = $this->getDistributorListAction->skipCache()->run(
            ['status' => DistributorStatus::ACTIVE],
            ['sort_order' => 'asc','created_at' => 'desc', 'id' => 'desc'],
            0,
            true,
            $this->currentLang,
            [],
            1
        );
        $this->distributors = $distributors;
        return $this->returnView('frontend', 'version2.home.components.home-store-v2');
    }
} 