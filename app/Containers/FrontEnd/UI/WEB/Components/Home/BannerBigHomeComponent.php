<?php

/**
 * @ Created by: VSCode
 * @ Author: Oops!Memory - OopsMemory.com
 * @ Create Time: 2021-07-31 16:10:44
 * @ Modified by: Oops!Memory - OopsMemory.com
 * @ Modified time: 2022-08-23 17:41:43
 * @ Description: Happy Coding!
 */

namespace App\Containers\FrontEnd\UI\WEB\Components\Home;

use App\Containers\Banner\Actions\GetAvailableBannerByPositionAction;
use App\Containers\BaseContainer\UI\WEB\Components\FrontEnd\BaseComponent;

class BannerBigHomeComponent extends BaseComponent
{
    public $bigHomeBanners;
    public $getAvailableBannerByPositionAction;

    public function __construct(GetAvailableBannerByPositionAction $getAvailableBannerByPositionAction)
    {
        parent::__construct();
        $this->getAvailableBannerByPositionAction = $getAvailableBannerByPositionAction;
    }

    public function render()
    {
        $this->bigHomeBanners = $this->getAvailableBannerByPositionAction->run(
            ['big_home'],
            $is_mobile = $this->isMobile,
            ['desc'],
            ['sort_order' => 'ASC'],
            $this->currentLang
        );
        return $this->returnView('frontend','version2.home.components.banner-big-home-v2');
    }
}
