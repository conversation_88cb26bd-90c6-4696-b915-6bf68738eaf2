<?php

namespace App\Containers\FrontEnd\UI\WEB\Components\Home;

use App\Containers\Banner\Actions\GetAvailableBannerByPositionAction;
use App\Containers\BaseContainer\UI\WEB\Components\FrontEnd\BaseComponent;

class HomeBrandComponent extends BaseComponent
{
    public $brand;
    public $getAvailableBannerByPositionAction;

    public function __construct(GetAvailableBannerByPositionAction $getAvailableBannerByPositionAction)
    {
        parent::__construct();
        $this->getAvailableBannerByPositionAction = $getAvailableBannerByPositionAction;
    }
    public function render()
    {
        $this->brand = $this->getAvailableBannerByPositionAction->run(
            ['home_brand_mh'],
            $is_mobile = $this->isMobile,
            ['desc'],
            ['sort_order' => 'ASC'],
            $this->currentLang,
            1
        );
        return $this->returnView('frontend', 'version2.home.components.home-brand-v2');
    }
} 