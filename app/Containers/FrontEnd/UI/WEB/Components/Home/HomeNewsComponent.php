<?php

namespace App\Containers\FrontEnd\UI\WEB\Components\Home;

use App\Containers\News\Actions\FrontEnd\GetNewsListAction;
use App\Containers\BaseContainer\UI\WEB\Components\FrontEnd\BaseComponent;

class HomeNewsComponent extends BaseComponent
{
    public $getNewsListAction;
    public $newsBottomHome;

    public function __construct(GetNewsListAction $getNewsListAction)
    {
        parent::__construct();
        $this->getNewsListAction = $getNewsListAction;
    }
    public function render()
    {
        $this->newsBottomHome = $this->getNewsListAction->run(
            ['status' => 2, 'is_hot' => 1, 'is_home' => 1],
            ['sort_order' => 'asc', 'created_at' => 'desc'],
            $limit = 4,
            $skipPagination = true,
            $this->currentLang
        );

        return $this->returnView('frontend', 'version2.home.components.home-news-v2');
    }
} 