<?php

/**
 * @ Created by: VSCode
 * @ Author: Oops!Memory - OopsMemory.com
 * @ Create Time: 2021-07-31 16:10:44
 * @ Modified by: Oops!Memory - OopsMemory.com
 * @ Modified time: 2022-08-23 17:41:43
 * @ Description: Happy Coding!
 */

namespace App\Containers\FrontEnd\UI\WEB\Components\Home;

use Apiato\Core\Foundation\Facades\Apiato;
use App\Containers\Product\Enums\ProductStatus;
use App\Containers\BaseContainer\UI\WEB\Components\FrontEnd\BaseComponent;

class HomeProductMainComponent extends BaseComponent
{
    public $products;
    public function __construct()
    {
        parent::__construct();
    }

    public function render()
    {
        $products = Apiato::call('Product@Admin\GetAllProductsAction', [['is_home' => ProductStatus::IS_HOME], ['with_relationship' => ['categories', 'categories.desc']], $limit = 20]);
        $this->products = $products;
        return $this->returnView('frontend','version2.home.components.home-product-main-v2');
    }
}
