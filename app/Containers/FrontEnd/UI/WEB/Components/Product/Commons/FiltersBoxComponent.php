<?php

/**
 * @ Created by: VSCode
 * @ Author: Oops!Memory - OopsMemory.com
 * @ Create Time: 2021-07-31 16:10:44
 * @ Modified by: Oops!Memory - OopsMemory.com
 * @ Modified time: 2022-09-08 14:17:27
 * @ Description: Happy Coding!
 */

namespace App\Containers\FrontEnd\UI\WEB\Components\Product\Commons;

use Apiato\Core\Traits\ResponseTrait;
use App\Containers\Product\Models\Product;
use App\Containers\Filter\Enums\FilterType;
use App\Containers\Category\Models\Category;
use App\Containers\Filter\Enums\FilterStatus;
use Apiato\Core\Transformers\Serializers\ArraySerializer;
use App\Containers\Personalish\Services\ListingProductService;
use App\Containers\Filter\Actions\GetFiltersGroupByFilterGroupsAction;
use App\Containers\Personalish\Actions\GetFilterForProductListingAction;
use App\Containers\BaseContainer\UI\WEB\Components\FrontEnd\BaseComponent;
use App\Containers\Personalish\UI\API\Transformers\FrontEnd\Filter\FilterGroupTransformer;

class FiltersBoxComponent extends BaseComponent
{    
    use ResponseTrait;
    public $filterParams, $listingProductService, $filterGroups, $category, $products;

    public function __construct(?Category $category, $products)
    {
        $this->category = $category;
        $this->products = $products;
        parent::__construct();
    }

    public function render()
    {
        $filterGroups = app(GetFilterForProductListingAction::class)->skipCache(false)->run([], $this->currentLang, ['status' => FilterStatus::ACTIVE]);

        if (!empty($filterGroups) && !$filterGroups->IsEmpty()) {

            // Lọc các filter theo từng loại
            $this->filterParams = [
                FilterType::OPTION => app(ListingProductService::class)->findFilters([], FilterType::OPTION),
                FilterType::RANGE_OPTION => app(ListingProductService::class)->findFilters([], FilterType::RANGE_OPTION, '-')
            ];

            // Lấy về toàn bộ filters và được group by id của filter_group
            $filtersGroupByGroup = app(GetFiltersGroupByFilterGroupsAction::class)->run($filterGroups->pluck('filter_group_id')->toArray(), $this->currentLang);

            // Transform dữ liệu để push xuống client
            $filtersGroupByGroup  = $this->transform($filtersGroupByGroup, new FilterGroupTransformer($this->filterParams), [], [], 'filters', new ArraySerializer(), false);

            // Array merge với mảng các tùy chọn lọc khi khời tạo hàm
            $filterGroups = $filtersGroupByGroup['filters'];
        }
        $this->filterGroups = $filterGroups;
        return $this->returnView('frontend', 'product.partials.filters-box', '', [
        ]);
    }
}
