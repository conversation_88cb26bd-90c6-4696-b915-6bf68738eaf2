<?php

/**
 * @ Created by: VSCode
 * @ Author: Oops!Memory - OopsMemory.com
 * @ Create Time: 2021-07-31 16:10:44
 * @ Modified by: Oops!Memory - OopsMemory.com
 * @ Modified time: 2021-10-07 20:06:41
 * @ Description: Happy Coding!
 */

namespace App\Containers\FrontEnd\UI\WEB\Components\Product\Collection;

use App\Containers\Collection\Models\Collection;
use App\Containers\BaseContainer\UI\WEB\Components\FrontEnd\BaseComponent;

class AnotherCollectionBoxComponent extends BaseComponent
{
    public $collections;
    public function __construct($collections)
    {
        $this->collections = $collections;
        parent::__construct();
    }

    public function render()
    {
        return $this->returnView('frontend', 'version2.collection.component.another-collection');
    }
}
