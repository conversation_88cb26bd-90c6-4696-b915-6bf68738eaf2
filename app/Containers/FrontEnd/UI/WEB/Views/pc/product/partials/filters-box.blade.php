<div class="product-tabslist">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="product-tabs">
                    <div class="product-tabs--left">
                        <div class="btn-opfillter--mb">
                            <div class="_icon">
                                <img src="{{ asset('template-op2/images/ic-fillter.png') }}" alt="">
                            </div>
                            {{ __('site.boLoc') }}
                        </div>
                        <div class="totla--page">
                            1-11 of {{ $products->total() }} items
                        </div>
                        <div class="--line"></div>
                        <div class="product-tabs--menu ">
                            <div class="btn-closefillter--mb">
                                <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32" fill="none">
                                    <path
                                        d="M17.8643 16L32 30.1562L30.1562 32L16 17.8643L1.84379 32L0 30.1562L14.1357 16L0 1.84379L1.84379 0L16 14.1357L30.1562 0L32 1.84379L17.8643 16Z"
                                        fill="#C3C3C3"/>
                                </svg>
                            </div>
                            @foreach ($filterGroups as $key => $filterGroup)
                                @if ($key < 25)
                                    <div class="items">
                                        <a href="javascript:Void(0);" class="product-tabs--title">
                                            {{ $filterGroup['display_name'] }}
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                width="12"
                                                height="6"
                                                viewBox="0 0 12 6"
                                                fill="none"
                                            >
                                                <path
                                                    d="M1 1L6 5L11 1"
                                                    stroke="#4F4F4F"
                                                    stroke-width="1.5"
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                />
                                            </svg>
                                        </a>
                                        <div class="items--tabsbody">
                                            @if (isset($filterGroup['values']['filter_group_value']) && !empty($filterGroup['values']['filter_group_value']))
                                                @foreach ($filterGroup['values']['filter_group_value'] as $filterValue)
                                                    <div class="items-checkbox">
                                                        <input name="filter_group_value[]" value="{{ $filterValue['query_value'] }}"
                                                               id="filter_group_value{{ $filterValue['query_value'] }}" type="checkbox"
                                                               hidden>
                                                        <label for="filter_group_value{{ $filterValue['query_value'] }}">
                                                            <span></span>
                                                            {{ $filterValue['display_value'] }}
                                                        </label>
                                                    </div>
                                                @endforeach
                                            @endif
                                        </div>
                                    </div>
                                @endif
                            @endforeach
                        </div>
                    </div>
                    <div class="filter-select">
                        <div class="txt">
                            {{ __('site.locTheo') }}:
                        </div>
                        <select name="sort_by" id="sort_by">
                            <option value="newest">
                                {{ __('site.moiNhat') }}
                            </option>
                            <option value="oldest">
                                {{ __('site.cuNhat') }}
                            </option>
                            <option value="price_asc">
                                {{ __('site.giaTangDan') }}
                            </option>
                        </select>
                    </div>
                </div>
                @include('frontend::pc.version2.product.pages.list.product-list-ajax')
            </div>
        </div>
    </div>
</div>

@include('frontend::pc.version2.news.components.partials.load-more-js')

@push('js_bot_all')
    <script>
        // Lắng nghe sự kiện thay đổi của tất cả checkbox
        $('input[type="checkbox"]').on('change', function () {
            const selectedValues = getSelectedFilterValues();
        });
        $('#sort_by').on('change', function () {
            const selectedValues = getSelectedFilterValues();
        });

        // Hàm lấy các giá trị của checkbox filter_group_value[] được tích chọn
        function getSelectedFilterValues(returnObject = false) {
            const selectedValues = [];

            // Lấy tất cả checkbox có name="filter_group_value[]" được tích chọn
            $('input[name="filter_group_value[]"]:checked').each(function () {
                selectedValues.push($(this).val());
            });

            let _token = $('meta[name="csrf-token"]').attr('content');
            let params = {
                filter_group_value: selectedValues,
                sort_by: $('#sort_by').val(),
                category_id: {{ $category->category_id }},
                '_token': _token,
                limit: 2
            };
            if (returnObject) {
                return params;
            }

            $.ajax({
                headers: {
                    'X-CSRF-TOKEN': _token
                },
                url: '{{ route('web_product_filter_page') }}',
                type: 'POST',
                data: params,
                success: function (response) {
                    $('.product--all').remove();
                    $('.product--panigation').remove();
                    $('.product-tabs').after(response);
                }
            });
            return selectedValues;
        }

        // Hàm lấy các giá trị dưới dạng object với key-value
        function getSelectedFilterValuesAsObject() {
            const selectedValues = {};

            $('input[name="filter_group_value[]"]:checked').each(function () {
                const value = $(this).val();
                const id = $(this).attr('id');
                selectedValues[id] = value;
            });

            return selectedValues;
        }

        // Hàm lấy các giá trị dưới dạng array với thông tin chi tiết
        function getSelectedFilterValuesDetailed() {
            const selectedValues = [];

            $('input[name="filter_group_value[]"]:checked').each(function () {
                const $checkbox = $(this);
                const $label = $('label[for="' + $checkbox.attr('id') + '"]');

                selectedValues.push({
                    id: $checkbox.attr('id'),
                    value: $checkbox.val(),
                    displayText: $label.text().trim(),
                    element: $checkbox[0]
                });
            });

            return selectedValues;
        }

        // Ví dụ sử dụng:
        // Lấy danh sách các giá trị được chọn
        // const selectedValues = getSelectedFilterValues();
        // console.log('Selected values:', selectedValues);

        // Lấy dưới dạng object
        // const selectedValuesObj = getSelectedFilterValuesAsObject();
        // console.log('Selected values object:', selectedValuesObj);

        // Lấy thông tin chi tiết
        // const selectedValuesDetailed = getSelectedFilterValuesDetailed();
        // console.log('Selected values detailed:', selectedValuesDetailed);
    </script>
@endpush
