<div class="home-chart">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="chart--fieldset">
                    <div class="fieldset--title">
                        {{__('site.bieuDoVang')}}
                    </div>
                    <div class="chart--fieldset---list">
                        <div class="_left">
                            <div class="--title">
                                
                            </div>
                            <div class="price--discount">
                                <div class="--number">
                                   
                                </div>
                                <div class="--number--discount">
                                    <span class="js-number--discount"></span>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="15" height="16" viewBox="0 0 15 16" fill="none">
                                            <path
                                                d="M1.65625 8.01159L7.98633 2L14.3164 8.01159"
                                                stroke="#EDD0A7"
                                                stroke-width="2"
                                                stroke-miterlimit="10"
                                            />
                                            <path
                                                d="M7.98828 2L7.98828 15.2122"
                                                stroke="#EDD0A7"
                                                stroke-width="2"
                                                stroke-miterlimit="10"
                                            />
                                        </svg>
                                </div>
                            </div>
                            <canvas id="myChart" class="chart-canvas"></canvas>
                        </div>
                        <div class="_right">
                            <div class="--title">
                                {{__('site.loaiVang')}}
                            </div>
                            <div class="pricegold--input">
                                <select  id="select-box-gold-type" x-onchange="getGolRateByType($(this).val())">
                                    @foreach ($generalGoldRates as $item)
                                        <option value="{{ $item['maVang'] }}" data-type="{{ $item['tenVang'] }}">{{ $item['tenVang'] }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="chart--fieldset---box">
                                <div class="--title">
                                    {{__('xemTheo:')}}
                                </div>
                                <div class="chart--fieldset---check">
                                    <div class="--items">
                                        <input class="chart-radio" id="checkbox1" type="radio" hidden  value="hour" name="customRadioInline1">
                                        <label for="checkbox1" class="custom-label">
                                            {{__('1Ngay')}}
                                        </label>
                                    </div>
                                    <div class="--items">
                                        <input class="chart-radio" id="checkbox2" type="radio" hidden  value="week" name="customRadioInline1">
                                        <label for="checkbox2" class="custom-label">
                                            {{__('site.1Tuan')}}
                                        </label>
                                    </div>
                                    <div class="--items">
                                        <input class="chart-radio" id="checkbox3" type="radio" hidden  value="month" name="customRadioInline1" checked>
                                        <label for="checkbox3" class="custom-label">
                                            {{__('site.1Thang')}}
                                        </label>
                                    </div>
                                    <div class="--items">
                                        <input class="chart-radio" id="checkbox4" type="radio" hidden  value="3month" name="customRadioInline1">
                                        <label for="checkbox4" class="custom-label">
                                            {{__('site.3Thang')}}
                                        </label>
                                    </div>
                                    <div class="--items">
                                        <input class="chart-radio" id="checkbox5" type="radio" hidden  value="6month" name="customRadioInline1">
                                        <label for="checkbox5" class="custom-label">
                                            {{__('site.6Thang')}}
                                        </label>    
                                    </div>
                                    <div class="--items">
                                        <input name="customRadioInline1" class="chart-radio" id="checkbox6" type="radio" hidden name="1" value="year">
                                        <label for="checkbox6" class="custom-label">
                                            {{__('site.12Thang')}}
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="chart--fieldset---unit">
                                <div class="--measure">
                                    {{__('site.donViTinh:')}} <br /> {{__('site.dong')}} / {{__('site.chi')}}
                                </div>
                                <div class="--explain">
                                    <div class="--sell-out">
                                        {{__('site.banRa')}}
                                    </div>
                                    <div class="--buy-in">
                                        {{__('site.muaVao')}}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="chart--table---title">
                    {{__('site.thiTruongVang')}}
                </div>
                <div class="chart--table">
                    <div class="_left">
                        <table class="chart--table-box">
                            <thead>
                                <tr>
                                    <th>{{__('site.loaiVang')}}</th>
                                    <th width="154">{{__('site.mua')}}</th>
                                    <th width="154">{{__('site.ban')}}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($generalGoldRates as $item)
                                    <tr>
                                        <td>{{ $item['tenVang'] }}</td>
                                        <td>
                                            <div class="number">
                                                {{ $item['tyGiaMua'] > 1 ? trim(number_format($item['tyGiaMua'], 0, ',', '.')) : '' }}
                                                @if ($item['tyGiaMuaTrend'] == 'up' && $item['tyGiaMua'] > 1)
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="15" height="20" viewBox="0 0 15 20" fill="none">
                                                        <path
                                                            d="M0.597656 9.10916L7.33508 2.00024L14.0725 9.10916"
                                                            stroke="#2ED151"
                                                            stroke-width="1.5"
                                                            stroke-miterlimit="10"
                                                        />
                                                        <path
                                                            d="M7.33594 2.00024V19.0734"
                                                            stroke="#2ED151"
                                                            stroke-width="1.5"
                                                            stroke-miterlimit="10"
                                                        />
                                                    </svg>
                                                @elseif($item['tyGiaMuaTrend'] == 'down' && $item['tyGiaMua'] > 1)
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="15" height="20" viewBox="0 0 15 20" fill="none">
                                                        <path
                                                            d="M14.0703 10.8894L7.33289 17.9983L0.595465 10.8894"
                                                            stroke="#922E38"
                                                            stroke-width="1.5"
                                                            stroke-miterlimit="10"
                                                        />
                                                        <path
                                                            d="M7.33203 17.9983L7.33203 0.925088"
                                                            stroke="#922E38"
                                                            stroke-width="1.5"
                                                            stroke-miterlimit="10"
                                                        />
                                                    </svg>
                                                @endif
                                            </div>
                                        </td>
                                        <td>
                                            <div class="number">
                                                {{ $item['tyGiaBan'] > 1 ? trim(number_format($item['tyGiaBan'], 0, ',', '.')) : '' }}
                                                @if ($item['tyGiaBanTrend'] == 'up' && $item['tyGiaBan'] > 1)
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="15" height="20" viewBox="0 0 15 20" fill="none">
                                                        <path
                                                            d="M0.597656 9.10916L7.33508 2.00024L14.0725 9.10916"
                                                            stroke="#2ED151"
                                                            stroke-width="1.5"
                                                            stroke-miterlimit="10"
                                                        />
                                                        <path
                                                            d="M7.33594 2.00024V19.0734"
                                                            stroke="#2ED151"
                                                            stroke-width="1.5"
                                                            stroke-miterlimit="10"
                                                        />
                                                    </svg>
                                                @elseif($item['tyGiaBanTrend'] == 'down' && $item['tyGiaBan'] > 1)
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="15" height="20" viewBox="0 0 15 20" fill="none">
                                                        <path
                                                            d="M14.0703 10.8894L7.33289 17.9983L0.595465 10.8894"
                                                            stroke="#922E38"
                                                            stroke-width="1.5"
                                                            stroke-miterlimit="10"
                                                        />
                                                        <path
                                                            d="M7.33203 17.9983L7.33203 0.925088"
                                                            stroke="#922E38"
                                                            stroke-width="1.5"
                                                            stroke-miterlimit="10"
                                                        />
                                                    </svg>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                            <tfoot>
                                <tr>
                                    <td colspan="3">
                                        <div class="table--footer">
                                            <p>
                                                {{__('site.capNhatLuc:')}} {{ \Carbon\Carbon::now()->format('H:i d/m/Y') }}
                                            </p>
                                            <p>
                                                {{__('site.donViTinh:')}} {{__('site.dong')}} / {{__('site.chi')}}
                                            </p>
                                        </div>
                                    </td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                    <div class="_right">
                        <div class="account-content">
                            <ul class="nav nav-pills" id="pills-tab" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="pills-home-tab" data-bs-toggle="pill" data-bs-target="#pills-home" type="button" role="tab" aria-controls="pills-home" aria-selected="true">
                                            {{__('site.giaoDichMua')}}
                                        </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="pills-profile-tab" data-bs-toggle="pill" data-bs-target="#pills-profile" type="button" role="tab" aria-controls="pills-profile" aria-selected="false">
                                            {{__('site.giaoDichBan')}}
                                        </button>
                                </li>
                            </ul>
                            <div class="tab-content" id="pills-tabContent">
                                <div class="tab-pane fade show active" id="pills-home" role="tabpanel" aria-labelledby="pills-home-tab">
                                    <form action="">
                                        <div class="form--content">
                                            <div class="form--col12">
                                                <div class="form--row">
                                                    <div class="pricegold--quantity">
                                                        <div class="quantity--title">
                                                            {{__('site.sanPhamBan')}}
                                                        </div>
                                                        <div class="pricegold--input">
                                                            <select name="product_id" id="product_id" onchange="getPrice($(this).val())">
                                                                    @foreach ($products as $item)
                                                                        <option value="{{ $item->product_id }}" data-price="{{ number_format($item->product->price, 0, ',', '.') }}">{{ $item->product->desc->name }}</option>
                                                                    @endforeach
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="form--col">
                                                <div class="form--row">
                                                    <div class="pricegold--quantity">
                                                        <div class="quantity--title">
                                                            {{__('site.soLuong')}}
                                                        </div>
                                                        <div class="pricegold--input">
                                                            <select name="quantity" id="quantity">
                                                                @for ($i = 1; $i <= 10; $i++)
                                                                    <option value="{{ $i }}">{{ $i }}</option>
                                                                @endfor
                                                                </select>
                                                        </div>
                                                        <div class="--note">
                                                            {{__('site.soLuongToiDa:')}} 10 (chỉ)
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="form--col">
                                                <div class="form--row">
                                                    <div class="pricegold--quantity">
                                                        <div class="quantity--title">
                                                            {{__('site.thanhTien')}}
                                                        </div>
                                                        <div class="quantity--total">
                                                            <div class="number" id="js-price-chart">
                                                                {{ number_format($products[0]->product->price ?? 0, 0, ',', '.') }}
                                                            </div>
                                                            <span>
                                                                    {{__('site.dong')}}
                                                                </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="chart--table--btn">
                                            <div class="txt">
                                                Thời gian giữ giá mua: 16s
                                            </div>
                                            <div class="form--btn">
                                                <button type="submit" class="storesystem--btn">
                                                        {{__('site.mua')}}
                                                    </button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                                <div class="tab-pane fade" id="pills-profile" role="tabpanel" aria-labelledby="pills-profile-tab">
                                    <form action="">
                                        <div class="form--content">
                                            <div class="form--col12">
                                                <div class="form--row">
                                                    <div class="pricegold--quantity">
                                                        <div class="quantity--title">
                                                            {{__('site.sanPhamBan')}}
                                                        </div>
                                                        <div class="pricegold--input">
                                                            <select name="product_id_sell" id="product_id_sell" x-onchange="getPrice($(this).val())">
                                                                @foreach ($products as $item)
                                                                    <option value="{{ $item->product_id }}" data-price="{{ number_format($item->product->price, 0, ',', '.') }}">{{ $item->product->desc->name }}</option>
                                                                @endforeach
                                                                </select>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="form--col">
                                                <div class="form--row">
                                                    <div class="pricegold--quantity">
                                                        <div class="quantity--title">
                                                            {{__('site.soLuong')}}
                                                        </div>
                                                        <div class="pricegold--input">
                                                            <select name="quantity_sell" id="quantity_sell">
                                                                @for ($i = 1; $i <= 10; $i++)
                                                                    <option value="{{ $i }}">{{ $i }}</option>
                                                                @endfor
                                                                </select>
                                                        </div>
                                                        <div class="--note">
                                                            {{__('site.soLuongToiDa:')}} 10 ({{__('site.chi')}})
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="form--col">
                                                <div class="form--row">
                                                    <div class="pricegold--quantity">
                                                        <div class="quantity--title">
                                                            {{__('site.thanhTien')}}
                                                        </div>
                                                        <div class="quantity--total">
                                                            <div class="number" id="js-price-chart-sell">
                                                                {{ number_format($products[0]->product->price ?? 0, 0, ',', '.') }}
                                                            </div>
                                                            <span>
                                                                    {{__('site.dong')}}
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="chart--table--btn">
                                            <div class="txt">
                                                {{__('site.thoiGianGiuGiaMua:')}} 16s
                                            </div>
                                            <div class="form--btn">
                                                <button type="submit" class="storesystem--btn">
                                                        {{__('site.ban')}}
                                                    </button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@push('js_bot_all')
@endpush