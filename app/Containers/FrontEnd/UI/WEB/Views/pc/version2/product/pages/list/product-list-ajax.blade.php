@if ($products && $products->isNotEmpty())
    <div class="product--all _load-more_list">
        @foreach ($products as $product)
            <div class="product--col">
                <div class="product--items">
                    <a href="{{ route('web_product_detail_page', ['slug' => @$product->desc->slug, 'id' => $product->id]) }}" class="img">
                        <img src="{{ ImageURL::getImageUrl($product->image, 'product', 'social') }}" alt="">
                    </a>
                    <div class="product--body">
                        <h3 class="title">
                            <a href="{{ route('web_product_detail_page', ['slug' => @$product->desc->slug, 'id' => $product->id]) }}">
                                {{ $product->desc->name }}
                            </a>
                        </h3>
                        <div class="gold--infor">
                            {{ $product->desc->short_description }}
                        </div>
                        <div class="price">
                            {{ number_format($product->price) }}
                        </div>
                        <button class="add--cart" type="submit">
                            <svg xmlns="http://www.w3.org/2000/svg" width="43" height="42" viewBox="0 0 43 42" fill="none">
                                <path d="M32.2422 40.833H0.584914V14.2905H32.2422" stroke="#AE8751" stroke-width="1.17444"
                                      stroke-miterlimit="10"/>
                                <path
                                    d="M8.87572 16.6765V7.37212C8.87572 3.69604 12.243 0.68457 16.4301 0.68457C20.6171 0.68457 23.9844 3.67527 23.9844 7.37212V16.6765"
                                    stroke="#AE8751" stroke-width="1.17444" stroke-miterlimit="10"/>
                                <path
                                    d="M31.9793 37.1113C26.3145 37.1113 21.7222 32.8061 21.7222 27.4953C21.7222 22.1846 26.3145 17.8794 31.9793 17.8794C37.6441 17.8794 42.2363 22.1846 42.2363 27.4953C42.2363 32.8061 37.6441 37.1113 31.9793 37.1113Z"
                                    fill="#AE8751"/>
                                <path d="M31.9961 20.7104V34.2309" stroke="white" stroke-width="1.17444"
                                      stroke-miterlimit="10"/>
                                <path d="M24.7793 27.478H39.2012" stroke="white" stroke-width="1.17444" stroke-miterlimit="10"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        @endforeach
    </div>
    <div class="product--panigation">
        <div class="txt">
            {{ $products->count() }}/ {{ $products->total() }}  {{ __('site.sanPham') }}
        </div>
        <div class="box--btn">
            <a href="javascript:" data-next-url="{{ route('web_product_filter_page', ['page' => 2]) }}"
               class="btn-links _btn_load-more">{{ __('site.xemToanBoSanPham') }}</a>
        </div>
    </div>
@endif
