@extends('basecontainer::frontend.pc.layouts-v2.default')

@section('content')
@if(!empty($data['banner']))
<div class="bannerslide-main">
    <div class="swiper bannerslide--slide">
        <div class="swiper-wrapper">
            @foreach($data['banner'] as $item)
            <div class="swiper-slide">
                <div class="bannerslide--items" style="background-image: url('{{ $item->desc->image ? \ImageURL::getImageUrl($item->desc->image, 'banner', 'largex2') : asset('template-op2/images/img-kgb.jpg') }}');">
                    <div class="container">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="bannerslide--other">
                                    <div class="box-title">
                                        <h2 class="title">
                                            {{ $item->desc->name }}
                                        </h2>
                                        <div class="sub_title">
                                            {{ $item->desc->sub_name }}
                                        </div>
                                        <div class="desc">
                                            {!! $item->desc->short_description !!}
                                        </div>
                                    </div>
                                    <div class="box--btn">
                                        <a href="{{ $item->desc->link }}" class="btn-links">
                                            {{__('site.xemToanBoSanPham')}}
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
        <div class="producbox--panigation">
            <div class="swiper-pagination swiper-pagination-bannerslide"></div>
        </div>
    </div>
</div>
@endif
@if(!empty($data['banner_middle']))
<div class="bannerslide-main bannerslide-boxstyle2">
    <div class="swiper bannerslide--slide2">
        <div class="swiper-wrapper">
            @foreach($data['banner_middle'] as $item) 
            <div class="swiper-slide">
                <div class="bannerslide--items" style="background-image: url('{{ $item->desc->image ? \ImageURL::getImageUrl($item->desc->image, 'banner', 'largex2') : asset('template-op2/images/img-kgb.jpg') }}');">
                    <div class="container">
                        <div class="row">
                            <div class="col-lg-12  justify-content-end d-flex">
                                <div class="bannerslide--other">
                                    <div class="box-title">
                                        <h2 class="title">
                                            {{ $item->desc->name }}
                                        </h2>
                                        <div class="sub_title">
                                            {{ $item->desc->sub_name }}
                                        </div>
                                        <div class="desc">
                                            {!! $item->desc->short_description !!}
                                        </div>
                                    </div>
                                    <div class="box--btn">
                                        <a href="{{ $item->desc->link }}" class="btn-links">
                                            {{__('site.xemToanBoSanPham')}}
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
        <div class="producbox--panigation">
            <div class="swiper-pagination swiper-pagination-bannerslide2"></div>
        </div>
    </div>
</div>
@endif
@if(!empty($data['collections']))
<div class="introchild-collections">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="box-title">
                    <h2 class="title">
                        {{__('site.cacBoSuuTap')}}
                    </h2>
                    <div class="desc">
                        {{__('site.cheTacTuyetPhamTuBaoTinManhHai')}}
                    </div>
                </div>
                <div class="collections--list">
                    <div class="swiper collections--slide">
                        <div class="swiper-wrapper">
                            @foreach($data['collections'] as $item)
                            <div class="swiper-slide">
                                <a href="" class="collections--items">
                                    <img src="{{ $item->image ? \ImageURL::getImageUrl($item->image, 'banner', 'largex2') : asset('template-op2/images/img-colec1.jpg') }}" alt="">
                                    <span class="collections--body">
                                        <span class="title">
                                            {{ $item->desc->name }}
                                        </span>
                                        <span class="btn-links">
                                            {{__('site.xemToanBoSanPham')}}
                                        </span>
                                    </span>
                                </a>
                            </div>
                            @endforeach
                        </div>
                    </div>
                    <div class="producbox--panigation">
                        <div class="swiper-pagination swiper-pagination-collections"></div>
                    </div>
                    <div class="producbox--btn">
                        <div class="swiper-button-next swiper-button-next-collections">
                            <svg xmlns="http://www.w3.org/2000/svg" width="10" height="16" viewBox="0 0 10 16" fill="none">
                                            <path
                                                fill-rule="evenodd"
                                                clip-rule="evenodd"
                                                d="M1.92383 15.209L0.675545 13.9607L6.91696 7.71929L0.675546 1.47787L1.92383 0.229586L8.78933 7.09509C8.78937 7.09513 8.78939 7.09514 8.16524 7.71929L8.78933 7.09509L9.41353 7.71929L1.92383 15.209Z"
                                                fill="#4F4F4F"
                                            />
                                        </svg>
                        </div>
                        <div class="swiper-button-prev swiper-button-prev-collections">
                            <svg xmlns="http://www.w3.org/2000/svg" width="10" height="16" viewBox="0 0 10 16" fill="none">
                                            <path
                                                fill-rule="evenodd"
                                                clip-rule="evenodd"
                                                d="M8.07617 15.209L9.32445 13.9607L3.08304 7.71929L9.32445 1.47787L8.07617 0.229586L1.21067 7.09509C1.21063 7.09513 1.21061 7.09514 1.83476 7.71929L1.21067 7.09509L0.586473 7.71929L8.07617 15.209Z"
                                                fill="#4F4F4F"
                                            />
                                        </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endif
@endsection

@push('js_bot_all')
<script>
 
  </script>
@endpush

@section('css')
@endsection