<!-- banner home -->
{{-- <div class="home-banner">
    <div class="swiper-container banner-slider">
        <div class="swiper-wrapper">
            @foreach ($bigHomeBanners as $banner)
                <div class="swiper-slide">
                    <a href="{{$banner->desc->link}}"><img class="img-fluid lazy" data-src="{{ \ImageURL::getImageUrl($banner->desc->image, 'banner', 'super_large') }}" src="{{ \ImageURL::getImageUrl($banner->desc->image, 'banner', 'super_large') }}" alt="{{$banner->desc->name}}" /></a>
                </div>
            @endforeach
        </div>

        <div class="swiper-pagination"></div>

        <div class="custom-slide-button red-btn">
            <div class="custom-button-prev">
                <img class="img-fluid" src="{{ asset('template/images/common/red-nav-prev.png') }}" alt="" />
            </div>
            <div class="custom-button-next">
                <img class="img-fluid" src="{{ asset('template/images/common/red-nav-next.png') }}" alt="" />
            </div>
        </div>
    </div>
</div> --}}
<div class="banner-main">
    <div class="img">
        @if(!empty($bigHomeBanners[0]->desc->image))
            <img src="{{ \ImageURL::getImageUrl($bigHomeBanners[0]->desc->image, 'banner', 'super_large') }}" alt="{{$bigHomeBanners[0]->desc->name}}">
        @else
            <img src="{{ asset('template-op2/images/img-banner.jpg') }}" alt="">
        @endif
    </div>
</div>
<!-- end banner home -->
