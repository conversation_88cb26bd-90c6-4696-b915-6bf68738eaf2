@push('js_bot_all')
    <script>
        $(document).ready(function () {
            $(document).on('click', '._btn_load-more', function (e) {
                e.preventDefault();
                let _this = $(this);
                _this.prop('disabled', true);
                // _this.addClass('disabled');
                let nextUrl = _this.attr('data-next-url') || '';
                if (!nextUrl) {
                    return;
                }

                let method = 'get';
                let params = {};
                if (typeof getSelectedFilterValues === 'function') {
                    method = 'post';
                    params = getSelectedFilterValues(true);
                }
                console.log(params)

                $[method](nextUrl, params).done(function (response) {
                    if (response.error) {
                        return;
                    }
                    let respHtml = response.data.html ?? response;
                    let nextPageUrl = $(respHtml).find('._btn_load-more').attr('data-next-url') || response.data.next_url || '';
                    console.log(nextPageUrl)
                    if (nextPageUrl) {
                        _this.attr('data-next-url', nextPageUrl);
                        _this.prop('disabled', false);
                        // _this.removeClass('disabled');
                    } else {
                        _this.remove();
                    }
                    let data = $(respHtml).find('._load-more_list').html();
                    console.log(data)
                    $('._load-more_list').append(data);
                });
            });
        });
    </script>
@endpush
