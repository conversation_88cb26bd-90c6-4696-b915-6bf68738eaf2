@extends('basecontainer::frontend.pc.layouts-v2.default')

@section('content')
    <main class="">
        <div class="bannerslide-main">
            <div class="swiper bannerslide--slide">
                <div class="swiper-wrapper">
                    @foreach($data['banner'] ?? [] as $item)
                    <div class="swiper-slide">
                        <div class="bannerslide--items style--2" style="background-image: url('{{ $item->desc->image ? \ImageURL::getImageUrl($item->desc->image, 'banner', 'largex2') : asset('template-op2/images/img-kgb.jpg') }}');">
                            <div class="container">
                                <div class="row align-items-center">
                                    <div class="col-md-6">
                                        <div class="bannerslide--other">
                                            <div class="box-title">
                                                {!! $item->desc->name !!}
                                                <div class="desc">
                                                    {!! $item->desc->short_description !!}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <h1 class="goldmain-title">
                                            {{ $item->desc->sub_name }}
                                        </h1>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
                <div class="producbox--panigation">
                    <div class="swiper-pagination swiper-pagination-bannerslide"></div>
                </div>
            </div>
        </div>
        @php
        $banner_middle = $data['banner_middle'][0];
        @endphp
        <div class="block-virtue">
            <div class="container">
                <div class="row a">
                    <div class="col-md-6">
                        <div class="virtue--content">
                            <img src="{{ asset('template-op2/images/logo-title2.png') }}" alt="">
                            <div class="line"></div>
                            <div class="desc">
                                {!! $banner_middle->desc->short_description !!}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="virtue--video">
                            <div class="virtue--other video--other">
                                <div onclick="playVideo(this,'{{ $banner_middle->desc->link }}')"
                                    class="buttonclick-autoplay" style="background-image: url('{{ $banner_middle->desc->image ? \ImageURL::getImageUrl($banner_middle->desc->image, 'banner', 'largex2') : asset('template-op2/images/img-poster.jpg') }}');">
                                    <span class="btn-links">
                                        {{__('site.xemVideo')}}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="block-pricegold">
            <div class="container">
                <div class="row">
                    <div class="col-lg-6">
                        <div class="pricegold--list">
                            <div class="swiper pricegold--slide">
                                <div class="swiper-wrapper">
                                    @for($i = 0; $i < 3; $i++)
                                    <div class="swiper-slide">
                                        <div class="box-title">
                                            <h2 class="title">
                                                Kim gia Bảo Tứ quý
                                            </h2>
                                            <div class="sub_title">
                                                Đồng vàng
                                            </div>
                                        </div>
                                        <div class="img">
                                            <img src="{{ asset('template-op2/images/img-vdd.jpg') }}" alt="">
                                        </div>
                                        <div class="desc">
                                            Đồng vàng óng ánh bền lâu,<br /> Ngàn năm vẫn sáng một màu phú sang.
                                        </div>
                                    </div>
                                    @endfor
                                </div>
                            </div>
                            <div class="producbox--panigation">
                                <div class="swiper-pagination swiper-pagination-pricegold"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="pricegold--right">
                            <div class="pricegold--other">
                                <h3 class="title">
                                    {{__('site.muaNhanh')}}
                                </h3>
                                <form action="">
                                    <div class="pricegold--form">
                                        @php
                                        $total = 0;
                                        @endphp
                                        @foreach($products as $key => $product)
                                        @if($key < 3)
                                        @php
                                        $total += $product->price;
                                        @endphp
                                        <div class="pricegold--row">
                                            <div class="pricegold--col1">
                                                <img src="{{ $product->image ? \ImageURL::getImageUrl($product->image, 'product', 'largex2') : asset('template-op2/images/img-prod3.png') }}" alt="">
                                            </div>
                                            <div class="pricegold--col2">
                                                <div class="pricegold--box">
                                                    <div class="name">
                                                        {{ $product->desc->name }}
                                                    </div>
                                                    <div class="parameter">
                                                        {{ $product->desc->short_description }}
                                                    </div>
                                                    <div class="price">
                                                        {{ $product->price > 0 ? number_format($product->price, 0, '.', '.') : __('site.contact') }}
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="pricegold--col3">
                                                <div class="pricegold--quantity">
                                                    <div class="quantity--title">
                                                        {{__('site.dinhLuong')}} ({{__('site.chi')}})
                                                    </div>
                                                    <div class="pricegold--input">
                                                        <select name="quantity[]" id="quantity">
                                                            @for($i = 1; $i <= 10; $i++)
                                                            <option value="{{ $i }}">
                                                                {{ $i }} {{__('site.chi')}}
                                                            </option>   
                                                            @endfor
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="pricegold--col3">
                                                <div class="pricegold--quantity">
                                                    <div class="quantity--title">
                                                        {{__('site.soLuong')}}
                                                    </div>
                                                    <div class="pricegold--input">
                                                        <div class="inp-sl">
                                                            <span class="minus">
                                                                <svg xmlns="http://www.w3.org/2000/svg" width="6"
                                                                    height="2" viewBox="0 0 6 2" fill="none">
                                                                    <path
                                                                        d="M0.679688 0.792969H5.90429V1.98599H0.679688V0.792969Z"
                                                                        fill="#4F4F4F" />
                                                                </svg>
                                                            </span>
                                                            <input type="text" class="num" value="01" name="quantity[]" id="quantity">
                                                            <span class="plus">
                                                                <svg xmlns="http://www.w3.org/2000/svg" width="9"
                                                                    height="9" viewBox="0 0 9 9" fill="none">
                                                                    <path
                                                                        d="M0 3.82589H3.82589V0H5.01891V3.82589H8.8448V5.01891H5.01891V8.8448H3.82589V5.01891H0V3.82589Z"
                                                                        fill="#4F4F4F" />
                                                                </svg>
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        @endif
                                        @endforeach
                                    </div>
                                    <div class="pricegold--total">
                                        <div class="number">
                                            <div class="txt">
                                                {{__('site.tong')}}
                                            </div>
                                            <div class="total--price">
                                                {{ number_format($total, 0, '.', '.') }}
                                            </div>
                                        </div>
                                        <button class="storesystem--btn" type="submit">
                                            {{__('site.mua')}}
                                        </button>
                                    </div>
                                </form>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="block-product">
            <div class="container">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="box-title">
                            <h2 class="title">
                                {{__('site.kimGiaBao')}}
                            </h2>
                        </div>
                        <div class="product--list">
                            <div class="row gx-lg-4 row-cols-lg-4 row-cols-md-3 row-cols-2">
                                @foreach($products as $product)
                                <div class="col">
                                    <div class="product--items">
                                        <a href="" class="img">
                                            <img src="{{ $product->image ? \ImageURL::getImageUrl($product->image, 'product', 'largex2') : asset('template-op2/images/img-prod3.png') }}" alt="">
                                        </a>
                                        <div class="product--body">
                                            <h3 class="title">
                                                <a href="">
                                                    {{ $product->desc->name }}
                                                </a>
                                            </h3>
                                            <div class="gold--infor">
                                                {{ $product->desc->short_description }}
                                            </div>
                                            <div class="price">
                                                {{ $product->price > 0 ? number_format($product->price, 0, '.', '.') : __('site.contact') }}
                                            </div>
                                            <button class="add--cart" type="submit">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="43" height="42"
                                                    viewBox="0 0 43 42" fill="none">
                                                    <path d="M32.2422 40.833H0.584914V14.2905H32.2422" stroke="#AE8751"
                                                        stroke-width="1.17444" stroke-miterlimit="10" />
                                                    <path
                                                        d="M8.87572 16.6765V7.37212C8.87572 3.69604 12.243 0.68457 16.4301 0.68457C20.6171 0.68457 23.9844 3.67527 23.9844 7.37212V16.6765"
                                                        stroke="#AE8751" stroke-width="1.17444" stroke-miterlimit="10" />
                                                    <path
                                                        d="M31.9793 37.1113C26.3145 37.1113 21.7222 32.8061 21.7222 27.4953C21.7222 22.1846 26.3145 17.8794 31.9793 17.8794C37.6441 17.8794 42.2363 22.1846 42.2363 27.4953C42.2363 32.8061 37.6441 37.1113 31.9793 37.1113Z"
                                                        fill="#AE8751" />
                                                    <path d="M31.9961 20.7104V34.2309" stroke="white"
                                                        stroke-width="1.17444" stroke-miterlimit="10" />
                                                    <path d="M24.7793 27.478H39.2012" stroke="white"
                                                        stroke-width="1.17444" stroke-miterlimit="10" />
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                @endforeach
                            </div>
                        </div>
                        <div class="product--panigation">
                            <div class="txt">
                                11/92 sản phẩm
                            </div>
                            <div class="box--btn">
                                <a href="" class="btn-links">
                                    Xem toàn bộ sản phẩm
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <myfooter></myfooter>
    </main>
@endsection
