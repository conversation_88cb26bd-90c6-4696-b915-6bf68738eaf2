<div class="home-storesystem">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="box-title">
                    <h2 class="title">
                        <PERSON><PERSON> thống cửa hàng
                    </h2>
                </div>
                <div class="storesystem--list">
                    <div class="swiper storesystem--slide">
                        <div class="swiper-wrapper">
                            @foreach($distributors as $distributor)
                            <div class="swiper-slide">
                                <div class="storesystem--items">
                                    <div class="img">
                                        <img class="img-fluid swiper-lazy" data-src="{{ \ImageURL::getImageUrl($distributor->image, 'distributor', 'w800') }}" src="{{ asset('template-op2/images/img-systemstore.jpg') }}" alt="">
                                        <div class="swiper-lazy-preloader"></div>
                                    </div>
                                    <div class="storesystem--body">
                                        <div class="storesystem--base">
                                            <a href="" class="name">
                                                {{ $distributor->desc->name }}
                                            </a>
                                            <div class="--adress">
                                                {{ $distributor->desc->address }}
                                            </div>
                                        </div>
                                        <a href="" class="storesystem--btn">
                                            {!! __('site.datLichHen') !!}
                                        </a>
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div> 