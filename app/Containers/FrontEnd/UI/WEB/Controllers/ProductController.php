<?php

/**
 * @ Created by: VSCode
 * @ Author: Oops!Memory - OopsMemory.com
 * @ Create Time: 2021-07-07 10:28:53
 * @ Modified by: Oops!Memory - OopsMemory.com
 * @ Modified time: 2022-08-30 11:29:54
 * @ Description: Happy Coding!
 */

namespace App\Containers\FrontEnd\UI\WEB\Controllers;

use Apiato\Core\Traits\ResponseTrait;
use App\Containers\FrontEnd\UI\WEB\Controllers\Features\Product\Index;
use App\Containers\BaseContainer\UI\WEB\Controllers\BaseFrontEndController;
use App\Containers\FrontEnd\UI\WEB\Controllers\Features\Product\SaleDetail;
use App\Containers\FrontEnd\UI\WEB\Controllers\Features\Product\CategoryIndex;
use App\Containers\FrontEnd\UI\WEB\Controllers\Features\Product\DetailProduct;
use App\Containers\FrontEnd\UI\WEB\Controllers\Features\Product\SearchProduct;
use App\Containers\FrontEnd\UI\WEB\Controllers\Features\Product\CollectionIndex;
use App\Containers\FrontEnd\UI\WEB\Controllers\Features\Product\DeliveryProduct;
use App\Containers\FrontEnd\UI\WEB\Controllers\Features\Product\FreeShipProduct;
use App\Containers\FrontEnd\UI\WEB\Controllers\Features\Product\HotTrendProduct;
use App\Containers\FrontEnd\UI\WEB\Controllers\Features\Product\PromotionDetail;
use App\Containers\FrontEnd\UI\WEB\Controllers\Features\Product\FlashSaleProduct;
use App\Containers\FrontEnd\UI\WEB\Controllers\Features\Product\TopSearchProduct;
use App\Containers\FrontEnd\UI\WEB\Controllers\Features\Product\ManufacturerIndex;
use App\Containers\FrontEnd\UI\WEB\Controllers\Features\Product\SearchProductByTag;
use App\Containers\FrontEnd\UI\WEB\Controllers\Features\Product\PromotionIndexProduct;
use App\Containers\FrontEnd\UI\WEB\Controllers\Features\Product\CategoryWithinProducts;

class ProductController extends BaseFrontEndController
{
    use ResponseTrait;

    use Index,
        CategoryWithinProducts,
        CategoryIndex,
        DetailProduct,
        SearchProduct,
        DeliveryProduct,
        FreeShipProduct,
        PromotionIndexProduct,
        PromotionDetail,
        HotTrendProduct,
        FlashSaleProduct,
        SearchProductByTag,
        CollectionIndex,
        ManufacturerIndex,
        TopSearchProduct,
        SaleDetail;
}
