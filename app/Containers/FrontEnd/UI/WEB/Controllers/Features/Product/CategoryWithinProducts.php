<?php

/**
 * @ Created by: VSCode
 * @ Author: Oops!Memory - OopsMemory.com
 * @ Create Time: 2021-08-01 14:57:39
 * @ Modified by: Oops!Memory - OopsMemory.com
 * @ Modified time: 2022-09-19 18:23:04
 * @ Description: Happy Coding!
 */

namespace App\Containers\FrontEnd\UI\WEB\Controllers\Features\Product;

use Apiato\Core\Foundation\ImageURL;
use App\Containers\Banner\Actions\GetAvailableBannerByPositionAction;
use App\Containers\Category\Actions\SubActions\GetAllCategoriesForFrontEndSubAction;
use App\Containers\Category\Tasks\GetCategoryByIdTask;
use App\Containers\Collection\Actions\Admin\GetAllCollectionAction;
use App\Containers\FrontEnd\UI\WEB\Requests\Product\CategoryWithinProductsRequest;
use App\Containers\Product\Actions\FrontEnd\ProductListingAction;
use App\Containers\Product\Enums\ProductStatus;
use App\Containers\Product\Tasks\API\GetProductByFilterTask;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

trait CategoryWithinProducts
{
    public function logicVangTichTru($category_id, $slug)
    {
        $this->seoSetting();
        $data = [];
        $data['banner'] = app(GetAvailableBannerByPositionAction::class)->run(
            ['pos' => ['category_top'], 'categoryIds' => [$category_id]],
            $this->isMobile,
            ['desc'],
            ['sort_order' => 'ASC'],
            $this->currentLang,
            8
        );
        $data['banner_middle'] = app(GetAvailableBannerByPositionAction::class)->run(
            ['pos' => ['category_middle'], 'categoryIds' => [$category_id]],
            $this->isMobile,
            ['desc'],
            ['sort_order' => 'ASC'],
            $this->currentLang,
            1
        );
        $products = app(ProductListingAction::class)->run(
            [
                'published' => ProductStatus::YES,
                'cate_ids'  => [$category_id]
            ], // $filters
            ['created_at' => 'DESC'], // $orderby
            12, // $Limit
            true, // $skipPagination
            $this->currentLang, // $currentLang
            [
                'with_relationship' => [
                    'manufacturer'
                ]
            ]
        );

        return $this->returnView('frontend', 'version2.product.pages.category.inc.vang-tich-tru', [
            'data'     => $data,
            'products' => $products
        ]);
    }

    public function categoryWithinProducts(CategoryWithinProductsRequest $request, string $slug, int $id)
    {
        $category = app(GetCategoryByIdTask::class)->run($id);
        switch ($category->block_type) {
            case 'product_vang_tich_tru':
                return $this->logicVangTichTru($id, $slug);
            case 'product_vang_trang_suc':
                return $this->logicVangTrangSuc($id, $slug);
            default:
                return $this->logicVangTrangSuc($id, $slug);
        }
    }

    public function filterWithoutCategory(Request $request)
    {
        // $this->dataSeo = ['type' => 'WebSite', 'title' => 'Tìm kiếm'];
        // $this->seoSetting($this->dataSeo);

        return $this->logic($request->filter_group_value, $request->category_id, $request->sort_by);
    }

    private function logicVangTrangSuc($id, $slug)
    {
        $category = app(GetAllCategoriesForFrontEndSubAction::class)->run([
            'cate_ids'   => $id,
            'get_object' => true,
        ], $this->currentLang, true, 1, 'category.sort_order asc, category.category_id desc', [
            'with_relationship' => [
                'parent:category_id',
                'parent.desc' => function ($query) {
                    $query->select('category_id', 'language_id', 'name');
                    $query->activeLang($this->currentLang->language_id);
                }
            ]
        ]);
        if (!$category) {
            abort(404);
        }
        if ($category->desc->slug != $slug) {
            $slug = $category->desc->slug ?: Str::slug($category->desc->name);
            return redirect()->route('web_product_category_page', [$slug, $id]);
        }

        $this->configSeo($category->toArray());
        $this->seoSetting($this->dataSeo);
        $products = app(ProductListingAction::class)->run(
            [
                'published' => ProductStatus::YES,
                'cate_ids'  => [$id]
            ],
            ['created_at' => 'DESC'],
            2,
            false,
            $this->currentLang,
        );

        $collections = app(GetAllCollectionAction::class)->run([], [
            // 'in_category_ids' => [$id],
            'status'          => 1,
            'order_by'        => 'sort_order asc, created_at desc, collections.id desc',
            'skip_pagination' => true
        ], 8, $this->currentLang);

        return $this->returnView('frontend', 'version2.product.pages.category.inc.vang-trang-suc', [
            'category'    => $category,
            'products'    => $products,
            'collections' => $collections
        ]);
    }

    private function logic($filter_group_value = [], $category_id = 0, $sort_by = 'newest')
    {
        $products = app(GetProductByFilterTask::class)->run([
            'filter_ids'  => $filter_group_value,
            'limit'       => 2,
            'category_id' => $category_id,
            'orderField'  => $sort_by
        ]);
        return $this->returnView('frontend', 'version2.product.pages.list.product-list-ajax', [
            'products' => $products
        ]);
    }

    private function configSeo($category)
    {
        $this->dataSeo = [
            'type'            => "WebSite",
            'image'           => ImageURL::getImageUrl($category['current']['image'] ?? $category['image'], 'category', 'social'),
            'title'           => $category['current']['desc']['meta_title'] ?? $category['current']['desc']['name'] ?? $category['desc']['meta_title'] ?? $category['desc']['name'],
            'description'     => $category['current']['desc']['meta_description'] ?? $category['current']['desc']['short_description'] ?? $category['desc']['meta_description'] ?? $category['desc']['short_description'] ?? $category['desc']['description'],
            'keyword'         => explode(',', trim($category['current']['desc']['meta_keyword'] ?? $category['desc']['meta_keyword'])),
            'extend'          => [
                'url'             => route('web_home_page') . '/',
                "alternateName"   => env('APP_DOMAIN'),
                "potentialAction" => [
                    "@type"       => "SearchAction",
                    "target"      => [
                        'type'        => 'EntryPoint',
                        'urlTemplate' => route('web_product_search_page') . "?q={search_term_string}"
                    ],
                    "query-input" => [
                        '@type'         => 'PropertyValueSpecification',
                        'valueRequired' => 'http://schema.org/True',
                        'valueName'     => "search_term_string"
                    ]
                ]
            ],
            'dataMultiSchema' => array_merge(
            // WebsiteSchema::getData(),
            // CategoryBreadcumbListSchema::getData(['category' => $category['current']]),
            )
        ];
    }
}
