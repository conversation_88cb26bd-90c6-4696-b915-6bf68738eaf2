<?php

/**
 * @ Created by: VSCode
 * @ Author: Oops!Memory - OopsMemory.com
 * @ Create Time: 2021-08-01 14:57:39
 * @ Modified by: Oops!Memory - OopsMemory.com
 * @ Modified time: 2022-09-19 18:23:04
 * @ Description: Happy Coding!
 */

namespace App\Containers\FrontEnd\UI\WEB\Controllers\Features\Product;

use Illuminate\Http\Request;
use Apiato\Core\Foundation\ImageURL;
use App\Containers\Category\Enums\CategoryType;
use App\Containers\Product\Enums\ProductStatus;
use App\Containers\Category\Tasks\GetCategoryByIdTask;
use App\Containers\Category\Actions\GetCategoryBySlugAction;
use App\Containers\Product\Actions\FrontEnd\ProductListingAction;
use App\Containers\Collection\Actions\Admin\GetAllCollectionAction;
use App\Containers\Banner\Actions\GetAvailableBannerByPositionAction;
use App\Containers\FrontEnd\UI\WEB\Requests\Product\CategoryWithinProductsRequest;
use App\Containers\Category\Actions\SubActions\GetAllCategoriesForFrontEndSubAction;

trait CategoryIndex
{
    public function categoryIndex($slug, $category_id){
        $category = app(GetCategoryByIdTask::class)->run($category_id);
        if($category->parent_id != 0){
            return abort(404);
        }
        $this->site_title = $category->name;
        $this->seoSetting();
        $data = [];
        // Hi Boss
        $data['banner'] = app(GetAvailableBannerByPositionAction::class)->run(
            ['pos' => ['category_top'], 'categoryIds' => [$category_id]],
            $is_mobile = $this->isMobile,
            ['desc'],
            ['sort_order' => 'ASC'],
            $this->currentLang,
            8
        );
        $data['banner_middle'] = app(GetAvailableBannerByPositionAction::class)->run(
            ['pos' => ['category_middle'], 'categoryIds' => [$category_id]],
            $is_mobile = $this->isMobile,
            ['desc'],
            ['sort_order' => 'ASC'],
            $this->currentLang,
            8
        );

        $data['collections'] = app(GetAllCollectionAction::class)->run(
            [],
            ['status' => 1],
            12
        );
        return $this->returnView('frontend', 'version2.product.pages.category.index', [
            'data' => $data
        ]);
    }     
}
