<?php

/**
 * @ Created by: VSCode
 * @ Author: Oops!Memory - OopsMemory.com
 * @ Create Time: 2021-08-01 14:57:39
 * @ Modified by: Oops!Memory - OopsMemory.com
 * @ Modified time: 2022-09-19 13:51:59
 * @ Description: Happy Coding!
 */

namespace App\Containers\FrontEnd\UI\WEB\Controllers\Features\Collection;

use Apiato\Core\Foundation\Facades\Apiato;
use Apiato\Core\Foundation\FunctionLib;
use App\Containers\Collection\Actions\Admin\GetAllCollectionAction;
use App\Containers\Collection\Actions\FrontEnd\GetAllProductByCollectionAction;
use App\Containers\FrontEnd\UI\WEB\Requests\Collection\CollectionRequest;
use Illuminate\Http\Request;

trait Index
{
    public function detailCollection(CollectionRequest $request)
    {
        $collectionData = Apiato::call('Collection@Admin\GetCollectionByIdAction', [$request->id]);
        $products = app(GetAllProductByCollectionAction::class)
            ->descSpecialOfffer($this->currentLang)
            ->descTags($this->currentLang)
            ->run($request->id, [], $this->currentLang, 12);
        $this->dataSeo = [
            'type'  => "WebSite",
            'title' => __('site.collection') . $collectionData->desc->name,
        ];
        $this->seoSetting($this->dataSeo);

        return $this->returnView('frontend', 'version2.collection.detail', [
            'collectionData' => $collectionData,
            'products'       => $products,
        ]);
    }

    public function listCollection(Request $request)
    {
        $collections = app(GetAllCollectionAction::class)->run($request, [
            'status' => 1
        ], 2, $this->currentLang);

        if ($request->ajax() && $request->has('page')) {
            return FunctionLib::ajaxRespond(true, 'success', [
                'html' => preg_replace('~[\r\n\t\s]+~', ' ',
                    '<div class="_load-more_list">' . $this->returnView('frontend', 'version2.collection.inc.list-collection', [
                        'collections' => $collections
                    ])->render() . '</div>')
            ]);
        }

        $this->dataSeo = [
            'type'  => 'WebSite',
            'title' => __('site.collection'),
        ];
        $this->seoSetting($this->dataSeo);

        return $this->returnView('frontend', 'version2.collection.index', [
            'collections' => $collections
        ]);
    }
}
