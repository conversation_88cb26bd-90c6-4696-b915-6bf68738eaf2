<?php

namespace App\Containers\Category\Models;

use App\Containers\Category\Enums\CategoryType;
use App\Containers\Filter\Models\FilterGroup;
use App\Containers\Product\Models\Product;
use App\Containers\Product\Models\ProductCategory;
use App\Containers\StaticPage\Models\StaticPage;
use App\Ship\Parents\Models\Model;
use Staudenmeir\EloquentEagerLimit\HasEagerLimit;

class Category extends Model
{
    use HasEagerLimit;

    protected $table = 'category';
    protected $primaryKey = 'category_id';
    protected static $cat = [];
    protected static $splitKey = '_';
    public static $type = [
        CategoryType::PRODUCT => 'Sản phẩm',
        CategoryType::NEWS    => 'Tin tức',
    ];

    protected $fillable = [
        'image',
        'banner',
        'icon',
        'icon2',
        'parent_id',
        'partner_id',
        'type',
        'top',
        'hot',
        'sort_order',
        'status',
        'primary_color',
        'second_color',
        'group',
        'block_type',
        'route',
        'note'
    ];

    public function desc()
    {
        return $this->hasOne(CategoryDesc::class, 'category_id', 'category_id');
    }

    public function all_desc()
    {
        return $this->hasMany(CategoryDesc::class, 'category_id', 'category_id');
    }

    public function parent()
    {
        return $this->belongsTo(Category::class, 'parent_id', 'category_id');
    }

    public function filter_groups()
    {
        return $this->belongsToMany(FilterGroup::class, CategoryFilterGroup::getTableName(), 'category_id', 'filter_group_id');
    }

    public function lang()
    {
        $lang = config('app.locales');
        return isset($lang[$this->lang]) ? $lang[$this->lang] : 'vi';
    }

    public function type()
    {
        return isset(self::$type[$this->type]) ? self::$type[$this->type] : '1';
    }

    public function getImageUrl($size = 'medium')
    {
        return \ImageURL::getImageUrl($this->image, 'category', $size);
    }
    public function getBannerUrl($size = 'medium')
    {
        return \ImageURL::getImageUrl($this->banner, 'category', $size);
    }
    public function getImageUrlByImg($img = '', $size = 'medium')
    {
        return \ImageURL::getImageUrl($img, 'category', $size);
    }

    public function link()
    {
        switch ($this->type) {
            case 1:
                return '';
            case 2:
                return '';
        }
        return '';
        //return route('product.list', ['safe_title' => str_slug($this->name), 'id' => $this->id]);
    }

    public static function getType()
    {
        return self::$type;
    }

    public function products($limit = 11)
    {
        return $this->hasMany(ProductCategory::class, 'category_id', 'category_id');
    }

    public function topProducts()
    {
        return $this->belongsToMany(Product::class, 'product_category', 'category_id', 'product_id');
    }

    public function scopeWithDescActiveLang($query, $langId)
    {
        return $query->with('desc', function ($query) use ($langId) {
            $query->where('language_id', $langId);
        });
    }
}
