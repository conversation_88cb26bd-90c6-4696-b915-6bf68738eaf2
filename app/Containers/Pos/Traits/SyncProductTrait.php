<?php

namespace App\Containers\Pos\Traits;

use Illuminate\Support\Arr;
use Apiato\Core\Foundation\Facades\Apiato;
use App\Containers\Pos\Models\ProductDesc;
use App\Containers\Product\Models\Product;
use App\Containers\Category\Models\Category;
use App\Containers\Pos\Traits\SetPosHostNew;
use Apiato\Core\Foundation\Facades\StringLib;
use App\Ship\Parents\Commands\ConsoleCommand;
use App\Containers\Category\Enums\CategoryType;
use App\Containers\Product\Enums\ProductStatus;
use App\Containers\Category\Enums\CategoryGroup;
use App\Containers\Category\Models\CategoryDesc;
use App\Containers\Pos\Enums\BaoTinCateCodeEnum;
use App\Containers\Product\Models\ProductVariant;
use App\Containers\Pos\Enums\BaoTinFilterDescEnum;
use App\Containers\Product\Models\ProductOptionValue;
use App\Containers\Option\Tasks\FindOptionByKeywordTask;
use App\Containers\Option\Tasks\SaveOptionValueDescTask;
use App\Containers\Filter\Actions\GetFilterByNamesAction;
use App\Containers\Option\Actions\GetOptionsByNameAction;
use App\Containers\Pos\Enums\BaoTinCollumnSelectSyncDesc;
use App\Containers\Product\Tasks\GetProductByIdChildTask;
use App\Containers\Product\Tasks\GetProductByIdPartnerTask;
use App\Containers\Localization\Actions\GetAllLanguageDBAction;
use App\Containers\Category\Actions\GetCategoryLikeSearchAction;
use App\Containers\Pos\Actions\Products\GetAllPosProductsAction;
use App\Containers\Category\Actions\Admin\GetAllCategoriesAction;
use App\Containers\Option\Tasks\SaveOptionValueDescNotClearAllTask;
use App\Containers\Product\Actions\ProductVariant\FindVariantAction;
use App\Containers\Product\Tasks\ProductVariant\DeleteProductVariantTask;
use App\Containers\Product\Actions\ProductVariant\StoreProductVariantNotTransporterAction;

trait SyncProductTrait
{
    public function getAndDetectFilter(array $productApi)
    {
        $product_filter = [];
        $test = collect(BaoTinFilterDescEnum::COLUMN_KEY_NOT_SYNC)->keys()->toArray();
        $productApiNew = Arr::except($productApi, $test);
        $data = app(GetFilterByNamesAction::class)->run($productApiNew, 1000);
        $dataProductIsset = app(GetProductByIdPartnerTask::class)->run($productApi['id']);
        foreach ($data as $key => $value) {
            # code...
            $product_filter[] = $value->filter_id;
        }
        if (!empty($product_filter)) {
            if (is_array($product_filter)) {
                $dataProductIsset->filters()->sync($product_filter);
                if (!empty($productApi['danhMucHangHoa'])) {
                    // foreach ($productApi['danhMucHangHoa'] as $value) {
                    $dataProductChildIsset = app(GetProductByIdPartnerTask::class)->run($productApi['id']);
                    $dataProductChildIsset->filters()->sync($product_filter);
                    // }
                }
            } else {
                $dataProductIsset->filters()->attach($product_filter);
                if (!empty($productApi['danhMucHangHoa'])) {
                    // foreach ($productApi['danhMucHangHoa'] as $value) {
                    $dataProductChildIsset = app(GetProductByIdPartnerTask::class)->run($productApi['id']);
                    $dataProductChildIsset->filters()->attach($product_filter);
                    // }
                }
            }
        } else {
            $dataProductIsset->filters()->sync([]);
            if (!empty($productApi['danhMucHangHoa'])) {
                foreach ($productApi['danhMucHangHoa'] as $value) {
                    $dataProductChildIsset = app(GetProductByIdPartnerTask::class)->run($value['id']);
                    $dataProductChildIsset->filters()->sync([]);
                }
            }
        }
    }


    public function addVariant(array $productApi)
    {

        $dataProductIsset = app(GetProductByIdPartnerTask::class)->run($productApi['id']);
        // cap nhat variant root 
        $this->checkrAndCreateVariant($dataProductIsset->id, $dataProductIsset->sku, 0, 0);
        if (!empty($productApi['danhMucHangHoa'])) {
            // foreach ($productApi['danhMucHangHoa'] as $key => $value) {
            # code...
            $dataProductIsset = app(GetProductByIdPartnerTask::class)->run($productApi['id']);
            $this->checkrAndCreateVariant($dataProductIsset->id, $dataProductIsset->sku, $productApi['danhMucHangHoa'][0]['giaBan'], $productApi['danhMucHangHoa'][0]['ton_kho']);
            // }
        }
    }
    public function checkrAndCreateVariant(int $id, $sku, $price = 0, $stock = 0)
    {
        // if($sku)        
        ProductVariant::updateOrCreate(
            ['product_id' => $id],
            [
                'product_id' => $id,
                'sku' => $sku,
                'is_root' => 1,
                'price' => $price,
                'stock' => $stock,
                // 'global_price' => '',
            ]
        );
    }
    public function getAndDetectCategory(array $productApi)
    {
        $product_cate = [];
        $cateKey = CategoryGroup::getCateSync();
        $dataCateNhomSP = app(GetAllCategoriesAction::class)->run(['cate_type' => CategoryType::PRODUCT, 'group' =>  CategoryGroup::PRODUCT_TYPE, 'name' => $productApi[$cateKey[CategoryGroup::PRODUCT_TYPE]]], $limit = 100, true);
        $dataCateChungloai = app(GetAllCategoriesAction::class)->run(['cate_type' => CategoryType::PRODUCT, 'group' =>  CategoryGroup::PRODUCT_GROUP, 'name' => $productApi[$cateKey[CategoryGroup::PRODUCT_GROUP]]], $limit = 100, true);
        $dataProductIsset = app(GetProductByIdPartnerTask::class)->run($productApi['id']);
        // cap nhat variant root 

        foreach ($dataCateNhomSP as $value) {
            # code...
            $product_cate[] = $value->category_id;
        }
        foreach ($dataCateChungloai as $value) {
            # code...
            $product_cate[] = $value->category_id;
        }
        if (!empty($product_cate)) {
            if (is_array($product_cate)) {
                $dataProductIsset->categories()->sync($product_cate);
                if (!empty($productApi['danhMucHangHoa'])) {
                    // foreach ($productApi['danhMucHangHoa'] as $value) {
                    $dataProductChildIsset = app(GetProductByIdPartnerTask::class)->run($productApi['id']);
                    $dataProductChildIsset->categories()->sync($product_cate);
                    // }
                }
            } else {
                $dataProductIsset->categories()->attach($product_cate);
                if (!empty($productApi['danhMucHangHoa'])) {
                    // foreach ($productApi['danhMucHangHoa'] as $value) {
                    $dataProductChildIsset = app(GetProductByIdPartnerTask::class)->run($productApi['id']);
                    $dataProductChildIsset->categories()->attach($product_cate);
                    // }
                }
            }
        } else {
            $dataProductIsset->categories()->sync([]);
            if (!empty($productApi['danhMucHangHoa'])) {
                // foreach ($productApi['danhMucHangHoa'] as $value) {
                $dataProductChildIsset = app(GetProductByIdPartnerTask::class)->run($productApi['id']);
                $dataProductChildIsset->categories()->sync([]);
                // }
            }
        }
    }

    public function insertData(array $dataJson)
    {
        try {
            //code...
            $lang = app(GetAllLanguageDBAction::class)->run();
            $productList = $dataJson;
            $productParentNeedInsert = [];
            $productParentDescNeedInsert =  array();
            foreach ($productList as $k => $prod) {
                $checkIsset = app(GetProductByIdChildTask::class)->run($prod['id']);
                $productParentNeedInsert[$prod['id']] = [
                    'draft_name' => $prod['moTa'],
                    'sku_group' => trim($prod['maNhomMaMau']),
                    'sku' => !empty($prod['danhMucHangHoa'][0]['maSanPham']) ? trim($prod['danhMucHangHoa'][0]['maSanPham']) :  trim($prod['maNhomMaMau']),
                    'status' => 2,
                    // 'status' => $prod['maNhomMaMau'] =='KGB2C' ? 1: 2,
                    // 'price' => !empty($prod['danhMucHangHoa'][0]['giaBan']) && is_null($checkIsset) ? trim($prod['danhMucHangHoa'][0]['giaBan']) :  0,
                    'price' => !empty($prod['danhMucHangHoa'][0]['giam_gia']) ? trim($prod['danhMucHangHoa'][0]['giam_gia']) :  trim($prod['danhMucHangHoa'][0]['giaBan']) ?? 0,
                    'global_price' => !empty($prod['danhMucHangHoa'][0]['giam_gia']) ? trim($prod['danhMucHangHoa'][0]['giaBan']) :  0,
                    // 'status' => $prod['inactive'] == false ? ProductStatus::ACTIVE : ProductStatus::DE_ACTIVE,
                    'partner_id' => $prod['id'],
                    'partner_need_sync' => 0,
                    'is_parent' => 1,
                    // 'published' => 1,
                ];
                // if (!empty($prod['danhMucHangHoa'])) {
                //     $productParentNeedInsert[$prod['id']]['status'] = ProductStatus::DE_ACTIVE;
                // }
                $product =  Product::updateOrCreate([
                    'sku_group' => trim($prod['maNhomMaMau'])
                ], $productParentNeedInsert[$prod['id']]);
                // if (empty($product)) {
                //     dump($product->sku_group);
                // }
                $baotinSyncDesc = BaoTinCollumnSelectSyncDesc::COLUMN_NAME_SYNC_DESC;
                if (isset($lang) && count($lang) > 1) {
                    foreach ($baotinSyncDesc as $key => $value) {
                        foreach ($lang as $k => $v) {
                            $productParentDescNeedInsert[$prod['id']][$v['language_id']][$key] = $prod[$value];
                        }
                    }
                } else {
                    foreach ($baotinSyncDesc as $key => $value) {
                        $productParentDescNeedInsert[$prod['id']][$key] = @$prod[$value];
                    }
                    $productParentDescNeedInsert[$prod['id']]['language_id'] = intval($lang[0]['language_id']);
                    $productParentDescNeedInsert[$prod['id']]['product_id'] =   $product->id;
                    $productParentDescNeedInsert[$prod['id']]['partner_id'] =   $prod['id'];
                    $productParentDescNeedInsert[$prod['id']]['slug'] = isset($prod['moTa']) && !empty($prod['moTa']) ? StringLib::slug($prod['moTa'] . ' ' . trim($prod['maNhomMaMau'])) : 'xxx';
                    $productParentDescNeedInsert[$prod['id']]['name'] = isset($prod['moTa']) && !empty($prod['moTa']) ? ($prod['moTa'] . ' ' . trim($prod['maNhomMaMau'])) : @$prod['ghichu'] ?? '';
                }
                // if (!empty($prod['danhMucHangHoa']) && $skuCount == 3) {
                //     $this->syncProductChild($prod['danhMucHangHoa'], $productParentNeedInsert[$prod['id']], $productParentDescNeedInsert[$prod['id']], @$prod['inactive']);
                // }else{
                $this->syncProductVariant($prod['danhMucHangHoa'], $product->id, trim($prod['maNhomMaMau']));
                // }
                // ProductDesc::upsert($productParentDescNeedInsert[$prod['id']], ['product_id'], $baotinSyncDesc);
                $product =  ProductDesc::updateOrCreate([
                    'product_id' => $product->id
                ], $productParentDescNeedInsert[$prod['id']]);
                if (is_null($checkIsset)) {
                    $this->getAndDetectCategory($prod);
                    $this->getAndDetectFilter($prod);
                }
            }
            // foreach ($productList as $k => $prod) {
            //     // $this->addVariant($prod);
            //     $this->getAndDetectCategory($prod);
            //     $this->getAndDetectFilter($prod);
            // }
            return true;
        } catch (\Throwable $th) {
            //
            dd($th->getMessage(), $th->getFile(), $th->getLine());
            return false;
        }
    }

    public function setDataOption(array $option)
    {
        $data = [];
        foreach ($option['option_value'] as $key => $value) {
            # code...
            $number = explode(' ', $value['name']);
            $data[$number[1]] = [
                "option_value_id" => $value['option_value_id'],
                "option_id" => $option['option_id']
            ];
        }
        return $data;
    }
    public function syncProductVariant(array $prd_child, int $prdID, string $sku_group)
    {
        $child_ids = [];
        $optionInsert = [];
        $choiceSize = BaoTinCateCodeEnum::choiceSize();
        $cut = substr($sku_group, 2, -13);
        $is_size = isset($choiceSize[$cut]) ? true : false;
        $textSearch = $is_size ? 'Size' : 'Trọng lượng';
        $stringSearch = $is_size ? 'kich_co' : 'trongluong';
        $data = app(FindOptionByKeywordTask::class)->with([
            'option_value.desc'
        ])->run($textSearch);
        $weight = $this->fillValueOptionToSync2($data);
        foreach ($prd_child as $key => $value) {
            # code...
            $result = trim($value[$stringSearch]);
            if (!isset($weight[(string) $result]) && !empty($result)) {
                $optionInsert['option_value']['option_value_id'][$key] = '_' . $key;
                $optionInsert['option_value']['option_value_description'][1]['_' . $key] = $result;
                $optionInsert['option_value']['sort_order'][$key] = null;
                $weight[(string) $result] = [];
            }
        }
        if (!empty($optionInsert)) {
            app(SaveOptionValueDescNotClearAllTask::class)->run($optionInsert, $data[0]->id);
        }
        $weight = $this->fillValueOptionToSync2($data);
        foreach ($prd_child as $key => $value) {
            # code...
            $result = trim($value[$stringSearch]);
            if (isset($weight[(string) $result])) {
                $choice = [];
                $child_ids[] = $value['id'];
                $variantID = app(FindVariantAction::class)->run(trim($value['maSanPham']), $prdID);
                $choice = [
                    'price' => !empty($value['giam_gia']) ? trim($value['giam_gia']) :  trim($value['giaBan']) ?? 0,
                    'global_price' => !empty($value['giam_gia']) ? trim($value['giaBan']) :  0,
                    'sku' => trim($value['maSanPham']),
                    'product_id' => $prdID,
                    'child_id_pos' => $value['id'],
                    'status' => 1, //  active 
                    'choice' => [
                        'optionIds' => [
                            $weight[(string) $result]['option_id']
                        ],
                        'optionValueIds' => [
                            $weight[(string) $result]['option_value_id']
                        ],
                    ]
                ];
                if (!empty($variantID)) {
                    $choice['product_variant_id'] = $variantID;
                }
                app(StoreProductVariantNotTransporterAction::class)->run($choice);
            }
        }
        // $hasRootVariant = ProductVariant::where('product_id', $prdID)->get();
        // if (!$hasRootVariant && !empty($prd_child[0]['maSanPham'])) {
        //     ProductVariant::create(
        //         [
        //         'product_id' => $prdID,
        //         'sku' => $prd_child[0]['maSanPham'],
        //         'is_root' => 0,
        //         'price' => $prd_child[0]['giaBan'],
        //         'child_id_pos' => $prd_child[0]['id'],
        //         'global_price' => 0
        //         ]
        //     );
        //     $child_ids[]= $prd_child[0]['id'];
        // 
        Apiato::call('Product@ProductVariant\DeleteProductsVariantNotInTask', [$child_ids, $prdID]);
    }
    public function fillValueOptionToSync($data)
    {
        $weight = [];
        foreach ($data[0]->option_value ?? [] as $key => $value) {
            # code...
            if (!empty($value->desc->name))
                $weight[$value->desc->name] = $value->desc;
        }
        return $weight;
    }

    public function fillValueOptionToSync2($data)
    {
        $weight = [];
        foreach ($data[0]->option_value ?? [] as $key => $value) {
            # code...
            if (!empty($value->desc->name)) {
                $weight[$value->desc->name] = $value->desc;
            } else {
                $weight['0'] = $value->desc;
            }
        }
        return $weight;
    }
    public function syncProductChild(array $prd_child, array $prdInfo, array $prdDescInfo, $inactive)
    {
        foreach ($prd_child as $key => $value) {
            // tao product
            $prdInfoChild = [];
            $prdDesChild = [];
            $prdInfoChild = $prdInfo;

            $prdInfoChild['sku'] = trim($value['maSanPham']);
            $prdInfoChild['status'] = $inactive == false ? ProductStatus::DE_ACTIVE : ProductStatus::DE_ACTIVE;
            $prdInfoChild['price'] = (int) $value['giaBan'];
            $prdInfoChild['stock'] = (int) $value['ton_kho'];
            $prdInfoChild['barcode'] = (int) $value['maVach'];
            $prdInfoChild['child_id'] = (int) $value['id'];
            $prdInfoChild['is_parent'] = 0;
            $product =  Product::updateOrCreate([
                'sku' => trim($value['maSanPham'])
            ], $prdInfoChild);
            $prdDesChild = $prdDescInfo;
            $prdDesChild['name'] = trim($value['tenSanPham']) . ' ' . trim($value['maSanPham']);
            $prdDesChild['slug'] = StringLib::slug(trim($value['tenSanPham']) . trim($value['maSanPham']));
            $prdDesChild['product_id'] = $product->id;
            ProductDesc::updateOrCreate([
                'product_id' => $product->id
            ], $prdDesChild);
        }
        // tao product descreption

    }
}
